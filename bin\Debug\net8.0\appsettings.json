{"Note01": "WorkDirectories are being checked by order, and upon one works, checking breaks and the worked one being used", "WorkDirectories": ["..\\..\\..\\Projects\\CA"], "TestSuiteFolderPaths": ["WorkSet01\\TestSuites", "WorkSet02\\TestSuites", "WorkSet03\\TestSuites", "WorkSet04\\TestSuites", "MainWorkSet\\TestSuites"], "BaseUrls": {"UAT": "https://digitalbanking.ebseg.com/DigitalBankingCustom2/eBankApplication/ePortal5Core/ePortal5.htm?Menu=New#/EN/Landing", "Pilot": "https://ebs.ca-egypt.com/OmniChannel/CEEPDigitalBankR6/eBankApplication/ePortal5Core/ePortal5.htm?Menu=New#/EN/Landing", "UAT61": "https://banki-uat61.ebseg.com/OmniChannel/CEEPDigitalBankR6/eBankApplication/ePortal5Core/ePortal5.htm?Menu=New#/EN/Landing"}, "LoadingSpinnerTargetSelector": "id=LoadingSpinner", "GeneralWaitTimeForSpinnerInMilliseconds": 90000, "DelayBeforeStepExecustionInMilliseconds": 500, "PageLoadTimeoutInSeconds": 90, "GlobalParamsPath": "GlobalParams.json", "ExcludedTestDataPath": "ExcludedTestData.json", "OutputDir": "Results", "MailSettings": {"smtpServer": "mail.ebseg.com", "smtpPort": 587, "smtpUsername": "<EMAIL>", "smtpPassword": "eBSEG@12345", "recipients": ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"]}}