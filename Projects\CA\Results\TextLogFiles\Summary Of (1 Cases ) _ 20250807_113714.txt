================================ Start of LogFile ================================
2025-08-07 11:36:41.447 [Info] Checking for work directory: ..\..\..\Projects\CA
2025-08-07 11:36:41.487 [Info] Using work directory: D:\TFS\DevWork\R and D Work\TestAutomationFramework\TestAutomationFramework\Projects\CA
2025-08-07 11:36:41.487 [Info] No Args for SuitePaths, scanning: WorkSet01\TestSuites, WorkSet02\TestSuites, WorkSet03\TestSuites, WorkSet04\TestSuites, MainWorkSet\TestSuites
2025-08-07 11:36:41.490 [Info] Retrieving all test cases.
2025-08-07 11:36:41.491 [Info] Retrieving test suite paths.
2025-08-07 11:36:41.492 [Info] Retrieved 0 test suite paths.
2025-08-07 11:36:41.493 [Info] Retrieved 1 test suite paths.
2025-08-07 11:36:41.493 [Info] Retrieved 99 test suite paths.
2025-08-07 11:36:41.494 [Info] Retrieved 154 test suite paths.
2025-08-07 11:36:41.494 [Info] Retrieved 164 test suite paths.
2025-08-07 11:36:41.495 [Info] Loading RunningInstructions configuration...
2025-08-07 11:36:41.496 [Info] Attempting to load configuration file: RunningInstructions.json
2025-08-07 11:36:41.501 [Info] Successfully loaded configuration file: RunningInstructions.json
2025-08-07 11:36:41.517 [Info] Attempting to load configuration file: RunningInstructions.local.json
2025-08-07 11:36:41.517 [Info] Successfully loaded configuration file: RunningInstructions.local.json
2025-08-07 11:36:41.517 [Info] Merging local RunningInstructions with main configuration.
2025-08-07 11:36:41.517 [Info] Successfully merged local RunningInstructions with main configuration.
2025-08-07 11:36:41.563 [Debug] Applying suite filtering with regexes: .*workset01.*testsuites.*
2025-08-07 11:36:41.573 [Info] Loading test suite: WorkSet01\TestSuites\Sanity.json
2025-08-07 11:36:41.573 [Info] Attempting to load test suite: WorkSet01\TestSuites\Sanity.json
2025-08-07 11:36:41.592 [Info] Successfully loaded test suite: WorkSet01\TestSuites\Sanity.json
2025-08-07 11:36:41.593 [Debug] Loading test case: WorkSet01/TestCases/021.HappyPoints/29.json
2025-08-07 11:36:41.593 [Info] Loading test case from: WorkSet01/TestCases/021.HappyPoints/29.json
2025-08-07 11:36:41.593 [Info] Attempting to load configuration file: WorkSet01/TestCases/021.HappyPoints/29.json
2025-08-07 11:36:41.602 [Info] Successfully loaded configuration file: WorkSet01/TestCases/021.HappyPoints/29.json
2025-08-07 11:36:41.620 [Info] No filtering configuration found. Including test case 'Happy Points valid redemption'.
2025-08-07 11:36:41.623 [Info] Attempting to load configuration file: GlobalParams.json
2025-08-07 11:36:41.629 [Info] Successfully loaded configuration file: GlobalParams.json
2025-08-07 11:36:41.640 [Debug] Resolving parameters for test case: Happy Points valid redemption
2025-08-07 11:36:41.641 [Debug] Merging params
2025-08-07 11:36:41.641 [Debug] Merged basic params
2025-08-07 11:36:41.641 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-07 11:36:41.641 [Debug] Merged SpecialEnvParams params
2025-08-07 11:36:41.641 [Debug] Merged params
2025-08-07 11:36:41.641 [Debug] Loading parameters from reference file: WorkSet01/Params/HappyPointsParams.json
2025-08-07 11:36:41.641 [Info] Attempting to load configuration file: WorkSet01/Params/HappyPointsParams.json
2025-08-07 11:36:41.651 [Info] Successfully loaded configuration file: WorkSet01/Params/HappyPointsParams.json
2025-08-07 11:36:41.651 [Debug] Merging params
2025-08-07 11:36:41.651 [Debug] Merged basic params
2025-08-07 11:36:41.651 [Debug] Merged params
2025-08-07 11:36:41.651 [Debug] Merging params
2025-08-07 11:36:41.651 [Debug] Merging params
2025-08-07 11:36:41.651 [Debug] Resolving placeholders in test steps for test case: Happy Points valid redemption
2025-08-07 11:36:41.651 [Debug] Resolving TestCaseReference in step: 
2025-08-07 11:36:41.651 [Info] Loading test case from: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:36:41.651 [Info] Attempting to load configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:36:41.658 [Info] Successfully loaded configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:36:41.660 [Debug] Resolving parameters for test case: Login
2025-08-07 11:36:41.660 [Debug] Merging params
2025-08-07 11:36:41.660 [Debug] Merged basic params
2025-08-07 11:36:41.660 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-07 11:36:41.660 [Debug] Merged SpecialEnvParams params
2025-08-07 11:36:41.660 [Debug] Merged params
2025-08-07 11:36:41.660 [Debug] Loading parameters from reference file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:36:41.660 [Info] Attempting to load configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:36:41.666 [Info] Successfully loaded configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:36:41.666 [Debug] Merging params
2025-08-07 11:36:41.666 [Debug] Merged basic params
2025-08-07 11:36:41.666 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-07 11:36:41.666 [Debug] Merged SpecialEnvParams params
2025-08-07 11:36:41.666 [Debug] Merged params
2025-08-07 11:36:41.666 [Debug] Merging params
2025-08-07 11:36:41.666 [Debug] Merging params
2025-08-07 11:36:41.666 [Debug] Resolving placeholders in test steps for test case: Login
2025-08-07 11:36:41.667 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-07 11:36:41.667 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-07 11:36:41.667 [Debug] Resolving placeholders in string: {{Selectors.FinishButton}}
2025-08-07 11:36:41.667 [Debug] Resolved string: id=finish
2025-08-07 11:36:41.668 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-07 11:36:41.668 [Debug] Resolved string: Click on Login Button
2025-08-07 11:36:41.668 [Debug] Resolving placeholders in string: {{Selectors.LoginButton}}
2025-08-07 11:36:41.668 [Debug] Resolved string: id=LoginBtn
2025-08-07 11:36:41.668 [Debug] Resolving placeholders in string: Type the User Name
2025-08-07 11:36:41.668 [Debug] Resolved string: Type the User Name
2025-08-07 11:36:41.668 [Debug] Resolving placeholders in string: {{Selectors.UserNameField}}
2025-08-07 11:36:41.668 [Debug] Resolved string: id=UserName
2025-08-07 11:36:41.668 [Debug] Resolving placeholders in string: {{TestData.UserName}}
2025-08-07 11:36:41.668 [Debug] Resolved string: Mm_azmy
2025-08-07 11:36:41.668 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-07 11:36:41.668 [Debug] Resolved string: Click on Continue Button
2025-08-07 11:36:41.668 [Debug] Resolving placeholders in string: {{Selectors.ContinueButton}}
2025-08-07 11:36:41.668 [Debug] Resolved string: id=btn
2025-08-07 11:36:41.668 [Debug] Resolving placeholders in string: Type the Password
2025-08-07 11:36:41.668 [Debug] Resolved string: Type the Password
2025-08-07 11:36:41.668 [Debug] Resolving placeholders in string: {{Selectors.PasswordField}}
2025-08-07 11:36:41.668 [Debug] Resolved string: id=Password
2025-08-07 11:36:41.668 [Debug] Resolving placeholders in string: $('[{{Selectors.PasswordField}}]').val('{{TestData.Password}}')
2025-08-07 11:36:41.668 [Debug] Resolved string: $('[id=Password]').val('Asdf2025')
2025-08-07 11:36:41.668 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-07 11:36:41.668 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-07 11:36:41.668 [Debug] Resolving placeholders in string: {{Selectors.ChallengeQuestionField}}
2025-08-07 11:36:41.668 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-07 11:36:41.668 [Debug] Resolving placeholders in string: {{TestData.ChallengeAnswer}}
2025-08-07 11:36:41.668 [Debug] Resolved string: armada
2025-08-07 11:36:41.668 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-07 11:36:41.668 [Debug] Resolved string: Click on the Login Button
2025-08-07 11:36:41.668 [Debug] Resolving placeholders in string: {{Selectors.LoginAuthButton}}
2025-08-07 11:36:41.669 [Debug] Resolved string: id=LoginAuthBtn
2025-08-07 11:36:41.669 [Info] Successfully resolved parameters for test case: Login
2025-08-07 11:36:41.669 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-07 11:36:41.669 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-07 11:36:41.669 [Debug] Resolving placeholders in string: id=finish
2025-08-07 11:36:41.669 [Debug] Resolved string: id=finish
2025-08-07 11:36:41.669 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-07 11:36:41.669 [Debug] Resolved string: Click on Login Button
2025-08-07 11:36:41.669 [Debug] Resolving placeholders in string: id=LoginBtn
2025-08-07 11:36:41.669 [Debug] Resolved string: id=LoginBtn
2025-08-07 11:36:41.669 [Debug] Resolving placeholders in string: Type the User Name
2025-08-07 11:36:41.669 [Debug] Resolved string: Type the User Name
2025-08-07 11:36:41.669 [Debug] Resolving placeholders in string: id=UserName
2025-08-07 11:36:41.669 [Debug] Resolved string: id=UserName
2025-08-07 11:36:41.669 [Debug] Resolving placeholders in string: Mm_azmy
2025-08-07 11:36:41.669 [Debug] Resolved string: Mm_azmy
2025-08-07 11:36:41.669 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-07 11:36:41.669 [Debug] Resolved string: Click on Continue Button
2025-08-07 11:36:41.669 [Debug] Resolving placeholders in string: id=btn
2025-08-07 11:36:41.669 [Debug] Resolved string: id=btn
2025-08-07 11:36:41.669 [Debug] Resolving placeholders in string: Type the Password
2025-08-07 11:36:41.669 [Debug] Resolved string: Type the Password
2025-08-07 11:36:41.669 [Debug] Resolving placeholders in string: id=Password
2025-08-07 11:36:41.669 [Debug] Resolved string: id=Password
2025-08-07 11:36:41.669 [Debug] Resolving placeholders in string: $('[id=Password]').val('Asdf2025')
2025-08-07 11:36:41.669 [Debug] Resolved string: $('[id=Password]').val('Asdf2025')
2025-08-07 11:36:41.669 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-07 11:36:41.669 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-07 11:36:41.669 [Debug] Resolving placeholders in string: id=ChallengeQuestionAnswer
2025-08-07 11:36:41.669 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-07 11:36:41.669 [Debug] Resolving placeholders in string: armada
2025-08-07 11:36:41.669 [Debug] Resolved string: armada
2025-08-07 11:36:41.669 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-07 11:36:41.669 [Debug] Resolved string: Click on the Login Button
2025-08-07 11:36:41.669 [Debug] Resolving placeholders in string: id=LoginAuthBtn
2025-08-07 11:36:41.669 [Debug] Resolved string: id=LoginAuthBtn
2025-08-07 11:36:41.669 [Debug] Resolving placeholders in string: Click on change later button on the password expiration popup message
2025-08-07 11:36:41.669 [Debug] Resolved string: Click on change later button on the password expiration popup message
2025-08-07 11:36:41.669 [Debug] Resolving placeholders in string: {{Selectors.popupCancel}}
2025-08-07 11:36:41.669 [Debug] Resolved string: id=popup_cancel
2025-08-07 11:36:41.669 [Debug] Resolving placeholders in string: Click on biometric alert
2025-08-07 11:36:41.669 [Debug] Resolved string: Click on biometric alert
2025-08-07 11:36:41.669 [Debug] Resolving placeholders in string: {{Selectors.BioMetricAlert}}
2025-08-07 11:36:41.669 [Debug] Resolved string: css=#popbuttons > div > button.btn.Cancel.back_gradient2
2025-08-07 11:36:41.669 [Debug] Resolving placeholders in string: Click on Happy points option from side menu
2025-08-07 11:36:41.669 [Debug] Resolved string: Click on Happy points option from side menu
2025-08-07 11:36:41.669 [Debug] Resolving placeholders in string: {{Selectors.HappyPointsOption}}
2025-08-07 11:36:41.669 [Debug] Resolved string: xpath=//div[@class='mainmenuitems']//label[contains(text(), 'Happy Points')]
2025-08-07 11:36:41.669 [Debug] Resolving placeholders in string: Click on Redeem Online button
2025-08-07 11:36:41.670 [Debug] Resolved string: Click on Redeem Online button
2025-08-07 11:36:41.670 [Debug] Resolving placeholders in string: {{Selectors.RedeemOnline}}
2025-08-07 11:36:41.670 [Debug] Resolved string: css=#BuyOnlineBTN > div
2025-08-07 11:36:41.670 [Debug] Resolving placeholders in string: Choose a merchant
2025-08-07 11:36:41.670 [Debug] Resolved string: Choose a merchant
2025-08-07 11:36:41.670 [Debug] Resolving placeholders in string: {{Selectors.SelectMerchant}}
2025-08-07 11:36:41.670 [Debug] Resolved string: css=#RequestItem0
2025-08-07 11:36:41.670 [Debug] Resolving placeholders in string: Click Redeem button
2025-08-07 11:36:41.670 [Debug] Resolved string: Click Redeem button
2025-08-07 11:36:41.670 [Debug] Resolving placeholders in string: {{Selectors.RedeemButton}}
2025-08-07 11:36:41.670 [Debug] Resolved string: id=RedeemID
2025-08-07 11:36:41.670 [Debug] Resolving placeholders in string: Enter the number of points to redeem
2025-08-07 11:36:41.670 [Debug] Resolved string: Enter the number of points to redeem
2025-08-07 11:36:41.670 [Debug] Resolving placeholders in string: {{Selectors.PointsTextbox}}
2025-08-07 11:36:41.670 [Debug] Resolved string: id=redeempointsID
2025-08-07 11:36:41.670 [Debug] Resolving placeholders in string: 15000
2025-08-07 11:36:41.670 [Debug] Resolved string: 15000
2025-08-07 11:36:41.670 [Debug] Resolving placeholders in string: Click on create cupon button
2025-08-07 11:36:41.670 [Debug] Resolved string: Click on create cupon button
2025-08-07 11:36:41.670 [Debug] Resolving placeholders in string: {{Selectors.CreateCupon}}
2025-08-07 11:36:41.670 [Debug] Resolved string: id=CCoponID
2025-08-07 11:36:41.670 [Debug] Resolving placeholders in string: Click on Continue button
2025-08-07 11:36:41.670 [Debug] Resolved string: Click on Continue button
2025-08-07 11:36:41.670 [Debug] Resolving placeholders in string: {{Selectors.CuntinueConfirmCupon}}
2025-08-07 11:36:41.670 [Debug] Resolved string: id=RequestRedemptionConfrimationSbmtBtn
2025-08-07 11:36:41.670 [Debug] Resolving placeholders in string: Ensure that it redirected to the token page
2025-08-07 11:36:41.670 [Debug] Resolved string: Ensure that it redirected to the token page
2025-08-07 11:36:41.670 [Debug] Resolving placeholders in string: {{Selectors.OTPbox}}
2025-08-07 11:36:41.670 [Debug] Resolved string: id=OTPInpt
2025-08-07 11:36:41.670 [Info] Successfully resolved parameters for test case: Happy Points valid redemption
2025-08-07 11:36:41.672 [Debug] Loading test case: WorkSet01/TestCases/021.HappyPoints/31.json
2025-08-07 11:36:41.672 [Info] Loading test case from: WorkSet01/TestCases/021.HappyPoints/31.json
2025-08-07 11:36:41.673 [Info] Attempting to load configuration file: WorkSet01/TestCases/021.HappyPoints/31.json
2025-08-07 11:36:41.676 [Info] Successfully loaded configuration file: WorkSet01/TestCases/021.HappyPoints/31.json
2025-08-07 11:36:41.676 [Info] No filtering configuration found. Including test case 'Redeem less than 10000 points'.
2025-08-07 11:36:41.676 [Debug] Resolving parameters for test case: Redeem less than 10000 points
2025-08-07 11:36:41.676 [Debug] Merging params
2025-08-07 11:36:41.676 [Debug] Merged basic params
2025-08-07 11:36:41.676 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-07 11:36:41.676 [Debug] Merged SpecialEnvParams params
2025-08-07 11:36:41.676 [Debug] Merged params
2025-08-07 11:36:41.677 [Debug] Loading parameters from reference file: WorkSet01/Params/HappyPointsParams.json
2025-08-07 11:36:41.677 [Info] Attempting to load configuration file: WorkSet01/Params/HappyPointsParams.json
2025-08-07 11:36:41.677 [Info] Successfully loaded configuration file: WorkSet01/Params/HappyPointsParams.json
2025-08-07 11:36:41.677 [Debug] Merging params
2025-08-07 11:36:41.677 [Debug] Merged basic params
2025-08-07 11:36:41.677 [Debug] Merged params
2025-08-07 11:36:41.677 [Debug] Merging params
2025-08-07 11:36:41.677 [Debug] Merging params
2025-08-07 11:36:41.677 [Debug] Resolving placeholders in test steps for test case: Redeem less than 10000 points
2025-08-07 11:36:41.677 [Debug] Resolving TestCaseReference in step: 
2025-08-07 11:36:41.677 [Info] Loading test case from: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:36:41.677 [Info] Attempting to load configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:36:41.678 [Info] Successfully loaded configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:36:41.678 [Debug] Resolving parameters for test case: Login
2025-08-07 11:36:41.678 [Debug] Merging params
2025-08-07 11:36:41.678 [Debug] Merged basic params
2025-08-07 11:36:41.678 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-07 11:36:41.678 [Debug] Merged SpecialEnvParams params
2025-08-07 11:36:41.678 [Debug] Merged params
2025-08-07 11:36:41.678 [Debug] Loading parameters from reference file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:36:41.689 [Info] Attempting to load configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:36:41.689 [Info] Successfully loaded configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:36:41.690 [Debug] Merging params
2025-08-07 11:36:41.690 [Debug] Merged basic params
2025-08-07 11:36:41.690 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-07 11:36:41.690 [Debug] Merged SpecialEnvParams params
2025-08-07 11:36:41.690 [Debug] Merged params
2025-08-07 11:36:41.690 [Debug] Merging params
2025-08-07 11:36:41.690 [Debug] Merging params
2025-08-07 11:36:41.690 [Debug] Resolving placeholders in test steps for test case: Login
2025-08-07 11:36:41.690 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-07 11:36:41.690 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-07 11:36:41.690 [Debug] Resolving placeholders in string: {{Selectors.FinishButton}}
2025-08-07 11:36:41.690 [Debug] Resolved string: id=finish
2025-08-07 11:36:41.690 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-07 11:36:41.690 [Debug] Resolved string: Click on Login Button
2025-08-07 11:36:41.690 [Debug] Resolving placeholders in string: {{Selectors.LoginButton}}
2025-08-07 11:36:41.690 [Debug] Resolved string: id=LoginBtn
2025-08-07 11:36:41.690 [Debug] Resolving placeholders in string: Type the User Name
2025-08-07 11:36:41.690 [Debug] Resolved string: Type the User Name
2025-08-07 11:36:41.690 [Debug] Resolving placeholders in string: {{Selectors.UserNameField}}
2025-08-07 11:36:41.690 [Debug] Resolved string: id=UserName
2025-08-07 11:36:41.690 [Debug] Resolving placeholders in string: {{TestData.UserName}}
2025-08-07 11:36:41.690 [Debug] Resolved string: Mm_azmy
2025-08-07 11:36:41.690 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-07 11:36:41.690 [Debug] Resolved string: Click on Continue Button
2025-08-07 11:36:41.690 [Debug] Resolving placeholders in string: {{Selectors.ContinueButton}}
2025-08-07 11:36:41.690 [Debug] Resolved string: id=btn
2025-08-07 11:36:41.690 [Debug] Resolving placeholders in string: Type the Password
2025-08-07 11:36:41.690 [Debug] Resolved string: Type the Password
2025-08-07 11:36:41.690 [Debug] Resolving placeholders in string: {{Selectors.PasswordField}}
2025-08-07 11:36:41.690 [Debug] Resolved string: id=Password
2025-08-07 11:36:41.690 [Debug] Resolving placeholders in string: $('[{{Selectors.PasswordField}}]').val('{{TestData.Password}}')
2025-08-07 11:36:41.690 [Debug] Resolved string: $('[id=Password]').val('Asdf2025')
2025-08-07 11:36:41.690 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-07 11:36:41.690 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-07 11:36:41.690 [Debug] Resolving placeholders in string: {{Selectors.ChallengeQuestionField}}
2025-08-07 11:36:41.690 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-07 11:36:41.690 [Debug] Resolving placeholders in string: {{TestData.ChallengeAnswer}}
2025-08-07 11:36:41.690 [Debug] Resolved string: armada
2025-08-07 11:36:41.690 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-07 11:36:41.690 [Debug] Resolved string: Click on the Login Button
2025-08-07 11:36:41.690 [Debug] Resolving placeholders in string: {{Selectors.LoginAuthButton}}
2025-08-07 11:36:41.690 [Debug] Resolved string: id=LoginAuthBtn
2025-08-07 11:36:41.690 [Info] Successfully resolved parameters for test case: Login
2025-08-07 11:36:41.690 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-07 11:36:41.690 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-07 11:36:41.690 [Debug] Resolving placeholders in string: id=finish
2025-08-07 11:36:41.690 [Debug] Resolved string: id=finish
2025-08-07 11:36:41.690 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-07 11:36:41.690 [Debug] Resolved string: Click on Login Button
2025-08-07 11:36:41.691 [Debug] Resolving placeholders in string: id=LoginBtn
2025-08-07 11:36:41.691 [Debug] Resolved string: id=LoginBtn
2025-08-07 11:36:41.691 [Debug] Resolving placeholders in string: Type the User Name
2025-08-07 11:36:41.691 [Debug] Resolved string: Type the User Name
2025-08-07 11:36:41.691 [Debug] Resolving placeholders in string: id=UserName
2025-08-07 11:36:41.691 [Debug] Resolved string: id=UserName
2025-08-07 11:36:41.691 [Debug] Resolving placeholders in string: Mm_azmy
2025-08-07 11:36:41.691 [Debug] Resolved string: Mm_azmy
2025-08-07 11:36:41.691 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-07 11:36:41.691 [Debug] Resolved string: Click on Continue Button
2025-08-07 11:36:41.691 [Debug] Resolving placeholders in string: id=btn
2025-08-07 11:36:41.691 [Debug] Resolved string: id=btn
2025-08-07 11:36:41.691 [Debug] Resolving placeholders in string: Type the Password
2025-08-07 11:36:41.691 [Debug] Resolved string: Type the Password
2025-08-07 11:36:41.691 [Debug] Resolving placeholders in string: id=Password
2025-08-07 11:36:41.691 [Debug] Resolved string: id=Password
2025-08-07 11:36:41.691 [Debug] Resolving placeholders in string: $('[id=Password]').val('Asdf2025')
2025-08-07 11:36:41.691 [Debug] Resolved string: $('[id=Password]').val('Asdf2025')
2025-08-07 11:36:41.691 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-07 11:36:41.691 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-07 11:36:41.691 [Debug] Resolving placeholders in string: id=ChallengeQuestionAnswer
2025-08-07 11:36:41.692 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-07 11:36:41.692 [Debug] Resolving placeholders in string: armada
2025-08-07 11:36:41.692 [Debug] Resolved string: armada
2025-08-07 11:36:41.692 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-07 11:36:41.692 [Debug] Resolved string: Click on the Login Button
2025-08-07 11:36:41.692 [Debug] Resolving placeholders in string: id=LoginAuthBtn
2025-08-07 11:36:41.692 [Debug] Resolved string: id=LoginAuthBtn
2025-08-07 11:36:41.692 [Debug] Resolving placeholders in string: Click on Happy points option from side menu
2025-08-07 11:36:41.692 [Debug] Resolved string: Click on Happy points option from side menu
2025-08-07 11:36:41.692 [Debug] Resolving placeholders in string: {{Selectors.HappyPointsOption}}
2025-08-07 11:36:41.692 [Debug] Resolved string: xpath=//div[@class='mainmenuitems']//label[contains(text(), 'Happy Points')]
2025-08-07 11:36:41.692 [Debug] Resolving placeholders in string: Click on Redeem Online button
2025-08-07 11:36:41.692 [Debug] Resolved string: Click on Redeem Online button
2025-08-07 11:36:41.692 [Debug] Resolving placeholders in string: {{Selectors.RedeemOnline}}
2025-08-07 11:36:41.692 [Debug] Resolved string: css=#BuyOnlineBTN > div
2025-08-07 11:36:41.692 [Debug] Resolving placeholders in string: Choose a merchant
2025-08-07 11:36:41.692 [Debug] Resolved string: Choose a merchant
2025-08-07 11:36:41.692 [Debug] Resolving placeholders in string: {{Selectors.SelectMerchant}}
2025-08-07 11:36:41.692 [Debug] Resolved string: css=#RequestItem0
2025-08-07 11:36:41.692 [Debug] Resolving placeholders in string: Click Redeem button
2025-08-07 11:36:41.692 [Debug] Resolved string: Click Redeem button
2025-08-07 11:36:41.692 [Debug] Resolving placeholders in string: {{Selectors.RedeemButton}}
2025-08-07 11:36:41.692 [Debug] Resolved string: id=RedeemID
2025-08-07 11:36:41.692 [Debug] Resolving placeholders in string: Enter the number of points to redeem
2025-08-07 11:36:41.692 [Debug] Resolved string: Enter the number of points to redeem
2025-08-07 11:36:41.692 [Debug] Resolving placeholders in string: {{Selectors.PointsTextbox}}
2025-08-07 11:36:41.692 [Debug] Resolved string: id=redeempointsID
2025-08-07 11:36:41.692 [Debug] Resolving placeholders in string: 900
2025-08-07 11:36:41.692 [Debug] Resolved string: 900
2025-08-07 11:36:41.692 [Debug] Resolving placeholders in string: Click on create cupon button
2025-08-07 11:36:41.693 [Debug] Resolved string: Click on create cupon button
2025-08-07 11:36:41.693 [Debug] Resolving placeholders in string: {{Selectors.CreateCupon}}
2025-08-07 11:36:41.693 [Debug] Resolved string: id=CCoponID
2025-08-07 11:36:41.693 [Debug] Resolving placeholders in string: Click on Continue button
2025-08-07 11:36:41.693 [Debug] Resolved string: Click on Continue button
2025-08-07 11:36:41.693 [Debug] Resolving placeholders in string: {{Selectors.CuntinueConfirmCupon}}
2025-08-07 11:36:41.693 [Debug] Resolved string: id=RequestRedemptionConfrimationSbmtBtn
2025-08-07 11:36:41.693 [Debug] Resolving placeholders in string: Ensure that it redirected to the token page
2025-08-07 11:36:41.693 [Debug] Resolved string: Ensure that it redirected to the token page
2025-08-07 11:36:41.693 [Debug] Resolving placeholders in string: {{Selectors.OTPbox}}
2025-08-07 11:36:41.693 [Debug] Resolved string: id=OTPInpt
2025-08-07 11:36:41.693 [Info] Successfully resolved parameters for test case: Redeem less than 10000 points
2025-08-07 11:36:41.693 [Debug] Loading test case: WorkSet01/TestCases/021.HappyPoints/32.json
2025-08-07 11:36:41.693 [Info] Loading test case from: WorkSet01/TestCases/021.HappyPoints/32.json
2025-08-07 11:36:41.693 [Info] Attempting to load configuration file: WorkSet01/TestCases/021.HappyPoints/32.json
2025-08-07 11:36:41.694 [Info] Successfully loaded configuration file: WorkSet01/TestCases/021.HappyPoints/32.json
2025-08-07 11:36:41.694 [Info] No filtering configuration found. Including test case 'Verify That User Can't Redeem Happy Points Using Wrong SMS'.
2025-08-07 11:36:41.694 [Debug] Resolving parameters for test case: Verify That User Can't Redeem Happy Points Using Wrong SMS
2025-08-07 11:36:41.694 [Debug] Merging params
2025-08-07 11:36:41.694 [Debug] Merged basic params
2025-08-07 11:36:41.694 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-07 11:36:41.694 [Debug] Merged SpecialEnvParams params
2025-08-07 11:36:41.694 [Debug] Merged params
2025-08-07 11:36:41.694 [Debug] Loading parameters from reference file: WorkSet01/Params/HappyPointsParams.json
2025-08-07 11:36:41.695 [Info] Attempting to load configuration file: WorkSet01/Params/HappyPointsParams.json
2025-08-07 11:36:41.695 [Info] Successfully loaded configuration file: WorkSet01/Params/HappyPointsParams.json
2025-08-07 11:36:41.695 [Debug] Merging params
2025-08-07 11:36:41.695 [Debug] Merged basic params
2025-08-07 11:36:41.695 [Debug] Merged params
2025-08-07 11:36:41.695 [Debug] Merging params
2025-08-07 11:36:41.697 [Debug] Merging params
2025-08-07 11:36:41.697 [Debug] Resolving placeholders in test steps for test case: Verify That User Can't Redeem Happy Points Using Wrong SMS
2025-08-07 11:36:41.697 [Debug] Resolving TestCaseReference in step: 
2025-08-07 11:36:41.697 [Info] Loading test case from: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:36:41.697 [Info] Attempting to load configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:36:41.698 [Info] Successfully loaded configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:36:41.698 [Debug] Resolving parameters for test case: Login
2025-08-07 11:36:41.698 [Debug] Merging params
2025-08-07 11:36:41.698 [Debug] Merged basic params
2025-08-07 11:36:41.698 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-07 11:36:41.698 [Debug] Merged SpecialEnvParams params
2025-08-07 11:36:41.698 [Debug] Merged params
2025-08-07 11:36:41.698 [Debug] Loading parameters from reference file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:36:41.698 [Info] Attempting to load configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:36:41.698 [Info] Successfully loaded configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:36:41.698 [Debug] Merging params
2025-08-07 11:36:41.699 [Debug] Merged basic params
2025-08-07 11:36:41.699 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-07 11:36:41.699 [Debug] Merged SpecialEnvParams params
2025-08-07 11:36:41.699 [Debug] Merged params
2025-08-07 11:36:41.699 [Debug] Merging params
2025-08-07 11:36:41.699 [Debug] Merging params
2025-08-07 11:36:41.699 [Debug] Resolving placeholders in test steps for test case: Login
2025-08-07 11:36:41.699 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-07 11:36:41.699 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-07 11:36:41.699 [Debug] Resolving placeholders in string: {{Selectors.FinishButton}}
2025-08-07 11:36:41.706 [Debug] Resolved string: id=finish
2025-08-07 11:36:41.706 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-07 11:36:41.706 [Debug] Resolved string: Click on Login Button
2025-08-07 11:36:41.706 [Debug] Resolving placeholders in string: {{Selectors.LoginButton}}
2025-08-07 11:36:41.706 [Debug] Resolved string: id=LoginBtn
2025-08-07 11:36:41.706 [Debug] Resolving placeholders in string: Type the User Name
2025-08-07 11:36:41.706 [Debug] Resolved string: Type the User Name
2025-08-07 11:36:41.706 [Debug] Resolving placeholders in string: {{Selectors.UserNameField}}
2025-08-07 11:36:41.706 [Debug] Resolved string: id=UserName
2025-08-07 11:36:41.707 [Debug] Resolving placeholders in string: {{TestData.UserName}}
2025-08-07 11:36:41.707 [Debug] Resolved string: Mm_azmy
2025-08-07 11:36:41.707 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-07 11:36:41.707 [Debug] Resolved string: Click on Continue Button
2025-08-07 11:36:41.707 [Debug] Resolving placeholders in string: {{Selectors.ContinueButton}}
2025-08-07 11:36:41.707 [Debug] Resolved string: id=btn
2025-08-07 11:36:41.707 [Debug] Resolving placeholders in string: Type the Password
2025-08-07 11:36:41.707 [Debug] Resolved string: Type the Password
2025-08-07 11:36:41.707 [Debug] Resolving placeholders in string: {{Selectors.PasswordField}}
2025-08-07 11:36:41.707 [Debug] Resolved string: id=Password
2025-08-07 11:36:41.707 [Debug] Resolving placeholders in string: $('[{{Selectors.PasswordField}}]').val('{{TestData.Password}}')
2025-08-07 11:36:41.707 [Debug] Resolved string: $('[id=Password]').val('Asdf2025')
2025-08-07 11:36:41.707 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-07 11:36:41.707 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-07 11:36:41.707 [Debug] Resolving placeholders in string: {{Selectors.ChallengeQuestionField}}
2025-08-07 11:36:41.707 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-07 11:36:41.708 [Debug] Resolving placeholders in string: {{TestData.ChallengeAnswer}}
2025-08-07 11:36:41.708 [Debug] Resolved string: armada
2025-08-07 11:36:41.708 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-07 11:36:41.708 [Debug] Resolved string: Click on the Login Button
2025-08-07 11:36:41.708 [Debug] Resolving placeholders in string: {{Selectors.LoginAuthButton}}
2025-08-07 11:36:41.708 [Debug] Resolved string: id=LoginAuthBtn
2025-08-07 11:36:41.708 [Info] Successfully resolved parameters for test case: Login
2025-08-07 11:36:41.708 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-07 11:36:41.708 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-07 11:36:41.708 [Debug] Resolving placeholders in string: id=finish
2025-08-07 11:36:41.708 [Debug] Resolved string: id=finish
2025-08-07 11:36:41.708 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-07 11:36:41.708 [Debug] Resolved string: Click on Login Button
2025-08-07 11:36:41.708 [Debug] Resolving placeholders in string: id=LoginBtn
2025-08-07 11:36:41.708 [Debug] Resolved string: id=LoginBtn
2025-08-07 11:36:41.708 [Debug] Resolving placeholders in string: Type the User Name
2025-08-07 11:36:41.708 [Debug] Resolved string: Type the User Name
2025-08-07 11:36:41.708 [Debug] Resolving placeholders in string: id=UserName
2025-08-07 11:36:41.708 [Debug] Resolved string: id=UserName
2025-08-07 11:36:41.708 [Debug] Resolving placeholders in string: Mm_azmy
2025-08-07 11:36:41.708 [Debug] Resolved string: Mm_azmy
2025-08-07 11:36:41.708 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-07 11:36:41.708 [Debug] Resolved string: Click on Continue Button
2025-08-07 11:36:41.709 [Debug] Resolving placeholders in string: id=btn
2025-08-07 11:36:41.709 [Debug] Resolved string: id=btn
2025-08-07 11:36:41.709 [Debug] Resolving placeholders in string: Type the Password
2025-08-07 11:36:41.709 [Debug] Resolved string: Type the Password
2025-08-07 11:36:41.709 [Debug] Resolving placeholders in string: id=Password
2025-08-07 11:36:41.709 [Debug] Resolved string: id=Password
2025-08-07 11:36:41.709 [Debug] Resolving placeholders in string: $('[id=Password]').val('Asdf2025')
2025-08-07 11:36:41.709 [Debug] Resolved string: $('[id=Password]').val('Asdf2025')
2025-08-07 11:36:41.709 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-07 11:36:41.710 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-07 11:36:41.710 [Debug] Resolving placeholders in string: id=ChallengeQuestionAnswer
2025-08-07 11:36:41.710 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-07 11:36:41.710 [Debug] Resolving placeholders in string: armada
2025-08-07 11:36:41.710 [Debug] Resolved string: armada
2025-08-07 11:36:41.710 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-07 11:36:41.710 [Debug] Resolved string: Click on the Login Button
2025-08-07 11:36:41.710 [Debug] Resolving placeholders in string: id=LoginAuthBtn
2025-08-07 11:36:41.710 [Debug] Resolved string: id=LoginAuthBtn
2025-08-07 11:36:41.710 [Debug] Resolving placeholders in string: Click on Happy points option from side menu
2025-08-07 11:36:41.710 [Debug] Resolved string: Click on Happy points option from side menu
2025-08-07 11:36:41.710 [Debug] Resolving placeholders in string: {{Selectors.HappyPointsOption}}
2025-08-07 11:36:41.710 [Debug] Resolved string: xpath=//div[@class='mainmenuitems']//label[contains(text(), 'Happy Points')]
2025-08-07 11:36:41.711 [Debug] Resolving placeholders in string: Click on Redeem Online button
2025-08-07 11:36:41.711 [Debug] Resolved string: Click on Redeem Online button
2025-08-07 11:36:41.711 [Debug] Resolving placeholders in string: {{Selectors.RedeemOnline}}
2025-08-07 11:36:41.711 [Debug] Resolved string: css=#BuyOnlineBTN > div
2025-08-07 11:36:41.711 [Debug] Resolving placeholders in string: Choose a merchant
2025-08-07 11:36:41.711 [Debug] Resolved string: Choose a merchant
2025-08-07 11:36:41.711 [Debug] Resolving placeholders in string: {{Selectors.SelectMerchant}}
2025-08-07 11:36:41.711 [Debug] Resolved string: css=#RequestItem0
2025-08-07 11:36:41.711 [Debug] Resolving placeholders in string: Click Redeem button
2025-08-07 11:36:41.711 [Debug] Resolved string: Click Redeem button
2025-08-07 11:36:41.711 [Debug] Resolving placeholders in string: {{Selectors.RedeemButton}}
2025-08-07 11:36:41.711 [Debug] Resolved string: id=RedeemID
2025-08-07 11:36:41.711 [Debug] Resolving placeholders in string: Enter the number of points to redeem
2025-08-07 11:36:41.711 [Debug] Resolved string: Enter the number of points to redeem
2025-08-07 11:36:41.711 [Debug] Resolving placeholders in string: {{Selectors.PointsTextbox}}
2025-08-07 11:36:41.711 [Debug] Resolved string: id=redeempointsID
2025-08-07 11:36:41.711 [Debug] Resolving placeholders in string: 1000
2025-08-07 11:36:41.711 [Debug] Resolved string: 1000
2025-08-07 11:36:41.711 [Debug] Resolving placeholders in string: Click on create cupon button
2025-08-07 11:36:41.711 [Debug] Resolved string: Click on create cupon button
2025-08-07 11:36:41.711 [Debug] Resolving placeholders in string: {{Selectors.CreateCupon}}
2025-08-07 11:36:41.712 [Debug] Resolved string: id=CCoponID
2025-08-07 11:36:41.712 [Debug] Resolving placeholders in string: Click on Continue button
2025-08-07 11:36:41.712 [Debug] Resolved string: Click on Continue button
2025-08-07 11:36:41.712 [Debug] Resolving placeholders in string: {{Selectors.CuntinueConfirmCupon}}
2025-08-07 11:36:41.712 [Debug] Resolved string: id=RequestRedemptionConfrimationSbmtBtn
2025-08-07 11:36:41.712 [Debug] Resolving placeholders in string: Enter wrong OTP
2025-08-07 11:36:41.712 [Debug] Resolved string: Enter wrong OTP
2025-08-07 11:36:41.712 [Debug] Resolving placeholders in string: {{Selectors.OTPbox}}
2025-08-07 11:36:41.712 [Debug] Resolved string: id=OTPInpt
2025-08-07 11:36:41.712 [Debug] Resolving placeholders in string: 0000000
2025-08-07 11:36:41.712 [Debug] Resolved string: 0000000
2025-08-07 11:36:41.713 [Debug] Resolving placeholders in string: Click on Confirm OTP button
2025-08-07 11:36:41.713 [Debug] Resolved string: Click on Confirm OTP button
2025-08-07 11:36:41.713 [Debug] Resolving placeholders in string: {{Selectors.OTPconfirmButton}}
2025-08-07 11:36:41.713 [Debug] Resolved string: id=ConfirmSbmtBtn
2025-08-07 11:36:41.713 [Debug] Resolving placeholders in string: Assert the error message: 
2025-08-07 11:36:41.713 [Debug] Resolved string: Assert the error message: 
2025-08-07 11:36:41.713 [Debug] Resolving placeholders in string: {{Selectors.popupMessage}}
2025-08-07 11:36:41.713 [Debug] Resolved string: id=popup_message
2025-08-07 11:36:41.713 [Debug] Resolving placeholders in string: Wrong activation code
2025-08-07 11:36:41.714 [Debug] Resolved string: Wrong activation code
2025-08-07 11:36:41.714 [Info] Successfully resolved parameters for test case: Verify That User Can't Redeem Happy Points Using Wrong SMS
2025-08-07 11:36:41.714 [Debug] Loading test case: WorkSet01/TestCases/021.HappyPoints/157-MorePointsThanAvailable.json
2025-08-07 11:36:41.714 [Info] Loading test case from: WorkSet01/TestCases/021.HappyPoints/157-MorePointsThanAvailable.json
2025-08-07 11:36:41.714 [Info] Attempting to load configuration file: WorkSet01/TestCases/021.HappyPoints/157-MorePointsThanAvailable.json
2025-08-07 11:36:41.728 [Info] Successfully loaded configuration file: WorkSet01/TestCases/021.HappyPoints/157-MorePointsThanAvailable.json
2025-08-07 11:36:41.728 [Info] No filtering configuration found. Including test case 'Redeem Online - input points more than the available'.
2025-08-07 11:36:41.728 [Debug] Resolving parameters for test case: Redeem Online - input points more than the available
2025-08-07 11:36:41.728 [Debug] Merging params
2025-08-07 11:36:41.728 [Debug] Merged basic params
2025-08-07 11:36:41.728 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-07 11:36:41.728 [Debug] Merged SpecialEnvParams params
2025-08-07 11:36:41.728 [Debug] Merged params
2025-08-07 11:36:41.728 [Debug] Loading parameters from reference file: WorkSet01/Params/HappyPointsParams.json
2025-08-07 11:36:41.728 [Info] Attempting to load configuration file: WorkSet01/Params/HappyPointsParams.json
2025-08-07 11:36:41.729 [Info] Successfully loaded configuration file: WorkSet01/Params/HappyPointsParams.json
2025-08-07 11:36:41.729 [Debug] Merging params
2025-08-07 11:36:41.729 [Debug] Merged basic params
2025-08-07 11:36:41.729 [Debug] Merged params
2025-08-07 11:36:41.730 [Debug] Merging params
2025-08-07 11:36:41.730 [Debug] Merging params
2025-08-07 11:36:41.730 [Debug] Resolving placeholders in test steps for test case: Redeem Online - input points more than the available
2025-08-07 11:36:41.730 [Debug] Resolving TestCaseReference in step: 
2025-08-07 11:36:41.730 [Info] Loading test case from: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:36:41.730 [Info] Attempting to load configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:36:41.730 [Info] Successfully loaded configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:36:41.731 [Debug] Resolving parameters for test case: Login
2025-08-07 11:36:41.731 [Debug] Merging params
2025-08-07 11:36:41.731 [Debug] Merged basic params
2025-08-07 11:36:41.731 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-07 11:36:41.731 [Debug] Merged SpecialEnvParams params
2025-08-07 11:36:41.731 [Debug] Merged params
2025-08-07 11:36:41.731 [Debug] Loading parameters from reference file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:36:41.731 [Info] Attempting to load configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:36:41.731 [Info] Successfully loaded configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:36:41.731 [Debug] Merging params
2025-08-07 11:36:41.731 [Debug] Merged basic params
2025-08-07 11:36:41.731 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-07 11:36:41.731 [Debug] Merged SpecialEnvParams params
2025-08-07 11:36:41.731 [Debug] Merged params
2025-08-07 11:36:41.732 [Debug] Merging params
2025-08-07 11:36:41.732 [Debug] Merging params
2025-08-07 11:36:41.732 [Debug] Resolving placeholders in test steps for test case: Login
2025-08-07 11:36:41.732 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-07 11:36:41.732 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-07 11:36:41.732 [Debug] Resolving placeholders in string: {{Selectors.FinishButton}}
2025-08-07 11:36:41.732 [Debug] Resolved string: id=finish
2025-08-07 11:36:41.732 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-07 11:36:41.732 [Debug] Resolved string: Click on Login Button
2025-08-07 11:36:41.732 [Debug] Resolving placeholders in string: {{Selectors.LoginButton}}
2025-08-07 11:36:41.732 [Debug] Resolved string: id=LoginBtn
2025-08-07 11:36:41.732 [Debug] Resolving placeholders in string: Type the User Name
2025-08-07 11:36:41.733 [Debug] Resolved string: Type the User Name
2025-08-07 11:36:41.733 [Debug] Resolving placeholders in string: {{Selectors.UserNameField}}
2025-08-07 11:36:41.733 [Debug] Resolved string: id=UserName
2025-08-07 11:36:41.733 [Debug] Resolving placeholders in string: {{TestData.UserName}}
2025-08-07 11:36:41.733 [Debug] Resolved string: Mm_azmy
2025-08-07 11:36:41.733 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-07 11:36:41.733 [Debug] Resolved string: Click on Continue Button
2025-08-07 11:36:41.733 [Debug] Resolving placeholders in string: {{Selectors.ContinueButton}}
2025-08-07 11:36:41.733 [Debug] Resolved string: id=btn
2025-08-07 11:36:41.733 [Debug] Resolving placeholders in string: Type the Password
2025-08-07 11:36:41.733 [Debug] Resolved string: Type the Password
2025-08-07 11:36:41.733 [Debug] Resolving placeholders in string: {{Selectors.PasswordField}}
2025-08-07 11:36:41.734 [Debug] Resolved string: id=Password
2025-08-07 11:36:41.734 [Debug] Resolving placeholders in string: $('[{{Selectors.PasswordField}}]').val('{{TestData.Password}}')
2025-08-07 11:36:41.734 [Debug] Resolved string: $('[id=Password]').val('Asdf2025')
2025-08-07 11:36:41.734 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-07 11:36:41.734 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-07 11:36:41.734 [Debug] Resolving placeholders in string: {{Selectors.ChallengeQuestionField}}
2025-08-07 11:36:41.734 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-07 11:36:41.735 [Debug] Resolving placeholders in string: {{TestData.ChallengeAnswer}}
2025-08-07 11:36:41.735 [Debug] Resolved string: armada
2025-08-07 11:36:41.735 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-07 11:36:41.735 [Debug] Resolved string: Click on the Login Button
2025-08-07 11:36:41.743 [Debug] Resolving placeholders in string: {{Selectors.LoginAuthButton}}
2025-08-07 11:36:41.743 [Debug] Resolved string: id=LoginAuthBtn
2025-08-07 11:36:41.743 [Info] Successfully resolved parameters for test case: Login
2025-08-07 11:36:41.743 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-07 11:36:41.743 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-07 11:36:41.743 [Debug] Resolving placeholders in string: id=finish
2025-08-07 11:36:41.743 [Debug] Resolved string: id=finish
2025-08-07 11:36:41.743 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-07 11:36:41.743 [Debug] Resolved string: Click on Login Button
2025-08-07 11:36:41.743 [Debug] Resolving placeholders in string: id=LoginBtn
2025-08-07 11:36:41.743 [Debug] Resolved string: id=LoginBtn
2025-08-07 11:36:41.743 [Debug] Resolving placeholders in string: Type the User Name
2025-08-07 11:36:41.743 [Debug] Resolved string: Type the User Name
2025-08-07 11:36:41.743 [Debug] Resolving placeholders in string: id=UserName
2025-08-07 11:36:41.743 [Debug] Resolved string: id=UserName
2025-08-07 11:36:41.743 [Debug] Resolving placeholders in string: Mm_azmy
2025-08-07 11:36:41.743 [Debug] Resolved string: Mm_azmy
2025-08-07 11:36:41.743 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-07 11:36:41.743 [Debug] Resolved string: Click on Continue Button
2025-08-07 11:36:41.743 [Debug] Resolving placeholders in string: id=btn
2025-08-07 11:36:41.743 [Debug] Resolved string: id=btn
2025-08-07 11:36:41.743 [Debug] Resolving placeholders in string: Type the Password
2025-08-07 11:36:41.743 [Debug] Resolved string: Type the Password
2025-08-07 11:36:41.743 [Debug] Resolving placeholders in string: id=Password
2025-08-07 11:36:41.743 [Debug] Resolved string: id=Password
2025-08-07 11:36:41.743 [Debug] Resolving placeholders in string: $('[id=Password]').val('Asdf2025')
2025-08-07 11:36:41.743 [Debug] Resolved string: $('[id=Password]').val('Asdf2025')
2025-08-07 11:36:41.743 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-07 11:36:41.743 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-07 11:36:41.743 [Debug] Resolving placeholders in string: id=ChallengeQuestionAnswer
2025-08-07 11:36:41.743 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-07 11:36:41.743 [Debug] Resolving placeholders in string: armada
2025-08-07 11:36:41.743 [Debug] Resolved string: armada
2025-08-07 11:36:41.743 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-07 11:36:41.746 [Debug] Resolved string: Click on the Login Button
2025-08-07 11:36:41.746 [Debug] Resolving placeholders in string: id=LoginAuthBtn
2025-08-07 11:36:41.746 [Debug] Resolved string: id=LoginAuthBtn
2025-08-07 11:36:41.746 [Debug] Resolving placeholders in string: Click on Happy points option from side menu
2025-08-07 11:36:41.746 [Debug] Resolved string: Click on Happy points option from side menu
2025-08-07 11:36:41.746 [Debug] Resolving placeholders in string: {{Selectors.HappyPointsOption}}
2025-08-07 11:36:41.746 [Debug] Resolved string: xpath=//div[@class='mainmenuitems']//label[contains(text(), 'Happy Points')]
2025-08-07 11:36:41.746 [Debug] Resolving placeholders in string: Click on Redeem Online button
2025-08-07 11:36:41.746 [Debug] Resolved string: Click on Redeem Online button
2025-08-07 11:36:41.746 [Debug] Resolving placeholders in string: {{Selectors.RedeemOnline}}
2025-08-07 11:36:41.746 [Debug] Resolved string: css=#BuyOnlineBTN > div
2025-08-07 11:36:41.746 [Debug] Resolving placeholders in string: Choose a merchant
2025-08-07 11:36:41.746 [Debug] Resolved string: Choose a merchant
2025-08-07 11:36:41.746 [Debug] Resolving placeholders in string: {{Selectors.SelectMerchant}}
2025-08-07 11:36:41.746 [Debug] Resolved string: css=#RequestItem0
2025-08-07 11:36:41.746 [Debug] Resolving placeholders in string: Click Redeem button
2025-08-07 11:36:41.746 [Debug] Resolved string: Click Redeem button
2025-08-07 11:36:41.746 [Debug] Resolving placeholders in string: {{Selectors.RedeemButton}}
2025-08-07 11:36:41.747 [Debug] Resolved string: id=RedeemID
2025-08-07 11:36:41.747 [Debug] Resolving placeholders in string: Enter the number of points to redeem
2025-08-07 11:36:41.747 [Debug] Resolved string: Enter the number of points to redeem
2025-08-07 11:36:41.747 [Debug] Resolving placeholders in string: {{Selectors.PointsTextbox}}
2025-08-07 11:36:41.747 [Debug] Resolved string: id=redeempointsID
2025-08-07 11:36:41.747 [Debug] Resolving placeholders in string: 50000
2025-08-07 11:36:41.747 [Debug] Resolved string: 50000
2025-08-07 11:36:41.747 [Debug] Resolving placeholders in string: Click on create cupon button and show the error message
2025-08-07 11:36:41.747 [Debug] Resolved string: Click on create cupon button and show the error message
2025-08-07 11:36:41.747 [Debug] Resolving placeholders in string: {{Selectors.CreateCupon}}
2025-08-07 11:36:41.747 [Debug] Resolved string: id=CCoponID
2025-08-07 11:36:41.747 [Info] Successfully resolved parameters for test case: Redeem Online - input points more than the available
2025-08-07 11:36:41.747 [Debug] Loading test case: WorkSet01/TestCases/03.Beneficiaries/Add/TC-255 AddingOtherCAEaccount.json
2025-08-07 11:36:41.747 [Info] Loading test case from: WorkSet01/TestCases/03.Beneficiaries/Add/TC-255 AddingOtherCAEaccount.json
2025-08-07 11:36:41.750 [Info] Attempting to load configuration file: WorkSet01/TestCases/03.Beneficiaries/Add/TC-255 AddingOtherCAEaccount.json
2025-08-07 11:36:41.784 [Info] Successfully loaded configuration file: WorkSet01/TestCases/03.Beneficiaries/Add/TC-255 AddingOtherCAEaccount.json
2025-08-07 11:36:41.784 [Info] No filtering configuration found. Including test case 'Add new beneficiary account'.
2025-08-07 11:36:41.784 [Debug] Resolving parameters for test case: Add new beneficiary account
2025-08-07 11:36:41.784 [Debug] Merging params
2025-08-07 11:36:41.784 [Debug] Merged basic params
2025-08-07 11:36:41.784 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-07 11:36:41.784 [Debug] Merged SpecialEnvParams params
2025-08-07 11:36:41.784 [Debug] Merged params
2025-08-07 11:36:41.784 [Debug] Loading parameters from reference file: WorkSet01/Params/BeneficiariesParams.json
2025-08-07 11:36:41.784 [Info] Attempting to load configuration file: WorkSet01/Params/BeneficiariesParams.json
2025-08-07 11:36:41.795 [Info] Successfully loaded configuration file: WorkSet01/Params/BeneficiariesParams.json
2025-08-07 11:36:41.795 [Debug] Merging params
2025-08-07 11:36:41.796 [Debug] Merged basic params
2025-08-07 11:36:41.796 [Debug] Merged params
2025-08-07 11:36:41.796 [Debug] Merging params
2025-08-07 11:36:41.796 [Debug] Merging params
2025-08-07 11:36:41.796 [Debug] Resolving placeholders in test steps for test case: Add new beneficiary account
2025-08-07 11:36:41.796 [Debug] Resolving TestCaseReference in step: 
2025-08-07 11:36:41.796 [Info] Loading test case from: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:36:41.796 [Info] Attempting to load configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:36:41.796 [Info] Successfully loaded configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:36:41.796 [Debug] Resolving parameters for test case: Login
2025-08-07 11:36:41.796 [Debug] Merging params
2025-08-07 11:36:41.796 [Debug] Merged basic params
2025-08-07 11:36:41.796 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-07 11:36:41.797 [Debug] Merged SpecialEnvParams params
2025-08-07 11:36:41.797 [Debug] Merged params
2025-08-07 11:36:41.797 [Debug] Loading parameters from reference file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:36:41.797 [Info] Attempting to load configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:36:41.797 [Info] Successfully loaded configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:36:41.801 [Debug] Merging params
2025-08-07 11:36:41.801 [Debug] Merged basic params
2025-08-07 11:36:41.801 [Debug] Merged params
2025-08-07 11:36:41.801 [Debug] Merging params
2025-08-07 11:36:41.801 [Debug] Merging params
2025-08-07 11:36:41.801 [Debug] Resolving placeholders in test steps for test case: Login
2025-08-07 11:36:41.801 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-07 11:36:41.801 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-07 11:36:41.801 [Debug] Resolving placeholders in string: {{Selectors.FinishButton}}
2025-08-07 11:36:41.801 [Debug] Resolved string: id=finish
2025-08-07 11:36:41.801 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-07 11:36:41.801 [Debug] Resolved string: Click on Login Button
2025-08-07 11:36:41.801 [Debug] Resolving placeholders in string: {{Selectors.LoginButton}}
2025-08-07 11:36:41.801 [Debug] Resolved string: id=LoginBtn
2025-08-07 11:36:41.801 [Debug] Resolving placeholders in string: Type the User Name
2025-08-07 11:36:41.801 [Debug] Resolved string: Type the User Name
2025-08-07 11:36:41.801 [Debug] Resolving placeholders in string: {{Selectors.UserNameField}}
2025-08-07 11:36:41.801 [Debug] Resolved string: id=UserName
2025-08-07 11:36:41.801 [Debug] Resolving placeholders in string: {{TestData.UserName}}
2025-08-07 11:36:41.801 [Debug] Resolved string: mahmoudsayed022
2025-08-07 11:36:41.801 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-07 11:36:41.801 [Debug] Resolved string: Click on Continue Button
2025-08-07 11:36:41.801 [Debug] Resolving placeholders in string: {{Selectors.ContinueButton}}
2025-08-07 11:36:41.801 [Debug] Resolved string: id=btn
2025-08-07 11:36:41.801 [Debug] Resolving placeholders in string: Type the Password
2025-08-07 11:36:41.802 [Debug] Resolved string: Type the Password
2025-08-07 11:36:41.802 [Debug] Resolving placeholders in string: {{Selectors.PasswordField}}
2025-08-07 11:36:41.802 [Debug] Resolved string: id=Password
2025-08-07 11:36:41.802 [Debug] Resolving placeholders in string: $('[{{Selectors.PasswordField}}]').val('{{TestData.Password}}')
2025-08-07 11:36:41.806 [Debug] Resolved string: $('[id=Password]').val('Password1')
2025-08-07 11:36:41.806 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-07 11:36:41.806 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-07 11:36:41.806 [Debug] Resolving placeholders in string: {{Selectors.ChallengeQuestionField}}
2025-08-07 11:36:41.806 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-07 11:36:41.806 [Debug] Resolving placeholders in string: {{TestData.ChallengeAnswer}}
2025-08-07 11:36:41.806 [Debug] Resolved string: bmw
2025-08-07 11:36:41.806 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-07 11:36:41.806 [Debug] Resolved string: Click on the Login Button
2025-08-07 11:36:41.806 [Debug] Resolving placeholders in string: {{Selectors.LoginAuthButton}}
2025-08-07 11:36:41.806 [Debug] Resolved string: id=LoginAuthBtn
2025-08-07 11:36:41.806 [Info] Successfully resolved parameters for test case: Login
2025-08-07 11:36:41.806 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-07 11:36:41.806 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-07 11:36:41.806 [Debug] Resolving placeholders in string: id=finish
2025-08-07 11:36:41.806 [Debug] Resolved string: id=finish
2025-08-07 11:36:41.806 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-07 11:36:41.806 [Debug] Resolved string: Click on Login Button
2025-08-07 11:36:41.806 [Debug] Resolving placeholders in string: id=LoginBtn
2025-08-07 11:36:41.806 [Debug] Resolved string: id=LoginBtn
2025-08-07 11:36:41.806 [Debug] Resolving placeholders in string: Type the User Name
2025-08-07 11:36:41.806 [Debug] Resolved string: Type the User Name
2025-08-07 11:36:41.806 [Debug] Resolving placeholders in string: id=UserName
2025-08-07 11:36:41.807 [Debug] Resolved string: id=UserName
2025-08-07 11:36:41.807 [Debug] Resolving placeholders in string: mahmoudsayed022
2025-08-07 11:36:41.807 [Debug] Resolved string: mahmoudsayed022
2025-08-07 11:36:41.807 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-07 11:36:41.807 [Debug] Resolved string: Click on Continue Button
2025-08-07 11:36:41.810 [Debug] Resolving placeholders in string: id=btn
2025-08-07 11:36:41.810 [Debug] Resolved string: id=btn
2025-08-07 11:36:41.810 [Debug] Resolving placeholders in string: Type the Password
2025-08-07 11:36:41.810 [Debug] Resolved string: Type the Password
2025-08-07 11:36:41.810 [Debug] Resolving placeholders in string: id=Password
2025-08-07 11:36:41.810 [Debug] Resolved string: id=Password
2025-08-07 11:36:41.810 [Debug] Resolving placeholders in string: $('[id=Password]').val('Password1')
2025-08-07 11:36:41.810 [Debug] Resolved string: $('[id=Password]').val('Password1')
2025-08-07 11:36:41.810 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-07 11:36:41.810 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-07 11:36:41.810 [Debug] Resolving placeholders in string: id=ChallengeQuestionAnswer
2025-08-07 11:36:41.810 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-07 11:36:41.810 [Debug] Resolving placeholders in string: bmw
2025-08-07 11:36:41.810 [Debug] Resolved string: bmw
2025-08-07 11:36:41.811 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-07 11:36:41.811 [Debug] Resolved string: Click on the Login Button
2025-08-07 11:36:41.811 [Debug] Resolving placeholders in string: id=LoginAuthBtn
2025-08-07 11:36:41.811 [Debug] Resolved string: id=LoginAuthBtn
2025-08-07 11:36:41.811 [Debug] Resolving placeholders in string: Click on change later button on the password expiration popup message
2025-08-07 11:36:41.811 [Debug] Resolved string: Click on change later button on the password expiration popup message
2025-08-07 11:36:41.811 [Debug] Resolving placeholders in string: {{Selectors.popupCancel}}
2025-08-07 11:36:41.811 [Debug] Resolved string: id=popup_cancel
2025-08-07 11:36:41.811 [Debug] Resolving placeholders in string: Click on biometric alert
2025-08-07 11:36:41.811 [Debug] Resolved string: Click on biometric alert
2025-08-07 11:36:41.811 [Debug] Resolving placeholders in string: {{Selectors.BioMetricAlert}}
2025-08-07 11:36:41.811 [Debug] Resolved string: css=#popbuttons > div > button.btn.Cancel.back_gradient2
2025-08-07 11:36:41.811 [Debug] Resolving placeholders in string: Execute JS code to activate token
2025-08-07 11:36:41.814 [Debug] Resolved string: Execute JS code to activate token
2025-08-07 11:36:41.814 [Debug] Resolving placeholders in string: Globals.ActivationState = 'ACTIVATED';
2025-08-07 11:36:41.814 [Debug] Resolved string: Globals.ActivationState = 'ACTIVATED';
2025-08-07 11:36:41.814 [Debug] Resolving placeholders in string: Click on Beneficiaries
2025-08-07 11:36:41.814 [Debug] Resolved string: Click on Beneficiaries
2025-08-07 11:36:41.814 [Debug] Resolving placeholders in string: {{Selectors.BeneficiariesToButton}}
2025-08-07 11:36:41.814 [Debug] Resolved string: xpath=//div[@class='mainmenuitems']//label[contains(text(), 'Beneficiaries')]
2025-08-07 11:36:41.815 [Debug] Resolving placeholders in string: Click on Other CAE Accounts
2025-08-07 11:36:41.815 [Debug] Resolved string: Click on Other CAE Accounts
2025-08-07 11:36:41.815 [Debug] Resolving placeholders in string: {{Selectors.OtherCAEAccounts}}
2025-08-07 11:36:41.815 [Debug] Resolved string: xpath=//div[@class='submenuitem']//label[contains(text(), 'Other CAE Accounts')]
2025-08-07 11:36:41.815 [Debug] Resolving placeholders in string: Click on Add Beneficiary
2025-08-07 11:36:41.815 [Debug] Resolved string: Click on Add Beneficiary
2025-08-07 11:36:41.815 [Debug] Resolving placeholders in string: {{Selectors.AddNewBeneficiaryButtonOtheCAEaccounts}}
2025-08-07 11:36:41.815 [Debug] Resolved string: id=AddDigitalBankBeneficiary
2025-08-07 11:36:41.815 [Debug] Resolving placeholders in string: Type the Nickname
2025-08-07 11:36:41.815 [Debug] Resolved string: Type the Nickname
2025-08-07 11:36:41.815 [Debug] Resolving placeholders in string: {{Selectors.BeneficiaryNameTextbox}}
2025-08-07 11:36:41.815 [Debug] Resolved string: id=BeneficiaryName
2025-08-07 11:36:41.815 [Debug] Resolving placeholders in string: OtherCAE_account_2
2025-08-07 11:36:41.815 [Debug] Resolved string: OtherCAE_account_2
2025-08-07 11:36:41.815 [Debug] Resolving placeholders in string: Type Account number
2025-08-07 11:36:41.815 [Debug] Resolved string: Type Account number
2025-08-07 11:36:41.815 [Debug] Resolving placeholders in string: {{Selectors.BeneficiaryAccountNumber}}
2025-08-07 11:36:41.815 [Debug] Resolved string: id=BeneficiaryAccountNumber
2025-08-07 11:36:41.815 [Debug] Resolving placeholders in string: ************** 
2025-08-07 11:36:41.821 [Debug] Resolved string: ************** 
2025-08-07 11:36:41.821 [Debug] Resolving placeholders in string: Click on Save button
2025-08-07 11:36:41.821 [Debug] Resolved string: Click on Save button
2025-08-07 11:36:41.821 [Debug] Resolving placeholders in string: {{Selectors.SaveButton}}
2025-08-07 11:36:41.821 [Debug] Resolved string: id=Save
2025-08-07 11:36:41.821 [Debug] Resolving placeholders in string: Click on continue
2025-08-07 11:36:41.821 [Debug] Resolved string: Click on continue
2025-08-07 11:36:41.821 [Debug] Resolving placeholders in string: {{Selectors.ContinueToSaveBeneficiary}}
2025-08-07 11:36:41.821 [Debug] Resolved string: id=DigitalBankBenfContinueDeleteBtn
2025-08-07 11:36:41.821 [Debug] Resolving placeholders in string: Enter Token number
2025-08-07 11:36:41.821 [Debug] Resolved string: Enter Token number
2025-08-07 11:36:41.821 [Debug] Resolving placeholders in string: {{Selectors.TokenInput}}
2025-08-07 11:36:41.822 [Debug] Resolved string: id=TokenNUMBER
2025-08-07 11:36:41.822 [Debug] Resolving placeholders in string: 123456
2025-08-07 11:36:41.822 [Debug] Resolved string: 123456
2025-08-07 11:36:41.822 [Debug] Resolving placeholders in string: Click on confirm and show the confirmation page
2025-08-07 11:36:41.822 [Debug] Resolved string: Click on confirm and show the confirmation page
2025-08-07 11:36:41.822 [Debug] Resolving placeholders in string: {{Selectors.btnTokenConfirm}}
2025-08-07 11:36:41.822 [Debug] Resolved string: id=btnTokenConfirm
2025-08-07 11:36:41.822 [Info] Successfully resolved parameters for test case: Add new beneficiary account
2025-08-07 11:36:41.822 [Debug] Loading test case: WorkSet01/TestCases/03.Beneficiaries/Add/TC-256 AddingOtherCAEcard.json
2025-08-07 11:36:41.822 [Info] Loading test case from: WorkSet01/TestCases/03.Beneficiaries/Add/TC-256 AddingOtherCAEcard.json
2025-08-07 11:36:41.822 [Info] Attempting to load configuration file: WorkSet01/TestCases/03.Beneficiaries/Add/TC-256 AddingOtherCAEcard.json
2025-08-07 11:36:41.823 [Info] Successfully loaded configuration file: WorkSet01/TestCases/03.Beneficiaries/Add/TC-256 AddingOtherCAEcard.json
2025-08-07 11:36:41.824 [Info] No filtering configuration found. Including test case 'Add new beneficiary card'.
2025-08-07 11:36:41.830 [Debug] Resolving parameters for test case: Add new beneficiary card
2025-08-07 11:36:41.830 [Debug] Merging params
2025-08-07 11:36:41.830 [Debug] Merged basic params
2025-08-07 11:36:41.830 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-07 11:36:41.830 [Debug] Merged SpecialEnvParams params
2025-08-07 11:36:41.830 [Debug] Merged params
2025-08-07 11:36:41.830 [Debug] Loading parameters from reference file: WorkSet01/Params/BeneficiariesParams.json
2025-08-07 11:36:41.830 [Info] Attempting to load configuration file: WorkSet01/Params/BeneficiariesParams.json
2025-08-07 11:36:41.830 [Info] Successfully loaded configuration file: WorkSet01/Params/BeneficiariesParams.json
2025-08-07 11:36:41.831 [Debug] Merging params
2025-08-07 11:36:41.831 [Debug] Merged basic params
2025-08-07 11:36:41.831 [Debug] Merged params
2025-08-07 11:36:41.831 [Debug] Merging params
2025-08-07 11:36:41.831 [Debug] Merging params
2025-08-07 11:36:41.831 [Debug] Resolving placeholders in test steps for test case: Add new beneficiary card
2025-08-07 11:36:41.831 [Debug] Resolving TestCaseReference in step: 
2025-08-07 11:36:41.831 [Info] Loading test case from: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:36:41.831 [Info] Attempting to load configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:36:41.832 [Info] Successfully loaded configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:36:41.832 [Debug] Resolving parameters for test case: Login
2025-08-07 11:36:41.832 [Debug] Merging params
2025-08-07 11:36:41.832 [Debug] Merged basic params
2025-08-07 11:36:41.832 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-07 11:36:41.832 [Debug] Merged SpecialEnvParams params
2025-08-07 11:36:41.835 [Debug] Merged params
2025-08-07 11:36:41.836 [Debug] Loading parameters from reference file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:36:41.836 [Info] Attempting to load configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:36:41.836 [Info] Successfully loaded configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:36:41.836 [Debug] Merging params
2025-08-07 11:36:41.836 [Debug] Merged basic params
2025-08-07 11:36:41.836 [Debug] Merged params
2025-08-07 11:36:41.836 [Debug] Merging params
2025-08-07 11:36:41.836 [Debug] Merging params
2025-08-07 11:36:41.836 [Debug] Resolving placeholders in test steps for test case: Login
2025-08-07 11:36:41.836 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-07 11:36:41.836 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-07 11:36:41.836 [Debug] Resolving placeholders in string: {{Selectors.FinishButton}}
2025-08-07 11:36:41.836 [Debug] Resolved string: id=finish
2025-08-07 11:36:41.836 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-07 11:36:41.836 [Debug] Resolved string: Click on Login Button
2025-08-07 11:36:41.837 [Debug] Resolving placeholders in string: {{Selectors.LoginButton}}
2025-08-07 11:36:41.837 [Debug] Resolved string: id=LoginBtn
2025-08-07 11:36:41.837 [Debug] Resolving placeholders in string: Type the User Name
2025-08-07 11:36:41.837 [Debug] Resolved string: Type the User Name
2025-08-07 11:36:41.837 [Debug] Resolving placeholders in string: {{Selectors.UserNameField}}
2025-08-07 11:36:41.837 [Debug] Resolved string: id=UserName
2025-08-07 11:36:41.837 [Debug] Resolving placeholders in string: {{TestData.UserName}}
2025-08-07 11:36:41.837 [Debug] Resolved string: mahmoudsayed022
2025-08-07 11:36:41.840 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-07 11:36:41.840 [Debug] Resolved string: Click on Continue Button
2025-08-07 11:36:41.840 [Debug] Resolving placeholders in string: {{Selectors.ContinueButton}}
2025-08-07 11:36:41.840 [Debug] Resolved string: id=btn
2025-08-07 11:36:41.840 [Debug] Resolving placeholders in string: Type the Password
2025-08-07 11:36:41.842 [Debug] Resolved string: Type the Password
2025-08-07 11:36:41.842 [Debug] Resolving placeholders in string: {{Selectors.PasswordField}}
2025-08-07 11:36:41.842 [Debug] Resolved string: id=Password
2025-08-07 11:36:41.842 [Debug] Resolving placeholders in string: $('[{{Selectors.PasswordField}}]').val('{{TestData.Password}}')
2025-08-07 11:36:41.842 [Debug] Resolved string: $('[id=Password]').val('Password1')
2025-08-07 11:36:41.842 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-07 11:36:41.842 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-07 11:36:41.842 [Debug] Resolving placeholders in string: {{Selectors.ChallengeQuestionField}}
2025-08-07 11:36:41.842 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-07 11:36:41.842 [Debug] Resolving placeholders in string: {{TestData.ChallengeAnswer}}
2025-08-07 11:36:41.842 [Debug] Resolved string: bmw
2025-08-07 11:36:41.842 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-07 11:36:41.842 [Debug] Resolved string: Click on the Login Button
2025-08-07 11:36:41.842 [Debug] Resolving placeholders in string: {{Selectors.LoginAuthButton}}
2025-08-07 11:36:41.842 [Debug] Resolved string: id=LoginAuthBtn
2025-08-07 11:36:41.842 [Info] Successfully resolved parameters for test case: Login
2025-08-07 11:36:41.842 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-07 11:36:41.842 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-07 11:36:41.845 [Debug] Resolving placeholders in string: id=finish
2025-08-07 11:36:41.845 [Debug] Resolved string: id=finish
2025-08-07 11:36:41.845 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-07 11:36:41.845 [Debug] Resolved string: Click on Login Button
2025-08-07 11:36:41.845 [Debug] Resolving placeholders in string: id=LoginBtn
2025-08-07 11:36:41.845 [Debug] Resolved string: id=LoginBtn
2025-08-07 11:36:41.845 [Debug] Resolving placeholders in string: Type the User Name
2025-08-07 11:36:41.845 [Debug] Resolved string: Type the User Name
2025-08-07 11:36:41.845 [Debug] Resolving placeholders in string: id=UserName
2025-08-07 11:36:41.845 [Debug] Resolved string: id=UserName
2025-08-07 11:36:41.845 [Debug] Resolving placeholders in string: mahmoudsayed022
2025-08-07 11:36:41.845 [Debug] Resolved string: mahmoudsayed022
2025-08-07 11:36:41.845 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-07 11:36:41.845 [Debug] Resolved string: Click on Continue Button
2025-08-07 11:36:41.845 [Debug] Resolving placeholders in string: id=btn
2025-08-07 11:36:41.845 [Debug] Resolved string: id=btn
2025-08-07 11:36:41.845 [Debug] Resolving placeholders in string: Type the Password
2025-08-07 11:36:41.845 [Debug] Resolved string: Type the Password
2025-08-07 11:36:41.846 [Debug] Resolving placeholders in string: id=Password
2025-08-07 11:36:41.846 [Debug] Resolved string: id=Password
2025-08-07 11:36:41.846 [Debug] Resolving placeholders in string: $('[id=Password]').val('Password1')
2025-08-07 11:36:41.846 [Debug] Resolved string: $('[id=Password]').val('Password1')
2025-08-07 11:36:41.846 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-07 11:36:41.848 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-07 11:36:41.849 [Debug] Resolving placeholders in string: id=ChallengeQuestionAnswer
2025-08-07 11:36:41.849 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-07 11:36:41.849 [Debug] Resolving placeholders in string: bmw
2025-08-07 11:36:41.849 [Debug] Resolved string: bmw
2025-08-07 11:36:41.849 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-07 11:36:41.849 [Debug] Resolved string: Click on the Login Button
2025-08-07 11:36:41.849 [Debug] Resolving placeholders in string: id=LoginAuthBtn
2025-08-07 11:36:41.849 [Debug] Resolved string: id=LoginAuthBtn
2025-08-07 11:36:41.849 [Debug] Resolving placeholders in string: Click on change later button on the password expiration popup message
2025-08-07 11:36:41.849 [Debug] Resolved string: Click on change later button on the password expiration popup message
2025-08-07 11:36:41.849 [Debug] Resolving placeholders in string: {{Selectors.popupCancel}}
2025-08-07 11:36:41.849 [Debug] Resolved string: id=popup_cancel
2025-08-07 11:36:41.849 [Debug] Resolving placeholders in string: Click on biometric alert
2025-08-07 11:36:41.849 [Debug] Resolved string: Click on biometric alert
2025-08-07 11:36:41.849 [Debug] Resolving placeholders in string: {{Selectors.BioMetricAlert}}
2025-08-07 11:36:41.849 [Debug] Resolved string: css=#popbuttons > div > button.btn.Cancel.back_gradient2
2025-08-07 11:36:41.849 [Debug] Resolving placeholders in string: Execute JS code to activate token
2025-08-07 11:36:41.849 [Debug] Resolved string: Execute JS code to activate token
2025-08-07 11:36:41.849 [Debug] Resolving placeholders in string: Globals.ActivationState = 'ACTIVATED';
2025-08-07 11:36:41.849 [Debug] Resolved string: Globals.ActivationState = 'ACTIVATED';
2025-08-07 11:36:41.849 [Debug] Resolving placeholders in string: Click on Beneficiaries
2025-08-07 11:36:41.854 [Debug] Resolved string: Click on Beneficiaries
2025-08-07 11:36:41.854 [Debug] Resolving placeholders in string: {{Selectors.BeneficiariesToButton}}
2025-08-07 11:36:41.854 [Debug] Resolved string: xpath=//div[@class='mainmenuitems']//label[contains(text(), 'Beneficiaries')]
2025-08-07 11:36:41.854 [Debug] Resolving placeholders in string: Click on Other CAE Cards
2025-08-07 11:36:41.854 [Debug] Resolved string: Click on Other CAE Cards
2025-08-07 11:36:41.854 [Debug] Resolving placeholders in string: {{Selectors.OtherCAECards}}
2025-08-07 11:36:41.854 [Debug] Resolved string: xpath=//div[@class='submenuitem']//label[contains(text(), 'Other CAE Cards')]
2025-08-07 11:36:41.854 [Debug] Resolving placeholders in string: Click on Add Beneficiary
2025-08-07 11:36:41.854 [Debug] Resolved string: Click on Add Beneficiary
2025-08-07 11:36:41.854 [Debug] Resolving placeholders in string: xpath=//button[text()='Add Beneficiary']
2025-08-07 11:36:41.854 [Debug] Resolved string: xpath=//button[text()='Add Beneficiary']
2025-08-07 11:36:41.854 [Debug] Resolving placeholders in string: Type the Nickname
2025-08-07 11:36:41.854 [Debug] Resolved string: Type the Nickname
2025-08-07 11:36:41.854 [Debug] Resolving placeholders in string: {{Selectors.BeneficiaryNameTextbox}}
2025-08-07 11:36:41.854 [Debug] Resolved string: id=BeneficiaryName
2025-08-07 11:36:41.854 [Debug] Resolving placeholders in string: OtherCAE_card_2
2025-08-07 11:36:41.854 [Debug] Resolved string: OtherCAE_card_2
2025-08-07 11:36:41.854 [Debug] Resolving placeholders in string: Type Card number
2025-08-07 11:36:41.854 [Debug] Resolved string: Type Card number
2025-08-07 11:36:41.854 [Debug] Resolving placeholders in string: {{Selectors.BeneficiaryAccountNumber}}
2025-08-07 11:36:41.854 [Debug] Resolved string: id=BeneficiaryAccountNumber
2025-08-07 11:36:41.857 [Debug] Resolving placeholders in string: ****************
2025-08-07 11:36:41.857 [Debug] Resolved string: ****************
2025-08-07 11:36:41.857 [Debug] Resolving placeholders in string: Click on Save button
2025-08-07 11:36:41.857 [Debug] Resolved string: Click on Save button
2025-08-07 11:36:41.857 [Debug] Resolving placeholders in string: {{Selectors.SaveButton}}
2025-08-07 11:36:41.857 [Debug] Resolved string: id=Save
2025-08-07 11:36:41.857 [Debug] Resolving placeholders in string: Click on continue
2025-08-07 11:36:41.858 [Debug] Resolved string: Click on continue
2025-08-07 11:36:41.858 [Debug] Resolving placeholders in string: {{Selectors.ContinueToSaveBeneficiary}}
2025-08-07 11:36:41.858 [Debug] Resolved string: id=DigitalBankBenfContinueDeleteBtn
2025-08-07 11:36:41.858 [Debug] Resolving placeholders in string: Enter Token number
2025-08-07 11:36:41.858 [Debug] Resolved string: Enter Token number
2025-08-07 11:36:41.858 [Debug] Resolving placeholders in string: {{Selectors.TokenInput}}
2025-08-07 11:36:41.858 [Debug] Resolved string: id=TokenNUMBER
2025-08-07 11:36:41.858 [Debug] Resolving placeholders in string: 123456
2025-08-07 11:36:41.858 [Debug] Resolved string: 123456
2025-08-07 11:36:41.858 [Debug] Resolving placeholders in string: Click on confirm and show the confirmation page
2025-08-07 11:36:41.858 [Debug] Resolved string: Click on confirm and show the confirmation page
2025-08-07 11:36:41.858 [Debug] Resolving placeholders in string: {{Selectors.btnTokenConfirm}}
2025-08-07 11:36:41.858 [Debug] Resolved string: id=btnTokenConfirm
2025-08-07 11:36:41.858 [Info] Successfully resolved parameters for test case: Add new beneficiary card
2025-08-07 11:36:41.861 [Debug] Loading test case: WorkSet01/TestCases/03.Beneficiaries/Verify/251-Existing Account beneficiaries displayed.json
2025-08-07 11:36:41.861 [Info] Loading test case from: WorkSet01/TestCases/03.Beneficiaries/Verify/251-Existing Account beneficiaries displayed.json
2025-08-07 11:36:41.861 [Info] Attempting to load configuration file: WorkSet01/TestCases/03.Beneficiaries/Verify/251-Existing Account beneficiaries displayed.json
2025-08-07 11:36:41.876 [Info] Successfully loaded configuration file: WorkSet01/TestCases/03.Beneficiaries/Verify/251-Existing Account beneficiaries displayed.json
2025-08-07 11:36:41.877 [Info] No filtering configuration found. Including test case 'Existing Account beneficiaries displayed'.
2025-08-07 11:36:41.877 [Debug] Resolving parameters for test case: Existing Account beneficiaries displayed
2025-08-07 11:36:41.877 [Debug] Merging params
2025-08-07 11:36:41.877 [Debug] Merged basic params
2025-08-07 11:36:41.877 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-07 11:36:41.877 [Debug] Merged SpecialEnvParams params
2025-08-07 11:36:41.877 [Debug] Merged params
2025-08-07 11:36:41.877 [Debug] Loading parameters from reference file: WorkSet01/Params/BeneficiariesParams.json
2025-08-07 11:36:41.877 [Info] Attempting to load configuration file: WorkSet01/Params/BeneficiariesParams.json
2025-08-07 11:36:41.877 [Info] Successfully loaded configuration file: WorkSet01/Params/BeneficiariesParams.json
2025-08-07 11:36:41.877 [Debug] Merging params
2025-08-07 11:36:41.877 [Debug] Merged basic params
2025-08-07 11:36:41.878 [Debug] Merged params
2025-08-07 11:36:41.878 [Debug] Merging params
2025-08-07 11:36:41.878 [Debug] Merging params
2025-08-07 11:36:41.878 [Debug] Resolving placeholders in test steps for test case: Existing Account beneficiaries displayed
2025-08-07 11:36:41.880 [Debug] Resolving TestCaseReference in step: 
2025-08-07 11:36:41.880 [Info] Loading test case from: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:36:41.880 [Info] Attempting to load configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:36:41.881 [Info] Successfully loaded configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:36:41.881 [Debug] Resolving parameters for test case: Login
2025-08-07 11:36:41.881 [Debug] Merging params
2025-08-07 11:36:41.881 [Debug] Merged basic params
2025-08-07 11:36:41.881 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-07 11:36:41.881 [Debug] Merged SpecialEnvParams params
2025-08-07 11:36:41.881 [Debug] Merged params
2025-08-07 11:36:41.881 [Debug] Loading parameters from reference file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:36:41.881 [Info] Attempting to load configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:36:41.881 [Info] Successfully loaded configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:36:41.881 [Debug] Merging params
2025-08-07 11:36:41.881 [Debug] Merged basic params
2025-08-07 11:36:41.881 [Debug] Merged params
2025-08-07 11:36:41.882 [Debug] Merging params
2025-08-07 11:36:41.882 [Debug] Merging params
2025-08-07 11:36:41.882 [Debug] Resolving placeholders in test steps for test case: Login
2025-08-07 11:36:41.882 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-07 11:36:41.884 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-07 11:36:41.884 [Debug] Resolving placeholders in string: {{Selectors.FinishButton}}
2025-08-07 11:36:41.884 [Debug] Resolved string: id=finish
2025-08-07 11:36:41.884 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-07 11:36:41.884 [Debug] Resolved string: Click on Login Button
2025-08-07 11:36:41.884 [Debug] Resolving placeholders in string: {{Selectors.LoginButton}}
2025-08-07 11:36:41.884 [Debug] Resolved string: id=LoginBtn
2025-08-07 11:36:41.884 [Debug] Resolving placeholders in string: Type the User Name
2025-08-07 11:36:41.885 [Debug] Resolved string: Type the User Name
2025-08-07 11:36:41.885 [Debug] Resolving placeholders in string: {{Selectors.UserNameField}}
2025-08-07 11:36:41.885 [Debug] Resolved string: id=UserName
2025-08-07 11:36:41.885 [Debug] Resolving placeholders in string: {{TestData.UserName}}
2025-08-07 11:36:41.885 [Debug] Resolved string: mahmoudsayed022
2025-08-07 11:36:41.885 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-07 11:36:41.885 [Debug] Resolved string: Click on Continue Button
2025-08-07 11:36:41.885 [Debug] Resolving placeholders in string: {{Selectors.ContinueButton}}
2025-08-07 11:36:41.885 [Debug] Resolved string: id=btn
2025-08-07 11:36:41.885 [Debug] Resolving placeholders in string: Type the Password
2025-08-07 11:36:41.885 [Debug] Resolved string: Type the Password
2025-08-07 11:36:41.885 [Debug] Resolving placeholders in string: {{Selectors.PasswordField}}
2025-08-07 11:36:41.888 [Debug] Resolved string: id=Password
2025-08-07 11:36:41.888 [Debug] Resolving placeholders in string: $('[{{Selectors.PasswordField}}]').val('{{TestData.Password}}')
2025-08-07 11:36:41.888 [Debug] Resolved string: $('[id=Password]').val('Password1')
2025-08-07 11:36:41.888 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-07 11:36:41.888 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-07 11:36:41.888 [Debug] Resolving placeholders in string: {{Selectors.ChallengeQuestionField}}
2025-08-07 11:36:41.888 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-07 11:36:41.888 [Debug] Resolving placeholders in string: {{TestData.ChallengeAnswer}}
2025-08-07 11:36:41.888 [Debug] Resolved string: bmw
2025-08-07 11:36:41.888 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-07 11:36:41.888 [Debug] Resolved string: Click on the Login Button
2025-08-07 11:36:41.888 [Debug] Resolving placeholders in string: {{Selectors.LoginAuthButton}}
2025-08-07 11:36:41.888 [Debug] Resolved string: id=LoginAuthBtn
2025-08-07 11:36:41.888 [Info] Successfully resolved parameters for test case: Login
2025-08-07 11:36:41.888 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-07 11:36:41.888 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-07 11:36:41.888 [Debug] Resolving placeholders in string: id=finish
2025-08-07 11:36:41.888 [Debug] Resolved string: id=finish
2025-08-07 11:36:41.888 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-07 11:36:41.891 [Debug] Resolved string: Click on Login Button
2025-08-07 11:36:41.891 [Debug] Resolving placeholders in string: id=LoginBtn
2025-08-07 11:36:41.891 [Debug] Resolved string: id=LoginBtn
2025-08-07 11:36:41.891 [Debug] Resolving placeholders in string: Type the User Name
2025-08-07 11:36:41.891 [Debug] Resolved string: Type the User Name
2025-08-07 11:36:41.891 [Debug] Resolving placeholders in string: id=UserName
2025-08-07 11:36:41.891 [Debug] Resolved string: id=UserName
2025-08-07 11:36:41.892 [Debug] Resolving placeholders in string: mahmoudsayed022
2025-08-07 11:36:41.892 [Debug] Resolved string: mahmoudsayed022
2025-08-07 11:36:41.892 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-07 11:36:41.892 [Debug] Resolved string: Click on Continue Button
2025-08-07 11:36:41.892 [Debug] Resolving placeholders in string: id=btn
2025-08-07 11:36:41.892 [Debug] Resolved string: id=btn
2025-08-07 11:36:41.892 [Debug] Resolving placeholders in string: Type the Password
2025-08-07 11:36:41.892 [Debug] Resolved string: Type the Password
2025-08-07 11:36:41.892 [Debug] Resolving placeholders in string: id=Password
2025-08-07 11:36:41.892 [Debug] Resolved string: id=Password
2025-08-07 11:36:41.892 [Debug] Resolving placeholders in string: $('[id=Password]').val('Password1')
2025-08-07 11:36:41.892 [Debug] Resolved string: $('[id=Password]').val('Password1')
2025-08-07 11:36:41.897 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-07 11:36:41.897 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-07 11:36:41.897 [Debug] Resolving placeholders in string: id=ChallengeQuestionAnswer
2025-08-07 11:36:41.897 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-07 11:36:41.897 [Debug] Resolving placeholders in string: bmw
2025-08-07 11:36:41.897 [Debug] Resolved string: bmw
2025-08-07 11:36:41.897 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-07 11:36:41.898 [Debug] Resolved string: Click on the Login Button
2025-08-07 11:36:41.898 [Debug] Resolving placeholders in string: id=LoginAuthBtn
2025-08-07 11:36:41.898 [Debug] Resolved string: id=LoginAuthBtn
2025-08-07 11:36:41.898 [Debug] Resolving placeholders in string: Click on change later button on the password expiration popup message
2025-08-07 11:36:41.898 [Debug] Resolved string: Click on change later button on the password expiration popup message
2025-08-07 11:36:41.898 [Debug] Resolving placeholders in string: {{Selectors.popupCancel}}
2025-08-07 11:36:41.898 [Debug] Resolved string: id=popup_cancel
2025-08-07 11:36:41.898 [Debug] Resolving placeholders in string: Click on biometric alert
2025-08-07 11:36:41.898 [Debug] Resolved string: Click on biometric alert
2025-08-07 11:36:41.898 [Debug] Resolving placeholders in string: {{Selectors.BioMetricAlert}}
2025-08-07 11:36:41.898 [Debug] Resolved string: css=#popbuttons > div > button.btn.Cancel.back_gradient2
2025-08-07 11:36:41.898 [Debug] Resolving placeholders in string: Execute JS code to activate token
2025-08-07 11:36:41.901 [Debug] Resolved string: Execute JS code to activate token
2025-08-07 11:36:41.901 [Debug] Resolving placeholders in string: Globals.ActivationState = 'ACTIVATED';
2025-08-07 11:36:41.901 [Debug] Resolved string: Globals.ActivationState = 'ACTIVATED';
2025-08-07 11:36:41.901 [Debug] Resolving placeholders in string: Click on Beneficiaries
2025-08-07 11:36:41.901 [Debug] Resolved string: Click on Beneficiaries
2025-08-07 11:36:41.901 [Debug] Resolving placeholders in string: {{Selectors.BeneficiariesToButton}}
2025-08-07 11:36:41.901 [Debug] Resolved string: xpath=//div[@class='mainmenuitems']//label[contains(text(), 'Beneficiaries')]
2025-08-07 11:36:41.901 [Debug] Resolving placeholders in string: Click on Other CAE Accounts
2025-08-07 11:36:41.901 [Debug] Resolved string: Click on Other CAE Accounts
2025-08-07 11:36:41.901 [Debug] Resolving placeholders in string: {{Selectors.OtherCAEAccounts}}
2025-08-07 11:36:41.901 [Debug] Resolved string: xpath=//div[@class='submenuitem']//label[contains(text(), 'Other CAE Accounts')]
2025-08-07 11:36:41.901 [Debug] Resolving placeholders in string: Check if the Account List appears
2025-08-07 11:36:41.901 [Debug] Resolved string: Check if the Account List appears
2025-08-07 11:36:41.901 [Debug] Resolving placeholders in string: {{Selectors.BeneficiaryHeader}}
2025-08-07 11:36:41.901 [Debug] Resolved string: id=BeneficiaryHeader
2025-08-07 11:36:41.901 [Info] Successfully resolved parameters for test case: Existing Account beneficiaries displayed
2025-08-07 11:36:41.901 [Debug] Loading test case: WorkSet01/TestCases/03.Beneficiaries/Verify/252-Existing Card beneficiaries displayed.json
2025-08-07 11:36:41.901 [Info] Loading test case from: WorkSet01/TestCases/03.Beneficiaries/Verify/252-Existing Card beneficiaries displayed.json
2025-08-07 11:36:41.904 [Info] Attempting to load configuration file: WorkSet01/TestCases/03.Beneficiaries/Verify/252-Existing Card beneficiaries displayed.json
2025-08-07 11:36:41.905 [Info] Successfully loaded configuration file: WorkSet01/TestCases/03.Beneficiaries/Verify/252-Existing Card beneficiaries displayed.json
2025-08-07 11:36:41.905 [Info] No filtering configuration found. Including test case 'Existing Card beneficiaries displayed'.
2025-08-07 11:36:41.905 [Debug] Resolving parameters for test case: Existing Card beneficiaries displayed
2025-08-07 11:36:41.905 [Debug] Merging params
2025-08-07 11:36:41.905 [Debug] Merged basic params
2025-08-07 11:36:41.905 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-07 11:36:41.905 [Debug] Merged SpecialEnvParams params
2025-08-07 11:36:41.905 [Debug] Merged params
2025-08-07 11:36:41.905 [Debug] Loading parameters from reference file: WorkSet01/Params/BeneficiariesParams.json
2025-08-07 11:36:41.905 [Info] Attempting to load configuration file: WorkSet01/Params/BeneficiariesParams.json
2025-08-07 11:36:41.906 [Info] Successfully loaded configuration file: WorkSet01/Params/BeneficiariesParams.json
2025-08-07 11:36:41.907 [Debug] Merging params
2025-08-07 11:36:41.907 [Debug] Merged basic params
2025-08-07 11:36:41.907 [Debug] Merged params
2025-08-07 11:36:41.907 [Debug] Merging params
2025-08-07 11:36:41.907 [Debug] Merging params
2025-08-07 11:36:41.907 [Debug] Resolving placeholders in test steps for test case: Existing Card beneficiaries displayed
2025-08-07 11:36:41.910 [Debug] Resolving TestCaseReference in step: 
2025-08-07 11:36:41.910 [Info] Loading test case from: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:36:41.910 [Info] Attempting to load configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:36:41.910 [Info] Successfully loaded configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:36:41.910 [Debug] Resolving parameters for test case: Login
2025-08-07 11:36:41.910 [Debug] Merging params
2025-08-07 11:36:41.910 [Debug] Merged basic params
2025-08-07 11:36:41.910 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-07 11:36:41.910 [Debug] Merged SpecialEnvParams params
2025-08-07 11:36:41.910 [Debug] Merged params
2025-08-07 11:36:41.910 [Debug] Loading parameters from reference file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:36:41.911 [Info] Attempting to load configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:36:41.911 [Info] Successfully loaded configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:36:41.911 [Debug] Merging params
2025-08-07 11:36:41.911 [Debug] Merged basic params
2025-08-07 11:36:41.911 [Debug] Merged params
2025-08-07 11:36:41.911 [Debug] Merging params
2025-08-07 11:36:41.911 [Debug] Merging params
2025-08-07 11:36:41.914 [Debug] Resolving placeholders in test steps for test case: Login
2025-08-07 11:36:41.914 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-07 11:36:41.914 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-07 11:36:41.914 [Debug] Resolving placeholders in string: {{Selectors.FinishButton}}
2025-08-07 11:36:41.914 [Debug] Resolved string: id=finish
2025-08-07 11:36:41.914 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-07 11:36:41.914 [Debug] Resolved string: Click on Login Button
2025-08-07 11:36:41.914 [Debug] Resolving placeholders in string: {{Selectors.LoginButton}}
2025-08-07 11:36:41.914 [Debug] Resolved string: id=LoginBtn
2025-08-07 11:36:41.914 [Debug] Resolving placeholders in string: Type the User Name
2025-08-07 11:36:41.914 [Debug] Resolved string: Type the User Name
2025-08-07 11:36:41.914 [Debug] Resolving placeholders in string: {{Selectors.UserNameField}}
2025-08-07 11:36:41.914 [Debug] Resolved string: id=UserName
2025-08-07 11:36:41.914 [Debug] Resolving placeholders in string: {{TestData.UserName}}
2025-08-07 11:36:41.914 [Debug] Resolved string: mahmoudsayed022
2025-08-07 11:36:41.914 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-07 11:36:41.914 [Debug] Resolved string: Click on Continue Button
2025-08-07 11:36:41.917 [Debug] Resolving placeholders in string: {{Selectors.ContinueButton}}
2025-08-07 11:36:41.917 [Debug] Resolved string: id=btn
2025-08-07 11:36:41.917 [Debug] Resolving placeholders in string: Type the Password
2025-08-07 11:36:41.917 [Debug] Resolved string: Type the Password
2025-08-07 11:36:41.917 [Debug] Resolving placeholders in string: {{Selectors.PasswordField}}
2025-08-07 11:36:41.917 [Debug] Resolved string: id=Password
2025-08-07 11:36:41.917 [Debug] Resolving placeholders in string: $('[{{Selectors.PasswordField}}]').val('{{TestData.Password}}')
2025-08-07 11:36:41.917 [Debug] Resolved string: $('[id=Password]').val('Password1')
2025-08-07 11:36:41.917 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-07 11:36:41.917 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-07 11:36:41.917 [Debug] Resolving placeholders in string: {{Selectors.ChallengeQuestionField}}
2025-08-07 11:36:41.917 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-07 11:36:41.917 [Debug] Resolving placeholders in string: {{TestData.ChallengeAnswer}}
2025-08-07 11:36:41.917 [Debug] Resolved string: bmw
2025-08-07 11:36:41.917 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-07 11:36:41.917 [Debug] Resolved string: Click on the Login Button
2025-08-07 11:36:41.918 [Debug] Resolving placeholders in string: {{Selectors.LoginAuthButton}}
2025-08-07 11:36:41.920 [Debug] Resolved string: id=LoginAuthBtn
2025-08-07 11:36:41.920 [Info] Successfully resolved parameters for test case: Login
2025-08-07 11:36:41.920 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-07 11:36:41.920 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-07 11:36:41.920 [Debug] Resolving placeholders in string: id=finish
2025-08-07 11:36:41.920 [Debug] Resolved string: id=finish
2025-08-07 11:36:41.920 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-07 11:36:41.920 [Debug] Resolved string: Click on Login Button
2025-08-07 11:36:41.920 [Debug] Resolving placeholders in string: id=LoginBtn
2025-08-07 11:36:41.921 [Debug] Resolved string: id=LoginBtn
2025-08-07 11:36:41.921 [Debug] Resolving placeholders in string: Type the User Name
2025-08-07 11:36:41.921 [Debug] Resolved string: Type the User Name
2025-08-07 11:36:41.921 [Debug] Resolving placeholders in string: id=UserName
2025-08-07 11:36:41.921 [Debug] Resolved string: id=UserName
2025-08-07 11:36:41.921 [Debug] Resolving placeholders in string: mahmoudsayed022
2025-08-07 11:36:41.921 [Debug] Resolved string: mahmoudsayed022
2025-08-07 11:36:41.921 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-07 11:36:41.924 [Debug] Resolved string: Click on Continue Button
2025-08-07 11:36:41.924 [Debug] Resolving placeholders in string: id=btn
2025-08-07 11:36:41.924 [Debug] Resolved string: id=btn
2025-08-07 11:36:41.924 [Debug] Resolving placeholders in string: Type the Password
2025-08-07 11:36:41.924 [Debug] Resolved string: Type the Password
2025-08-07 11:36:41.924 [Debug] Resolving placeholders in string: id=Password
2025-08-07 11:36:41.924 [Debug] Resolved string: id=Password
2025-08-07 11:36:41.924 [Debug] Resolving placeholders in string: $('[id=Password]').val('Password1')
2025-08-07 11:36:41.924 [Debug] Resolved string: $('[id=Password]').val('Password1')
2025-08-07 11:36:41.924 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-07 11:36:41.924 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-07 11:36:41.924 [Debug] Resolving placeholders in string: id=ChallengeQuestionAnswer
2025-08-07 11:36:41.924 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-07 11:36:41.924 [Debug] Resolving placeholders in string: bmw
2025-08-07 11:36:41.924 [Debug] Resolved string: bmw
2025-08-07 11:36:41.925 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-07 11:36:41.925 [Debug] Resolved string: Click on the Login Button
2025-08-07 11:36:41.929 [Debug] Resolving placeholders in string: id=LoginAuthBtn
2025-08-07 11:36:41.929 [Debug] Resolved string: id=LoginAuthBtn
2025-08-07 11:36:41.929 [Debug] Resolving placeholders in string: Click on change later button on the password expiration popup message
2025-08-07 11:36:41.929 [Debug] Resolved string: Click on change later button on the password expiration popup message
2025-08-07 11:36:41.929 [Debug] Resolving placeholders in string: {{Selectors.popupCancel}}
2025-08-07 11:36:41.929 [Debug] Resolved string: id=popup_cancel
2025-08-07 11:36:41.929 [Debug] Resolving placeholders in string: Click on biometric alert
2025-08-07 11:36:41.929 [Debug] Resolved string: Click on biometric alert
2025-08-07 11:36:41.929 [Debug] Resolving placeholders in string: {{Selectors.BioMetricAlert}}
2025-08-07 11:36:41.929 [Debug] Resolved string: css=#popbuttons > div > button.btn.Cancel.back_gradient2
2025-08-07 11:36:41.929 [Debug] Resolving placeholders in string: Execute JS code to activate token
2025-08-07 11:36:41.929 [Debug] Resolved string: Execute JS code to activate token
2025-08-07 11:36:41.929 [Debug] Resolving placeholders in string: Globals.ActivationState = 'ACTIVATED';
2025-08-07 11:36:41.929 [Debug] Resolved string: Globals.ActivationState = 'ACTIVATED';
2025-08-07 11:36:41.929 [Debug] Resolving placeholders in string: Click on Beneficiaries
2025-08-07 11:36:41.929 [Debug] Resolved string: Click on Beneficiaries
2025-08-07 11:36:41.932 [Debug] Resolving placeholders in string: {{Selectors.BeneficiariesToButton}}
2025-08-07 11:36:41.932 [Debug] Resolved string: xpath=//div[@class='mainmenuitems']//label[contains(text(), 'Beneficiaries')]
2025-08-07 11:36:41.932 [Debug] Resolving placeholders in string: Click on Other CAE Cards
2025-08-07 11:36:41.932 [Debug] Resolved string: Click on Other CAE Cards
2025-08-07 11:36:41.932 [Debug] Resolving placeholders in string: {{Selectors.OtherCAECards}}
2025-08-07 11:36:41.932 [Debug] Resolved string: xpath=//div[@class='submenuitem']//label[contains(text(), 'Other CAE Cards')]
2025-08-07 11:36:41.932 [Debug] Resolving placeholders in string: Check if the Account List appears
2025-08-07 11:36:41.932 [Debug] Resolved string: Check if the Account List appears
2025-08-07 11:36:41.932 [Debug] Resolving placeholders in string: {{Selectors.BeneficiaryHeader}}
2025-08-07 11:36:41.932 [Debug] Resolved string: id=BeneficiaryHeader
2025-08-07 11:36:41.932 [Info] Successfully resolved parameters for test case: Existing Card beneficiaries displayed
2025-08-07 11:36:41.932 [Debug] Loading test case: WorkSet01/TestCases/03.Beneficiaries/Verify/TC-145 Existing bankinsideegypt beneficiaries displayed.json
2025-08-07 11:36:41.932 [Info] Loading test case from: WorkSet01/TestCases/03.Beneficiaries/Verify/TC-145 Existing bankinsideegypt beneficiaries displayed.json
2025-08-07 11:36:41.932 [Info] Attempting to load configuration file: WorkSet01/TestCases/03.Beneficiaries/Verify/TC-145 Existing bankinsideegypt beneficiaries displayed.json
2025-08-07 11:36:41.938 [Info] Successfully loaded configuration file: WorkSet01/TestCases/03.Beneficiaries/Verify/TC-145 Existing bankinsideegypt beneficiaries displayed.json
2025-08-07 11:36:41.938 [Info] No filtering configuration found. Including test case 'Existing Banks Inside Egypt beneficiaries displayed'.
2025-08-07 11:36:41.941 [Debug] Resolving parameters for test case: Existing Banks Inside Egypt beneficiaries displayed
2025-08-07 11:36:41.941 [Debug] Merging params
2025-08-07 11:36:41.941 [Debug] Merged basic params
2025-08-07 11:36:41.941 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-07 11:36:41.942 [Debug] Merged SpecialEnvParams params
2025-08-07 11:36:41.942 [Debug] Merged params
2025-08-07 11:36:41.942 [Debug] Loading parameters from reference file: WorkSet01/Params/BeneficiariesParams.json
2025-08-07 11:36:41.942 [Info] Attempting to load configuration file: WorkSet01/Params/BeneficiariesParams.json
2025-08-07 11:36:41.942 [Info] Successfully loaded configuration file: WorkSet01/Params/BeneficiariesParams.json
2025-08-07 11:36:41.942 [Debug] Merging params
2025-08-07 11:36:41.942 [Debug] Merged basic params
2025-08-07 11:36:41.942 [Debug] Merged params
2025-08-07 11:36:41.942 [Debug] Merging params
2025-08-07 11:36:41.943 [Debug] Merging params
2025-08-07 11:36:41.943 [Debug] Resolving placeholders in test steps for test case: Existing Banks Inside Egypt beneficiaries displayed
2025-08-07 11:36:41.943 [Debug] Resolving TestCaseReference in step: 
2025-08-07 11:36:41.945 [Info] Loading test case from: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:36:41.945 [Info] Attempting to load configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:36:41.946 [Info] Successfully loaded configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:36:41.946 [Debug] Resolving parameters for test case: Login
2025-08-07 11:36:41.946 [Debug] Merging params
2025-08-07 11:36:41.946 [Debug] Merged basic params
2025-08-07 11:36:41.946 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-07 11:36:41.946 [Debug] Merged SpecialEnvParams params
2025-08-07 11:36:41.946 [Debug] Merged params
2025-08-07 11:36:41.946 [Debug] Loading parameters from reference file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:36:41.946 [Info] Attempting to load configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:36:41.947 [Info] Successfully loaded configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:36:41.947 [Debug] Merging params
2025-08-07 11:36:41.947 [Debug] Merged basic params
2025-08-07 11:36:41.947 [Debug] Merged params
2025-08-07 11:36:41.947 [Debug] Merging params
2025-08-07 11:36:41.950 [Debug] Merging params
2025-08-07 11:36:41.950 [Debug] Resolving placeholders in test steps for test case: Login
2025-08-07 11:36:41.950 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-07 11:36:41.950 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-07 11:36:41.950 [Debug] Resolving placeholders in string: {{Selectors.FinishButton}}
2025-08-07 11:36:41.950 [Debug] Resolved string: id=finish
2025-08-07 11:36:41.950 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-07 11:36:41.950 [Debug] Resolved string: Click on Login Button
2025-08-07 11:36:41.950 [Debug] Resolving placeholders in string: {{Selectors.LoginButton}}
2025-08-07 11:36:41.950 [Debug] Resolved string: id=LoginBtn
2025-08-07 11:36:41.950 [Debug] Resolving placeholders in string: Type the User Name
2025-08-07 11:36:41.950 [Debug] Resolved string: Type the User Name
2025-08-07 11:36:41.950 [Debug] Resolving placeholders in string: {{Selectors.UserNameField}}
2025-08-07 11:36:41.950 [Debug] Resolved string: id=UserName
2025-08-07 11:36:41.951 [Debug] Resolving placeholders in string: {{TestData.UserName}}
2025-08-07 11:36:41.951 [Debug] Resolved string: mahmoudsayed022
2025-08-07 11:36:41.955 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-07 11:36:41.955 [Debug] Resolved string: Click on Continue Button
2025-08-07 11:36:41.955 [Debug] Resolving placeholders in string: {{Selectors.ContinueButton}}
2025-08-07 11:36:41.964 [Debug] Resolved string: id=btn
2025-08-07 11:36:41.965 [Debug] Resolving placeholders in string: Type the Password
2025-08-07 11:36:41.965 [Debug] Resolved string: Type the Password
2025-08-07 11:36:41.965 [Debug] Resolving placeholders in string: {{Selectors.PasswordField}}
2025-08-07 11:36:41.965 [Debug] Resolved string: id=Password
2025-08-07 11:36:41.965 [Debug] Resolving placeholders in string: $('[{{Selectors.PasswordField}}]').val('{{TestData.Password}}')
2025-08-07 11:36:41.965 [Debug] Resolved string: $('[id=Password]').val('Password1')
2025-08-07 11:36:41.965 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-07 11:36:41.965 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-07 11:36:41.965 [Debug] Resolving placeholders in string: {{Selectors.ChallengeQuestionField}}
2025-08-07 11:36:41.965 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-07 11:36:41.965 [Debug] Resolving placeholders in string: {{TestData.ChallengeAnswer}}
2025-08-07 11:36:41.968 [Debug] Resolved string: bmw
2025-08-07 11:36:41.968 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-07 11:36:41.969 [Debug] Resolved string: Click on the Login Button
2025-08-07 11:36:41.969 [Debug] Resolving placeholders in string: {{Selectors.LoginAuthButton}}
2025-08-07 11:36:41.969 [Debug] Resolved string: id=LoginAuthBtn
2025-08-07 11:36:41.969 [Info] Successfully resolved parameters for test case: Login
2025-08-07 11:36:41.969 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-07 11:36:41.969 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-07 11:36:41.969 [Debug] Resolving placeholders in string: id=finish
2025-08-07 11:36:41.969 [Debug] Resolved string: id=finish
2025-08-07 11:36:41.969 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-07 11:36:41.969 [Debug] Resolved string: Click on Login Button
2025-08-07 11:36:41.969 [Debug] Resolving placeholders in string: id=LoginBtn
2025-08-07 11:36:41.969 [Debug] Resolved string: id=LoginBtn
2025-08-07 11:36:41.969 [Debug] Resolving placeholders in string: Type the User Name
2025-08-07 11:36:41.973 [Debug] Resolved string: Type the User Name
2025-08-07 11:36:41.973 [Debug] Resolving placeholders in string: id=UserName
2025-08-07 11:36:41.973 [Debug] Resolved string: id=UserName
2025-08-07 11:36:41.973 [Debug] Resolving placeholders in string: mahmoudsayed022
2025-08-07 11:36:41.973 [Debug] Resolved string: mahmoudsayed022
2025-08-07 11:36:41.973 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-07 11:36:41.973 [Debug] Resolved string: Click on Continue Button
2025-08-07 11:36:41.973 [Debug] Resolving placeholders in string: id=btn
2025-08-07 11:36:41.973 [Debug] Resolved string: id=btn
2025-08-07 11:36:41.973 [Debug] Resolving placeholders in string: Type the Password
2025-08-07 11:36:41.974 [Debug] Resolved string: Type the Password
2025-08-07 11:36:41.974 [Debug] Resolving placeholders in string: id=Password
2025-08-07 11:36:41.974 [Debug] Resolved string: id=Password
2025-08-07 11:36:41.974 [Debug] Resolving placeholders in string: $('[id=Password]').val('Password1')
2025-08-07 11:36:41.974 [Debug] Resolved string: $('[id=Password]').val('Password1')
2025-08-07 11:36:41.977 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-07 11:36:41.977 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-07 11:36:41.977 [Debug] Resolving placeholders in string: id=ChallengeQuestionAnswer
2025-08-07 11:36:41.977 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-07 11:36:41.977 [Debug] Resolving placeholders in string: bmw
2025-08-07 11:36:41.977 [Debug] Resolved string: bmw
2025-08-07 11:36:41.977 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-07 11:36:41.977 [Debug] Resolved string: Click on the Login Button
2025-08-07 11:36:41.977 [Debug] Resolving placeholders in string: id=LoginAuthBtn
2025-08-07 11:36:41.977 [Debug] Resolved string: id=LoginAuthBtn
2025-08-07 11:36:41.977 [Debug] Resolving placeholders in string: Click on change later button on the password expiration popup message
2025-08-07 11:36:41.977 [Debug] Resolved string: Click on change later button on the password expiration popup message
2025-08-07 11:36:41.977 [Debug] Resolving placeholders in string: {{Selectors.popupCancel}}
2025-08-07 11:36:41.977 [Debug] Resolved string: id=popup_cancel
2025-08-07 11:36:41.978 [Debug] Resolving placeholders in string: Click on biometric alert
2025-08-07 11:36:41.983 [Debug] Resolved string: Click on biometric alert
2025-08-07 11:36:41.983 [Debug] Resolving placeholders in string: {{Selectors.BioMetricAlert}}
2025-08-07 11:36:41.983 [Debug] Resolved string: css=#popbuttons > div > button.btn.Cancel.back_gradient2
2025-08-07 11:36:41.984 [Debug] Resolving placeholders in string: Execute JS code to activate token
2025-08-07 11:36:41.984 [Debug] Resolved string: Execute JS code to activate token
2025-08-07 11:36:41.984 [Debug] Resolving placeholders in string: Globals.ActivationState = 'ACTIVATED';
2025-08-07 11:36:41.984 [Debug] Resolved string: Globals.ActivationState = 'ACTIVATED';
2025-08-07 11:36:41.984 [Debug] Resolving placeholders in string: Click on Beneficiaries
2025-08-07 11:36:41.984 [Debug] Resolved string: Click on Beneficiaries
2025-08-07 11:36:41.984 [Debug] Resolving placeholders in string: {{Selectors.BeneficiariesToButton}}
2025-08-07 11:36:41.984 [Debug] Resolved string: xpath=//div[@class='mainmenuitems']//label[contains(text(), 'Beneficiaries')]
2025-08-07 11:36:41.984 [Debug] Resolving placeholders in string: Click on Banks Inside Egypt
2025-08-07 11:36:41.984 [Debug] Resolved string: Click on Banks Inside Egypt
2025-08-07 11:36:41.984 [Debug] Resolving placeholders in string: {{Selectors.BanksInsideEgypt}}
2025-08-07 11:36:41.984 [Debug] Resolved string: xpath=//div[@class='submenuitem']//label[contains(text(), 'Banks Inside Egypt')]
2025-08-07 11:36:41.987 [Debug] Resolving placeholders in string: Check if the Account List appears
2025-08-07 11:36:41.987 [Debug] Resolved string: Check if the Account List appears
2025-08-07 11:36:41.987 [Debug] Resolving placeholders in string: {{Selectors.BeneficiaryHeader}}
2025-08-07 11:36:41.988 [Debug] Resolved string: id=BeneficiaryHeader
2025-08-07 11:36:41.988 [Info] Successfully resolved parameters for test case: Existing Banks Inside Egypt beneficiaries displayed
2025-08-07 11:36:41.988 [Debug] Loading test case: WorkSet01/TestCases/03.Beneficiaries/Delete/147-Delete Existing beneficiary.json
2025-08-07 11:36:41.988 [Info] Loading test case from: WorkSet01/TestCases/03.Beneficiaries/Delete/147-Delete Existing beneficiary.json
2025-08-07 11:36:41.988 [Info] Attempting to load configuration file: WorkSet01/TestCases/03.Beneficiaries/Delete/147-Delete Existing beneficiary.json
2025-08-07 11:36:41.998 [Info] Successfully loaded configuration file: WorkSet01/TestCases/03.Beneficiaries/Delete/147-Delete Existing beneficiary.json
2025-08-07 11:36:41.999 [Info] No filtering configuration found. Including test case 'Delete Existing beneficiary'.
2025-08-07 11:36:41.999 [Debug] Resolving parameters for test case: Delete Existing beneficiary
2025-08-07 11:36:41.999 [Debug] Merging params
2025-08-07 11:36:41.999 [Debug] Merged basic params
2025-08-07 11:36:42.003 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-07 11:36:42.004 [Debug] Merged SpecialEnvParams params
2025-08-07 11:36:42.009 [Debug] Merged params
2025-08-07 11:36:42.009 [Debug] Loading parameters from reference file: WorkSet01/Params/BeneficiariesParams.json
2025-08-07 11:36:42.009 [Info] Attempting to load configuration file: WorkSet01/Params/BeneficiariesParams.json
2025-08-07 11:36:42.009 [Info] Successfully loaded configuration file: WorkSet01/Params/BeneficiariesParams.json
2025-08-07 11:36:42.010 [Debug] Merging params
2025-08-07 11:36:42.010 [Debug] Merged basic params
2025-08-07 11:36:42.010 [Debug] Merged params
2025-08-07 11:36:42.010 [Debug] Merging params
2025-08-07 11:36:42.010 [Debug] Merging params
2025-08-07 11:36:42.010 [Debug] Resolving placeholders in test steps for test case: Delete Existing beneficiary
2025-08-07 11:36:42.011 [Debug] Resolving TestCaseReference in step: 
2025-08-07 11:36:42.011 [Info] Loading test case from: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:36:42.011 [Info] Attempting to load configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:36:42.011 [Info] Successfully loaded configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:36:42.016 [Debug] Resolving parameters for test case: Login
2025-08-07 11:36:42.016 [Debug] Merging params
2025-08-07 11:36:42.016 [Debug] Merged basic params
2025-08-07 11:36:42.016 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-07 11:36:42.017 [Debug] Merged SpecialEnvParams params
2025-08-07 11:36:42.017 [Debug] Merged params
2025-08-07 11:36:42.017 [Debug] Loading parameters from reference file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:36:42.017 [Info] Attempting to load configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:36:42.017 [Info] Successfully loaded configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:36:42.018 [Debug] Merging params
2025-08-07 11:36:42.018 [Debug] Merged basic params
2025-08-07 11:36:42.018 [Debug] Merged params
2025-08-07 11:36:42.018 [Debug] Merging params
2025-08-07 11:36:42.018 [Debug] Merging params
2025-08-07 11:36:42.022 [Debug] Resolving placeholders in test steps for test case: Login
2025-08-07 11:36:42.022 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-07 11:36:42.022 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-07 11:36:42.022 [Debug] Resolving placeholders in string: {{Selectors.FinishButton}}
2025-08-07 11:36:42.022 [Debug] Resolved string: id=finish
2025-08-07 11:36:42.022 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-07 11:36:42.022 [Debug] Resolved string: Click on Login Button
2025-08-07 11:36:42.022 [Debug] Resolving placeholders in string: {{Selectors.LoginButton}}
2025-08-07 11:36:42.022 [Debug] Resolved string: id=LoginBtn
2025-08-07 11:36:42.022 [Debug] Resolving placeholders in string: Type the User Name
2025-08-07 11:36:42.023 [Debug] Resolved string: Type the User Name
2025-08-07 11:36:42.023 [Debug] Resolving placeholders in string: {{Selectors.UserNameField}}
2025-08-07 11:36:42.023 [Debug] Resolved string: id=UserName
2025-08-07 11:36:42.023 [Debug] Resolving placeholders in string: {{TestData.UserName}}
2025-08-07 11:36:42.026 [Debug] Resolved string: mahmoudsayed022
2025-08-07 11:36:42.027 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-07 11:36:42.027 [Debug] Resolved string: Click on Continue Button
2025-08-07 11:36:42.027 [Debug] Resolving placeholders in string: {{Selectors.ContinueButton}}
2025-08-07 11:36:42.027 [Debug] Resolved string: id=btn
2025-08-07 11:36:42.027 [Debug] Resolving placeholders in string: Type the Password
2025-08-07 11:36:42.027 [Debug] Resolved string: Type the Password
2025-08-07 11:36:42.027 [Debug] Resolving placeholders in string: {{Selectors.PasswordField}}
2025-08-07 11:36:42.027 [Debug] Resolved string: id=Password
2025-08-07 11:36:42.027 [Debug] Resolving placeholders in string: $('[{{Selectors.PasswordField}}]').val('{{TestData.Password}}')
2025-08-07 11:36:42.027 [Debug] Resolved string: $('[id=Password]').val('Password1')
2025-08-07 11:36:42.027 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-07 11:36:42.027 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-07 11:36:42.028 [Debug] Resolving placeholders in string: {{Selectors.ChallengeQuestionField}}
2025-08-07 11:36:42.031 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-07 11:36:42.032 [Debug] Resolving placeholders in string: {{TestData.ChallengeAnswer}}
2025-08-07 11:36:42.032 [Debug] Resolved string: bmw
2025-08-07 11:36:42.032 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-07 11:36:42.032 [Debug] Resolved string: Click on the Login Button
2025-08-07 11:36:42.032 [Debug] Resolving placeholders in string: {{Selectors.LoginAuthButton}}
2025-08-07 11:36:42.032 [Debug] Resolved string: id=LoginAuthBtn
2025-08-07 11:36:42.032 [Info] Successfully resolved parameters for test case: Login
2025-08-07 11:36:42.032 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-07 11:36:42.032 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-07 11:36:42.032 [Debug] Resolving placeholders in string: id=finish
2025-08-07 11:36:42.032 [Debug] Resolved string: id=finish
2025-08-07 11:36:42.032 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-07 11:36:42.032 [Debug] Resolved string: Click on Login Button
2025-08-07 11:36:42.036 [Debug] Resolving placeholders in string: id=LoginBtn
2025-08-07 11:36:42.036 [Debug] Resolved string: id=LoginBtn
2025-08-07 11:36:42.036 [Debug] Resolving placeholders in string: Type the User Name
2025-08-07 11:36:42.036 [Debug] Resolved string: Type the User Name
2025-08-07 11:36:42.036 [Debug] Resolving placeholders in string: id=UserName
2025-08-07 11:36:42.037 [Debug] Resolved string: id=UserName
2025-08-07 11:36:42.037 [Debug] Resolving placeholders in string: mahmoudsayed022
2025-08-07 11:36:42.037 [Debug] Resolved string: mahmoudsayed022
2025-08-07 11:36:42.037 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-07 11:36:42.037 [Debug] Resolved string: Click on Continue Button
2025-08-07 11:36:42.037 [Debug] Resolving placeholders in string: id=btn
2025-08-07 11:36:42.037 [Debug] Resolved string: id=btn
2025-08-07 11:36:42.037 [Debug] Resolving placeholders in string: Type the Password
2025-08-07 11:36:42.037 [Debug] Resolved string: Type the Password
2025-08-07 11:36:42.042 [Debug] Resolving placeholders in string: id=Password
2025-08-07 11:36:42.042 [Debug] Resolved string: id=Password
2025-08-07 11:36:42.042 [Debug] Resolving placeholders in string: $('[id=Password]').val('Password1')
2025-08-07 11:36:42.042 [Debug] Resolved string: $('[id=Password]').val('Password1')
2025-08-07 11:36:42.042 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-07 11:36:42.042 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-07 11:36:42.042 [Debug] Resolving placeholders in string: id=ChallengeQuestionAnswer
2025-08-07 11:36:42.043 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-07 11:36:42.043 [Debug] Resolving placeholders in string: bmw
2025-08-07 11:36:42.043 [Debug] Resolved string: bmw
2025-08-07 11:36:42.043 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-07 11:36:42.043 [Debug] Resolved string: Click on the Login Button
2025-08-07 11:36:42.043 [Debug] Resolving placeholders in string: id=LoginAuthBtn
2025-08-07 11:36:42.043 [Debug] Resolved string: id=LoginAuthBtn
2025-08-07 11:36:42.046 [Debug] Resolving placeholders in string: Click on change later button on the password expiration popup message
2025-08-07 11:36:42.047 [Debug] Resolved string: Click on change later button on the password expiration popup message
2025-08-07 11:36:42.047 [Debug] Resolving placeholders in string: {{Selectors.popupCancel}}
2025-08-07 11:36:42.047 [Debug] Resolved string: id=popup_cancel
2025-08-07 11:36:42.047 [Debug] Resolving placeholders in string: Click on biometric alert
2025-08-07 11:36:42.047 [Debug] Resolved string: Click on biometric alert
2025-08-07 11:36:42.047 [Debug] Resolving placeholders in string: {{Selectors.BioMetricAlert}}
2025-08-07 11:36:42.047 [Debug] Resolved string: css=#popbuttons > div > button.btn.Cancel.back_gradient2
2025-08-07 11:36:42.047 [Debug] Resolving placeholders in string: Click on biometric alert
2025-08-07 11:36:42.047 [Debug] Resolved string: Click on biometric alert
2025-08-07 11:36:42.047 [Debug] Resolving placeholders in string: {{Selectors.KYCskip}}
2025-08-07 11:36:42.047 [Debug] Resolved string: {{Selectors.KYCskip}}
2025-08-07 11:36:42.047 [Debug] Resolving placeholders in string: Execute JS code to activate token
2025-08-07 11:36:42.053 [Debug] Resolved string: Execute JS code to activate token
2025-08-07 11:36:42.053 [Debug] Resolving placeholders in string: Globals.ActivationState = 'ACTIVATED';
2025-08-07 11:36:42.053 [Debug] Resolved string: Globals.ActivationState = 'ACTIVATED';
2025-08-07 11:36:42.053 [Debug] Resolving placeholders in string: Click on Beneficiaries
2025-08-07 11:36:42.053 [Debug] Resolved string: Click on Beneficiaries
2025-08-07 11:36:42.053 [Debug] Resolving placeholders in string: {{Selectors.BeneficiariesToButton}}
2025-08-07 11:36:42.053 [Debug] Resolved string: xpath=//div[@class='mainmenuitems']//label[contains(text(), 'Beneficiaries')]
2025-08-07 11:36:42.053 [Debug] Resolving placeholders in string: Click on Local Transfer option
2025-08-07 11:36:42.053 [Debug] Resolved string: Click on Local Transfer option
2025-08-07 11:36:42.053 [Debug] Resolving placeholders in string: {{Selectors.LocalTransfer}}
2025-08-07 11:36:42.053 [Debug] Resolved string: xpath=//div[@class='submenuitem']//label[contains(text(), 'Local Transfer')]
2025-08-07 11:36:42.053 [Debug] Resolving placeholders in string: Click on Delete button
2025-08-07 11:36:42.053 [Debug] Resolved string: Click on Delete button
2025-08-07 11:36:42.057 [Debug] Resolving placeholders in string: id=DeleteBtn
2025-08-07 11:36:42.058 [Debug] Resolved string: id=DeleteBtn
2025-08-07 11:36:42.058 [Debug] Resolving placeholders in string: Click on continue button
2025-08-07 11:36:42.058 [Debug] Resolved string: Click on continue button
2025-08-07 11:36:42.058 [Debug] Resolving placeholders in string: id=DelLocBenSumContinue
2025-08-07 11:36:42.058 [Debug] Resolved string: id=DelLocBenSumContinue
2025-08-07 11:36:42.058 [Info] Successfully resolved parameters for test case: Delete Existing beneficiary
2025-08-07 11:36:42.058 [Debug] Loading test case: WorkSet01/TestCases/03.Beneficiaries/Delete/TC-254 Delete an Existing Card beneficiary.json
2025-08-07 11:36:42.058 [Info] Loading test case from: WorkSet01/TestCases/03.Beneficiaries/Delete/TC-254 Delete an Existing Card beneficiary.json
2025-08-07 11:36:42.058 [Info] Attempting to load configuration file: WorkSet01/TestCases/03.Beneficiaries/Delete/TC-254 Delete an Existing Card beneficiary.json
2025-08-07 11:36:42.069 [Info] Successfully loaded configuration file: WorkSet01/TestCases/03.Beneficiaries/Delete/TC-254 Delete an Existing Card beneficiary.json
2025-08-07 11:36:42.070 [Info] No filtering configuration found. Including test case 'Delete an Existing Card beneficiary'.
2025-08-07 11:36:42.070 [Debug] Resolving parameters for test case: Delete an Existing Card beneficiary
2025-08-07 11:36:42.076 [Debug] Merging params
2025-08-07 11:36:42.076 [Debug] Merged basic params
2025-08-07 11:36:42.076 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-07 11:36:42.077 [Debug] Merged SpecialEnvParams params
2025-08-07 11:36:42.077 [Debug] Merged params
2025-08-07 11:36:42.077 [Debug] Loading parameters from reference file: WorkSet01/Params/BeneficiariesParams.json
2025-08-07 11:36:42.077 [Info] Attempting to load configuration file: WorkSet01/Params/BeneficiariesParams.json
2025-08-07 11:36:42.077 [Info] Successfully loaded configuration file: WorkSet01/Params/BeneficiariesParams.json
2025-08-07 11:36:42.078 [Debug] Merging params
2025-08-07 11:36:42.078 [Debug] Merged basic params
2025-08-07 11:36:42.078 [Debug] Merged params
2025-08-07 11:36:42.078 [Debug] Merging params
2025-08-07 11:36:42.078 [Debug] Merging params
2025-08-07 11:36:42.082 [Debug] Resolving placeholders in test steps for test case: Delete an Existing Card beneficiary
2025-08-07 11:36:42.082 [Debug] Resolving TestCaseReference in step: 
2025-08-07 11:36:42.082 [Info] Loading test case from: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:36:42.083 [Info] Attempting to load configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:36:42.083 [Info] Successfully loaded configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:36:42.084 [Debug] Resolving parameters for test case: Login
2025-08-07 11:36:42.084 [Debug] Merging params
2025-08-07 11:36:42.084 [Debug] Merged basic params
2025-08-07 11:36:42.085 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-07 11:36:42.085 [Debug] Merged SpecialEnvParams params
2025-08-07 11:36:42.085 [Debug] Merged params
2025-08-07 11:36:42.087 [Debug] Loading parameters from reference file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:36:42.091 [Info] Attempting to load configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:36:42.097 [Info] Successfully loaded configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:36:42.098 [Debug] Merging params
2025-08-07 11:36:42.098 [Debug] Merged basic params
2025-08-07 11:36:42.098 [Debug] Merged params
2025-08-07 11:36:42.099 [Debug] Merging params
2025-08-07 11:36:42.100 [Debug] Merging params
2025-08-07 11:36:42.100 [Debug] Resolving placeholders in test steps for test case: Login
2025-08-07 11:36:42.100 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-07 11:36:42.101 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-07 11:36:42.102 [Debug] Resolving placeholders in string: {{Selectors.FinishButton}}
2025-08-07 11:36:42.102 [Debug] Resolved string: id=finish
2025-08-07 11:36:42.102 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-07 11:36:42.102 [Debug] Resolved string: Click on Login Button
2025-08-07 11:36:42.105 [Debug] Resolving placeholders in string: {{Selectors.LoginButton}}
2025-08-07 11:36:42.105 [Debug] Resolved string: id=LoginBtn
2025-08-07 11:36:42.105 [Debug] Resolving placeholders in string: Type the User Name
2025-08-07 11:36:42.105 [Debug] Resolved string: Type the User Name
2025-08-07 11:36:42.105 [Debug] Resolving placeholders in string: {{Selectors.UserNameField}}
2025-08-07 11:36:42.105 [Debug] Resolved string: id=UserName
2025-08-07 11:36:42.105 [Debug] Resolving placeholders in string: {{TestData.UserName}}
2025-08-07 11:36:42.105 [Debug] Resolved string: mahmoudsayed022
2025-08-07 11:36:42.105 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-07 11:36:42.105 [Debug] Resolved string: Click on Continue Button
2025-08-07 11:36:42.106 [Debug] Resolving placeholders in string: {{Selectors.ContinueButton}}
2025-08-07 11:36:42.106 [Debug] Resolved string: id=btn
2025-08-07 11:36:42.106 [Debug] Resolving placeholders in string: Type the Password
2025-08-07 11:36:42.110 [Debug] Resolved string: Type the Password
2025-08-07 11:36:42.110 [Debug] Resolving placeholders in string: {{Selectors.PasswordField}}
2025-08-07 11:36:42.110 [Debug] Resolved string: id=Password
2025-08-07 11:36:42.110 [Debug] Resolving placeholders in string: $('[{{Selectors.PasswordField}}]').val('{{TestData.Password}}')
2025-08-07 11:36:42.110 [Debug] Resolved string: $('[id=Password]').val('Password1')
2025-08-07 11:36:42.110 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-07 11:36:42.110 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-07 11:36:42.110 [Debug] Resolving placeholders in string: {{Selectors.ChallengeQuestionField}}
2025-08-07 11:36:42.110 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-07 11:36:42.110 [Debug] Resolving placeholders in string: {{TestData.ChallengeAnswer}}
2025-08-07 11:36:42.110 [Debug] Resolved string: bmw
2025-08-07 11:36:42.110 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-07 11:36:42.110 [Debug] Resolved string: Click on the Login Button
2025-08-07 11:36:42.114 [Debug] Resolving placeholders in string: {{Selectors.LoginAuthButton}}
2025-08-07 11:36:42.114 [Debug] Resolved string: id=LoginAuthBtn
2025-08-07 11:36:42.114 [Info] Successfully resolved parameters for test case: Login
2025-08-07 11:36:42.114 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-07 11:36:42.115 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-07 11:36:42.115 [Debug] Resolving placeholders in string: id=finish
2025-08-07 11:36:42.115 [Debug] Resolved string: id=finish
2025-08-07 11:36:42.115 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-07 11:36:42.115 [Debug] Resolved string: Click on Login Button
2025-08-07 11:36:42.115 [Debug] Resolving placeholders in string: id=LoginBtn
2025-08-07 11:36:42.115 [Debug] Resolved string: id=LoginBtn
2025-08-07 11:36:42.115 [Debug] Resolving placeholders in string: Type the User Name
2025-08-07 11:36:42.115 [Debug] Resolved string: Type the User Name
2025-08-07 11:36:42.119 [Debug] Resolving placeholders in string: id=UserName
2025-08-07 11:36:42.119 [Debug] Resolved string: id=UserName
2025-08-07 11:36:42.119 [Debug] Resolving placeholders in string: mahmoudsayed022
2025-08-07 11:36:42.119 [Debug] Resolved string: mahmoudsayed022
2025-08-07 11:36:42.119 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-07 11:36:42.119 [Debug] Resolved string: Click on Continue Button
2025-08-07 11:36:42.119 [Debug] Resolving placeholders in string: id=btn
2025-08-07 11:36:42.120 [Debug] Resolved string: id=btn
2025-08-07 11:36:42.120 [Debug] Resolving placeholders in string: Type the Password
2025-08-07 11:36:42.120 [Debug] Resolved string: Type the Password
2025-08-07 11:36:42.120 [Debug] Resolving placeholders in string: id=Password
2025-08-07 11:36:42.120 [Debug] Resolved string: id=Password
2025-08-07 11:36:42.120 [Debug] Resolving placeholders in string: $('[id=Password]').val('Password1')
2025-08-07 11:36:42.124 [Debug] Resolved string: $('[id=Password]').val('Password1')
2025-08-07 11:36:42.124 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-07 11:36:42.124 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-07 11:36:42.124 [Debug] Resolving placeholders in string: id=ChallengeQuestionAnswer
2025-08-07 11:36:42.124 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-07 11:36:42.124 [Debug] Resolving placeholders in string: bmw
2025-08-07 11:36:42.124 [Debug] Resolved string: bmw
2025-08-07 11:36:42.124 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-07 11:36:42.124 [Debug] Resolved string: Click on the Login Button
2025-08-07 11:36:42.124 [Debug] Resolving placeholders in string: id=LoginAuthBtn
2025-08-07 11:36:42.124 [Debug] Resolved string: id=LoginAuthBtn
2025-08-07 11:36:42.124 [Debug] Resolving placeholders in string: Click on change later button on the password expiration popup message
2025-08-07 11:36:42.128 [Debug] Resolved string: Click on change later button on the password expiration popup message
2025-08-07 11:36:42.128 [Debug] Resolving placeholders in string: {{Selectors.popupCancel}}
2025-08-07 11:36:42.128 [Debug] Resolved string: id=popup_cancel
2025-08-07 11:36:42.128 [Debug] Resolving placeholders in string: Click on biometric alert
2025-08-07 11:36:42.129 [Debug] Resolved string: Click on biometric alert
2025-08-07 11:36:42.129 [Debug] Resolving placeholders in string: {{Selectors.BioMetricAlert}}
2025-08-07 11:36:42.129 [Debug] Resolved string: css=#popbuttons > div > button.btn.Cancel.back_gradient2
2025-08-07 11:36:42.129 [Debug] Resolving placeholders in string: Click on biometric alert
2025-08-07 11:36:42.129 [Debug] Resolved string: Click on biometric alert
2025-08-07 11:36:42.129 [Debug] Resolving placeholders in string: {{Selectors.KYCskip}}
2025-08-07 11:36:42.129 [Debug] Resolved string: {{Selectors.KYCskip}}
2025-08-07 11:36:42.129 [Debug] Resolving placeholders in string: Execute JS code to activate token
2025-08-07 11:36:42.134 [Debug] Resolved string: Execute JS code to activate token
2025-08-07 11:36:42.134 [Debug] Resolving placeholders in string: Globals.ActivationState = 'ACTIVATED';
2025-08-07 11:36:42.134 [Debug] Resolved string: Globals.ActivationState = 'ACTIVATED';
2025-08-07 11:36:42.134 [Debug] Resolving placeholders in string: Click on Beneficiaries
2025-08-07 11:36:42.135 [Debug] Resolved string: Click on Beneficiaries
2025-08-07 11:36:42.136 [Debug] Resolving placeholders in string: {{Selectors.BeneficiariesToButton}}
2025-08-07 11:36:42.136 [Debug] Resolved string: xpath=//div[@class='mainmenuitems']//label[contains(text(), 'Beneficiaries')]
2025-08-07 11:36:42.136 [Debug] Resolving placeholders in string: Click on Other CAE Cards
2025-08-07 11:36:42.136 [Debug] Resolved string: Click on Other CAE Cards
2025-08-07 11:36:42.136 [Debug] Resolving placeholders in string: {{Selectors.OtherCAECards}}
2025-08-07 11:36:42.136 [Debug] Resolved string: xpath=//div[@class='submenuitem']//label[contains(text(), 'Other CAE Cards')]
2025-08-07 11:36:42.136 [Debug] Resolving placeholders in string: Click on Delete button
2025-08-07 11:36:42.142 [Debug] Resolved string: Click on Delete button
2025-08-07 11:36:42.143 [Debug] Resolving placeholders in string: id=DeleteBeneficiary
2025-08-07 11:36:42.143 [Debug] Resolved string: id=DeleteBeneficiary
2025-08-07 11:36:42.143 [Debug] Resolving placeholders in string: Click on continue button and show confirmation
2025-08-07 11:36:42.143 [Debug] Resolved string: Click on continue button and show confirmation
2025-08-07 11:36:42.143 [Debug] Resolving placeholders in string: id=DigitalBankBenfContinueDeleteBtn
2025-08-07 11:36:42.143 [Debug] Resolved string: id=DigitalBankBenfContinueDeleteBtn
2025-08-07 11:36:42.143 [Info] Successfully resolved parameters for test case: Delete an Existing Card beneficiary
2025-08-07 11:36:42.143 [Debug] Loading test case: WorkSet01/TestCases/03.Beneficiaries/Delete/TC-253 Delete an Existing Account beneficiary.json
2025-08-07 11:36:42.143 [Info] Loading test case from: WorkSet01/TestCases/03.Beneficiaries/Delete/TC-253 Delete an Existing Account beneficiary.json
2025-08-07 11:36:42.143 [Info] Attempting to load configuration file: WorkSet01/TestCases/03.Beneficiaries/Delete/TC-253 Delete an Existing Account beneficiary.json
2025-08-07 11:36:42.144 [Info] Successfully loaded configuration file: WorkSet01/TestCases/03.Beneficiaries/Delete/TC-253 Delete an Existing Account beneficiary.json
2025-08-07 11:36:42.148 [Info] No filtering configuration found. Including test case 'Delete an Existing Account beneficiary'.
2025-08-07 11:36:42.148 [Debug] Resolving parameters for test case: Delete an Existing Account beneficiary
2025-08-07 11:36:42.148 [Debug] Merging params
2025-08-07 11:36:42.148 [Debug] Merged basic params
2025-08-07 11:36:42.148 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-07 11:36:42.149 [Debug] Merged SpecialEnvParams params
2025-08-07 11:36:42.149 [Debug] Merged params
2025-08-07 11:36:42.149 [Debug] Loading parameters from reference file: WorkSet01/Params/BeneficiariesParams.json
2025-08-07 11:36:42.149 [Info] Attempting to load configuration file: WorkSet01/Params/BeneficiariesParams.json
2025-08-07 11:36:42.149 [Info] Successfully loaded configuration file: WorkSet01/Params/BeneficiariesParams.json
2025-08-07 11:36:42.150 [Debug] Merging params
2025-08-07 11:36:42.150 [Debug] Merged basic params
2025-08-07 11:36:42.154 [Debug] Merged params
2025-08-07 11:36:42.154 [Debug] Merging params
2025-08-07 11:36:42.154 [Debug] Merging params
2025-08-07 11:36:42.154 [Debug] Resolving placeholders in test steps for test case: Delete an Existing Account beneficiary
2025-08-07 11:36:42.154 [Debug] Resolving TestCaseReference in step: 
2025-08-07 11:36:42.155 [Info] Loading test case from: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:36:42.155 [Info] Attempting to load configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:36:42.155 [Info] Successfully loaded configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:36:42.156 [Debug] Resolving parameters for test case: Login
2025-08-07 11:36:42.156 [Debug] Merging params
2025-08-07 11:36:42.156 [Debug] Merged basic params
2025-08-07 11:36:42.156 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-07 11:36:42.160 [Debug] Merged SpecialEnvParams params
2025-08-07 11:36:42.160 [Debug] Merged params
2025-08-07 11:36:42.161 [Debug] Loading parameters from reference file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:36:42.161 [Info] Attempting to load configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:36:42.161 [Info] Successfully loaded configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:36:42.161 [Debug] Merging params
2025-08-07 11:36:42.161 [Debug] Merged basic params
2025-08-07 11:36:42.162 [Debug] Merged params
2025-08-07 11:36:42.162 [Debug] Merging params
2025-08-07 11:36:42.162 [Debug] Merging params
2025-08-07 11:36:42.162 [Debug] Resolving placeholders in test steps for test case: Login
2025-08-07 11:36:42.163 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-07 11:36:42.166 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-07 11:36:42.166 [Debug] Resolving placeholders in string: {{Selectors.FinishButton}}
2025-08-07 11:36:42.167 [Debug] Resolved string: id=finish
2025-08-07 11:36:42.167 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-07 11:36:42.167 [Debug] Resolved string: Click on Login Button
2025-08-07 11:36:42.167 [Debug] Resolving placeholders in string: {{Selectors.LoginButton}}
2025-08-07 11:36:42.167 [Debug] Resolved string: id=LoginBtn
2025-08-07 11:36:42.167 [Debug] Resolving placeholders in string: Type the User Name
2025-08-07 11:36:42.167 [Debug] Resolved string: Type the User Name
2025-08-07 11:36:42.167 [Debug] Resolving placeholders in string: {{Selectors.UserNameField}}
2025-08-07 11:36:42.167 [Debug] Resolved string: id=UserName
2025-08-07 11:36:42.167 [Debug] Resolving placeholders in string: {{TestData.UserName}}
2025-08-07 11:36:42.171 [Debug] Resolved string: mahmoudsayed022
2025-08-07 11:36:42.171 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-07 11:36:42.171 [Debug] Resolved string: Click on Continue Button
2025-08-07 11:36:42.171 [Debug] Resolving placeholders in string: {{Selectors.ContinueButton}}
2025-08-07 11:36:42.171 [Debug] Resolved string: id=btn
2025-08-07 11:36:42.171 [Debug] Resolving placeholders in string: Type the Password
2025-08-07 11:36:42.171 [Debug] Resolved string: Type the Password
2025-08-07 11:36:42.171 [Debug] Resolving placeholders in string: {{Selectors.PasswordField}}
2025-08-07 11:36:42.171 [Debug] Resolved string: id=Password
2025-08-07 11:36:42.171 [Debug] Resolving placeholders in string: $('[{{Selectors.PasswordField}}]').val('{{TestData.Password}}')
2025-08-07 11:36:42.171 [Debug] Resolved string: $('[id=Password]').val('Password1')
2025-08-07 11:36:42.171 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-07 11:36:42.175 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-07 11:36:42.176 [Debug] Resolving placeholders in string: {{Selectors.ChallengeQuestionField}}
2025-08-07 11:36:42.176 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-07 11:36:42.176 [Debug] Resolving placeholders in string: {{TestData.ChallengeAnswer}}
2025-08-07 11:36:42.176 [Debug] Resolved string: bmw
2025-08-07 11:36:42.176 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-07 11:36:42.176 [Debug] Resolved string: Click on the Login Button
2025-08-07 11:36:42.176 [Debug] Resolving placeholders in string: {{Selectors.LoginAuthButton}}
2025-08-07 11:36:42.176 [Debug] Resolved string: id=LoginAuthBtn
2025-08-07 11:36:42.176 [Info] Successfully resolved parameters for test case: Login
2025-08-07 11:36:42.176 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-07 11:36:42.176 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-07 11:36:42.180 [Debug] Resolving placeholders in string: id=finish
2025-08-07 11:36:42.180 [Debug] Resolved string: id=finish
2025-08-07 11:36:42.180 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-07 11:36:42.180 [Debug] Resolved string: Click on Login Button
2025-08-07 11:36:42.180 [Debug] Resolving placeholders in string: id=LoginBtn
2025-08-07 11:36:42.180 [Debug] Resolved string: id=LoginBtn
2025-08-07 11:36:42.180 [Debug] Resolving placeholders in string: Type the User Name
2025-08-07 11:36:42.180 [Debug] Resolved string: Type the User Name
2025-08-07 11:36:42.180 [Debug] Resolving placeholders in string: id=UserName
2025-08-07 11:36:42.180 [Debug] Resolved string: id=UserName
2025-08-07 11:36:42.180 [Debug] Resolving placeholders in string: mahmoudsayed022
2025-08-07 11:36:42.181 [Debug] Resolved string: mahmoudsayed022
2025-08-07 11:36:42.186 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-07 11:36:42.186 [Debug] Resolved string: Click on Continue Button
2025-08-07 11:36:42.186 [Debug] Resolving placeholders in string: id=btn
2025-08-07 11:36:42.186 [Debug] Resolved string: id=btn
2025-08-07 11:36:42.186 [Debug] Resolving placeholders in string: Type the Password
2025-08-07 11:36:42.186 [Debug] Resolved string: Type the Password
2025-08-07 11:36:42.187 [Debug] Resolving placeholders in string: id=Password
2025-08-07 11:36:42.187 [Debug] Resolved string: id=Password
2025-08-07 11:36:42.187 [Debug] Resolving placeholders in string: $('[id=Password]').val('Password1')
2025-08-07 11:36:42.187 [Debug] Resolved string: $('[id=Password]').val('Password1')
2025-08-07 11:36:42.187 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-07 11:36:42.190 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-07 11:36:42.191 [Debug] Resolving placeholders in string: id=ChallengeQuestionAnswer
2025-08-07 11:36:42.191 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-07 11:36:42.191 [Debug] Resolving placeholders in string: bmw
2025-08-07 11:36:42.191 [Debug] Resolved string: bmw
2025-08-07 11:36:42.191 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-07 11:36:42.191 [Debug] Resolved string: Click on the Login Button
2025-08-07 11:36:42.191 [Debug] Resolving placeholders in string: id=LoginAuthBtn
2025-08-07 11:36:42.191 [Debug] Resolved string: id=LoginAuthBtn
2025-08-07 11:36:42.191 [Debug] Resolving placeholders in string: Click on change later button on the password expiration popup message
2025-08-07 11:36:42.191 [Debug] Resolved string: Click on change later button on the password expiration popup message
2025-08-07 11:36:42.195 [Debug] Resolving placeholders in string: {{Selectors.popupCancel}}
2025-08-07 11:36:42.195 [Debug] Resolved string: id=popup_cancel
2025-08-07 11:36:42.195 [Debug] Resolving placeholders in string: Click on biometric alert
2025-08-07 11:36:42.195 [Debug] Resolved string: Click on biometric alert
2025-08-07 11:36:42.195 [Debug] Resolving placeholders in string: {{Selectors.BioMetricAlert}}
2025-08-07 11:36:42.195 [Debug] Resolved string: css=#popbuttons > div > button.btn.Cancel.back_gradient2
2025-08-07 11:36:42.196 [Debug] Resolving placeholders in string: Click on biometric alert
2025-08-07 11:36:42.196 [Debug] Resolved string: Click on biometric alert
2025-08-07 11:36:42.196 [Debug] Resolving placeholders in string: {{Selectors.KYCskip}}
2025-08-07 11:36:42.196 [Debug] Resolved string: {{Selectors.KYCskip}}
2025-08-07 11:36:42.196 [Debug] Resolving placeholders in string: Execute JS code to activate token
2025-08-07 11:36:42.200 [Debug] Resolved string: Execute JS code to activate token
2025-08-07 11:36:42.200 [Debug] Resolving placeholders in string: Globals.ActivationState = 'ACTIVATED';
2025-08-07 11:36:42.200 [Debug] Resolved string: Globals.ActivationState = 'ACTIVATED';
2025-08-07 11:36:42.200 [Debug] Resolving placeholders in string: Click on Beneficiaries
2025-08-07 11:36:42.200 [Debug] Resolved string: Click on Beneficiaries
2025-08-07 11:36:42.200 [Debug] Resolving placeholders in string: {{Selectors.BeneficiariesToButton}}
2025-08-07 11:36:42.200 [Debug] Resolved string: xpath=//div[@class='mainmenuitems']//label[contains(text(), 'Beneficiaries')]
2025-08-07 11:36:42.200 [Debug] Resolving placeholders in string: Click on Other CAE Accounts
2025-08-07 11:36:42.200 [Debug] Resolved string: Click on Other CAE Accounts
2025-08-07 11:36:42.200 [Debug] Resolving placeholders in string: {{Selectors.OtherCAEAccounts}}
2025-08-07 11:36:42.201 [Debug] Resolved string: xpath=//div[@class='submenuitem']//label[contains(text(), 'Other CAE Accounts')]
2025-08-07 11:36:42.204 [Debug] Resolving placeholders in string: Click on Delete button
2025-08-07 11:36:42.204 [Debug] Resolved string: Click on Delete button
2025-08-07 11:36:42.204 [Debug] Resolving placeholders in string: id=DeleteBeneficiary
2025-08-07 11:36:42.205 [Debug] Resolved string: id=DeleteBeneficiary
2025-08-07 11:36:42.205 [Debug] Resolving placeholders in string: Click on continue button and show confirmation
2025-08-07 11:36:42.205 [Debug] Resolved string: Click on continue button and show confirmation
2025-08-07 11:36:42.205 [Debug] Resolving placeholders in string: id=DigitalBankBenfContinueDeleteBtn
2025-08-07 11:36:42.205 [Debug] Resolved string: id=DigitalBankBenfContinueDeleteBtn
2025-08-07 11:36:42.205 [Info] Successfully resolved parameters for test case: Delete an Existing Account beneficiary
2025-08-07 11:36:42.205 [Debug] Loading test case: WorkSet01/TestCases/04.ATMdispute/Request Status/TC-430 Ensure that all request displayed Dispute Request.json
2025-08-07 11:36:42.205 [Info] Loading test case from: WorkSet01/TestCases/04.ATMdispute/Request Status/TC-430 Ensure that all request displayed Dispute Request.json
2025-08-07 11:36:42.211 [Info] Attempting to load configuration file: WorkSet01/TestCases/04.ATMdispute/Request Status/TC-430 Ensure that all request displayed Dispute Request.json
2025-08-07 11:36:42.221 [Info] Successfully loaded configuration file: WorkSet01/TestCases/04.ATMdispute/Request Status/TC-430 Ensure that all request displayed Dispute Request.json
2025-08-07 11:36:42.221 [Info] No filtering configuration found. Including test case ' Ensure that all request displayed -Dispute Request-'.
2025-08-07 11:36:42.221 [Debug] Resolving parameters for test case:  Ensure that all request displayed -Dispute Request-
2025-08-07 11:36:42.221 [Debug] Merging params
2025-08-07 11:36:42.221 [Debug] Merged basic params
2025-08-07 11:36:42.221 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-07 11:36:42.221 [Debug] Merged SpecialEnvParams params
2025-08-07 11:36:42.221 [Debug] Merged params
2025-08-07 11:36:42.222 [Debug] Loading parameters from reference file: WorkSet01/Params/CardServicesParams.json
2025-08-07 11:36:42.222 [Info] Attempting to load configuration file: WorkSet01/Params/CardServicesParams.json
2025-08-07 11:36:42.237 [Info] Successfully loaded configuration file: WorkSet01/Params/CardServicesParams.json
2025-08-07 11:36:42.237 [Debug] Merging params
2025-08-07 11:36:42.237 [Debug] Merged basic params
2025-08-07 11:36:42.237 [Debug] Merged params
2025-08-07 11:36:42.237 [Debug] Merging params
2025-08-07 11:36:42.237 [Debug] Merging params
2025-08-07 11:36:42.237 [Debug] Resolving placeholders in test steps for test case:  Ensure that all request displayed -Dispute Request-
2025-08-07 11:36:42.237 [Debug] Resolving TestCaseReference in step: 
2025-08-07 11:36:42.237 [Info] Loading test case from: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:36:42.238 [Info] Attempting to load configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:36:42.238 [Info] Successfully loaded configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:36:42.242 [Debug] Resolving parameters for test case: Login
2025-08-07 11:36:42.242 [Debug] Merging params
2025-08-07 11:36:42.243 [Debug] Merged basic params
2025-08-07 11:36:42.243 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-07 11:36:42.243 [Debug] Merged SpecialEnvParams params
2025-08-07 11:36:42.243 [Debug] Merged params
2025-08-07 11:36:42.243 [Debug] Loading parameters from reference file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:36:42.243 [Info] Attempting to load configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:36:42.243 [Info] Successfully loaded configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:36:42.244 [Debug] Merging params
2025-08-07 11:36:42.244 [Debug] Merged basic params
2025-08-07 11:36:42.258 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-07 11:36:42.258 [Debug] Merged SpecialEnvParams params
2025-08-07 11:36:42.258 [Debug] Merged params
2025-08-07 11:36:42.258 [Debug] Merging params
2025-08-07 11:36:42.258 [Debug] Merging params
2025-08-07 11:36:42.258 [Debug] Resolving placeholders in test steps for test case: Login
2025-08-07 11:36:42.259 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-07 11:36:42.259 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-07 11:36:42.259 [Debug] Resolving placeholders in string: {{Selectors.FinishButton}}
2025-08-07 11:36:42.259 [Debug] Resolved string: id=finish
2025-08-07 11:36:42.259 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-07 11:36:42.263 [Debug] Resolved string: Click on Login Button
2025-08-07 11:36:42.263 [Debug] Resolving placeholders in string: {{Selectors.LoginButton}}
2025-08-07 11:36:42.263 [Debug] Resolved string: id=LoginBtn
2025-08-07 11:36:42.263 [Debug] Resolving placeholders in string: Type the User Name
2025-08-07 11:36:42.263 [Debug] Resolved string: Type the User Name
2025-08-07 11:36:42.263 [Debug] Resolving placeholders in string: {{Selectors.UserNameField}}
2025-08-07 11:36:42.263 [Debug] Resolved string: id=UserName
2025-08-07 11:36:42.263 [Debug] Resolving placeholders in string: {{TestData.UserName}}
2025-08-07 11:36:42.263 [Debug] Resolved string: Mm_azmy
2025-08-07 11:36:42.264 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-07 11:36:42.264 [Debug] Resolved string: Click on Continue Button
2025-08-07 11:36:42.269 [Debug] Resolving placeholders in string: {{Selectors.ContinueButton}}
2025-08-07 11:36:42.269 [Debug] Resolved string: id=btn
2025-08-07 11:36:42.269 [Debug] Resolving placeholders in string: Type the Password
2025-08-07 11:36:42.269 [Debug] Resolved string: Type the Password
2025-08-07 11:36:42.269 [Debug] Resolving placeholders in string: {{Selectors.PasswordField}}
2025-08-07 11:36:42.269 [Debug] Resolved string: id=Password
2025-08-07 11:36:42.269 [Debug] Resolving placeholders in string: $('[{{Selectors.PasswordField}}]').val('{{TestData.Password}}')
2025-08-07 11:36:42.269 [Debug] Resolved string: $('[id=Password]').val('Asdf2025')
2025-08-07 11:36:42.269 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-07 11:36:42.269 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-07 11:36:42.274 [Debug] Resolving placeholders in string: {{Selectors.ChallengeQuestionField}}
2025-08-07 11:36:42.278 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-07 11:36:42.278 [Debug] Resolving placeholders in string: {{TestData.ChallengeAnswer}}
2025-08-07 11:36:42.278 [Debug] Resolved string: armada
2025-08-07 11:36:42.279 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-07 11:36:42.279 [Debug] Resolved string: Click on the Login Button
2025-08-07 11:36:42.279 [Debug] Resolving placeholders in string: {{Selectors.LoginAuthButton}}
2025-08-07 11:36:42.279 [Debug] Resolved string: id=LoginAuthBtn
2025-08-07 11:36:42.279 [Info] Successfully resolved parameters for test case: Login
2025-08-07 11:36:42.279 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-07 11:36:42.279 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-07 11:36:42.279 [Debug] Resolving placeholders in string: id=finish
2025-08-07 11:36:42.286 [Debug] Resolved string: id=finish
2025-08-07 11:36:42.286 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-07 11:36:42.286 [Debug] Resolved string: Click on Login Button
2025-08-07 11:36:42.286 [Debug] Resolving placeholders in string: id=LoginBtn
2025-08-07 11:36:42.286 [Debug] Resolved string: id=LoginBtn
2025-08-07 11:36:42.286 [Debug] Resolving placeholders in string: Type the User Name
2025-08-07 11:36:42.286 [Debug] Resolved string: Type the User Name
2025-08-07 11:36:42.286 [Debug] Resolving placeholders in string: id=UserName
2025-08-07 11:36:42.286 [Debug] Resolved string: id=UserName
2025-08-07 11:36:42.287 [Debug] Resolving placeholders in string: Mm_azmy
2025-08-07 11:36:42.287 [Debug] Resolved string: Mm_azmy
2025-08-07 11:36:42.290 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-07 11:36:42.291 [Debug] Resolved string: Click on Continue Button
2025-08-07 11:36:42.291 [Debug] Resolving placeholders in string: id=btn
2025-08-07 11:36:42.291 [Debug] Resolved string: id=btn
2025-08-07 11:36:42.291 [Debug] Resolving placeholders in string: Type the Password
2025-08-07 11:36:42.291 [Debug] Resolved string: Type the Password
2025-08-07 11:36:42.291 [Debug] Resolving placeholders in string: id=Password
2025-08-07 11:36:42.291 [Debug] Resolved string: id=Password
2025-08-07 11:36:42.291 [Debug] Resolving placeholders in string: $('[id=Password]').val('Asdf2025')
2025-08-07 11:36:42.291 [Debug] Resolved string: $('[id=Password]').val('Asdf2025')
2025-08-07 11:36:42.292 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-07 11:36:42.295 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-07 11:36:42.295 [Debug] Resolving placeholders in string: id=ChallengeQuestionAnswer
2025-08-07 11:36:42.295 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-07 11:36:42.296 [Debug] Resolving placeholders in string: armada
2025-08-07 11:36:42.296 [Debug] Resolved string: armada
2025-08-07 11:36:42.296 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-07 11:36:42.296 [Debug] Resolved string: Click on the Login Button
2025-08-07 11:36:42.296 [Debug] Resolving placeholders in string: id=LoginAuthBtn
2025-08-07 11:36:42.296 [Debug] Resolved string: id=LoginAuthBtn
2025-08-07 11:36:42.296 [Debug] Resolving placeholders in string: Click on Cards option on the side menu
2025-08-07 11:36:42.296 [Debug] Resolved string: Click on Cards option on the side menu
2025-08-07 11:36:42.300 [Debug] Resolving placeholders in string: {{Selectors.CardsButton}}
2025-08-07 11:36:42.300 [Debug] Resolved string: xpath=//div[@class='mainmenuitems']//label[contains(text(), 'Cards')]
2025-08-07 11:36:42.300 [Debug] Resolving placeholders in string: Click on Card Services
2025-08-07 11:36:42.300 [Debug] Resolved string: Click on Card Services
2025-08-07 11:36:42.300 [Debug] Resolving placeholders in string: {{Selectors.CardServices}}
2025-08-07 11:36:42.300 [Debug] Resolved string: xpath=//div[@class='submenuitem']//label[contains(text(), 'Card services')]
2025-08-07 11:36:42.300 [Debug] Resolving placeholders in string: Click on request status and Ensure that all request displayed
2025-08-07 11:36:42.300 [Debug] Resolved string: Click on request status and Ensure that all request displayed
2025-08-07 11:36:42.300 [Debug] Resolving placeholders in string: {{Selectors.RequestStatues}}
2025-08-07 11:36:42.300 [Debug] Resolved string: id=RequestStatus
2025-08-07 11:36:42.304 [Info] Successfully resolved parameters for test case:  Ensure that all request displayed -Dispute Request-
2025-08-07 11:36:42.304 [Debug] Loading test case: WorkSet01/TestCases/04.ATMdispute/128-ATM Dispute, click View Details.json
2025-08-07 11:36:42.304 [Info] Loading test case from: WorkSet01/TestCases/04.ATMdispute/128-ATM Dispute, click View Details.json
2025-08-07 11:36:42.304 [Info] Attempting to load configuration file: WorkSet01/TestCases/04.ATMdispute/128-ATM Dispute, click View Details.json
2025-08-07 11:36:42.315 [Info] Successfully loaded configuration file: WorkSet01/TestCases/04.ATMdispute/128-ATM Dispute, click View Details.json
2025-08-07 11:36:42.316 [Info] No filtering configuration found. Including test case 'ATM Dispute, click View Details'.
2025-08-07 11:36:42.316 [Debug] Resolving parameters for test case: ATM Dispute, click View Details
2025-08-07 11:36:42.316 [Debug] Merging params
2025-08-07 11:36:42.316 [Debug] Merged basic params
2025-08-07 11:36:42.316 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-07 11:36:42.320 [Debug] Merged SpecialEnvParams params
2025-08-07 11:36:42.320 [Debug] Merged params
2025-08-07 11:36:42.320 [Debug] Loading parameters from reference file: WorkSet01/Params/ATMdisputeParams.json
2025-08-07 11:36:42.320 [Info] Attempting to load configuration file: WorkSet01/Params/ATMdisputeParams.json
2025-08-07 11:36:42.332 [Info] Successfully loaded configuration file: WorkSet01/Params/ATMdisputeParams.json
2025-08-07 11:36:42.340 [Debug] Merging params
2025-08-07 11:36:42.340 [Debug] Merged basic params
2025-08-07 11:36:42.340 [Debug] Merged params
2025-08-07 11:36:42.340 [Debug] Merging params
2025-08-07 11:36:42.340 [Debug] Merging params
2025-08-07 11:36:42.344 [Debug] Resolving placeholders in test steps for test case: ATM Dispute, click View Details
2025-08-07 11:36:42.344 [Debug] Resolving TestCaseReference in step: 
2025-08-07 11:36:42.344 [Info] Loading test case from: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:36:42.344 [Info] Attempting to load configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:36:42.344 [Info] Successfully loaded configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:36:42.345 [Debug] Resolving parameters for test case: Login
2025-08-07 11:36:42.345 [Debug] Merging params
2025-08-07 11:36:42.345 [Debug] Merged basic params
2025-08-07 11:36:42.345 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-07 11:36:42.345 [Debug] Merged SpecialEnvParams params
2025-08-07 11:36:42.349 [Debug] Merged params
2025-08-07 11:36:42.349 [Debug] Loading parameters from reference file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:36:42.349 [Info] Attempting to load configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:36:42.350 [Info] Successfully loaded configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:36:42.350 [Debug] Merging params
2025-08-07 11:36:42.350 [Debug] Merged basic params
2025-08-07 11:36:42.350 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-07 11:36:42.350 [Debug] Merged SpecialEnvParams params
2025-08-07 11:36:42.350 [Debug] Merged params
2025-08-07 11:36:42.351 [Debug] Merging params
2025-08-07 11:36:42.354 [Debug] Merging params
2025-08-07 11:36:42.354 [Debug] Resolving placeholders in test steps for test case: Login
2025-08-07 11:36:42.354 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-07 11:36:42.354 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-07 11:36:42.354 [Debug] Resolving placeholders in string: {{Selectors.FinishButton}}
2025-08-07 11:36:42.354 [Debug] Resolved string: id=finish
2025-08-07 11:36:42.354 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-07 11:36:42.355 [Debug] Resolved string: Click on Login Button
2025-08-07 11:36:42.355 [Debug] Resolving placeholders in string: {{Selectors.LoginButton}}
2025-08-07 11:36:42.355 [Debug] Resolved string: id=LoginBtn
2025-08-07 11:36:42.359 [Debug] Resolving placeholders in string: Type the User Name
2025-08-07 11:36:42.359 [Debug] Resolved string: Type the User Name
2025-08-07 11:36:42.359 [Debug] Resolving placeholders in string: {{Selectors.UserNameField}}
2025-08-07 11:36:42.359 [Debug] Resolved string: id=UserName
2025-08-07 11:36:42.359 [Debug] Resolving placeholders in string: {{TestData.UserName}}
2025-08-07 11:36:42.359 [Debug] Resolved string: Mm_azmy
2025-08-07 11:36:42.359 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-07 11:36:42.359 [Debug] Resolved string: Click on Continue Button
2025-08-07 11:36:42.359 [Debug] Resolving placeholders in string: {{Selectors.ContinueButton}}
2025-08-07 11:36:42.360 [Debug] Resolved string: id=btn
2025-08-07 11:36:42.366 [Debug] Resolving placeholders in string: Type the Password
2025-08-07 11:36:42.366 [Debug] Resolved string: Type the Password
2025-08-07 11:36:42.366 [Debug] Resolving placeholders in string: {{Selectors.PasswordField}}
2025-08-07 11:36:42.366 [Debug] Resolved string: id=Password
2025-08-07 11:36:42.366 [Debug] Resolving placeholders in string: $('[{{Selectors.PasswordField}}]').val('{{TestData.Password}}')
2025-08-07 11:36:42.366 [Debug] Resolved string: $('[id=Password]').val('Asdf2025')
2025-08-07 11:36:42.366 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-07 11:36:42.366 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-07 11:36:42.367 [Debug] Resolving placeholders in string: {{Selectors.ChallengeQuestionField}}
2025-08-07 11:36:42.367 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-07 11:36:42.370 [Debug] Resolving placeholders in string: {{TestData.ChallengeAnswer}}
2025-08-07 11:36:42.370 [Debug] Resolved string: armada
2025-08-07 11:36:42.371 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-07 11:36:42.371 [Debug] Resolved string: Click on the Login Button
2025-08-07 11:36:42.371 [Debug] Resolving placeholders in string: {{Selectors.LoginAuthButton}}
2025-08-07 11:36:42.371 [Debug] Resolved string: id=LoginAuthBtn
2025-08-07 11:36:42.371 [Info] Successfully resolved parameters for test case: Login
2025-08-07 11:36:42.371 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-07 11:36:42.371 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-07 11:36:42.371 [Debug] Resolving placeholders in string: id=finish
2025-08-07 11:36:42.378 [Debug] Resolved string: id=finish
2025-08-07 11:36:42.378 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-07 11:36:42.378 [Debug] Resolved string: Click on Login Button
2025-08-07 11:36:42.378 [Debug] Resolving placeholders in string: id=LoginBtn
2025-08-07 11:36:42.378 [Debug] Resolved string: id=LoginBtn
2025-08-07 11:36:42.378 [Debug] Resolving placeholders in string: Type the User Name
2025-08-07 11:36:42.378 [Debug] Resolved string: Type the User Name
2025-08-07 11:36:42.378 [Debug] Resolving placeholders in string: id=UserName
2025-08-07 11:36:42.378 [Debug] Resolved string: id=UserName
2025-08-07 11:36:42.378 [Debug] Resolving placeholders in string: Mm_azmy
2025-08-07 11:36:42.382 [Debug] Resolved string: Mm_azmy
2025-08-07 11:36:42.382 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-07 11:36:42.382 [Debug] Resolved string: Click on Continue Button
2025-08-07 11:36:42.382 [Debug] Resolving placeholders in string: id=btn
2025-08-07 11:36:42.382 [Debug] Resolved string: id=btn
2025-08-07 11:36:42.382 [Debug] Resolving placeholders in string: Type the Password
2025-08-07 11:36:42.382 [Debug] Resolved string: Type the Password
2025-08-07 11:36:42.382 [Debug] Resolving placeholders in string: id=Password
2025-08-07 11:36:42.383 [Debug] Resolved string: id=Password
2025-08-07 11:36:42.383 [Debug] Resolving placeholders in string: $('[id=Password]').val('Asdf2025')
2025-08-07 11:36:42.386 [Debug] Resolved string: $('[id=Password]').val('Asdf2025')
2025-08-07 11:36:42.387 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-07 11:36:42.387 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-07 11:36:42.387 [Debug] Resolving placeholders in string: id=ChallengeQuestionAnswer
2025-08-07 11:36:42.387 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-07 11:36:42.387 [Debug] Resolving placeholders in string: armada
2025-08-07 11:36:42.387 [Debug] Resolved string: armada
2025-08-07 11:36:42.387 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-07 11:36:42.387 [Debug] Resolved string: Click on the Login Button
2025-08-07 11:36:42.387 [Debug] Resolving placeholders in string: id=LoginAuthBtn
2025-08-07 11:36:42.398 [Debug] Resolved string: id=LoginAuthBtn
2025-08-07 11:36:42.398 [Debug] Resolving placeholders in string: Click on Cards option on the side menu
2025-08-07 11:36:42.398 [Debug] Resolved string: Click on Cards option on the side menu
2025-08-07 11:36:42.398 [Debug] Resolving placeholders in string: {{Selectors.CardsButton}}
2025-08-07 11:36:42.398 [Debug] Resolved string: xpath=//div[@class='mainmenuitems']//label[contains(text(), 'Cards')]
2025-08-07 11:36:42.398 [Debug] Resolving placeholders in string: Click on Card Services
2025-08-07 11:36:42.398 [Debug] Resolved string: Click on Card Services
2025-08-07 11:36:42.398 [Debug] Resolving placeholders in string: {{Selectors.CardServices}}
2025-08-07 11:36:42.398 [Debug] Resolved string: xpath=//div[@class='submenuitem']//label[contains(text(), 'Card services')]
2025-08-07 11:36:42.399 [Debug] Resolving placeholders in string: Click on ATM dispute
2025-08-07 11:36:42.402 [Debug] Resolved string: Click on ATM dispute
2025-08-07 11:36:42.402 [Debug] Resolving placeholders in string: {{Selectors.ATMdisputeButton}}
2025-08-07 11:36:42.402 [Debug] Resolved string: id=ATMDispute
2025-08-07 11:36:42.402 [Debug] Resolving placeholders in string: Click on dispute type
2025-08-07 11:36:42.402 [Debug] Resolved string: Click on dispute type
2025-08-07 11:36:42.402 [Debug] Resolving placeholders in string: {{Selectors.SelectDisputeType}}
2025-08-07 11:36:42.402 [Debug] Resolved string: xpath=//div[@class='details_container']//span[normalize-space(text())='Select...']
2025-08-07 11:36:42.402 [Debug] Resolving placeholders in string: Choose dispute type
2025-08-07 11:36:42.403 [Debug] Resolved string: Choose dispute type
2025-08-07 11:36:42.403 [Debug] Resolving placeholders in string: {{Selectors.DisputeType}}
2025-08-07 11:36:42.407 [Debug] Resolved string: xpath=//div[@class='details_container']//span[normalize-space(text())='Withdrawal did not dispense']
2025-08-07 11:36:42.407 [Debug] Resolving placeholders in string: Click on Select Cards button
2025-08-07 11:36:42.408 [Debug] Resolved string: Click on Select Cards button
2025-08-07 11:36:42.408 [Debug] Resolving placeholders in string: {{Selectors.SelectButton}}
2025-08-07 11:36:42.408 [Debug] Resolved string: xpath=//div[@class='details_container']//button[normalize-space(text())='Select']
2025-08-07 11:36:42.408 [Debug] Resolving placeholders in string: Choose a card with ATM transactions
2025-08-07 11:36:42.408 [Debug] Resolved string: Choose a card with ATM transactions
2025-08-07 11:36:42.408 [Debug] Resolving placeholders in string: {{Selectors.CardWithTransactions}}
2025-08-07 11:36:42.408 [Debug] Resolved string: xpath=//div[@class='module_list_container cardData']//h4[normalize-space(text())='4204XXXXXXXX2562']
2025-08-07 11:36:42.408 [Debug] Resolving placeholders in string: Click on select a transaction
2025-08-07 11:36:42.412 [Debug] Resolved string: Click on select a transaction
2025-08-07 11:36:42.412 [Debug] Resolving placeholders in string: {{Selectors.SelectTransactionButton}}
2025-08-07 11:36:42.412 [Debug] Resolved string: xpath=//div[@class='details_container']//button[normalize-space(text())='Select Transaction']
2025-08-07 11:36:42.412 [Debug] Resolving placeholders in string: Choose a transaction
2025-08-07 11:36:42.412 [Debug] Resolved string: Choose a transaction
2025-08-07 11:36:42.412 [Debug] Resolving placeholders in string: {{Selectors.Transaction}}
2025-08-07 11:36:42.412 [Debug] Resolved string: css=#Transaction > div:nth-child(1) > div.list_header
2025-08-07 11:36:42.412 [Debug] Resolving placeholders in string: Click on continue
2025-08-07 11:36:42.412 [Debug] Resolved string: Click on continue
2025-08-07 11:36:42.412 [Debug] Resolving placeholders in string: {{Selectors.ContinueButtonPilot1}}
2025-08-07 11:36:42.421 [Debug] Resolved string: xpath=//div[@class='details_container']//button[normalize-space(text())='Continue']
2025-08-07 11:36:42.421 [Debug] Resolving placeholders in string: Enter the disputed amount
2025-08-07 11:36:42.422 [Debug] Resolved string: Enter the disputed amount
2025-08-07 11:36:42.425 [Debug] Resolving placeholders in string: {{Selectors.DisputedAmmountField}}
2025-08-07 11:36:42.425 [Debug] Resolved string: xpath=//div[@class='details_container']//input[normalize-space(@placeholder)='Enter Disputed amount']
2025-08-07 11:36:42.425 [Debug] Resolving placeholders in string: 0
2025-08-07 11:36:42.425 [Debug] Resolved string: 0
2025-08-07 11:36:42.425 [Debug] Resolving placeholders in string: Enter the dispute note
2025-08-07 11:36:42.426 [Debug] Resolved string: Enter the dispute note
2025-08-07 11:36:42.426 [Debug] Resolving placeholders in string: {{Selectors.DisputeNoteField}}
2025-08-07 11:36:42.431 [Debug] Resolved string: xpath=//div[@class='details_container']//textarea[normalize-space(@placeholder)='']
2025-08-07 11:36:42.431 [Debug] Resolving placeholders in string: This is a test for the feature by the dev team. Please ignore this request.
2025-08-07 11:36:42.431 [Debug] Resolved string: This is a test for the feature by the dev team. Please ignore this request.
2025-08-07 11:36:42.431 [Debug] Resolving placeholders in string: Click on the agree terms checkbox
2025-08-07 11:36:42.431 [Debug] Resolved string: Click on the agree terms checkbox
2025-08-07 11:36:42.431 [Debug] Resolving placeholders in string: {{Selectors.TermsCheckbox}}
2025-08-07 11:36:42.432 [Debug] Resolved string: xpath=//div[@class='details_container']//input[@type='checkbox']
2025-08-07 11:36:42.432 [Debug] Resolving placeholders in string: Click on continue button
2025-08-07 11:36:42.432 [Debug] Resolved string: Click on continue button
2025-08-07 11:36:42.432 [Debug] Resolving placeholders in string: {{Selectors.ContinueButtonPilot2}}
2025-08-07 11:36:42.440 [Debug] Resolved string: xpath=//div[@class='details_container']//button[normalize-space(text())='Confirm']
2025-08-07 11:36:42.440 [Debug] Resolving placeholders in string: Click on confirm button
2025-08-07 11:36:42.440 [Debug] Resolved string: Click on confirm button
2025-08-07 11:36:42.441 [Debug] Resolving placeholders in string: {{Selectors.ConfirmDispute}}
2025-08-07 11:36:42.441 [Debug] Resolved string: id=DisputePreConfirmationBtn
2025-08-07 11:36:42.441 [Debug] Resolving placeholders in string: Click on Request Details
2025-08-07 11:36:42.441 [Debug] Resolved string: Click on Request Details
2025-08-07 11:36:42.441 [Debug] Resolving placeholders in string: {{Selectors.SubmitProdRequestDetailsBtn}}
2025-08-07 11:36:42.441 [Debug] Resolved string: id=SubmitProdRequestDetailsBtn
2025-08-07 11:36:42.441 [Info] Successfully resolved parameters for test case: ATM Dispute, click View Details
2025-08-07 11:36:42.445 [Debug] Loading test case: WorkSet01/TestCases/012.CardServices/DirectDebit/130-DirectDebitRequest.json
2025-08-07 11:36:42.445 [Info] Loading test case from: WorkSet01/TestCases/012.CardServices/DirectDebit/130-DirectDebitRequest.json
2025-08-07 11:36:42.445 [Info] Attempting to load configuration file: WorkSet01/TestCases/012.CardServices/DirectDebit/130-DirectDebitRequest.json
2025-08-07 11:36:42.457 [Info] Successfully loaded configuration file: WorkSet01/TestCases/012.CardServices/DirectDebit/130-DirectDebitRequest.json
2025-08-07 11:36:42.461 [Info] No filtering configuration found. Including test case 'Direct Debit, standard users'.
2025-08-07 11:36:42.461 [Debug] Resolving parameters for test case: Direct Debit, standard users
2025-08-07 11:36:42.461 [Debug] Merging params
2025-08-07 11:36:42.461 [Debug] Merged basic params
2025-08-07 11:36:42.462 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-07 11:36:42.462 [Debug] Merged SpecialEnvParams params
2025-08-07 11:36:42.466 [Debug] Merged params
2025-08-07 11:36:42.466 [Debug] Loading parameters from reference file: WorkSet01/Params/BlockCardParams.json
2025-08-07 11:36:42.466 [Info] Attempting to load configuration file: WorkSet01/Params/BlockCardParams.json
2025-08-07 11:36:42.472 [Info] Successfully loaded configuration file: WorkSet01/Params/BlockCardParams.json
2025-08-07 11:36:42.472 [Debug] Merging params
2025-08-07 11:36:42.472 [Debug] Merged basic params
2025-08-07 11:36:42.472 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-07 11:36:42.473 [Debug] Merged SpecialEnvParams params
2025-08-07 11:36:42.473 [Debug] Merged params
2025-08-07 11:36:42.477 [Debug] Merging params
2025-08-07 11:36:42.478 [Debug] Merging params
2025-08-07 11:36:42.478 [Debug] Resolving placeholders in test steps for test case: Direct Debit, standard users
2025-08-07 11:36:42.478 [Debug] Resolving TestCaseReference in step: 
2025-08-07 11:36:42.478 [Info] Loading test case from: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:36:42.478 [Info] Attempting to load configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:36:42.478 [Info] Successfully loaded configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:36:42.479 [Debug] Resolving parameters for test case: Login
2025-08-07 11:36:42.479 [Debug] Merging params
2025-08-07 11:36:42.482 [Debug] Merged basic params
2025-08-07 11:36:42.482 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-07 11:36:42.482 [Debug] Merged SpecialEnvParams params
2025-08-07 11:36:42.482 [Debug] Merged params
2025-08-07 11:36:42.483 [Debug] Loading parameters from reference file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:36:42.483 [Info] Attempting to load configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:36:42.483 [Info] Successfully loaded configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:36:42.483 [Debug] Merging params
2025-08-07 11:36:42.484 [Debug] Merged basic params
2025-08-07 11:36:42.487 [Debug] Merged params
2025-08-07 11:36:42.487 [Debug] Merging params
2025-08-07 11:36:42.487 [Debug] Merging params
2025-08-07 11:36:42.487 [Debug] Resolving placeholders in test steps for test case: Login
2025-08-07 11:36:42.488 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-07 11:36:42.488 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-07 11:36:42.488 [Debug] Resolving placeholders in string: {{Selectors.FinishButton}}
2025-08-07 11:36:42.488 [Debug] Resolved string: id=finish
2025-08-07 11:36:42.488 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-07 11:36:42.493 [Debug] Resolved string: Click on Login Button
2025-08-07 11:36:42.493 [Debug] Resolving placeholders in string: {{Selectors.LoginButton}}
2025-08-07 11:36:42.493 [Debug] Resolved string: id=LoginBtn
2025-08-07 11:36:42.493 [Debug] Resolving placeholders in string: Type the User Name
2025-08-07 11:36:42.493 [Debug] Resolved string: Type the User Name
2025-08-07 11:36:42.493 [Debug] Resolving placeholders in string: {{Selectors.UserNameField}}
2025-08-07 11:36:42.493 [Debug] Resolved string: id=UserName
2025-08-07 11:36:42.493 [Debug] Resolving placeholders in string: {{TestData.UserName}}
2025-08-07 11:36:42.494 [Debug] Resolved string: mahmoudsayed022
2025-08-07 11:36:42.500 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-07 11:36:42.500 [Debug] Resolved string: Click on Continue Button
2025-08-07 11:36:42.500 [Debug] Resolving placeholders in string: {{Selectors.ContinueButton}}
2025-08-07 11:36:42.500 [Debug] Resolved string: id=btn
2025-08-07 11:36:42.500 [Debug] Resolving placeholders in string: Type the Password
2025-08-07 11:36:42.500 [Debug] Resolved string: Type the Password
2025-08-07 11:36:42.500 [Debug] Resolving placeholders in string: {{Selectors.PasswordField}}
2025-08-07 11:36:42.500 [Debug] Resolved string: id=Password
2025-08-07 11:36:42.501 [Debug] Resolving placeholders in string: $('[{{Selectors.PasswordField}}]').val('{{TestData.Password}}')
2025-08-07 11:36:42.505 [Debug] Resolved string: $('[id=Password]').val('Password1')
2025-08-07 11:36:42.505 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-07 11:36:42.505 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-07 11:36:42.505 [Debug] Resolving placeholders in string: {{Selectors.ChallengeQuestionField}}
2025-08-07 11:36:42.505 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-07 11:36:42.505 [Debug] Resolving placeholders in string: {{TestData.ChallengeAnswer}}
2025-08-07 11:36:42.505 [Debug] Resolved string: bmw
2025-08-07 11:36:42.506 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-07 11:36:42.506 [Debug] Resolved string: Click on the Login Button
2025-08-07 11:36:42.510 [Debug] Resolving placeholders in string: {{Selectors.LoginAuthButton}}
2025-08-07 11:36:42.510 [Debug] Resolved string: id=LoginAuthBtn
2025-08-07 11:36:42.510 [Info] Successfully resolved parameters for test case: Login
2025-08-07 11:36:42.510 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-07 11:36:42.510 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-07 11:36:42.510 [Debug] Resolving placeholders in string: id=finish
2025-08-07 11:36:42.510 [Debug] Resolved string: id=finish
2025-08-07 11:36:42.510 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-07 11:36:42.510 [Debug] Resolved string: Click on Login Button
2025-08-07 11:36:42.517 [Debug] Resolving placeholders in string: id=LoginBtn
2025-08-07 11:36:42.517 [Debug] Resolved string: id=LoginBtn
2025-08-07 11:36:42.517 [Debug] Resolving placeholders in string: Type the User Name
2025-08-07 11:36:42.517 [Debug] Resolved string: Type the User Name
2025-08-07 11:36:42.517 [Debug] Resolving placeholders in string: id=UserName
2025-08-07 11:36:42.517 [Debug] Resolved string: id=UserName
2025-08-07 11:36:42.517 [Debug] Resolving placeholders in string: mahmoudsayed022
2025-08-07 11:36:42.517 [Debug] Resolved string: mahmoudsayed022
2025-08-07 11:36:42.518 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-07 11:36:42.521 [Debug] Resolved string: Click on Continue Button
2025-08-07 11:36:42.521 [Debug] Resolving placeholders in string: id=btn
2025-08-07 11:36:42.521 [Debug] Resolved string: id=btn
2025-08-07 11:36:42.521 [Debug] Resolving placeholders in string: Type the Password
2025-08-07 11:36:42.521 [Debug] Resolved string: Type the Password
2025-08-07 11:36:42.521 [Debug] Resolving placeholders in string: id=Password
2025-08-07 11:36:42.521 [Debug] Resolved string: id=Password
2025-08-07 11:36:42.522 [Debug] Resolving placeholders in string: $('[id=Password]').val('Password1')
2025-08-07 11:36:42.522 [Debug] Resolved string: $('[id=Password]').val('Password1')
2025-08-07 11:36:42.525 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-07 11:36:42.525 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-07 11:36:42.525 [Debug] Resolving placeholders in string: id=ChallengeQuestionAnswer
2025-08-07 11:36:42.525 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-07 11:36:42.525 [Debug] Resolving placeholders in string: bmw
2025-08-07 11:36:42.525 [Debug] Resolved string: bmw
2025-08-07 11:36:42.526 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-07 11:36:42.526 [Debug] Resolved string: Click on the Login Button
2025-08-07 11:36:42.526 [Debug] Resolving placeholders in string: id=LoginAuthBtn
2025-08-07 11:36:42.529 [Debug] Resolved string: id=LoginAuthBtn
2025-08-07 11:36:42.529 [Debug] Resolving placeholders in string: Click on change later button on the password expiration popup message
2025-08-07 11:36:42.529 [Debug] Resolved string: Click on change later button on the password expiration popup message
2025-08-07 11:36:42.529 [Debug] Resolving placeholders in string: {{Selectors.popupCancel}}
2025-08-07 11:36:42.529 [Debug] Resolved string: id=popup_cancel
2025-08-07 11:36:42.529 [Debug] Resolving placeholders in string: Click on biometric alert
2025-08-07 11:36:42.529 [Debug] Resolved string: Click on biometric alert
2025-08-07 11:36:42.529 [Debug] Resolving placeholders in string: {{Selectors.BioMetricAlert}}
2025-08-07 11:36:42.530 [Debug] Resolved string: css=#popbuttons > div > button.btn.Cancel.back_gradient2
2025-08-07 11:36:42.533 [Debug] Resolving placeholders in string: Execute JS code to activate token
2025-08-07 11:36:42.533 [Debug] Resolved string: Execute JS code to activate token
2025-08-07 11:36:42.534 [Debug] Resolving placeholders in string: Globals.ActivationState = 'ACTIVATED';
2025-08-07 11:36:42.534 [Debug] Resolved string: Globals.ActivationState = 'ACTIVATED';
2025-08-07 11:36:42.534 [Debug] Resolving placeholders in string: Click on Cards button from side menu
2025-08-07 11:36:42.534 [Debug] Resolved string: Click on Cards button from side menu
2025-08-07 11:36:42.534 [Debug] Resolving placeholders in string: {{Selectors.CardsButton}}
2025-08-07 11:36:42.534 [Debug] Resolved string: xpath=//div[@class='mainmenuitems']//label[contains(text(), 'Cards')]
2025-08-07 11:36:42.535 [Debug] Resolving placeholders in string: Click on Cards Services button from side menu
2025-08-07 11:36:42.538 [Debug] Resolved string: Click on Cards Services button from side menu
2025-08-07 11:36:42.538 [Debug] Resolving placeholders in string: {{Selectors.CardServices}}
2025-08-07 11:36:42.538 [Debug] Resolved string: xpath=//div[@class='submenuitem']//label[contains(text(), 'Card services')]
2025-08-07 11:36:42.538 [Debug] Resolving placeholders in string: Click on Direct Debit
2025-08-07 11:36:42.538 [Debug] Resolved string: Click on Direct Debit
2025-08-07 11:36:42.538 [Debug] Resolving placeholders in string: {{Selectors.DirectDebitBtn}}
2025-08-07 11:36:42.539 [Debug] Resolved string: id=DirectDebit
2025-08-07 11:36:42.539 [Debug] Resolving placeholders in string: select card 
2025-08-07 11:36:42.539 [Debug] Resolved string: select card 
2025-08-07 11:36:42.542 [Debug] Resolving placeholders in string: {{Selectors.CardSelectedinDirectDebit}}
2025-08-07 11:36:42.542 [Debug] Resolved string: xpath=//div[@class='module_list_container cardData']/*[1]
2025-08-07 11:36:42.542 [Debug] Resolving placeholders in string: click account button 
2025-08-07 11:36:42.542 [Debug] Resolved string: click account button 
2025-08-07 11:36:42.543 [Debug] Resolving placeholders in string: {{Selectors.SelectAccountBtn}}
2025-08-07 11:36:42.543 [Debug] Resolved string: id=SelectAccountBtn
2025-08-07 11:36:42.543 [Debug] Resolving placeholders in string: select account
2025-08-07 11:36:42.543 [Debug] Resolved string: select account
2025-08-07 11:36:42.543 [Debug] Resolving placeholders in string: {{Selectors.DebitAccount}}
2025-08-07 11:36:42.547 [Debug] Resolved string: xpath=//div[@class='module_list_container']//h4[contains(text(), '**************')]
2025-08-07 11:36:42.547 [Debug] Resolving placeholders in string: click Debited Percentage select
2025-08-07 11:36:42.547 [Debug] Resolved string: click Debited Percentage select
2025-08-07 11:36:42.547 [Debug] Resolving placeholders in string: {{Selectors.DebitedPercentageSelect}}
2025-08-07 11:36:42.547 [Debug] Resolved string: css=#CardsSelectionErrorHandle > div.select
2025-08-07 11:36:42.547 [Debug] Resolving placeholders in string: click Debited Percentage select option
2025-08-07 11:36:42.547 [Debug] Resolved string: click Debited Percentage select option
2025-08-07 11:36:42.548 [Debug] Resolving placeholders in string: {{Selectors.DebitedPercentageSelectOption}}
2025-08-07 11:36:42.548 [Debug] Resolved string: css=#CardsSelectionErrorHandle > div.options.back_white_solid.dropdown-active.d-block > span:nth-child(1)
2025-08-07 11:36:42.551 [Debug] Resolving placeholders in string: click ageree checkbox
2025-08-07 11:36:42.551 [Debug] Resolved string: click ageree checkbox
2025-08-07 11:36:42.551 [Debug] Resolving placeholders in string: {{Selectors.AgreeCheckBoxinDebit}}
2025-08-07 11:36:42.551 [Debug] Resolved string: css=#wginsDirectDebit_Default > div > div.section_body > div > div > div.terms.AgreeTermsAndConditions > input[type=checkbox]
2025-08-07 11:36:42.552 [Debug] Resolving placeholders in string: click on continue button
2025-08-07 11:36:42.552 [Debug] Resolved string: click on continue button
2025-08-07 11:36:42.552 [Debug] Resolving placeholders in string: {{Selectors.SubmitDirectDebitReq}}
2025-08-07 11:36:42.552 [Debug] Resolved string: id=SubmitDirectDebitReq
2025-08-07 11:36:42.552 [Debug] Resolving placeholders in string: click on confirm button
2025-08-07 11:36:42.555 [Debug] Resolved string: click on confirm button
2025-08-07 11:36:42.555 [Debug] Resolving placeholders in string: {{Selectors.DirectDebitConfirm}}
2025-08-07 11:36:42.555 [Debug] Resolved string: id=DirectDebitConfirm
2025-08-07 11:36:42.555 [Debug] Resolving placeholders in string: Enter Token number
2025-08-07 11:36:42.555 [Debug] Resolved string: Enter Token number
2025-08-07 11:36:42.555 [Debug] Resolving placeholders in string: {{Selectors.TokenInput}}
2025-08-07 11:36:42.556 [Debug] Resolved string: id=TokenNUMBER
2025-08-07 11:36:42.556 [Debug] Resolving placeholders in string: 123456
2025-08-07 11:36:42.556 [Debug] Resolved string: 123456
2025-08-07 11:36:42.559 [Debug] Resolving placeholders in string: Click on confirm and show the confirmation page
2025-08-07 11:36:42.559 [Debug] Resolved string: Click on confirm and show the confirmation page
2025-08-07 11:36:42.559 [Debug] Resolving placeholders in string: {{Selectors.btnTokenConfirm}}
2025-08-07 11:36:42.559 [Debug] Resolved string: id=btnTokenConfirm
2025-08-07 11:36:42.559 [Info] Successfully resolved parameters for test case: Direct Debit, standard users
2025-08-07 11:36:42.559 [Debug] Loading test case: WorkSet01/TestCases/012.CardServices/ActivateCard/121-Activate Card, check that inactive card in backend are displayed.json
2025-08-07 11:36:42.559 [Info] Loading test case from: WorkSet01/TestCases/012.CardServices/ActivateCard/121-Activate Card, check that inactive card in backend are displayed.json
2025-08-07 11:36:42.560 [Info] Attempting to load configuration file: WorkSet01/TestCases/012.CardServices/ActivateCard/121-Activate Card, check that inactive card in backend are displayed.json
2025-08-07 11:36:42.565 [Info] Successfully loaded configuration file: WorkSet01/TestCases/012.CardServices/ActivateCard/121-Activate Card, check that inactive card in backend are displayed.json
2025-08-07 11:36:42.568 [Info] No filtering configuration found. Including test case 'Activate Card, check that inactive card in backend are displayed'.
2025-08-07 11:36:42.568 [Debug] Resolving parameters for test case: Activate Card, check that inactive card in backend are displayed
2025-08-07 11:36:42.568 [Debug] Merging params
2025-08-07 11:36:42.568 [Debug] Merged basic params
2025-08-07 11:36:42.568 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-07 11:36:42.568 [Debug] Merged SpecialEnvParams params
2025-08-07 11:36:42.568 [Debug] Merged params
2025-08-07 11:36:42.568 [Debug] Loading parameters from reference file: WorkSet01/Params/ActivateCardParm.json
2025-08-07 11:36:42.568 [Info] Attempting to load configuration file: WorkSet01/Params/ActivateCardParm.json
2025-08-07 11:36:42.582 [Info] Successfully loaded configuration file: WorkSet01/Params/ActivateCardParm.json
2025-08-07 11:36:42.582 [Debug] Merging params
2025-08-07 11:36:42.582 [Debug] Merged basic params
2025-08-07 11:36:42.582 [Debug] Merged params
2025-08-07 11:36:42.582 [Debug] Merging params
2025-08-07 11:36:42.582 [Debug] Merging params
2025-08-07 11:36:42.582 [Debug] Resolving placeholders in test steps for test case: Activate Card, check that inactive card in backend are displayed
2025-08-07 11:36:42.583 [Debug] Resolving TestCaseReference in step: 
2025-08-07 11:36:42.583 [Info] Loading test case from: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:36:42.586 [Info] Attempting to load configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:36:42.586 [Info] Successfully loaded configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:36:42.586 [Debug] Resolving parameters for test case: Login
2025-08-07 11:36:42.586 [Debug] Merging params
2025-08-07 11:36:42.586 [Debug] Merged basic params
2025-08-07 11:36:42.586 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-07 11:36:42.586 [Debug] Merged SpecialEnvParams params
2025-08-07 11:36:42.586 [Debug] Merged params
2025-08-07 11:36:42.586 [Debug] Loading parameters from reference file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:36:42.589 [Info] Attempting to load configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:36:42.590 [Info] Successfully loaded configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:36:42.590 [Debug] Merging params
2025-08-07 11:36:42.590 [Debug] Merged basic params
2025-08-07 11:36:42.590 [Debug] Merged params
2025-08-07 11:36:42.590 [Debug] Merging params
2025-08-07 11:36:42.590 [Debug] Merging params
2025-08-07 11:36:42.590 [Debug] Resolving placeholders in test steps for test case: Login
2025-08-07 11:36:42.590 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-07 11:36:42.595 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-07 11:36:42.595 [Debug] Resolving placeholders in string: {{Selectors.FinishButton}}
2025-08-07 11:36:42.595 [Debug] Resolved string: id=finish
2025-08-07 11:36:42.595 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-07 11:36:42.595 [Debug] Resolved string: Click on Login Button
2025-08-07 11:36:42.595 [Debug] Resolving placeholders in string: {{Selectors.LoginButton}}
2025-08-07 11:36:42.595 [Debug] Resolved string: id=LoginBtn
2025-08-07 11:36:42.596 [Debug] Resolving placeholders in string: Type the User Name
2025-08-07 11:36:42.596 [Debug] Resolved string: Type the User Name
2025-08-07 11:36:42.602 [Debug] Resolving placeholders in string: {{Selectors.UserNameField}}
2025-08-07 11:36:42.602 [Debug] Resolved string: id=UserName
2025-08-07 11:36:42.602 [Debug] Resolving placeholders in string: {{TestData.UserName}}
2025-08-07 11:36:42.602 [Debug] Resolved string: mahmoudsayed022
2025-08-07 11:36:42.602 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-07 11:36:42.602 [Debug] Resolved string: Click on Continue Button
2025-08-07 11:36:42.602 [Debug] Resolving placeholders in string: {{Selectors.ContinueButton}}
2025-08-07 11:36:42.603 [Debug] Resolved string: id=btn
2025-08-07 11:36:42.603 [Debug] Resolving placeholders in string: Type the Password
2025-08-07 11:36:42.608 [Debug] Resolved string: Type the Password
2025-08-07 11:36:42.608 [Debug] Resolving placeholders in string: {{Selectors.PasswordField}}
2025-08-07 11:36:42.608 [Debug] Resolved string: id=Password
2025-08-07 11:36:42.608 [Debug] Resolving placeholders in string: $('[{{Selectors.PasswordField}}]').val('{{TestData.Password}}')
2025-08-07 11:36:42.608 [Debug] Resolved string: $('[id=Password]').val('Password1')
2025-08-07 11:36:42.608 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-07 11:36:42.608 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-07 11:36:42.608 [Debug] Resolving placeholders in string: {{Selectors.ChallengeQuestionField}}
2025-08-07 11:36:42.608 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-07 11:36:42.612 [Debug] Resolving placeholders in string: {{TestData.ChallengeAnswer}}
2025-08-07 11:36:42.612 [Debug] Resolved string: bmw
2025-08-07 11:36:42.612 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-07 11:36:42.612 [Debug] Resolved string: Click on the Login Button
2025-08-07 11:36:42.612 [Debug] Resolving placeholders in string: {{Selectors.LoginAuthButton}}
2025-08-07 11:36:42.613 [Debug] Resolved string: id=LoginAuthBtn
2025-08-07 11:36:42.613 [Info] Successfully resolved parameters for test case: Login
2025-08-07 11:36:42.613 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-07 11:36:42.613 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-07 11:36:42.619 [Debug] Resolving placeholders in string: id=finish
2025-08-07 11:36:42.619 [Debug] Resolved string: id=finish
2025-08-07 11:36:42.619 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-07 11:36:42.619 [Debug] Resolved string: Click on Login Button
2025-08-07 11:36:42.619 [Debug] Resolving placeholders in string: id=LoginBtn
2025-08-07 11:36:42.619 [Debug] Resolved string: id=LoginBtn
2025-08-07 11:36:42.619 [Debug] Resolving placeholders in string: Type the User Name
2025-08-07 11:36:42.619 [Debug] Resolved string: Type the User Name
2025-08-07 11:36:42.619 [Debug] Resolving placeholders in string: id=UserName
2025-08-07 11:36:42.624 [Debug] Resolved string: id=UserName
2025-08-07 11:36:42.624 [Debug] Resolving placeholders in string: mahmoudsayed022
2025-08-07 11:36:42.624 [Debug] Resolved string: mahmoudsayed022
2025-08-07 11:36:42.624 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-07 11:36:42.624 [Debug] Resolved string: Click on Continue Button
2025-08-07 11:36:42.624 [Debug] Resolving placeholders in string: id=btn
2025-08-07 11:36:42.625 [Debug] Resolved string: id=btn
2025-08-07 11:36:42.625 [Debug] Resolving placeholders in string: Type the Password
2025-08-07 11:36:42.625 [Debug] Resolved string: Type the Password
2025-08-07 11:36:42.628 [Debug] Resolving placeholders in string: id=Password
2025-08-07 11:36:42.628 [Debug] Resolved string: id=Password
2025-08-07 11:36:42.628 [Debug] Resolving placeholders in string: $('[id=Password]').val('Password1')
2025-08-07 11:36:42.628 [Debug] Resolved string: $('[id=Password]').val('Password1')
2025-08-07 11:36:42.628 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-07 11:36:42.628 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-07 11:36:42.628 [Debug] Resolving placeholders in string: id=ChallengeQuestionAnswer
2025-08-07 11:36:42.628 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-07 11:36:42.634 [Debug] Resolving placeholders in string: bmw
2025-08-07 11:36:42.634 [Debug] Resolved string: bmw
2025-08-07 11:36:42.635 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-07 11:36:42.635 [Debug] Resolved string: Click on the Login Button
2025-08-07 11:36:42.635 [Debug] Resolving placeholders in string: id=LoginAuthBtn
2025-08-07 11:36:42.635 [Debug] Resolved string: id=LoginAuthBtn
2025-08-07 11:36:42.635 [Debug] Resolving placeholders in string: Click on change later button on the password expiration popup message
2025-08-07 11:36:42.636 [Debug] Resolved string: Click on change later button on the password expiration popup message
2025-08-07 11:36:42.656 [Debug] Resolving placeholders in string: {{Selectors.popupCancel}}
2025-08-07 11:36:42.656 [Debug] Resolved string: id=popup_cancel
2025-08-07 11:36:42.657 [Debug] Resolving placeholders in string: Click on biometric alert
2025-08-07 11:36:42.657 [Debug] Resolved string: Click on biometric alert
2025-08-07 11:36:42.657 [Debug] Resolving placeholders in string: {{Selectors.BioMetricAlert}}
2025-08-07 11:36:42.657 [Debug] Resolved string: css=#popbuttons > div > button.btn.Cancel.back_gradient2
2025-08-07 11:36:42.657 [Debug] Resolving placeholders in string: Click on Cards button from side menu
2025-08-07 11:36:42.657 [Debug] Resolved string: Click on Cards button from side menu
2025-08-07 11:36:42.663 [Debug] Resolving placeholders in string: {{Selectors.CardsButton}}
2025-08-07 11:36:42.663 [Debug] Resolved string: xpath=//div[@class='mainmenuitems']//label[contains(text(), 'Cards')]
2025-08-07 11:36:42.663 [Debug] Resolving placeholders in string: Click on Cards Services button from side menu
2025-08-07 11:36:42.663 [Debug] Resolved string: Click on Cards Services button from side menu
2025-08-07 11:36:42.663 [Debug] Resolving placeholders in string: {{Selectors.CardServices}}
2025-08-07 11:36:42.663 [Debug] Resolved string: xpath=//div[@class='submenuitem']//label[contains(text(), 'Card services')]
2025-08-07 11:36:42.664 [Debug] Resolving placeholders in string: Click on Activate Card and show the cards list
2025-08-07 11:36:42.664 [Debug] Resolved string: Click on Activate Card and show the cards list
2025-08-07 11:36:42.683 [Debug] Resolving placeholders in string: {{Selectors.ActivateCard}}
2025-08-07 11:36:42.684 [Debug] Resolved string: id=ActivateCard
2025-08-07 11:36:42.684 [Debug] Resolving placeholders in string: Check if the cards list appear
2025-08-07 11:36:42.684 [Debug] Resolved string: Check if the cards list appear
2025-08-07 11:36:42.684 [Debug] Resolving placeholders in string: {{Selectors.CardList}}
2025-08-07 11:36:42.684 [Debug] Resolved string: css=#CardsSelectionErrorHandle
2025-08-07 11:36:42.684 [Info] Successfully resolved parameters for test case: Activate Card, check that inactive card in backend are displayed
2025-08-07 11:36:42.684 [Debug] Loading test case: WorkSet01/TestCases/012.CardServices/BlockCard/120-ClickBlockCardInquiry.json
2025-08-07 11:36:42.689 [Info] Loading test case from: WorkSet01/TestCases/012.CardServices/BlockCard/120-ClickBlockCardInquiry.json
2025-08-07 11:36:42.689 [Info] Attempting to load configuration file: WorkSet01/TestCases/012.CardServices/BlockCard/120-ClickBlockCardInquiry.json
2025-08-07 11:36:42.707 [Info] Successfully loaded configuration file: WorkSet01/TestCases/012.CardServices/BlockCard/120-ClickBlockCardInquiry.json
2025-08-07 11:36:42.707 [Info] No filtering configuration found. Including test case 'Click Block card'.
2025-08-07 11:36:42.708 [Debug] Resolving parameters for test case: Click Block card
2025-08-07 11:36:42.708 [Debug] Merging params
2025-08-07 11:36:42.708 [Debug] Merged basic params
2025-08-07 11:36:42.708 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-07 11:36:42.713 [Debug] Merged SpecialEnvParams params
2025-08-07 11:36:42.713 [Debug] Merged params
2025-08-07 11:36:42.713 [Debug] Loading parameters from reference file: WorkSet01/Params/BlockCardParams.json
2025-08-07 11:36:42.713 [Info] Attempting to load configuration file: WorkSet01/Params/BlockCardParams.json
2025-08-07 11:36:42.714 [Info] Successfully loaded configuration file: WorkSet01/Params/BlockCardParams.json
2025-08-07 11:36:42.714 [Debug] Merging params
2025-08-07 11:36:42.714 [Debug] Merged basic params
2025-08-07 11:36:42.714 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-07 11:36:42.721 [Debug] Merged SpecialEnvParams params
2025-08-07 11:36:42.721 [Debug] Merged params
2025-08-07 11:36:42.721 [Debug] Merging params
2025-08-07 11:36:42.721 [Debug] Merging params
2025-08-07 11:36:42.721 [Debug] Resolving placeholders in test steps for test case: Click Block card
2025-08-07 11:36:42.722 [Debug] Resolving TestCaseReference in step: 
2025-08-07 11:36:42.722 [Info] Loading test case from: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:36:42.728 [Info] Attempting to load configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:36:42.733 [Info] Successfully loaded configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:36:42.733 [Debug] Resolving parameters for test case: Login
2025-08-07 11:36:42.733 [Debug] Merging params
2025-08-07 11:36:42.734 [Debug] Merged basic params
2025-08-07 11:36:42.734 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-07 11:36:42.734 [Debug] Merged SpecialEnvParams params
2025-08-07 11:36:42.734 [Debug] Merged params
2025-08-07 11:36:42.734 [Debug] Loading parameters from reference file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:36:42.740 [Info] Attempting to load configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:36:42.740 [Info] Successfully loaded configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:36:42.740 [Debug] Merging params
2025-08-07 11:36:42.741 [Debug] Merged basic params
2025-08-07 11:36:42.741 [Debug] Merged params
2025-08-07 11:36:42.741 [Debug] Merging params
2025-08-07 11:36:42.741 [Debug] Merging params
2025-08-07 11:36:42.741 [Debug] Resolving placeholders in test steps for test case: Login
2025-08-07 11:36:42.745 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-07 11:36:42.745 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-07 11:36:42.745 [Debug] Resolving placeholders in string: {{Selectors.FinishButton}}
2025-08-07 11:36:42.745 [Debug] Resolved string: id=finish
2025-08-07 11:36:42.746 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-07 11:36:42.746 [Debug] Resolved string: Click on Login Button
2025-08-07 11:36:42.746 [Debug] Resolving placeholders in string: {{Selectors.LoginButton}}
2025-08-07 11:36:42.746 [Debug] Resolved string: id=LoginBtn
2025-08-07 11:36:42.751 [Debug] Resolving placeholders in string: Type the User Name
2025-08-07 11:36:42.751 [Debug] Resolved string: Type the User Name
2025-08-07 11:36:42.751 [Debug] Resolving placeholders in string: {{Selectors.UserNameField}}
2025-08-07 11:36:42.751 [Debug] Resolved string: id=UserName
2025-08-07 11:36:42.751 [Debug] Resolving placeholders in string: {{TestData.UserName}}
2025-08-07 11:36:42.751 [Debug] Resolved string: mahmoudsayed022
2025-08-07 11:36:42.752 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-07 11:36:42.752 [Debug] Resolved string: Click on Continue Button
2025-08-07 11:36:42.757 [Debug] Resolving placeholders in string: {{Selectors.ContinueButton}}
2025-08-07 11:36:42.757 [Debug] Resolved string: id=btn
2025-08-07 11:36:42.757 [Debug] Resolving placeholders in string: Type the Password
2025-08-07 11:36:42.757 [Debug] Resolved string: Type the Password
2025-08-07 11:36:42.757 [Debug] Resolving placeholders in string: {{Selectors.PasswordField}}
2025-08-07 11:36:42.757 [Debug] Resolved string: id=Password
2025-08-07 11:36:42.757 [Debug] Resolving placeholders in string: $('[{{Selectors.PasswordField}}]').val('{{TestData.Password}}')
2025-08-07 11:36:42.757 [Debug] Resolved string: $('[id=Password]').val('Password1')
2025-08-07 11:36:42.761 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-07 11:36:42.761 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-07 11:36:42.761 [Debug] Resolving placeholders in string: {{Selectors.ChallengeQuestionField}}
2025-08-07 11:36:42.761 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-07 11:36:42.761 [Debug] Resolving placeholders in string: {{TestData.ChallengeAnswer}}
2025-08-07 11:36:42.761 [Debug] Resolved string: bmw
2025-08-07 11:36:42.761 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-07 11:36:42.762 [Debug] Resolved string: Click on the Login Button
2025-08-07 11:36:42.765 [Debug] Resolving placeholders in string: {{Selectors.LoginAuthButton}}
2025-08-07 11:36:42.765 [Debug] Resolved string: id=LoginAuthBtn
2025-08-07 11:36:42.765 [Info] Successfully resolved parameters for test case: Login
2025-08-07 11:36:42.765 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-07 11:36:42.765 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-07 11:36:42.765 [Debug] Resolving placeholders in string: id=finish
2025-08-07 11:36:42.765 [Debug] Resolved string: id=finish
2025-08-07 11:36:42.765 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-07 11:36:42.768 [Debug] Resolved string: Click on Login Button
2025-08-07 11:36:42.768 [Debug] Resolving placeholders in string: id=LoginBtn
2025-08-07 11:36:42.769 [Debug] Resolved string: id=LoginBtn
2025-08-07 11:36:42.769 [Debug] Resolving placeholders in string: Type the User Name
2025-08-07 11:36:42.769 [Debug] Resolved string: Type the User Name
2025-08-07 11:36:42.769 [Debug] Resolving placeholders in string: id=UserName
2025-08-07 11:36:42.769 [Debug] Resolved string: id=UserName
2025-08-07 11:36:42.769 [Debug] Resolving placeholders in string: mahmoudsayed022
2025-08-07 11:36:42.772 [Debug] Resolved string: mahmoudsayed022
2025-08-07 11:36:42.772 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-07 11:36:42.772 [Debug] Resolved string: Click on Continue Button
2025-08-07 11:36:42.772 [Debug] Resolving placeholders in string: id=btn
2025-08-07 11:36:42.772 [Debug] Resolved string: id=btn
2025-08-07 11:36:42.772 [Debug] Resolving placeholders in string: Type the Password
2025-08-07 11:36:42.772 [Debug] Resolved string: Type the Password
2025-08-07 11:36:42.772 [Debug] Resolving placeholders in string: id=Password
2025-08-07 11:36:42.775 [Debug] Resolved string: id=Password
2025-08-07 11:36:42.775 [Debug] Resolving placeholders in string: $('[id=Password]').val('Password1')
2025-08-07 11:36:42.775 [Debug] Resolved string: $('[id=Password]').val('Password1')
2025-08-07 11:36:42.775 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-07 11:36:42.776 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-07 11:36:42.776 [Debug] Resolving placeholders in string: id=ChallengeQuestionAnswer
2025-08-07 11:36:42.776 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-07 11:36:42.776 [Debug] Resolving placeholders in string: bmw
2025-08-07 11:36:42.779 [Debug] Resolved string: bmw
2025-08-07 11:36:42.779 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-07 11:36:42.779 [Debug] Resolved string: Click on the Login Button
2025-08-07 11:36:42.779 [Debug] Resolving placeholders in string: id=LoginAuthBtn
2025-08-07 11:36:42.779 [Debug] Resolved string: id=LoginAuthBtn
2025-08-07 11:36:42.779 [Debug] Resolving placeholders in string: Click on change later button on the password expiration popup message
2025-08-07 11:36:42.779 [Debug] Resolved string: Click on change later button on the password expiration popup message
2025-08-07 11:36:42.779 [Debug] Resolving placeholders in string: {{Selectors.popupCancel}}
2025-08-07 11:36:42.782 [Debug] Resolved string: id=popup_cancel
2025-08-07 11:36:42.782 [Debug] Resolving placeholders in string: Click on biometric alert
2025-08-07 11:36:42.782 [Debug] Resolved string: Click on biometric alert
2025-08-07 11:36:42.782 [Debug] Resolving placeholders in string: {{Selectors.BioMetricAlert}}
2025-08-07 11:36:42.782 [Debug] Resolved string: css=#popbuttons > div > button.btn.Cancel.back_gradient2
2025-08-07 11:36:42.782 [Debug] Resolving placeholders in string: Click on biometric alert
2025-08-07 11:36:42.782 [Debug] Resolved string: Click on biometric alert
2025-08-07 11:36:42.783 [Debug] Resolving placeholders in string: {{Selectors.KYCskip}}
2025-08-07 11:36:42.785 [Debug] Resolved string: css=#popbuttons > div > button.btn.Cancel.back_gradient2
2025-08-07 11:36:42.785 [Debug] Resolving placeholders in string: Execute JS code to activate token
2025-08-07 11:36:42.785 [Debug] Resolved string: Execute JS code to activate token
2025-08-07 11:36:42.786 [Debug] Resolving placeholders in string: Globals.ActivationState = 'ACTIVATED';
2025-08-07 11:36:42.786 [Debug] Resolved string: Globals.ActivationState = 'ACTIVATED';
2025-08-07 11:36:42.786 [Debug] Resolving placeholders in string: Click on Cards button from side menu
2025-08-07 11:36:42.786 [Debug] Resolved string: Click on Cards button from side menu
2025-08-07 11:36:42.786 [Debug] Resolving placeholders in string: {{Selectors.CardsButton}}
2025-08-07 11:36:42.788 [Debug] Resolved string: xpath=//div[@class='mainmenuitems']//label[contains(text(), 'Cards')]
2025-08-07 11:36:42.789 [Debug] Resolving placeholders in string: Click on Card Inquiry and check cards details
2025-08-07 11:36:42.789 [Debug] Resolved string: Click on Card Inquiry and check cards details
2025-08-07 11:36:42.789 [Debug] Resolving placeholders in string: {{Selectors.CardInquiry}}
2025-08-07 11:36:42.789 [Debug] Resolved string: xpath=//div[@class='submenuitem']//label[contains(text(), 'Card Inquiry')]
2025-08-07 11:36:42.789 [Debug] Resolving placeholders in string: Click on a card to block it
2025-08-07 11:36:42.789 [Debug] Resolved string: Click on a card to block it
2025-08-07 11:36:42.789 [Debug] Resolving placeholders in string: {{Selectors.CardToBlockFromInquiry}}
2025-08-07 11:36:42.792 [Debug] Resolved string: xpath=//div[@id='CardsListWorking']//h3[contains(text(), '4023XXXXXXXX0007')]
2025-08-07 11:36:42.792 [Debug] Resolving placeholders in string: Click on Block Button
2025-08-07 11:36:42.792 [Debug] Resolved string: Click on Block Button
2025-08-07 11:36:42.792 [Debug] Resolving placeholders in string: {{Selectors.BlockCardOptionInquiry}}
2025-08-07 11:36:42.792 [Debug] Resolved string: id=Stopbtn
2025-08-07 11:36:42.792 [Debug] Resolving placeholders in string: Click on block card button
2025-08-07 11:36:42.793 [Debug] Resolved string: Click on block card button
2025-08-07 11:36:42.793 [Debug] Resolving placeholders in string: {{Selectors.BlockCardBtnSubmit}}
2025-08-07 11:36:42.796 [Debug] Resolved string: id=SubmitBlockCardReq
2025-08-07 11:36:42.796 [Debug] Resolving placeholders in string: Click on block card confirm
2025-08-07 11:36:42.796 [Debug] Resolved string: Click on block card confirm
2025-08-07 11:36:42.796 [Debug] Resolving placeholders in string: {{Selectors.BlockCardConfirm}}
2025-08-07 11:36:42.796 [Debug] Resolved string: id=BlockCardConfirm
2025-08-07 11:36:42.796 [Info] Successfully resolved parameters for test case: Click Block card
2025-08-07 11:36:42.796 [Debug] Loading test case: WorkSet01/TestCases/012.CardServices/BlockCard/125-CheckBlockedCardsList.json
2025-08-07 11:36:42.797 [Info] Loading test case from: WorkSet01/TestCases/012.CardServices/BlockCard/125-CheckBlockedCardsList.json
2025-08-07 11:36:42.800 [Info] Attempting to load configuration file: WorkSet01/TestCases/012.CardServices/BlockCard/125-CheckBlockedCardsList.json
2025-08-07 11:36:42.800 [Info] Successfully loaded configuration file: WorkSet01/TestCases/012.CardServices/BlockCard/125-CheckBlockedCardsList.json
2025-08-07 11:36:42.801 [Info] No filtering configuration found. Including test case 'Check Blocked Cards List'.
2025-08-07 11:36:42.801 [Debug] Resolving parameters for test case: Check Blocked Cards List
2025-08-07 11:36:42.801 [Debug] Merging params
2025-08-07 11:36:42.801 [Debug] Merged basic params
2025-08-07 11:36:42.801 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-07 11:36:42.801 [Debug] Merged SpecialEnvParams params
2025-08-07 11:36:42.804 [Debug] Merged params
2025-08-07 11:36:42.804 [Debug] Loading parameters from reference file: WorkSet01/Params/BlockCardParams.json
2025-08-07 11:36:42.804 [Info] Attempting to load configuration file: WorkSet01/Params/BlockCardParams.json
2025-08-07 11:36:42.804 [Info] Successfully loaded configuration file: WorkSet01/Params/BlockCardParams.json
2025-08-07 11:36:42.804 [Debug] Merging params
2025-08-07 11:36:42.804 [Debug] Merged basic params
2025-08-07 11:36:42.805 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-07 11:36:42.805 [Debug] Merged SpecialEnvParams params
2025-08-07 11:36:42.808 [Debug] Merged params
2025-08-07 11:36:42.808 [Debug] Merging params
2025-08-07 11:36:42.808 [Debug] Merging params
2025-08-07 11:36:42.808 [Debug] Resolving placeholders in test steps for test case: Check Blocked Cards List
2025-08-07 11:36:42.808 [Debug] Resolving TestCaseReference in step: 
2025-08-07 11:36:42.808 [Info] Loading test case from: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:36:42.808 [Info] Attempting to load configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:36:42.808 [Info] Successfully loaded configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:36:42.811 [Debug] Resolving parameters for test case: Login
2025-08-07 11:36:42.811 [Debug] Merging params
2025-08-07 11:36:42.811 [Debug] Merged basic params
2025-08-07 11:36:42.811 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-07 11:36:42.811 [Debug] Merged SpecialEnvParams params
2025-08-07 11:36:42.811 [Debug] Merged params
2025-08-07 11:36:42.812 [Debug] Loading parameters from reference file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:36:42.812 [Info] Attempting to load configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:36:42.815 [Info] Successfully loaded configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:36:42.815 [Debug] Merging params
2025-08-07 11:36:42.815 [Debug] Merged basic params
2025-08-07 11:36:42.815 [Debug] Merged params
2025-08-07 11:36:42.815 [Debug] Merging params
2025-08-07 11:36:42.815 [Debug] Merging params
2025-08-07 11:36:42.815 [Debug] Resolving placeholders in test steps for test case: Login
2025-08-07 11:36:42.815 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-07 11:36:42.818 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-07 11:36:42.818 [Debug] Resolving placeholders in string: {{Selectors.FinishButton}}
2025-08-07 11:36:42.818 [Debug] Resolved string: id=finish
2025-08-07 11:36:42.818 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-07 11:36:42.818 [Debug] Resolved string: Click on Login Button
2025-08-07 11:36:42.818 [Debug] Resolving placeholders in string: {{Selectors.LoginButton}}
2025-08-07 11:36:42.819 [Debug] Resolved string: id=LoginBtn
2025-08-07 11:36:42.819 [Debug] Resolving placeholders in string: Type the User Name
2025-08-07 11:36:42.822 [Debug] Resolved string: Type the User Name
2025-08-07 11:36:42.822 [Debug] Resolving placeholders in string: {{Selectors.UserNameField}}
2025-08-07 11:36:42.822 [Debug] Resolved string: id=UserName
2025-08-07 11:36:42.822 [Debug] Resolving placeholders in string: {{TestData.UserName}}
2025-08-07 11:36:42.822 [Debug] Resolved string: mahmoudsayed022
2025-08-07 11:36:42.822 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-07 11:36:42.822 [Debug] Resolved string: Click on Continue Button
2025-08-07 11:36:42.822 [Debug] Resolving placeholders in string: {{Selectors.ContinueButton}}
2025-08-07 11:36:42.825 [Debug] Resolved string: id=btn
2025-08-07 11:36:42.825 [Debug] Resolving placeholders in string: Type the Password
2025-08-07 11:36:42.825 [Debug] Resolved string: Type the Password
2025-08-07 11:36:42.825 [Debug] Resolving placeholders in string: {{Selectors.PasswordField}}
2025-08-07 11:36:42.825 [Debug] Resolved string: id=Password
2025-08-07 11:36:42.825 [Debug] Resolving placeholders in string: $('[{{Selectors.PasswordField}}]').val('{{TestData.Password}}')
2025-08-07 11:36:42.826 [Debug] Resolved string: $('[id=Password]').val('Password1')
2025-08-07 11:36:42.826 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-07 11:36:42.829 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-07 11:36:42.829 [Debug] Resolving placeholders in string: {{Selectors.ChallengeQuestionField}}
2025-08-07 11:36:42.829 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-07 11:36:42.829 [Debug] Resolving placeholders in string: {{TestData.ChallengeAnswer}}
2025-08-07 11:36:42.829 [Debug] Resolved string: bmw
2025-08-07 11:36:42.829 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-07 11:36:42.829 [Debug] Resolved string: Click on the Login Button
2025-08-07 11:36:42.829 [Debug] Resolving placeholders in string: {{Selectors.LoginAuthButton}}
2025-08-07 11:36:42.832 [Debug] Resolved string: id=LoginAuthBtn
2025-08-07 11:36:42.832 [Info] Successfully resolved parameters for test case: Login
2025-08-07 11:36:42.832 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-07 11:36:42.832 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-07 11:36:42.832 [Debug] Resolving placeholders in string: id=finish
2025-08-07 11:36:42.832 [Debug] Resolved string: id=finish
2025-08-07 11:36:42.832 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-07 11:36:42.833 [Debug] Resolved string: Click on Login Button
2025-08-07 11:36:42.835 [Debug] Resolving placeholders in string: id=LoginBtn
2025-08-07 11:36:42.836 [Debug] Resolved string: id=LoginBtn
2025-08-07 11:36:42.836 [Debug] Resolving placeholders in string: Type the User Name
2025-08-07 11:36:42.836 [Debug] Resolved string: Type the User Name
2025-08-07 11:36:42.836 [Debug] Resolving placeholders in string: id=UserName
2025-08-07 11:36:42.836 [Debug] Resolved string: id=UserName
2025-08-07 11:36:42.836 [Debug] Resolving placeholders in string: mahmoudsayed022
2025-08-07 11:36:42.836 [Debug] Resolved string: mahmoudsayed022
2025-08-07 11:36:42.839 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-07 11:36:42.839 [Debug] Resolved string: Click on Continue Button
2025-08-07 11:36:42.839 [Debug] Resolving placeholders in string: id=btn
2025-08-07 11:36:42.839 [Debug] Resolved string: id=btn
2025-08-07 11:36:42.839 [Debug] Resolving placeholders in string: Type the Password
2025-08-07 11:36:42.839 [Debug] Resolved string: Type the Password
2025-08-07 11:36:42.839 [Debug] Resolving placeholders in string: id=Password
2025-08-07 11:36:42.840 [Debug] Resolved string: id=Password
2025-08-07 11:36:42.842 [Debug] Resolving placeholders in string: $('[id=Password]').val('Password1')
2025-08-07 11:36:42.843 [Debug] Resolved string: $('[id=Password]').val('Password1')
2025-08-07 11:36:42.843 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-07 11:36:42.843 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-07 11:36:42.843 [Debug] Resolving placeholders in string: id=ChallengeQuestionAnswer
2025-08-07 11:36:42.843 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-07 11:36:42.843 [Debug] Resolving placeholders in string: bmw
2025-08-07 11:36:42.843 [Debug] Resolved string: bmw
2025-08-07 11:36:42.846 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-07 11:36:42.846 [Debug] Resolved string: Click on the Login Button
2025-08-07 11:36:42.846 [Debug] Resolving placeholders in string: id=LoginAuthBtn
2025-08-07 11:36:42.846 [Debug] Resolved string: id=LoginAuthBtn
2025-08-07 11:36:42.846 [Debug] Resolving placeholders in string: Click on change later button on the password expiration popup message
2025-08-07 11:36:42.846 [Debug] Resolved string: Click on change later button on the password expiration popup message
2025-08-07 11:36:42.846 [Debug] Resolving placeholders in string: {{Selectors.popupCancel}}
2025-08-07 11:36:42.846 [Debug] Resolved string: id=popup_cancel
2025-08-07 11:36:42.849 [Debug] Resolving placeholders in string: Click on biometric alert
2025-08-07 11:36:42.849 [Debug] Resolved string: Click on biometric alert
2025-08-07 11:36:42.849 [Debug] Resolving placeholders in string: {{Selectors.BioMetricAlert}}
2025-08-07 11:36:42.849 [Debug] Resolved string: css=#popbuttons > div > button.btn.Cancel.back_gradient2
2025-08-07 11:36:42.849 [Debug] Resolving placeholders in string: Execute JS code to activate token
2025-08-07 11:36:42.849 [Debug] Resolved string: Execute JS code to activate token
2025-08-07 11:36:42.849 [Debug] Resolving placeholders in string: Globals.ActivationState = 'ACTIVATED';
2025-08-07 11:36:42.850 [Debug] Resolved string: Globals.ActivationState = 'ACTIVATED';
2025-08-07 11:36:42.852 [Debug] Resolving placeholders in string: Click on Cards option on the side menu
2025-08-07 11:36:42.852 [Debug] Resolved string: Click on Cards option on the side menu
2025-08-07 11:36:42.852 [Debug] Resolving placeholders in string: {{Selectors.CardsButton}}
2025-08-07 11:36:42.853 [Debug] Resolved string: xpath=//div[@class='mainmenuitems']//label[contains(text(), 'Cards')]
2025-08-07 11:36:42.853 [Debug] Resolving placeholders in string: Click on Card Services
2025-08-07 11:36:42.853 [Debug] Resolved string: Click on Card Services
2025-08-07 11:36:42.853 [Debug] Resolving placeholders in string: {{Selectors.CardServices}}
2025-08-07 11:36:42.853 [Debug] Resolved string: xpath=//div[@class='submenuitem']//label[contains(text(), 'Card services')]
2025-08-07 11:36:42.857 [Debug] Resolving placeholders in string: Click on UnBlock Btn
2025-08-07 11:36:42.857 [Debug] Resolved string: Click on UnBlock Btn
2025-08-07 11:36:42.857 [Debug] Resolving placeholders in string: {{Selectors.UnblockCard}}
2025-08-07 11:36:42.858 [Debug] Resolved string: id=UnblockCard
2025-08-07 11:36:42.858 [Debug] Resolving placeholders in string: Click on Select Card Button and show the blocked cards list
2025-08-07 11:36:42.858 [Debug] Resolved string: Click on Select Card Button and show the blocked cards list
2025-08-07 11:36:42.858 [Debug] Resolving placeholders in string: {{Selectors.SelectCardButton}}
2025-08-07 11:36:42.858 [Debug] Resolved string: id=SelectCardBtn
2025-08-07 11:36:42.861 [Debug] Resolving placeholders in string: Check if the blocked cards appear in the card list.
2025-08-07 11:36:42.862 [Debug] Resolved string: Check if the blocked cards appear in the card list.
2025-08-07 11:36:42.862 [Debug] Resolving placeholders in string: css=#CardsSelectionErrorHandle > div:nth-child(1)
2025-08-07 11:36:42.862 [Debug] Resolved string: css=#CardsSelectionErrorHandle > div:nth-child(1)
2025-08-07 11:36:42.862 [Info] Successfully resolved parameters for test case: Check Blocked Cards List
2025-08-07 11:36:42.862 [Debug] Loading test case: WorkSet01/TestCases/012.CardServices/BlockCard/114-CardInquiry2Widgets.json
2025-08-07 11:36:42.862 [Info] Loading test case from: WorkSet01/TestCases/012.CardServices/BlockCard/114-CardInquiry2Widgets.json
2025-08-07 11:36:42.865 [Info] Attempting to load configuration file: WorkSet01/TestCases/012.CardServices/BlockCard/114-CardInquiry2Widgets.json
2025-08-07 11:36:42.876 [Info] Successfully loaded configuration file: WorkSet01/TestCases/012.CardServices/BlockCard/114-CardInquiry2Widgets.json
2025-08-07 11:36:42.877 [Info] No filtering configuration found. Including test case 'Credit cards and prepaid cards displayed on 2 widgets'.
2025-08-07 11:36:42.877 [Debug] Resolving parameters for test case: Credit cards and prepaid cards displayed on 2 widgets
2025-08-07 11:36:42.877 [Debug] Merging params
2025-08-07 11:36:42.877 [Debug] Merged basic params
2025-08-07 11:36:42.877 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-07 11:36:42.880 [Debug] Merged SpecialEnvParams params
2025-08-07 11:36:42.880 [Debug] Merged params
2025-08-07 11:36:42.880 [Debug] Loading parameters from reference file: WorkSet01/Params/CardServicesParams.json
2025-08-07 11:36:42.880 [Info] Attempting to load configuration file: WorkSet01/Params/CardServicesParams.json
2025-08-07 11:36:42.880 [Info] Successfully loaded configuration file: WorkSet01/Params/CardServicesParams.json
2025-08-07 11:36:42.881 [Debug] Merging params
2025-08-07 11:36:42.881 [Debug] Merged basic params
2025-08-07 11:36:42.883 [Debug] Merged params
2025-08-07 11:36:42.884 [Debug] Merging params
2025-08-07 11:36:42.884 [Debug] Merging params
2025-08-07 11:36:42.884 [Debug] Resolving placeholders in test steps for test case: Credit cards and prepaid cards displayed on 2 widgets
2025-08-07 11:36:42.884 [Debug] Resolving TestCaseReference in step: 
2025-08-07 11:36:42.884 [Info] Loading test case from: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:36:42.884 [Info] Attempting to load configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:36:42.887 [Info] Successfully loaded configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:36:42.887 [Debug] Resolving parameters for test case: Login
2025-08-07 11:36:42.887 [Debug] Merging params
2025-08-07 11:36:42.887 [Debug] Merged basic params
2025-08-07 11:36:42.887 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-07 11:36:42.888 [Debug] Merged SpecialEnvParams params
2025-08-07 11:36:42.888 [Debug] Merged params
2025-08-07 11:36:42.891 [Debug] Loading parameters from reference file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:36:42.891 [Info] Attempting to load configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:36:42.891 [Info] Successfully loaded configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:36:42.891 [Debug] Merging params
2025-08-07 11:36:42.891 [Debug] Merged basic params
2025-08-07 11:36:42.891 [Debug] Merged params
2025-08-07 11:36:42.891 [Debug] Merging params
2025-08-07 11:36:42.894 [Debug] Merging params
2025-08-07 11:36:42.894 [Debug] Resolving placeholders in test steps for test case: Login
2025-08-07 11:36:42.894 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-07 11:36:42.894 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-07 11:36:42.894 [Debug] Resolving placeholders in string: {{Selectors.FinishButton}}
2025-08-07 11:36:42.895 [Debug] Resolved string: id=finish
2025-08-07 11:36:42.895 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-07 11:36:42.897 [Debug] Resolved string: Click on Login Button
2025-08-07 11:36:42.898 [Debug] Resolving placeholders in string: {{Selectors.LoginButton}}
2025-08-07 11:36:42.898 [Debug] Resolved string: id=LoginBtn
2025-08-07 11:36:42.898 [Debug] Resolving placeholders in string: Type the User Name
2025-08-07 11:36:42.898 [Debug] Resolved string: Type the User Name
2025-08-07 11:36:42.898 [Debug] Resolving placeholders in string: {{Selectors.UserNameField}}
2025-08-07 11:36:42.898 [Debug] Resolved string: id=UserName
2025-08-07 11:36:42.901 [Debug] Resolving placeholders in string: {{TestData.UserName}}
2025-08-07 11:36:42.901 [Debug] Resolved string: mahmoudsayed022
2025-08-07 11:36:42.901 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-07 11:36:42.901 [Debug] Resolved string: Click on Continue Button
2025-08-07 11:36:42.901 [Debug] Resolving placeholders in string: {{Selectors.ContinueButton}}
2025-08-07 11:36:42.901 [Debug] Resolved string: id=btn
2025-08-07 11:36:42.901 [Debug] Resolving placeholders in string: Type the Password
2025-08-07 11:36:42.904 [Debug] Resolved string: Type the Password
2025-08-07 11:36:42.904 [Debug] Resolving placeholders in string: {{Selectors.PasswordField}}
2025-08-07 11:36:42.904 [Debug] Resolved string: id=Password
2025-08-07 11:36:42.904 [Debug] Resolving placeholders in string: $('[{{Selectors.PasswordField}}]').val('{{TestData.Password}}')
2025-08-07 11:36:42.904 [Debug] Resolved string: $('[id=Password]').val('Password1')
2025-08-07 11:36:42.904 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-07 11:36:42.904 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-07 11:36:42.908 [Debug] Resolving placeholders in string: {{Selectors.ChallengeQuestionField}}
2025-08-07 11:36:42.908 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-07 11:36:42.909 [Debug] Resolving placeholders in string: {{TestData.ChallengeAnswer}}
2025-08-07 11:36:42.909 [Debug] Resolved string: bmw
2025-08-07 11:36:42.909 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-07 11:36:42.909 [Debug] Resolved string: Click on the Login Button
2025-08-07 11:36:42.909 [Debug] Resolving placeholders in string: {{Selectors.LoginAuthButton}}
2025-08-07 11:36:42.913 [Debug] Resolved string: id=LoginAuthBtn
2025-08-07 11:36:42.913 [Info] Successfully resolved parameters for test case: Login
2025-08-07 11:36:42.913 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-07 11:36:42.913 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-07 11:36:42.913 [Debug] Resolving placeholders in string: id=finish
2025-08-07 11:36:42.913 [Debug] Resolved string: id=finish
2025-08-07 11:36:42.913 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-07 11:36:42.917 [Debug] Resolved string: Click on Login Button
2025-08-07 11:36:42.917 [Debug] Resolving placeholders in string: id=LoginBtn
2025-08-07 11:36:42.917 [Debug] Resolved string: id=LoginBtn
2025-08-07 11:36:42.917 [Debug] Resolving placeholders in string: Type the User Name
2025-08-07 11:36:42.917 [Debug] Resolved string: Type the User Name
2025-08-07 11:36:42.917 [Debug] Resolving placeholders in string: id=UserName
2025-08-07 11:36:42.918 [Debug] Resolved string: id=UserName
2025-08-07 11:36:42.921 [Debug] Resolving placeholders in string: mahmoudsayed022
2025-08-07 11:36:42.921 [Debug] Resolved string: mahmoudsayed022
2025-08-07 11:36:42.921 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-07 11:36:42.921 [Debug] Resolved string: Click on Continue Button
2025-08-07 11:36:42.921 [Debug] Resolving placeholders in string: id=btn
2025-08-07 11:36:42.921 [Debug] Resolved string: id=btn
2025-08-07 11:36:42.921 [Debug] Resolving placeholders in string: Type the Password
2025-08-07 11:36:42.925 [Debug] Resolved string: Type the Password
2025-08-07 11:36:42.925 [Debug] Resolving placeholders in string: id=Password
2025-08-07 11:36:42.925 [Debug] Resolved string: id=Password
2025-08-07 11:36:42.925 [Debug] Resolving placeholders in string: $('[id=Password]').val('Password1')
2025-08-07 11:36:42.925 [Debug] Resolved string: $('[id=Password]').val('Password1')
2025-08-07 11:36:42.925 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-07 11:36:42.926 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-07 11:36:42.929 [Debug] Resolving placeholders in string: id=ChallengeQuestionAnswer
2025-08-07 11:36:42.929 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-07 11:36:42.929 [Debug] Resolving placeholders in string: bmw
2025-08-07 11:36:42.929 [Debug] Resolved string: bmw
2025-08-07 11:36:42.929 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-07 11:36:42.929 [Debug] Resolved string: Click on the Login Button
2025-08-07 11:36:42.929 [Debug] Resolving placeholders in string: id=LoginAuthBtn
2025-08-07 11:36:42.932 [Debug] Resolved string: id=LoginAuthBtn
2025-08-07 11:36:42.933 [Debug] Resolving placeholders in string: Click on change later button on the password expiration popup message
2025-08-07 11:36:42.933 [Debug] Resolved string: Click on change later button on the password expiration popup message
2025-08-07 11:36:42.933 [Debug] Resolving placeholders in string: {{Selectors.popupCancel}}
2025-08-07 11:36:42.933 [Debug] Resolved string: id=popup_cancel
2025-08-07 11:36:42.933 [Debug] Resolving placeholders in string: Execute JS code to activate token
2025-08-07 11:36:42.933 [Debug] Resolved string: Execute JS code to activate token
2025-08-07 11:36:42.937 [Debug] Resolving placeholders in string: Globals.ActivationState = 'ACTIVATED';
2025-08-07 11:36:42.937 [Debug] Resolved string: Globals.ActivationState = 'ACTIVATED';
2025-08-07 11:36:42.937 [Debug] Resolving placeholders in string: Click on Cards option on the side menu
2025-08-07 11:36:42.937 [Debug] Resolved string: Click on Cards option on the side menu
2025-08-07 11:36:42.938 [Debug] Resolving placeholders in string: {{Selectors.CardsButton}}
2025-08-07 11:36:42.938 [Debug] Resolved string: xpath=//div[@class='mainmenuitems']//label[contains(text(), 'Cards')]
2025-08-07 11:36:42.938 [Debug] Resolving placeholders in string: Click on Card Inquiry
2025-08-07 11:36:42.941 [Debug] Resolved string: Click on Card Inquiry
2025-08-07 11:36:42.941 [Debug] Resolving placeholders in string: {{Selectors.CardInquiry}}
2025-08-07 11:36:42.941 [Debug] Resolved string: xpath=//div[@class='submenuitem']//label[contains(text(), 'Card Inquiry')]
2025-08-07 11:36:42.942 [Debug] Resolving placeholders in string: check Credit cards and prepaid cards displayed on 2 widgets
2025-08-07 11:36:42.942 [Debug] Resolved string: check Credit cards and prepaid cards displayed on 2 widgets
2025-08-07 11:36:42.942 [Debug] Resolving placeholders in string: id=CreditCard
2025-08-07 11:36:42.942 [Debug] Resolved string: id=CreditCard
2025-08-07 11:36:42.945 [Info] Successfully resolved parameters for test case: Credit cards and prepaid cards displayed on 2 widgets
2025-08-07 11:36:42.945 [Debug] Loading test case: WorkSet01/TestCases/012.CardServices/BlockCard/113-CardInquiry.json
2025-08-07 11:36:42.946 [Info] Loading test case from: WorkSet01/TestCases/012.CardServices/BlockCard/113-CardInquiry.json
2025-08-07 11:36:42.946 [Info] Attempting to load configuration file: WorkSet01/TestCases/012.CardServices/BlockCard/113-CardInquiry.json
2025-08-07 11:36:42.946 [Info] Successfully loaded configuration file: WorkSet01/TestCases/012.CardServices/BlockCard/113-CardInquiry.json
2025-08-07 11:36:42.947 [Info] No filtering configuration found. Including test case 'All users Cards are displayed successfully'.
2025-08-07 11:36:42.947 [Debug] Resolving parameters for test case: All users Cards are displayed successfully
2025-08-07 11:36:42.950 [Debug] Merging params
2025-08-07 11:36:42.950 [Debug] Merged basic params
2025-08-07 11:36:42.951 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-07 11:36:42.951 [Debug] Merged SpecialEnvParams params
2025-08-07 11:36:42.951 [Debug] Merged params
2025-08-07 11:36:42.951 [Debug] Loading parameters from reference file: WorkSet01/Params/CardServicesParams.json
2025-08-07 11:36:42.951 [Info] Attempting to load configuration file: WorkSet01/Params/CardServicesParams.json
2025-08-07 11:36:42.955 [Info] Successfully loaded configuration file: WorkSet01/Params/CardServicesParams.json
2025-08-07 11:36:42.956 [Debug] Merging params
2025-08-07 11:36:42.956 [Debug] Merged basic params
2025-08-07 11:36:42.956 [Debug] Merged params
2025-08-07 11:36:42.956 [Debug] Merging params
2025-08-07 11:36:42.956 [Debug] Merging params
2025-08-07 11:36:42.956 [Debug] Resolving placeholders in test steps for test case: All users Cards are displayed successfully
2025-08-07 11:36:42.960 [Debug] Resolving TestCaseReference in step: 
2025-08-07 11:36:42.960 [Info] Loading test case from: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:36:42.960 [Info] Attempting to load configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:36:42.960 [Info] Successfully loaded configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:36:42.961 [Debug] Resolving parameters for test case: Login
2025-08-07 11:36:42.961 [Debug] Merging params
2025-08-07 11:36:42.961 [Debug] Merged basic params
2025-08-07 11:36:42.964 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-07 11:36:42.964 [Debug] Merged SpecialEnvParams params
2025-08-07 11:36:42.965 [Debug] Merged params
2025-08-07 11:36:42.965 [Debug] Loading parameters from reference file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:36:42.965 [Info] Attempting to load configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:36:42.965 [Info] Successfully loaded configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:36:42.965 [Debug] Merging params
2025-08-07 11:36:42.969 [Debug] Merged basic params
2025-08-07 11:36:42.969 [Debug] Merged params
2025-08-07 11:36:42.969 [Debug] Merging params
2025-08-07 11:36:42.969 [Debug] Merging params
2025-08-07 11:36:42.969 [Debug] Resolving placeholders in test steps for test case: Login
2025-08-07 11:36:42.969 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-07 11:36:42.970 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-07 11:36:42.973 [Debug] Resolving placeholders in string: {{Selectors.FinishButton}}
2025-08-07 11:36:42.973 [Debug] Resolved string: id=finish
2025-08-07 11:36:42.974 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-07 11:36:42.974 [Debug] Resolved string: Click on Login Button
2025-08-07 11:36:42.974 [Debug] Resolving placeholders in string: {{Selectors.LoginButton}}
2025-08-07 11:36:42.974 [Debug] Resolved string: id=LoginBtn
2025-08-07 11:36:42.974 [Debug] Resolving placeholders in string: Type the User Name
2025-08-07 11:36:42.977 [Debug] Resolved string: Type the User Name
2025-08-07 11:36:42.977 [Debug] Resolving placeholders in string: {{Selectors.UserNameField}}
2025-08-07 11:36:42.977 [Debug] Resolved string: id=UserName
2025-08-07 11:36:42.978 [Debug] Resolving placeholders in string: {{TestData.UserName}}
2025-08-07 11:36:42.978 [Debug] Resolved string: mahmoudsayed022
2025-08-07 11:36:42.978 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-07 11:36:42.978 [Debug] Resolved string: Click on Continue Button
2025-08-07 11:36:42.981 [Debug] Resolving placeholders in string: {{Selectors.ContinueButton}}
2025-08-07 11:36:42.982 [Debug] Resolved string: id=btn
2025-08-07 11:36:42.982 [Debug] Resolving placeholders in string: Type the Password
2025-08-07 11:36:42.982 [Debug] Resolved string: Type the Password
2025-08-07 11:36:42.982 [Debug] Resolving placeholders in string: {{Selectors.PasswordField}}
2025-08-07 11:36:42.982 [Debug] Resolved string: id=Password
2025-08-07 11:36:42.982 [Debug] Resolving placeholders in string: $('[{{Selectors.PasswordField}}]').val('{{TestData.Password}}')
2025-08-07 11:36:42.986 [Debug] Resolved string: $('[id=Password]').val('Password1')
2025-08-07 11:36:42.986 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-07 11:36:42.986 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-07 11:36:42.986 [Debug] Resolving placeholders in string: {{Selectors.ChallengeQuestionField}}
2025-08-07 11:36:42.986 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-07 11:36:42.986 [Debug] Resolving placeholders in string: {{TestData.ChallengeAnswer}}
2025-08-07 11:36:42.987 [Debug] Resolved string: bmw
2025-08-07 11:36:42.990 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-07 11:36:42.990 [Debug] Resolved string: Click on the Login Button
2025-08-07 11:36:42.990 [Debug] Resolving placeholders in string: {{Selectors.LoginAuthButton}}
2025-08-07 11:36:42.990 [Debug] Resolved string: id=LoginAuthBtn
2025-08-07 11:36:42.990 [Info] Successfully resolved parameters for test case: Login
2025-08-07 11:36:42.990 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-07 11:36:42.990 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-07 11:36:42.994 [Debug] Resolving placeholders in string: id=finish
2025-08-07 11:36:42.994 [Debug] Resolved string: id=finish
2025-08-07 11:36:42.994 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-07 11:36:42.994 [Debug] Resolved string: Click on Login Button
2025-08-07 11:36:42.994 [Debug] Resolving placeholders in string: id=LoginBtn
2025-08-07 11:36:42.995 [Debug] Resolved string: id=LoginBtn
2025-08-07 11:36:42.995 [Debug] Resolving placeholders in string: Type the User Name
2025-08-07 11:36:42.998 [Debug] Resolved string: Type the User Name
2025-08-07 11:36:42.998 [Debug] Resolving placeholders in string: id=UserName
2025-08-07 11:36:42.998 [Debug] Resolved string: id=UserName
2025-08-07 11:36:42.999 [Debug] Resolving placeholders in string: mahmoudsayed022
2025-08-07 11:36:42.999 [Debug] Resolved string: mahmoudsayed022
2025-08-07 11:36:42.999 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-07 11:36:42.999 [Debug] Resolved string: Click on Continue Button
2025-08-07 11:36:43.002 [Debug] Resolving placeholders in string: id=btn
2025-08-07 11:36:43.002 [Debug] Resolved string: id=btn
2025-08-07 11:36:43.002 [Debug] Resolving placeholders in string: Type the Password
2025-08-07 11:36:43.002 [Debug] Resolved string: Type the Password
2025-08-07 11:36:43.002 [Debug] Resolving placeholders in string: id=Password
2025-08-07 11:36:43.003 [Debug] Resolved string: id=Password
2025-08-07 11:36:43.003 [Debug] Resolving placeholders in string: $('[id=Password]').val('Password1')
2025-08-07 11:36:43.007 [Debug] Resolved string: $('[id=Password]').val('Password1')
2025-08-07 11:36:43.007 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-07 11:36:43.007 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-07 11:36:43.007 [Debug] Resolving placeholders in string: id=ChallengeQuestionAnswer
2025-08-07 11:36:43.007 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-07 11:36:43.007 [Debug] Resolving placeholders in string: bmw
2025-08-07 11:36:43.007 [Debug] Resolved string: bmw
2025-08-07 11:36:43.012 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-07 11:36:43.012 [Debug] Resolved string: Click on the Login Button
2025-08-07 11:36:43.012 [Debug] Resolving placeholders in string: id=LoginAuthBtn
2025-08-07 11:36:43.013 [Debug] Resolved string: id=LoginAuthBtn
2025-08-07 11:36:43.013 [Debug] Resolving placeholders in string: Click on change later button on the password expiration popup message
2025-08-07 11:36:43.013 [Debug] Resolved string: Click on change later button on the password expiration popup message
2025-08-07 11:36:43.013 [Debug] Resolving placeholders in string: {{Selectors.popupCancel}}
2025-08-07 11:36:43.016 [Debug] Resolved string: id=popup_cancel
2025-08-07 11:36:43.016 [Debug] Resolving placeholders in string: Execute JS code to activate token
2025-08-07 11:36:43.016 [Debug] Resolved string: Execute JS code to activate token
2025-08-07 11:36:43.016 [Debug] Resolving placeholders in string: Globals.ActivationState = 'ACTIVATED';
2025-08-07 11:36:43.016 [Debug] Resolved string: Globals.ActivationState = 'ACTIVATED';
2025-08-07 11:36:43.017 [Debug] Resolving placeholders in string: Click on Cards option on the side menu
2025-08-07 11:36:43.017 [Debug] Resolved string: Click on Cards option on the side menu
2025-08-07 11:36:43.020 [Debug] Resolving placeholders in string: {{Selectors.CardsButton}}
2025-08-07 11:36:43.020 [Debug] Resolved string: xpath=//div[@class='mainmenuitems']//label[contains(text(), 'Cards')]
2025-08-07 11:36:43.020 [Debug] Resolving placeholders in string: Click on Card Inquiry and check cards details
2025-08-07 11:36:43.020 [Debug] Resolved string: Click on Card Inquiry and check cards details
2025-08-07 11:36:43.020 [Debug] Resolving placeholders in string: {{Selectors.CardInquiry}}
2025-08-07 11:36:43.021 [Debug] Resolved string: xpath=//div[@class='submenuitem']//label[contains(text(), 'Card Inquiry')]
2025-08-07 11:36:43.021 [Debug] Resolving placeholders in string: Check if the blocked cards appear in the card list.
2025-08-07 11:36:43.024 [Debug] Resolved string: Check if the blocked cards appear in the card list.
2025-08-07 11:36:43.024 [Debug] Resolving placeholders in string: css=#CardsSelectionErrorHandle > div:nth-child(1)
2025-08-07 11:36:43.025 [Debug] Resolved string: css=#CardsSelectionErrorHandle > div:nth-child(1)
2025-08-07 11:36:43.025 [Info] Successfully resolved parameters for test case: All users Cards are displayed successfully
2025-08-07 11:36:43.025 [Debug] Loading test case: WorkSet01/TestCases/012.CardServices/ActivateCard/57-fullCardActivation.json
2025-08-07 11:36:43.025 [Info] Loading test case from: WorkSet01/TestCases/012.CardServices/ActivateCard/57-fullCardActivation.json
2025-08-07 11:36:43.025 [Info] Attempting to load configuration file: WorkSet01/TestCases/012.CardServices/ActivateCard/57-fullCardActivation.json
2025-08-07 11:36:43.041 [Info] Successfully loaded configuration file: WorkSet01/TestCases/012.CardServices/ActivateCard/57-fullCardActivation.json
2025-08-07 11:36:43.041 [Info] No filtering configuration found. Including test case 'Verify that a user can successfully activate a card'.
2025-08-07 11:36:43.042 [Debug] Resolving parameters for test case: Verify that a user can successfully activate a card
2025-08-07 11:36:43.042 [Debug] Merging params
2025-08-07 11:36:43.042 [Debug] Merged basic params
2025-08-07 11:36:43.042 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-07 11:36:43.042 [Debug] Merged SpecialEnvParams params
2025-08-07 11:36:43.045 [Debug] Merged params
2025-08-07 11:36:43.046 [Debug] Loading parameters from reference file: WorkSet01/Params/ActivateCardParm.json
2025-08-07 11:36:43.046 [Info] Attempting to load configuration file: WorkSet01/Params/ActivateCardParm.json
2025-08-07 11:36:43.046 [Info] Successfully loaded configuration file: WorkSet01/Params/ActivateCardParm.json
2025-08-07 11:36:43.046 [Debug] Merging params
2025-08-07 11:36:43.046 [Debug] Merged basic params
2025-08-07 11:36:43.046 [Debug] Merged params
2025-08-07 11:36:43.050 [Debug] Merging params
2025-08-07 11:36:43.050 [Debug] Merging params
2025-08-07 11:36:43.050 [Debug] Resolving placeholders in test steps for test case: Verify that a user can successfully activate a card
2025-08-07 11:36:43.050 [Debug] Resolving TestCaseReference in step: 
2025-08-07 11:36:43.050 [Info] Loading test case from: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:36:43.051 [Info] Attempting to load configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:36:43.052 [Info] Successfully loaded configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:36:43.055 [Debug] Resolving parameters for test case: Login
2025-08-07 11:36:43.055 [Debug] Merging params
2025-08-07 11:36:43.055 [Debug] Merged basic params
2025-08-07 11:36:43.055 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-07 11:36:43.055 [Debug] Merged SpecialEnvParams params
2025-08-07 11:36:43.055 [Debug] Merged params
2025-08-07 11:36:43.055 [Debug] Loading parameters from reference file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:36:43.059 [Info] Attempting to load configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:36:43.059 [Info] Successfully loaded configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:36:43.059 [Debug] Merging params
2025-08-07 11:36:43.059 [Debug] Merged basic params
2025-08-07 11:36:43.059 [Debug] Merged params
2025-08-07 11:36:43.060 [Debug] Merging params
2025-08-07 11:36:43.060 [Debug] Merging params
2025-08-07 11:36:43.063 [Debug] Resolving placeholders in test steps for test case: Login
2025-08-07 11:36:43.063 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-07 11:36:43.063 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-07 11:36:43.063 [Debug] Resolving placeholders in string: {{Selectors.FinishButton}}
2025-08-07 11:36:43.064 [Debug] Resolved string: id=finish
2025-08-07 11:36:43.064 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-07 11:36:43.064 [Debug] Resolved string: Click on Login Button
2025-08-07 11:36:43.067 [Debug] Resolving placeholders in string: {{Selectors.LoginButton}}
2025-08-07 11:36:43.068 [Debug] Resolved string: id=LoginBtn
2025-08-07 11:36:43.068 [Debug] Resolving placeholders in string: Type the User Name
2025-08-07 11:36:43.068 [Debug] Resolved string: Type the User Name
2025-08-07 11:36:43.068 [Debug] Resolving placeholders in string: {{Selectors.UserNameField}}
2025-08-07 11:36:43.068 [Debug] Resolved string: id=UserName
2025-08-07 11:36:43.068 [Debug] Resolving placeholders in string: {{TestData.UserName}}
2025-08-07 11:36:43.072 [Debug] Resolved string: mahmoudsayed022
2025-08-07 11:36:43.072 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-07 11:36:43.072 [Debug] Resolved string: Click on Continue Button
2025-08-07 11:36:43.072 [Debug] Resolving placeholders in string: {{Selectors.ContinueButton}}
2025-08-07 11:36:43.072 [Debug] Resolved string: id=btn
2025-08-07 11:36:43.072 [Debug] Resolving placeholders in string: Type the Password
2025-08-07 11:36:43.072 [Debug] Resolved string: Type the Password
2025-08-07 11:36:43.076 [Debug] Resolving placeholders in string: {{Selectors.PasswordField}}
2025-08-07 11:36:43.076 [Debug] Resolved string: id=Password
2025-08-07 11:36:43.076 [Debug] Resolving placeholders in string: $('[{{Selectors.PasswordField}}]').val('{{TestData.Password}}')
2025-08-07 11:36:43.076 [Debug] Resolved string: $('[id=Password]').val('Password1')
2025-08-07 11:36:43.076 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-07 11:36:43.076 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-07 11:36:43.077 [Debug] Resolving placeholders in string: {{Selectors.ChallengeQuestionField}}
2025-08-07 11:36:43.080 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-07 11:36:43.080 [Debug] Resolving placeholders in string: {{TestData.ChallengeAnswer}}
2025-08-07 11:36:43.080 [Debug] Resolved string: bmw
2025-08-07 11:36:43.080 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-07 11:36:43.080 [Debug] Resolved string: Click on the Login Button
2025-08-07 11:36:43.080 [Debug] Resolving placeholders in string: {{Selectors.LoginAuthButton}}
2025-08-07 11:36:43.080 [Debug] Resolved string: id=LoginAuthBtn
2025-08-07 11:36:43.083 [Info] Successfully resolved parameters for test case: Login
2025-08-07 11:36:43.083 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-07 11:36:43.083 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-07 11:36:43.083 [Debug] Resolving placeholders in string: id=finish
2025-08-07 11:36:43.084 [Debug] Resolved string: id=finish
2025-08-07 11:36:43.084 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-07 11:36:43.085 [Debug] Resolved string: Click on Login Button
2025-08-07 11:36:43.088 [Debug] Resolving placeholders in string: id=LoginBtn
2025-08-07 11:36:43.088 [Debug] Resolved string: id=LoginBtn
2025-08-07 11:36:43.089 [Debug] Resolving placeholders in string: Type the User Name
2025-08-07 11:36:43.089 [Debug] Resolved string: Type the User Name
2025-08-07 11:36:43.089 [Debug] Resolving placeholders in string: id=UserName
2025-08-07 11:36:43.089 [Debug] Resolved string: id=UserName
2025-08-07 11:36:43.089 [Debug] Resolving placeholders in string: mahmoudsayed022
2025-08-07 11:36:43.092 [Debug] Resolved string: mahmoudsayed022
2025-08-07 11:36:43.092 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-07 11:36:43.093 [Debug] Resolved string: Click on Continue Button
2025-08-07 11:36:43.093 [Debug] Resolving placeholders in string: id=btn
2025-08-07 11:36:43.093 [Debug] Resolved string: id=btn
2025-08-07 11:36:43.093 [Debug] Resolving placeholders in string: Type the Password
2025-08-07 11:36:43.094 [Debug] Resolved string: Type the Password
2025-08-07 11:36:43.097 [Debug] Resolving placeholders in string: id=Password
2025-08-07 11:36:43.097 [Debug] Resolved string: id=Password
2025-08-07 11:36:43.097 [Debug] Resolving placeholders in string: $('[id=Password]').val('Password1')
2025-08-07 11:36:43.097 [Debug] Resolved string: $('[id=Password]').val('Password1')
2025-08-07 11:36:43.097 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-07 11:36:43.097 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-07 11:36:43.097 [Debug] Resolving placeholders in string: id=ChallengeQuestionAnswer
2025-08-07 11:36:43.100 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-07 11:36:43.101 [Debug] Resolving placeholders in string: bmw
2025-08-07 11:36:43.101 [Debug] Resolved string: bmw
2025-08-07 11:36:43.101 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-07 11:36:43.101 [Debug] Resolved string: Click on the Login Button
2025-08-07 11:36:43.101 [Debug] Resolving placeholders in string: id=LoginAuthBtn
2025-08-07 11:36:43.101 [Debug] Resolved string: id=LoginAuthBtn
2025-08-07 11:36:43.105 [Debug] Resolving placeholders in string: Click on change later button on the password expiration popup message
2025-08-07 11:36:43.105 [Debug] Resolved string: Click on change later button on the password expiration popup message
2025-08-07 11:36:43.105 [Debug] Resolving placeholders in string: {{Selectors.popupCancel}}
2025-08-07 11:36:43.106 [Debug] Resolved string: id=popup_cancel
2025-08-07 11:36:43.106 [Debug] Resolving placeholders in string: Click on biometric alert
2025-08-07 11:36:43.106 [Debug] Resolved string: Click on biometric alert
2025-08-07 11:36:43.106 [Debug] Resolving placeholders in string: {{Selectors.BioMetricAlert}}
2025-08-07 11:36:43.109 [Debug] Resolved string: css=#popbuttons > div > button.btn.Cancel.back_gradient2
2025-08-07 11:36:43.109 [Debug] Resolving placeholders in string: Click on Cards button from side menu
2025-08-07 11:36:43.109 [Debug] Resolved string: Click on Cards button from side menu
2025-08-07 11:36:43.109 [Debug] Resolving placeholders in string: {{Selectors.CardsButton}}
2025-08-07 11:36:43.109 [Debug] Resolved string: xpath=//div[@class='mainmenuitems']//label[contains(text(), 'Cards')]
2025-08-07 11:36:43.110 [Debug] Resolving placeholders in string: Click on Cards Services button from side menu
2025-08-07 11:36:43.110 [Debug] Resolved string: Click on Cards Services button from side menu
2025-08-07 11:36:43.112 [Debug] Resolving placeholders in string: {{Selectors.CardServices}}
2025-08-07 11:36:43.112 [Debug] Resolved string: xpath=//div[@class='submenuitem']//label[contains(text(), 'Card services')]
2025-08-07 11:36:43.113 [Debug] Resolving placeholders in string: Click on Activate Card
2025-08-07 11:36:43.113 [Debug] Resolved string: Click on Activate Card
2025-08-07 11:36:43.113 [Debug] Resolving placeholders in string: {{Selectors.ActivateCard}}
2025-08-07 11:36:43.114 [Debug] Resolved string: id=ActivateCard
2025-08-07 11:36:43.115 [Debug] Resolving placeholders in string: Click on card that need to be  Activated
2025-08-07 11:36:43.116 [Debug] Resolved string: Click on card that need to be  Activated
2025-08-07 11:36:43.116 [Debug] Resolving placeholders in string: {{Selectors.DeActivatedCard}}
2025-08-07 11:36:43.116 [Debug] Resolved string: css=#CardsSelectionErrorHandle > div:nth-child(2) > div > div.balance_cardList > h4
2025-08-07 11:36:43.116 [Debug] Resolving placeholders in string: Click on Continue
2025-08-07 11:36:43.116 [Debug] Resolved string: Click on Continue
2025-08-07 11:36:43.116 [Debug] Resolving placeholders in string: {{Selectors.ContinueBtn}}
2025-08-07 11:36:43.116 [Debug] Resolved string: id=btncontinue
2025-08-07 11:36:43.116 [Debug] Resolving placeholders in string: Enter new PIN
2025-08-07 11:36:43.117 [Debug] Resolved string: Enter new PIN
2025-08-07 11:36:43.117 [Debug] Resolving placeholders in string: {{Selectors.NewPINfield}}
2025-08-07 11:36:43.117 [Debug] Resolved string: id=ACpinNumber
2025-08-07 11:36:43.117 [Debug] Resolving placeholders in string: 1234
2025-08-07 11:36:43.118 [Debug] Resolved string: 1234
2025-08-07 11:36:43.118 [Debug] Resolving placeholders in string: Confirm new PIN
2025-08-07 11:36:43.118 [Debug] Resolved string: Confirm new PIN
2025-08-07 11:36:43.119 [Debug] Resolving placeholders in string: {{Selectors.ConfirmPINfield}}
2025-08-07 11:36:43.120 [Debug] Resolved string: id=ACconfirmPin
2025-08-07 11:36:43.120 [Debug] Resolving placeholders in string: 1234
2025-08-07 11:36:43.120 [Debug] Resolved string: 1234
2025-08-07 11:36:43.120 [Debug] Resolving placeholders in string: Enter a valid OTP
2025-08-07 11:36:43.120 [Debug] Resolved string: Enter a valid OTP
2025-08-07 11:36:43.121 [Debug] Resolving placeholders in string: {{Selectors.OTPfield}}
2025-08-07 11:36:43.121 [Debug] Resolved string: id=ACsmsCode
2025-08-07 11:36:43.121 [Debug] Resolving placeholders in string: 123456
2025-08-07 11:36:43.121 [Debug] Resolved string: 123456
2025-08-07 11:36:43.121 [Debug] Resolving placeholders in string: Click on Continue and showing activation is successfull
2025-08-07 11:36:43.121 [Debug] Resolved string: Click on Continue and showing activation is successfull
2025-08-07 11:36:43.121 [Debug] Resolving placeholders in string: {{Selectors.ContinueBtn2}}
2025-08-07 11:36:43.122 [Debug] Resolved string: id=ACcontinueBtn
2025-08-07 11:36:43.122 [Info] Successfully resolved parameters for test case: Verify that a user can successfully activate a card
2025-08-07 11:36:43.122 [Debug] Loading test case: WorkSet01/TestCases/012.CardServices/BlockCard/52-UnBlockCard.json
2025-08-07 11:36:43.122 [Info] Loading test case from: WorkSet01/TestCases/012.CardServices/BlockCard/52-UnBlockCard.json
2025-08-07 11:36:43.122 [Info] Attempting to load configuration file: WorkSet01/TestCases/012.CardServices/BlockCard/52-UnBlockCard.json
2025-08-07 11:36:43.128 [Info] Successfully loaded configuration file: WorkSet01/TestCases/012.CardServices/BlockCard/52-UnBlockCard.json
2025-08-07 11:36:43.129 [Info] No filtering configuration found. Including test case 'Verify that a user can successfully unblock a card'.
2025-08-07 11:36:43.129 [Debug] Resolving parameters for test case: Verify that a user can successfully unblock a card
2025-08-07 11:36:43.129 [Debug] Merging params
2025-08-07 11:36:43.129 [Debug] Merged basic params
2025-08-07 11:36:43.129 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-07 11:36:43.129 [Debug] Merged SpecialEnvParams params
2025-08-07 11:36:43.129 [Debug] Merged params
2025-08-07 11:36:43.130 [Debug] Loading parameters from reference file: WorkSet01/Params/BlockCardParams.json
2025-08-07 11:36:43.130 [Info] Attempting to load configuration file: WorkSet01/Params/BlockCardParams.json
2025-08-07 11:36:43.130 [Info] Successfully loaded configuration file: WorkSet01/Params/BlockCardParams.json
2025-08-07 11:36:43.131 [Debug] Merging params
2025-08-07 11:36:43.131 [Debug] Merged basic params
2025-08-07 11:36:43.131 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-07 11:36:43.131 [Debug] Merged SpecialEnvParams params
2025-08-07 11:36:43.132 [Debug] Merged params
2025-08-07 11:36:43.132 [Debug] Merging params
2025-08-07 11:36:43.132 [Debug] Merging params
2025-08-07 11:36:43.133 [Debug] Resolving placeholders in test steps for test case: Verify that a user can successfully unblock a card
2025-08-07 11:36:43.133 [Debug] Resolving TestCaseReference in step: 
2025-08-07 11:36:43.133 [Info] Loading test case from: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:36:43.133 [Info] Attempting to load configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:36:43.133 [Info] Successfully loaded configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:36:43.134 [Debug] Resolving parameters for test case: Login
2025-08-07 11:36:43.134 [Debug] Merging params
2025-08-07 11:36:43.134 [Debug] Merged basic params
2025-08-07 11:36:43.134 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-07 11:36:43.134 [Debug] Merged SpecialEnvParams params
2025-08-07 11:36:43.134 [Debug] Merged params
2025-08-07 11:36:43.135 [Debug] Loading parameters from reference file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:36:43.135 [Info] Attempting to load configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:36:43.135 [Info] Successfully loaded configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:36:43.135 [Debug] Merging params
2025-08-07 11:36:43.135 [Debug] Merged basic params
2025-08-07 11:36:43.136 [Debug] Merged params
2025-08-07 11:36:43.136 [Debug] Merging params
2025-08-07 11:36:43.137 [Debug] Merging params
2025-08-07 11:36:43.137 [Debug] Resolving placeholders in test steps for test case: Login
2025-08-07 11:36:43.137 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-07 11:36:43.137 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-07 11:36:43.137 [Debug] Resolving placeholders in string: {{Selectors.FinishButton}}
2025-08-07 11:36:43.137 [Debug] Resolved string: id=finish
2025-08-07 11:36:43.137 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-07 11:36:43.137 [Debug] Resolved string: Click on Login Button
2025-08-07 11:36:43.138 [Debug] Resolving placeholders in string: {{Selectors.LoginButton}}
2025-08-07 11:36:43.138 [Debug] Resolved string: id=LoginBtn
2025-08-07 11:36:43.138 [Debug] Resolving placeholders in string: Type the User Name
2025-08-07 11:36:43.138 [Debug] Resolved string: Type the User Name
2025-08-07 11:36:43.138 [Debug] Resolving placeholders in string: {{Selectors.UserNameField}}
2025-08-07 11:36:43.138 [Debug] Resolved string: id=UserName
2025-08-07 11:36:43.138 [Debug] Resolving placeholders in string: {{TestData.UserName}}
2025-08-07 11:36:43.138 [Debug] Resolved string: mahmoudsayed022
2025-08-07 11:36:43.139 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-07 11:36:43.139 [Debug] Resolved string: Click on Continue Button
2025-08-07 11:36:43.139 [Debug] Resolving placeholders in string: {{Selectors.ContinueButton}}
2025-08-07 11:36:43.139 [Debug] Resolved string: id=btn
2025-08-07 11:36:43.139 [Debug] Resolving placeholders in string: Type the Password
2025-08-07 11:36:43.139 [Debug] Resolved string: Type the Password
2025-08-07 11:36:43.140 [Debug] Resolving placeholders in string: {{Selectors.PasswordField}}
2025-08-07 11:36:43.140 [Debug] Resolved string: id=Password
2025-08-07 11:36:43.141 [Debug] Resolving placeholders in string: $('[{{Selectors.PasswordField}}]').val('{{TestData.Password}}')
2025-08-07 11:36:43.141 [Debug] Resolved string: $('[id=Password]').val('Password1')
2025-08-07 11:36:43.141 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-07 11:36:43.141 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-07 11:36:43.141 [Debug] Resolving placeholders in string: {{Selectors.ChallengeQuestionField}}
2025-08-07 11:36:43.141 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-07 11:36:43.141 [Debug] Resolving placeholders in string: {{TestData.ChallengeAnswer}}
2025-08-07 11:36:43.141 [Debug] Resolved string: bmw
2025-08-07 11:36:43.142 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-07 11:36:43.142 [Debug] Resolved string: Click on the Login Button
2025-08-07 11:36:43.142 [Debug] Resolving placeholders in string: {{Selectors.LoginAuthButton}}
2025-08-07 11:36:43.142 [Debug] Resolved string: id=LoginAuthBtn
2025-08-07 11:36:43.142 [Info] Successfully resolved parameters for test case: Login
2025-08-07 11:36:43.142 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-07 11:36:43.142 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-07 11:36:43.142 [Debug] Resolving placeholders in string: id=finish
2025-08-07 11:36:43.144 [Debug] Resolved string: id=finish
2025-08-07 11:36:43.144 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-07 11:36:43.144 [Debug] Resolved string: Click on Login Button
2025-08-07 11:36:43.144 [Debug] Resolving placeholders in string: id=LoginBtn
2025-08-07 11:36:43.144 [Debug] Resolved string: id=LoginBtn
2025-08-07 11:36:43.144 [Debug] Resolving placeholders in string: Type the User Name
2025-08-07 11:36:43.144 [Debug] Resolved string: Type the User Name
2025-08-07 11:36:43.144 [Debug] Resolving placeholders in string: id=UserName
2025-08-07 11:36:43.144 [Debug] Resolved string: id=UserName
2025-08-07 11:36:43.145 [Debug] Resolving placeholders in string: mahmoudsayed022
2025-08-07 11:36:43.145 [Debug] Resolved string: mahmoudsayed022
2025-08-07 11:36:43.146 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-07 11:36:43.146 [Debug] Resolved string: Click on Continue Button
2025-08-07 11:36:43.146 [Debug] Resolving placeholders in string: id=btn
2025-08-07 11:36:43.146 [Debug] Resolved string: id=btn
2025-08-07 11:36:43.146 [Debug] Resolving placeholders in string: Type the Password
2025-08-07 11:36:43.146 [Debug] Resolved string: Type the Password
2025-08-07 11:36:43.146 [Debug] Resolving placeholders in string: id=Password
2025-08-07 11:36:43.147 [Debug] Resolved string: id=Password
2025-08-07 11:36:43.147 [Debug] Resolving placeholders in string: $('[id=Password]').val('Password1')
2025-08-07 11:36:43.147 [Debug] Resolved string: $('[id=Password]').val('Password1')
2025-08-07 11:36:43.147 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-07 11:36:43.147 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-07 11:36:43.147 [Debug] Resolving placeholders in string: id=ChallengeQuestionAnswer
2025-08-07 11:36:43.147 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-07 11:36:43.148 [Debug] Resolving placeholders in string: bmw
2025-08-07 11:36:43.148 [Debug] Resolved string: bmw
2025-08-07 11:36:43.148 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-07 11:36:43.148 [Debug] Resolved string: Click on the Login Button
2025-08-07 11:36:43.148 [Debug] Resolving placeholders in string: id=LoginAuthBtn
2025-08-07 11:36:43.148 [Debug] Resolved string: id=LoginAuthBtn
2025-08-07 11:36:43.148 [Debug] Resolving placeholders in string: Click on change later button on the password expiration popup message
2025-08-07 11:36:43.148 [Debug] Resolved string: Click on change later button on the password expiration popup message
2025-08-07 11:36:43.148 [Debug] Resolving placeholders in string: {{Selectors.popupCancel}}
2025-08-07 11:36:43.148 [Debug] Resolved string: id=popup_cancel
2025-08-07 11:36:43.148 [Debug] Resolving placeholders in string: Click on biometric alert
2025-08-07 11:36:43.148 [Debug] Resolved string: Click on biometric alert
2025-08-07 11:36:43.149 [Debug] Resolving placeholders in string: {{Selectors.BioMetricAlert}}
2025-08-07 11:36:43.149 [Debug] Resolved string: css=#popbuttons > div > button.btn.Cancel.back_gradient2
2025-08-07 11:36:43.149 [Debug] Resolving placeholders in string: Click on Cards button from side menu
2025-08-07 11:36:43.150 [Debug] Resolved string: Click on Cards button from side menu
2025-08-07 11:36:43.150 [Debug] Resolving placeholders in string: {{Selectors.CardsButton}}
2025-08-07 11:36:43.150 [Debug] Resolved string: xpath=//div[@class='mainmenuitems']//label[contains(text(), 'Cards')]
2025-08-07 11:36:43.150 [Debug] Resolving placeholders in string: Execute JS code to activate token
2025-08-07 11:36:43.150 [Debug] Resolved string: Execute JS code to activate token
2025-08-07 11:36:43.150 [Debug] Resolving placeholders in string: Globals.ActivationState = 'ACTIVATED';
2025-08-07 11:36:43.150 [Debug] Resolved string: Globals.ActivationState = 'ACTIVATED';
2025-08-07 11:36:43.151 [Debug] Resolving placeholders in string: Click on Cards Services button from side menu
2025-08-07 11:36:43.151 [Debug] Resolved string: Click on Cards Services button from side menu
2025-08-07 11:36:43.151 [Debug] Resolving placeholders in string: {{Selectors.CardServices}}
2025-08-07 11:36:43.151 [Debug] Resolved string: xpath=//div[@class='submenuitem']//label[contains(text(), 'Card services')]
2025-08-07 11:36:43.151 [Debug] Resolving placeholders in string: Click on UnBlock Btn
2025-08-07 11:36:43.151 [Debug] Resolved string: Click on UnBlock Btn
2025-08-07 11:36:43.151 [Debug] Resolving placeholders in string: {{Selectors.UnblockCard}}
2025-08-07 11:36:43.151 [Debug] Resolved string: id=UnblockCard
2025-08-07 11:36:43.151 [Debug] Resolving placeholders in string: Click on Blocked Card
2025-08-07 11:36:43.151 [Debug] Resolved string: Click on Blocked Card
2025-08-07 11:36:43.152 [Debug] Resolving placeholders in string: {{Selectors.BlockedCard}}
2025-08-07 11:36:43.152 [Debug] Resolved string: css=#CardsSelectionErrorHandle > div
2025-08-07 11:36:43.152 [Debug] Resolving placeholders in string: Click on Agree Check Box
2025-08-07 11:36:43.152 [Debug] Resolved string: Click on Agree Check Box
2025-08-07 11:36:43.152 [Debug] Resolving placeholders in string: {{Selectors.AgreeCheckBox}}
2025-08-07 11:36:43.152 [Debug] Resolved string: css=#wginsUnblockCard_Default > div > div > div > div.details_items_container.back_white > div.terms.AgreeTermsAndConditions > input[type=checkbox]
2025-08-07 11:36:43.152 [Debug] Resolving placeholders in string: Click on Continue
2025-08-07 11:36:43.152 [Debug] Resolved string: Click on Continue
2025-08-07 11:36:43.152 [Debug] Resolving placeholders in string: {{Selectors.ContinueBtn}}
2025-08-07 11:36:43.152 [Debug] Resolved string: id=SubmitUnblockCardReq
2025-08-07 11:36:43.152 [Debug] Resolving placeholders in string: Type valid Token
2025-08-07 11:36:43.153 [Debug] Resolved string: Type valid Token
2025-08-07 11:36:43.153 [Debug] Resolving placeholders in string: {{Selectors.TokenInput}}
2025-08-07 11:36:43.154 [Debug] Resolved string: id=TokenNUMBER
2025-08-07 11:36:43.154 [Debug] Resolving placeholders in string: 123456
2025-08-07 11:36:43.154 [Debug] Resolved string: 123456
2025-08-07 11:36:43.154 [Debug] Resolving placeholders in string: Click On Confirm
2025-08-07 11:36:43.154 [Debug] Resolved string: Click On Confirm
2025-08-07 11:36:43.154 [Debug] Resolving placeholders in string: {{Selectors.ConfirmBtn}}
2025-08-07 11:36:43.154 [Debug] Resolved string: id=btnTokenConfirm
2025-08-07 11:36:43.154 [Info] Successfully resolved parameters for test case: Verify that a user can successfully unblock a card
2025-08-07 11:36:43.154 [Debug] Loading test case: WorkSet01/TestCases/012.CardServices/BlockCard/48-CheckCardsList.json
2025-08-07 11:36:43.155 [Info] Loading test case from: WorkSet01/TestCases/012.CardServices/BlockCard/48-CheckCardsList.json
2025-08-07 11:36:43.155 [Info] Attempting to load configuration file: WorkSet01/TestCases/012.CardServices/BlockCard/48-CheckCardsList.json
2025-08-07 11:36:43.162 [Info] Successfully loaded configuration file: WorkSet01/TestCases/012.CardServices/BlockCard/48-CheckCardsList.json
2025-08-07 11:36:43.162 [Info] No filtering configuration found. Including test case 'Check Cards List'.
2025-08-07 11:36:43.162 [Debug] Resolving parameters for test case: Check Cards List
2025-08-07 11:36:43.162 [Debug] Merging params
2025-08-07 11:36:43.163 [Debug] Merged basic params
2025-08-07 11:36:43.163 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-07 11:36:43.163 [Debug] Merged SpecialEnvParams params
2025-08-07 11:36:43.163 [Debug] Merged params
2025-08-07 11:36:43.163 [Debug] Loading parameters from reference file: WorkSet01/Params/BlockCardParams.json
2025-08-07 11:36:43.163 [Info] Attempting to load configuration file: WorkSet01/Params/BlockCardParams.json
2025-08-07 11:36:43.164 [Info] Successfully loaded configuration file: WorkSet01/Params/BlockCardParams.json
2025-08-07 11:36:43.165 [Debug] Merging params
2025-08-07 11:36:43.165 [Debug] Merged basic params
2025-08-07 11:36:43.166 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-07 11:36:43.166 [Debug] Merged SpecialEnvParams params
2025-08-07 11:36:43.166 [Debug] Merged params
2025-08-07 11:36:43.166 [Debug] Merging params
2025-08-07 11:36:43.166 [Debug] Merging params
2025-08-07 11:36:43.166 [Debug] Resolving placeholders in test steps for test case: Check Cards List
2025-08-07 11:36:43.166 [Debug] Resolving TestCaseReference in step: 
2025-08-07 11:36:43.167 [Info] Loading test case from: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:36:43.167 [Info] Attempting to load configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:36:43.167 [Info] Successfully loaded configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:36:43.168 [Debug] Resolving parameters for test case: Login
2025-08-07 11:36:43.168 [Debug] Merging params
2025-08-07 11:36:43.168 [Debug] Merged basic params
2025-08-07 11:36:43.169 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-07 11:36:43.170 [Debug] Merged SpecialEnvParams params
2025-08-07 11:36:43.170 [Debug] Merged params
2025-08-07 11:36:43.170 [Debug] Loading parameters from reference file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:36:43.170 [Info] Attempting to load configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:36:43.171 [Info] Successfully loaded configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:36:43.171 [Debug] Merging params
2025-08-07 11:36:43.171 [Debug] Merged basic params
2025-08-07 11:36:43.171 [Debug] Merged params
2025-08-07 11:36:43.171 [Debug] Merging params
2025-08-07 11:36:43.172 [Debug] Merging params
2025-08-07 11:36:43.172 [Debug] Resolving placeholders in test steps for test case: Login
2025-08-07 11:36:43.172 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-07 11:36:43.172 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-07 11:36:43.172 [Debug] Resolving placeholders in string: {{Selectors.FinishButton}}
2025-08-07 11:36:43.172 [Debug] Resolved string: id=finish
2025-08-07 11:36:43.172 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-07 11:36:43.172 [Debug] Resolved string: Click on Login Button
2025-08-07 11:36:43.173 [Debug] Resolving placeholders in string: {{Selectors.LoginButton}}
2025-08-07 11:36:43.173 [Debug] Resolved string: id=LoginBtn
2025-08-07 11:36:43.173 [Debug] Resolving placeholders in string: Type the User Name
2025-08-07 11:36:43.174 [Debug] Resolved string: Type the User Name
2025-08-07 11:36:43.174 [Debug] Resolving placeholders in string: {{Selectors.UserNameField}}
2025-08-07 11:36:43.174 [Debug] Resolved string: id=UserName
2025-08-07 11:36:43.175 [Debug] Resolving placeholders in string: {{TestData.UserName}}
2025-08-07 11:36:43.175 [Debug] Resolved string: mahmoudsayed022
2025-08-07 11:36:43.175 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-07 11:36:43.175 [Debug] Resolved string: Click on Continue Button
2025-08-07 11:36:43.175 [Debug] Resolving placeholders in string: {{Selectors.ContinueButton}}
2025-08-07 11:36:43.175 [Debug] Resolved string: id=btn
2025-08-07 11:36:43.175 [Debug] Resolving placeholders in string: Type the Password
2025-08-07 11:36:43.176 [Debug] Resolved string: Type the Password
2025-08-07 11:36:43.176 [Debug] Resolving placeholders in string: {{Selectors.PasswordField}}
2025-08-07 11:36:43.176 [Debug] Resolved string: id=Password
2025-08-07 11:36:43.176 [Debug] Resolving placeholders in string: $('[{{Selectors.PasswordField}}]').val('{{TestData.Password}}')
2025-08-07 11:36:43.176 [Debug] Resolved string: $('[id=Password]').val('Password1')
2025-08-07 11:36:43.176 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-07 11:36:43.176 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-07 11:36:43.176 [Debug] Resolving placeholders in string: {{Selectors.ChallengeQuestionField}}
2025-08-07 11:36:43.176 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-07 11:36:43.176 [Debug] Resolving placeholders in string: {{TestData.ChallengeAnswer}}
2025-08-07 11:36:43.176 [Debug] Resolved string: bmw
2025-08-07 11:36:43.177 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-07 11:36:43.177 [Debug] Resolved string: Click on the Login Button
2025-08-07 11:36:43.177 [Debug] Resolving placeholders in string: {{Selectors.LoginAuthButton}}
2025-08-07 11:36:43.178 [Debug] Resolved string: id=LoginAuthBtn
2025-08-07 11:36:43.178 [Info] Successfully resolved parameters for test case: Login
2025-08-07 11:36:43.178 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-07 11:36:43.178 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-07 11:36:43.178 [Debug] Resolving placeholders in string: id=finish
2025-08-07 11:36:43.178 [Debug] Resolved string: id=finish
2025-08-07 11:36:43.179 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-07 11:36:43.179 [Debug] Resolved string: Click on Login Button
2025-08-07 11:36:43.179 [Debug] Resolving placeholders in string: id=LoginBtn
2025-08-07 11:36:43.179 [Debug] Resolved string: id=LoginBtn
2025-08-07 11:36:43.179 [Debug] Resolving placeholders in string: Type the User Name
2025-08-07 11:36:43.179 [Debug] Resolved string: Type the User Name
2025-08-07 11:36:43.179 [Debug] Resolving placeholders in string: id=UserName
2025-08-07 11:36:43.179 [Debug] Resolved string: id=UserName
2025-08-07 11:36:43.180 [Debug] Resolving placeholders in string: mahmoudsayed022
2025-08-07 11:36:43.180 [Debug] Resolved string: mahmoudsayed022
2025-08-07 11:36:43.180 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-07 11:36:43.180 [Debug] Resolved string: Click on Continue Button
2025-08-07 11:36:43.180 [Debug] Resolving placeholders in string: id=btn
2025-08-07 11:36:43.180 [Debug] Resolved string: id=btn
2025-08-07 11:36:43.180 [Debug] Resolving placeholders in string: Type the Password
2025-08-07 11:36:43.180 [Debug] Resolved string: Type the Password
2025-08-07 11:36:43.180 [Debug] Resolving placeholders in string: id=Password
2025-08-07 11:36:43.180 [Debug] Resolved string: id=Password
2025-08-07 11:36:43.181 [Debug] Resolving placeholders in string: $('[id=Password]').val('Password1')
2025-08-07 11:36:43.181 [Debug] Resolved string: $('[id=Password]').val('Password1')
2025-08-07 11:36:43.181 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-07 11:36:43.182 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-07 11:36:43.182 [Debug] Resolving placeholders in string: id=ChallengeQuestionAnswer
2025-08-07 11:36:43.182 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-07 11:36:43.182 [Debug] Resolving placeholders in string: bmw
2025-08-07 11:36:43.182 [Debug] Resolved string: bmw
2025-08-07 11:36:43.182 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-07 11:36:43.182 [Debug] Resolved string: Click on the Login Button
2025-08-07 11:36:43.183 [Debug] Resolving placeholders in string: id=LoginAuthBtn
2025-08-07 11:36:43.183 [Debug] Resolved string: id=LoginAuthBtn
2025-08-07 11:36:43.183 [Debug] Resolving placeholders in string: Click on change later button on the password expiration popup message
2025-08-07 11:36:43.183 [Debug] Resolved string: Click on change later button on the password expiration popup message
2025-08-07 11:36:43.183 [Debug] Resolving placeholders in string: {{Selectors.popupCancel}}
2025-08-07 11:36:43.183 [Debug] Resolved string: id=popup_cancel
2025-08-07 11:36:43.183 [Debug] Resolving placeholders in string: Click on biometric alert
2025-08-07 11:36:43.184 [Debug] Resolved string: Click on biometric alert
2025-08-07 11:36:43.184 [Debug] Resolving placeholders in string: {{Selectors.BioMetricAlert}}
2025-08-07 11:36:43.184 [Debug] Resolved string: css=#popbuttons > div > button.btn.Cancel.back_gradient2
2025-08-07 11:36:43.184 [Debug] Resolving placeholders in string: Execute JS code to activate token
2025-08-07 11:36:43.184 [Debug] Resolved string: Execute JS code to activate token
2025-08-07 11:36:43.184 [Debug] Resolving placeholders in string: Globals.ActivationState = 'ACTIVATED';
2025-08-07 11:36:43.184 [Debug] Resolved string: Globals.ActivationState = 'ACTIVATED';
2025-08-07 11:36:43.184 [Debug] Resolving placeholders in string: Click on Cards option on the side menu
2025-08-07 11:36:43.184 [Debug] Resolved string: Click on Cards option on the side menu
2025-08-07 11:36:43.184 [Debug] Resolving placeholders in string: {{Selectors.CardsButton}}
2025-08-07 11:36:43.184 [Debug] Resolved string: xpath=//div[@class='mainmenuitems']//label[contains(text(), 'Cards')]
2025-08-07 11:36:43.185 [Debug] Resolving placeholders in string: Click on Card Services
2025-08-07 11:36:43.185 [Debug] Resolved string: Click on Card Services
2025-08-07 11:36:43.185 [Debug] Resolving placeholders in string: {{Selectors.CardServices}}
2025-08-07 11:36:43.185 [Debug] Resolved string: xpath=//div[@class='submenuitem']//label[contains(text(), 'Card services')]
2025-08-07 11:36:43.185 [Debug] Resolving placeholders in string: Click on Block Card option
2025-08-07 11:36:43.186 [Debug] Resolved string: Click on Block Card option
2025-08-07 11:36:43.186 [Debug] Resolving placeholders in string: {{Selectors.BlockCardOption}}
2025-08-07 11:36:43.186 [Debug] Resolved string: id=BlockCard
2025-08-07 11:36:43.186 [Debug] Resolving placeholders in string: Click on Select Card Button and show the cards list
2025-08-07 11:36:43.186 [Debug] Resolved string: Click on Select Card Button and show the cards list
2025-08-07 11:36:43.186 [Debug] Resolving placeholders in string: {{Selectors.SelectCardButton}}
2025-08-07 11:36:43.186 [Debug] Resolved string: id=SelectCardBtn
2025-08-07 11:36:43.187 [Debug] Resolving placeholders in string: Check if the blocked cards appear in the card list.
2025-08-07 11:36:43.187 [Debug] Resolved string: Check if the blocked cards appear in the card list.
2025-08-07 11:36:43.187 [Debug] Resolving placeholders in string: css=#CardsSelectionErrorHandle > div:nth-child(1)
2025-08-07 11:36:43.187 [Debug] Resolved string: css=#CardsSelectionErrorHandle > div:nth-child(1)
2025-08-07 11:36:43.187 [Info] Successfully resolved parameters for test case: Check Cards List
2025-08-07 11:36:43.187 [Debug] Loading test case: WorkSet01/TestCases/04.ATMdispute/43-CompleteRequest.json
2025-08-07 11:36:43.187 [Info] Loading test case from: WorkSet01/TestCases/04.ATMdispute/43-CompleteRequest.json
2025-08-07 11:36:43.187 [Info] Attempting to load configuration file: WorkSet01/TestCases/04.ATMdispute/43-CompleteRequest.json
2025-08-07 11:36:43.203 [Info] Successfully loaded configuration file: WorkSet01/TestCases/04.ATMdispute/43-CompleteRequest.json
2025-08-07 11:36:43.203 [Info] No filtering configuration found. Including test case 'Complete ATM dispute request'.
2025-08-07 11:36:43.203 [Debug] Resolving parameters for test case: Complete ATM dispute request
2025-08-07 11:36:43.203 [Debug] Merging params
2025-08-07 11:36:43.203 [Debug] Merged basic params
2025-08-07 11:36:43.203 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-07 11:36:43.204 [Debug] Merged SpecialEnvParams params
2025-08-07 11:36:43.204 [Debug] Merged params
2025-08-07 11:36:43.204 [Debug] Loading parameters from reference file: WorkSet01/Params/ATMdisputeParams.json
2025-08-07 11:36:43.204 [Info] Attempting to load configuration file: WorkSet01/Params/ATMdisputeParams.json
2025-08-07 11:36:43.205 [Info] Successfully loaded configuration file: WorkSet01/Params/ATMdisputeParams.json
2025-08-07 11:36:43.205 [Debug] Merging params
2025-08-07 11:36:43.205 [Debug] Merged basic params
2025-08-07 11:36:43.205 [Debug] Merged params
2025-08-07 11:36:43.205 [Debug] Merging params
2025-08-07 11:36:43.206 [Debug] Merging params
2025-08-07 11:36:43.207 [Debug] Resolving placeholders in test steps for test case: Complete ATM dispute request
2025-08-07 11:36:43.207 [Debug] Resolving TestCaseReference in step: 
2025-08-07 11:36:43.207 [Info] Loading test case from: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:36:43.207 [Info] Attempting to load configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:36:43.208 [Info] Successfully loaded configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:36:43.209 [Debug] Resolving parameters for test case: Login
2025-08-07 11:36:43.209 [Debug] Merging params
2025-08-07 11:36:43.209 [Debug] Merged basic params
2025-08-07 11:36:43.209 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-07 11:36:43.210 [Debug] Merged SpecialEnvParams params
2025-08-07 11:36:43.210 [Debug] Merged params
2025-08-07 11:36:43.210 [Debug] Loading parameters from reference file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:36:43.210 [Info] Attempting to load configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:36:43.210 [Info] Successfully loaded configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:36:43.210 [Debug] Merging params
2025-08-07 11:36:43.210 [Debug] Merged basic params
2025-08-07 11:36:43.211 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-07 11:36:43.211 [Debug] Merged SpecialEnvParams params
2025-08-07 11:36:43.211 [Debug] Merged params
2025-08-07 11:36:43.212 [Debug] Merging params
2025-08-07 11:36:43.212 [Debug] Merging params
2025-08-07 11:36:43.212 [Debug] Resolving placeholders in test steps for test case: Login
2025-08-07 11:36:43.213 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-07 11:36:43.213 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-07 11:36:43.213 [Debug] Resolving placeholders in string: {{Selectors.FinishButton}}
2025-08-07 11:36:43.213 [Debug] Resolved string: id=finish
2025-08-07 11:36:43.213 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-07 11:36:43.213 [Debug] Resolved string: Click on Login Button
2025-08-07 11:36:43.213 [Debug] Resolving placeholders in string: {{Selectors.LoginButton}}
2025-08-07 11:36:43.214 [Debug] Resolved string: id=LoginBtn
2025-08-07 11:36:43.214 [Debug] Resolving placeholders in string: Type the User Name
2025-08-07 11:36:43.214 [Debug] Resolved string: Type the User Name
2025-08-07 11:36:43.214 [Debug] Resolving placeholders in string: {{Selectors.UserNameField}}
2025-08-07 11:36:43.214 [Debug] Resolved string: id=UserName
2025-08-07 11:36:43.214 [Debug] Resolving placeholders in string: {{TestData.UserName}}
2025-08-07 11:36:43.214 [Debug] Resolved string: Mm_azmy
2025-08-07 11:36:43.214 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-07 11:36:43.214 [Debug] Resolved string: Click on Continue Button
2025-08-07 11:36:43.214 [Debug] Resolving placeholders in string: {{Selectors.ContinueButton}}
2025-08-07 11:36:43.214 [Debug] Resolved string: id=btn
2025-08-07 11:36:43.215 [Debug] Resolving placeholders in string: Type the Password
2025-08-07 11:36:43.215 [Debug] Resolved string: Type the Password
2025-08-07 11:36:43.216 [Debug] Resolving placeholders in string: {{Selectors.PasswordField}}
2025-08-07 11:36:43.216 [Debug] Resolved string: id=Password
2025-08-07 11:36:43.216 [Debug] Resolving placeholders in string: $('[{{Selectors.PasswordField}}]').val('{{TestData.Password}}')
2025-08-07 11:36:43.216 [Debug] Resolved string: $('[id=Password]').val('Asdf2025')
2025-08-07 11:36:43.217 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-07 11:36:43.217 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-07 11:36:43.217 [Debug] Resolving placeholders in string: {{Selectors.ChallengeQuestionField}}
2025-08-07 11:36:43.217 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-07 11:36:43.217 [Debug] Resolving placeholders in string: {{TestData.ChallengeAnswer}}
2025-08-07 11:36:43.217 [Debug] Resolved string: armada
2025-08-07 11:36:43.217 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-07 11:36:43.217 [Debug] Resolved string: Click on the Login Button
2025-08-07 11:36:43.218 [Debug] Resolving placeholders in string: {{Selectors.LoginAuthButton}}
2025-08-07 11:36:43.218 [Debug] Resolved string: id=LoginAuthBtn
2025-08-07 11:36:43.218 [Info] Successfully resolved parameters for test case: Login
2025-08-07 11:36:43.218 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-07 11:36:43.218 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-07 11:36:43.218 [Debug] Resolving placeholders in string: id=finish
2025-08-07 11:36:43.218 [Debug] Resolved string: id=finish
2025-08-07 11:36:43.218 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-07 11:36:43.218 [Debug] Resolved string: Click on Login Button
2025-08-07 11:36:43.218 [Debug] Resolving placeholders in string: id=LoginBtn
2025-08-07 11:36:43.218 [Debug] Resolved string: id=LoginBtn
2025-08-07 11:36:43.219 [Debug] Resolving placeholders in string: Type the User Name
2025-08-07 11:36:43.219 [Debug] Resolved string: Type the User Name
2025-08-07 11:36:43.219 [Debug] Resolving placeholders in string: id=UserName
2025-08-07 11:36:43.219 [Debug] Resolved string: id=UserName
2025-08-07 11:36:43.220 [Debug] Resolving placeholders in string: Mm_azmy
2025-08-07 11:36:43.220 [Debug] Resolved string: Mm_azmy
2025-08-07 11:36:43.220 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-07 11:36:43.220 [Debug] Resolved string: Click on Continue Button
2025-08-07 11:36:43.221 [Debug] Resolving placeholders in string: id=btn
2025-08-07 11:36:43.221 [Debug] Resolved string: id=btn
2025-08-07 11:36:43.221 [Debug] Resolving placeholders in string: Type the Password
2025-08-07 11:36:43.221 [Debug] Resolved string: Type the Password
2025-08-07 11:36:43.221 [Debug] Resolving placeholders in string: id=Password
2025-08-07 11:36:43.221 [Debug] Resolved string: id=Password
2025-08-07 11:36:43.221 [Debug] Resolving placeholders in string: $('[id=Password]').val('Asdf2025')
2025-08-07 11:36:43.222 [Debug] Resolved string: $('[id=Password]').val('Asdf2025')
2025-08-07 11:36:43.222 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-07 11:36:43.222 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-07 11:36:43.222 [Debug] Resolving placeholders in string: id=ChallengeQuestionAnswer
2025-08-07 11:36:43.222 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-07 11:36:43.222 [Debug] Resolving placeholders in string: armada
2025-08-07 11:36:43.222 [Debug] Resolved string: armada
2025-08-07 11:36:43.223 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-07 11:36:43.223 [Debug] Resolved string: Click on the Login Button
2025-08-07 11:36:43.223 [Debug] Resolving placeholders in string: id=LoginAuthBtn
2025-08-07 11:36:43.223 [Debug] Resolved string: id=LoginAuthBtn
2025-08-07 11:36:43.223 [Debug] Resolving placeholders in string: Click on change later button on the password expiration popup message
2025-08-07 11:36:43.223 [Debug] Resolved string: Click on change later button on the password expiration popup message
2025-08-07 11:36:43.223 [Debug] Resolving placeholders in string: {{Selectors.popupCancel}}
2025-08-07 11:36:43.223 [Debug] Resolved string: id=popup_cancel
2025-08-07 11:36:43.224 [Debug] Resolving placeholders in string: Click on biometric alert
2025-08-07 11:36:43.224 [Debug] Resolved string: Click on biometric alert
2025-08-07 11:36:43.225 [Debug] Resolving placeholders in string: {{Selectors.BioMetricAlert}}
2025-08-07 11:36:43.225 [Debug] Resolved string: css=#popbuttons > div > button.btn.Cancel.back_gradient2
2025-08-07 11:36:43.225 [Debug] Resolving placeholders in string: Execute JS code to activate token
2025-08-07 11:36:43.225 [Debug] Resolved string: Execute JS code to activate token
2025-08-07 11:36:43.225 [Debug] Resolving placeholders in string: Globals.ActivationState = 'ACTIVATED';
2025-08-07 11:36:43.225 [Debug] Resolved string: Globals.ActivationState = 'ACTIVATED';
2025-08-07 11:36:43.225 [Debug] Resolving placeholders in string: Click on Cards option on the side menu
2025-08-07 11:36:43.226 [Debug] Resolved string: Click on Cards option on the side menu
2025-08-07 11:36:43.226 [Debug] Resolving placeholders in string: {{Selectors.CardsButton}}
2025-08-07 11:36:43.226 [Debug] Resolved string: xpath=//div[@class='mainmenuitems']//label[contains(text(), 'Cards')]
2025-08-07 11:36:43.226 [Debug] Resolving placeholders in string: Click on Card Services
2025-08-07 11:36:43.226 [Debug] Resolved string: Click on Card Services
2025-08-07 11:36:43.226 [Debug] Resolving placeholders in string: {{Selectors.CardServices}}
2025-08-07 11:36:43.226 [Debug] Resolved string: xpath=//div[@class='submenuitem']//label[contains(text(), 'Card services')]
2025-08-07 11:36:43.227 [Debug] Resolving placeholders in string: Click on ATM dispute
2025-08-07 11:36:43.227 [Debug] Resolved string: Click on ATM dispute
2025-08-07 11:36:43.227 [Debug] Resolving placeholders in string: {{Selectors.ATMdisputeButton}}
2025-08-07 11:36:43.227 [Debug] Resolved string: id=ATMDispute
2025-08-07 11:36:43.227 [Debug] Resolving placeholders in string: Click on dispute type
2025-08-07 11:36:43.227 [Debug] Resolved string: Click on dispute type
2025-08-07 11:36:43.227 [Debug] Resolving placeholders in string: {{Selectors.SelectDisputeType}}
2025-08-07 11:36:43.227 [Debug] Resolved string: xpath=//div[@class='details_container']//span[normalize-space(text())='Select...']
2025-08-07 11:36:43.227 [Debug] Resolving placeholders in string: Choose dispute type
2025-08-07 11:36:43.227 [Debug] Resolved string: Choose dispute type
2025-08-07 11:36:43.227 [Debug] Resolving placeholders in string: {{Selectors.DisputeType}}
2025-08-07 11:36:43.228 [Debug] Resolved string: xpath=//div[@class='details_container']//span[normalize-space(text())='Withdrawal did not dispense']
2025-08-07 11:36:43.228 [Debug] Resolving placeholders in string: Click on Select Cards button
2025-08-07 11:36:43.228 [Debug] Resolved string: Click on Select Cards button
2025-08-07 11:36:43.228 [Debug] Resolving placeholders in string: {{Selectors.SelectButton}}
2025-08-07 11:36:43.229 [Debug] Resolved string: xpath=//div[@class='details_container']//button[normalize-space(text())='Select']
2025-08-07 11:36:43.229 [Debug] Resolving placeholders in string: Choose a card with ATM transactions
2025-08-07 11:36:43.229 [Debug] Resolved string: Choose a card with ATM transactions
2025-08-07 11:36:43.229 [Debug] Resolving placeholders in string: {{Selectors.CardWithTransactions}}
2025-08-07 11:36:43.229 [Debug] Resolved string: xpath=//div[@class='module_list_container cardData']//h4[normalize-space(text())='4204XXXXXXXX2562']
2025-08-07 11:36:43.229 [Debug] Resolving placeholders in string: Click on select a transaction
2025-08-07 11:36:43.229 [Debug] Resolved string: Click on select a transaction
2025-08-07 11:36:43.230 [Debug] Resolving placeholders in string: {{Selectors.SelectTransactionButton}}
2025-08-07 11:36:43.230 [Debug] Resolved string: xpath=//div[@class='details_container']//button[normalize-space(text())='Select Transaction']
2025-08-07 11:36:43.230 [Debug] Resolving placeholders in string: Choose a transaction
2025-08-07 11:36:43.230 [Debug] Resolved string: Choose a transaction
2025-08-07 11:36:43.230 [Debug] Resolving placeholders in string: {{Selectors.Transaction}}
2025-08-07 11:36:43.230 [Debug] Resolved string: css=#Transaction > div:nth-child(1) > div.list_header
2025-08-07 11:36:43.230 [Debug] Resolving placeholders in string: Click on continue
2025-08-07 11:36:43.231 [Debug] Resolved string: Click on continue
2025-08-07 11:36:43.231 [Debug] Resolving placeholders in string: {{Selectors.ContinueButtonPilot1}}
2025-08-07 11:36:43.231 [Debug] Resolved string: xpath=//div[@class='details_container']//button[normalize-space(text())='Continue']
2025-08-07 11:36:43.231 [Debug] Resolving placeholders in string: Enter the disputed amount
2025-08-07 11:36:43.231 [Debug] Resolved string: Enter the disputed amount
2025-08-07 11:36:43.231 [Debug] Resolving placeholders in string: {{Selectors.DisputedAmmountField}}
2025-08-07 11:36:43.231 [Debug] Resolved string: xpath=//div[@class='details_container']//input[normalize-space(@placeholder)='Enter Disputed amount']
2025-08-07 11:36:43.231 [Debug] Resolving placeholders in string: 0
2025-08-07 11:36:43.231 [Debug] Resolved string: 0
2025-08-07 11:36:43.231 [Debug] Resolving placeholders in string: Enter the dispute note
2025-08-07 11:36:43.232 [Debug] Resolved string: Enter the dispute note
2025-08-07 11:36:43.232 [Debug] Resolving placeholders in string: {{Selectors.DisputeNoteField}}
2025-08-07 11:36:43.232 [Debug] Resolved string: xpath=//div[@class='details_container']//textarea[normalize-space(@placeholder)='']
2025-08-07 11:36:43.232 [Debug] Resolving placeholders in string: This is a test for the feature by the dev team. Please ignore this request.
2025-08-07 11:36:43.232 [Debug] Resolved string: This is a test for the feature by the dev team. Please ignore this request.
2025-08-07 11:36:43.233 [Debug] Resolving placeholders in string: Click on the agree terms checkbox
2025-08-07 11:36:43.233 [Debug] Resolved string: Click on the agree terms checkbox
2025-08-07 11:36:43.233 [Debug] Resolving placeholders in string: {{Selectors.TermsCheckbox}}
2025-08-07 11:36:43.233 [Debug] Resolved string: xpath=//div[@class='details_container']//input[@type='checkbox']
2025-08-07 11:36:43.233 [Debug] Resolving placeholders in string: Click on continue button
2025-08-07 11:36:43.233 [Debug] Resolved string: Click on continue button
2025-08-07 11:36:43.233 [Debug] Resolving placeholders in string: {{Selectors.ContinueButtonPilot2}}
2025-08-07 11:36:43.234 [Debug] Resolved string: xpath=//div[@class='details_container']//button[normalize-space(text())='Confirm']
2025-08-07 11:36:43.234 [Debug] Resolving placeholders in string: Click on confirm button
2025-08-07 11:36:43.234 [Debug] Resolved string: Click on confirm button
2025-08-07 11:36:43.234 [Debug] Resolving placeholders in string: {{Selectors.ConfirmDispute}}
2025-08-07 11:36:43.234 [Debug] Resolved string: id=DisputePreConfirmationBtn
2025-08-07 11:36:43.234 [Info] Successfully resolved parameters for test case: Complete ATM dispute request
2025-08-07 11:36:43.234 [Debug] Loading test case: WorkSet01/TestCases/03.Beneficiaries/Add/TC-34 AddinglocalBen.json
2025-08-07 11:36:43.234 [Info] Loading test case from: WorkSet01/TestCases/03.Beneficiaries/Add/TC-34 AddinglocalBen.json
2025-08-07 11:36:43.235 [Info] Attempting to load configuration file: WorkSet01/TestCases/03.Beneficiaries/Add/TC-34 AddinglocalBen.json
2025-08-07 11:36:43.239 [Info] Successfully loaded configuration file: WorkSet01/TestCases/03.Beneficiaries/Add/TC-34 AddinglocalBen.json
2025-08-07 11:36:43.240 [Info] No filtering configuration found. Including test case 'Add local beneficiary with valid data '.
2025-08-07 11:36:43.240 [Debug] Resolving parameters for test case: Add local beneficiary with valid data 
2025-08-07 11:36:43.240 [Debug] Merging params
2025-08-07 11:36:43.240 [Debug] Merged basic params
2025-08-07 11:36:43.240 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-07 11:36:43.240 [Debug] Merged SpecialEnvParams params
2025-08-07 11:36:43.241 [Debug] Merged params
2025-08-07 11:36:43.241 [Debug] Loading parameters from reference file: WorkSet01/Params/VerifictionParams.json
2025-08-07 11:36:43.241 [Info] Attempting to load configuration file: WorkSet01/Params/VerifictionParams.json
2025-08-07 11:36:43.246 [Info] Successfully loaded configuration file: WorkSet01/Params/VerifictionParams.json
2025-08-07 11:36:43.246 [Debug] Merging params
2025-08-07 11:36:43.246 [Debug] Merged basic params
2025-08-07 11:36:43.247 [Debug] Merged params
2025-08-07 11:36:43.247 [Debug] Merging params
2025-08-07 11:36:43.247 [Debug] Merging params
2025-08-07 11:36:43.247 [Debug] Resolving placeholders in test steps for test case: Add local beneficiary with valid data 
2025-08-07 11:36:43.247 [Debug] Resolving TestCaseReference in step: 
2025-08-07 11:36:43.247 [Info] Loading test case from: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:36:43.248 [Info] Attempting to load configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:36:43.248 [Info] Successfully loaded configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:36:43.473 [Debug] Resolving parameters for test case: Login
2025-08-07 11:36:43.475 [Debug] Merging params
2025-08-07 11:36:43.475 [Debug] Merged basic params
2025-08-07 11:36:43.475 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-07 11:36:43.475 [Debug] Merged SpecialEnvParams params
2025-08-07 11:36:43.476 [Debug] Merged params
2025-08-07 11:36:43.476 [Debug] Loading parameters from reference file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:36:43.476 [Info] Attempting to load configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:36:43.478 [Info] Successfully loaded configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:36:43.479 [Debug] Merging params
2025-08-07 11:36:43.479 [Debug] Merged basic params
2025-08-07 11:36:43.479 [Debug] Merged params
2025-08-07 11:36:43.479 [Debug] Merging params
2025-08-07 11:36:43.481 [Debug] Merging params
2025-08-07 11:36:43.482 [Debug] Resolving placeholders in test steps for test case: Login
2025-08-07 11:36:43.484 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-07 11:36:43.485 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-07 11:36:43.486 [Debug] Resolving placeholders in string: {{Selectors.FinishButton}}
2025-08-07 11:36:43.486 [Debug] Resolved string: id=finish
2025-08-07 11:36:43.486 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-07 11:36:43.486 [Debug] Resolved string: Click on Login Button
2025-08-07 11:36:43.487 [Debug] Resolving placeholders in string: {{Selectors.LoginButton}}
2025-08-07 11:36:43.487 [Debug] Resolved string: id=LoginBtn
2025-08-07 11:36:43.488 [Debug] Resolving placeholders in string: Type the User Name
2025-08-07 11:36:43.489 [Debug] Resolved string: Type the User Name
2025-08-07 11:36:43.489 [Debug] Resolving placeholders in string: {{Selectors.UserNameField}}
2025-08-07 11:36:43.489 [Debug] Resolved string: id=UserName
2025-08-07 11:36:43.489 [Debug] Resolving placeholders in string: {{TestData.UserName}}
2025-08-07 11:36:43.490 [Debug] Resolved string: mahmoudsayed022
2025-08-07 11:36:43.490 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-07 11:36:43.490 [Debug] Resolved string: Click on Continue Button
2025-08-07 11:36:43.490 [Debug] Resolving placeholders in string: {{Selectors.ContinueButton}}
2025-08-07 11:36:43.492 [Debug] Resolved string: id=btn
2025-08-07 11:36:43.493 [Debug] Resolving placeholders in string: Type the Password
2025-08-07 11:36:43.495 [Debug] Resolved string: Type the Password
2025-08-07 11:36:43.496 [Debug] Resolving placeholders in string: {{Selectors.PasswordField}}
2025-08-07 11:36:43.500 [Debug] Resolved string: id=Password
2025-08-07 11:36:43.501 [Debug] Resolving placeholders in string: $('[{{Selectors.PasswordField}}]').val('{{TestData.Password}}')
2025-08-07 11:36:43.502 [Debug] Resolved string: $('[id=Password]').val('Password1')
2025-08-07 11:36:43.502 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-07 11:36:43.502 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-07 11:36:43.503 [Debug] Resolving placeholders in string: {{Selectors.ChallengeQuestionField}}
2025-08-07 11:36:43.503 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-07 11:36:43.503 [Debug] Resolving placeholders in string: {{TestData.ChallengeAnswer}}
2025-08-07 11:36:43.504 [Debug] Resolved string: bmw
2025-08-07 11:36:43.505 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-07 11:36:43.505 [Debug] Resolved string: Click on the Login Button
2025-08-07 11:36:43.506 [Debug] Resolving placeholders in string: {{Selectors.LoginAuthButton}}
2025-08-07 11:36:43.506 [Debug] Resolved string: id=LoginAuthBtn
2025-08-07 11:36:43.506 [Info] Successfully resolved parameters for test case: Login
2025-08-07 11:36:43.507 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-07 11:36:43.507 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-07 11:36:43.507 [Debug] Resolving placeholders in string: id=finish
2025-08-07 11:36:43.508 [Debug] Resolved string: id=finish
2025-08-07 11:36:43.508 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-07 11:36:43.508 [Debug] Resolved string: Click on Login Button
2025-08-07 11:36:43.509 [Debug] Resolving placeholders in string: id=LoginBtn
2025-08-07 11:36:43.509 [Debug] Resolved string: id=LoginBtn
2025-08-07 11:36:43.509 [Debug] Resolving placeholders in string: Type the User Name
2025-08-07 11:36:43.511 [Debug] Resolved string: Type the User Name
2025-08-07 11:36:43.512 [Debug] Resolving placeholders in string: id=UserName
2025-08-07 11:36:43.513 [Debug] Resolved string: id=UserName
2025-08-07 11:36:43.513 [Debug] Resolving placeholders in string: mahmoudsayed022
2025-08-07 11:36:43.513 [Debug] Resolved string: mahmoudsayed022
2025-08-07 11:36:43.514 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-07 11:36:43.514 [Debug] Resolved string: Click on Continue Button
2025-08-07 11:36:43.514 [Debug] Resolving placeholders in string: id=btn
2025-08-07 11:36:43.514 [Debug] Resolved string: id=btn
2025-08-07 11:36:43.515 [Debug] Resolving placeholders in string: Type the Password
2025-08-07 11:36:43.515 [Debug] Resolved string: Type the Password
2025-08-07 11:36:43.515 [Debug] Resolving placeholders in string: id=Password
2025-08-07 11:36:43.515 [Debug] Resolved string: id=Password
2025-08-07 11:36:43.515 [Debug] Resolving placeholders in string: $('[id=Password]').val('Password1')
2025-08-07 11:36:43.516 [Debug] Resolved string: $('[id=Password]').val('Password1')
2025-08-07 11:36:43.516 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-07 11:36:43.516 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-07 11:36:43.516 [Debug] Resolving placeholders in string: id=ChallengeQuestionAnswer
2025-08-07 11:36:43.516 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-07 11:36:43.516 [Debug] Resolving placeholders in string: bmw
2025-08-07 11:36:43.516 [Debug] Resolved string: bmw
2025-08-07 11:36:43.516 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-07 11:36:43.517 [Debug] Resolved string: Click on the Login Button
2025-08-07 11:36:43.517 [Debug] Resolving placeholders in string: id=LoginAuthBtn
2025-08-07 11:36:43.517 [Debug] Resolved string: id=LoginAuthBtn
2025-08-07 11:36:43.517 [Debug] Resolving placeholders in string: Click on change later button on the password expiration popup message
2025-08-07 11:36:43.518 [Debug] Resolved string: Click on change later button on the password expiration popup message
2025-08-07 11:36:43.519 [Debug] Resolving placeholders in string: {{Selectors.popupCancel}}
2025-08-07 11:36:43.520 [Debug] Resolved string: id=popup_cancel
2025-08-07 11:36:43.521 [Debug] Resolving placeholders in string: Click on biometric alert
2025-08-07 11:36:43.522 [Debug] Resolved string: Click on biometric alert
2025-08-07 11:36:43.523 [Debug] Resolving placeholders in string: {{Selectors.BioMetricAlert}}
2025-08-07 11:36:43.524 [Debug] Resolved string: css=#popbuttons > div > button.btn.Cancel.back_gradient2
2025-08-07 11:36:43.525 [Debug] Resolving placeholders in string: Execute JS code to activate token
2025-08-07 11:36:43.525 [Debug] Resolved string: Execute JS code to activate token
2025-08-07 11:36:43.526 [Debug] Resolving placeholders in string: Globals.ActivationState = 'ACTIVATED';
2025-08-07 11:36:43.526 [Debug] Resolved string: Globals.ActivationState = 'ACTIVATED';
2025-08-07 11:36:43.527 [Debug] Resolving placeholders in string: Click on Beneficiaries
2025-08-07 11:36:43.527 [Debug] Resolved string: Click on Beneficiaries
2025-08-07 11:36:43.527 [Debug] Resolving placeholders in string: {{Selectors.BeneficiariesToButton}}
2025-08-07 11:36:43.527 [Debug] Resolved string: xpath=//div[@class='mainmenuitems']//label[contains(text(), 'Beneficiaries')]
2025-08-07 11:36:43.527 [Debug] Resolving placeholders in string: Click on Local Transfer option
2025-08-07 11:36:43.528 [Debug] Resolved string: Click on Local Transfer option
2025-08-07 11:36:43.528 [Debug] Resolving placeholders in string: {{Selectors.LocalTransfer}}
2025-08-07 11:36:43.528 [Debug] Resolved string: xpath=//div[@class='submenuitem']//label[contains(text(), 'Local Transfer')]
2025-08-07 11:36:43.528 [Debug] Resolving placeholders in string: Click on Add Beneficiary
2025-08-07 11:36:43.528 [Debug] Resolved string: Click on Add Beneficiary
2025-08-07 11:36:43.529 [Debug] Resolving placeholders in string: {{Selectors.AddLocalBenBTn}}
2025-08-07 11:36:43.530 [Debug] Resolved string: id=AddLocalBtn
2025-08-07 11:36:43.531 [Debug] Resolving placeholders in string: Type Account number
2025-08-07 11:36:43.531 [Debug] Resolved string: Type Account number
2025-08-07 11:36:43.531 [Debug] Resolving placeholders in string: {{Selectors.AccountNumberInput}}
2025-08-07 11:36:43.531 [Debug] Resolved string: id=BenFAccountNum
2025-08-07 11:36:43.531 [Debug] Resolving placeholders in string: **********
2025-08-07 11:36:43.531 [Debug] Resolved string: **********
2025-08-07 11:36:43.531 [Debug] Resolving placeholders in string: Type the Nick Name
2025-08-07 11:36:43.531 [Debug] Resolved string: Type the Nick Name
2025-08-07 11:36:43.531 [Debug] Resolving placeholders in string: {{Selectors.NickNameInput}}
2025-08-07 11:36:43.532 [Debug] Resolved string: id=BenfNickName
2025-08-07 11:36:43.532 [Debug] Resolving placeholders in string: eedhefdet
2025-08-07 11:36:43.532 [Debug] Resolved string: eedhefdet
2025-08-07 11:36:43.533 [Debug] Resolving placeholders in string: Type the  Beneficiary Full Name
2025-08-07 11:36:43.533 [Debug] Resolved string: Type the  Beneficiary Full Name
2025-08-07 11:36:43.533 [Debug] Resolving placeholders in string: {{Selectors.BeneficiaryFullNameInput}}
2025-08-07 11:36:43.533 [Debug] Resolved string: id=BenFName
2025-08-07 11:36:43.533 [Debug] Resolving placeholders in string: rmrldcrt
2025-08-07 11:36:43.533 [Debug] Resolved string: rmrldcrt
2025-08-07 11:36:43.534 [Debug] Resolving placeholders in string: select bank name
2025-08-07 11:36:43.534 [Debug] Resolved string: select bank name
2025-08-07 11:36:43.535 [Debug] Resolving placeholders in string: {{Selectors.BankNameSelect}}
2025-08-07 11:36:43.535 [Debug] Resolved string: id=SelectBankName
2025-08-07 11:36:43.536 [Debug] Resolving placeholders in string: select bank option
2025-08-07 11:36:43.537 [Debug] Resolved string: select bank option
2025-08-07 11:36:43.538 [Debug] Resolving placeholders in string: {{Selectors.BankOption}}
2025-08-07 11:36:43.540 [Debug] Resolved string: css=#Banks > div > div:nth-child(1) > span
2025-08-07 11:36:43.541 [Debug] Resolving placeholders in string: click on currency dropdown list
2025-08-07 11:36:43.542 [Debug] Resolved string: click on currency dropdown list
2025-08-07 11:36:43.542 [Debug] Resolving placeholders in string: {{Selectors.currencySelect}}
2025-08-07 11:36:43.543 [Debug] Resolved string: id=SelectCurrency_Name
2025-08-07 11:36:43.563 [Debug] Resolving placeholders in string: select currency option 
2025-08-07 11:36:43.563 [Debug] Resolved string: select currency option 
2025-08-07 11:36:43.563 [Debug] Resolving placeholders in string: {{Selectors.currencyoptionEGP}}
2025-08-07 11:36:43.563 [Debug] Resolved string: css=#wginsAddLocalBeneficiary_Default > div > div > div.section_body > div > div.input_group.retail > div.select_warpper > div.options.back_white_solid.dropdown-active.d-block > div > div:nth-child(1)
2025-08-07 11:36:43.563 [Debug] Resolving placeholders in string: Click on continue
2025-08-07 11:36:43.564 [Debug] Resolved string: Click on continue
2025-08-07 11:36:43.564 [Debug] Resolving placeholders in string: {{Selectors.SaveBtn}}
2025-08-07 11:36:43.564 [Debug] Resolved string: id=SavaBtn
2025-08-07 11:36:43.564 [Debug] Resolving placeholders in string: Click on continue
2025-08-07 11:36:43.565 [Debug] Resolved string: Click on continue
2025-08-07 11:36:43.565 [Debug] Resolving placeholders in string: {{Selectors.SecondContinueBen}}
2025-08-07 11:36:43.565 [Debug] Resolved string: id=DelLocBenSumContinue
2025-08-07 11:36:43.565 [Debug] Resolving placeholders in string: Enter Token number
2025-08-07 11:36:43.565 [Debug] Resolved string: Enter Token number
2025-08-07 11:36:43.565 [Debug] Resolving placeholders in string: {{Selectors.TokenInput}}
2025-08-07 11:36:43.565 [Debug] Resolved string: id=TokenNUMBER
2025-08-07 11:36:43.565 [Debug] Resolving placeholders in string: 123456
2025-08-07 11:36:43.566 [Debug] Resolved string: 123456
2025-08-07 11:36:43.566 [Debug] Resolving placeholders in string: Click on confirm and show the confirmation page
2025-08-07 11:36:43.566 [Debug] Resolved string: Click on confirm and show the confirmation page
2025-08-07 11:36:43.566 [Debug] Resolving placeholders in string: {{Selectors.btnTokenConfirm}}
2025-08-07 11:36:43.566 [Debug] Resolved string: id=btnTokenConfirm
2025-08-07 11:36:43.566 [Info] Successfully resolved parameters for test case: Add local beneficiary with valid data 
2025-08-07 11:36:43.567 [Info] Attempting to load configuration file: ExcludedTestData.json
2025-08-07 11:36:43.568 [Info] Successfully loaded configuration file: ExcludedTestData.json
2025-08-07 11:36:43.570 [Info] Retrieved 25 test cases.
2025-08-07 11:36:43.571 [Info] Total test cases found: 25
2025-08-07 11:36:44.161 [Info] Starting test case execution: Click Block card
2025-08-07 11:36:44.878 [Info] Executing test case: Click Block card
2025-08-07 11:36:44.892 [Info] Using browser: System.String[] and BaseUrl: https://digitalbanking.ebseg.com/DigitalBankingCustom2/eBankApplication/ePortal5Core/ePortal5.htm?Menu=New#/EN/Landing
2025-08-07 11:36:44.983 [Info] Initializing WebDriver for browser: Chrome
2025-08-07 11:36:44.985 [Debug] Setting up Chrome WebDriver.
2025-08-07 11:36:52.511 [Info] WebDriver initialized and window maximized.
2025-08-07 11:36:53.049 [Info] Navigated to BaseUrl: https://digitalbanking.ebseg.com/DigitalBankingCustom2/eBankApplication/ePortal5Core/ePortal5.htm?Menu=New#/EN/Landing
2025-08-07 11:36:53.084 [Debug] Executing step: click on target: id=finish
2025-08-07 11:36:53.085 [Debug] Waiting for loading spinner to disappear.
2025-08-07 11:36:53.089 [Debug] Parsing selector: id=LoadingSpinner
2025-08-07 11:37:00.160 [Debug] Delaying step execution by 2000 ms.
2025-08-07 11:37:02.175 [Debug] Locating element with target: id=finish
2025-08-07 11:37:02.177 [Debug] Parsing selector: id=finish
2025-08-07 11:37:02.637 [Debug] Clicking on target: id=finish
2025-08-07 11:37:02.836 [Debug] Waiting for loading spinner to disappear.
2025-08-07 11:37:02.837 [Debug] Parsing selector: id=LoadingSpinner
2025-08-07 11:37:03.161 [Info] The ScreenShot was successfully taken for Step: Click on Finish Button to skip the demo, in TestCase:Click Block card
2025-08-07 11:37:03.168 [Debug] Executing step: click on target: id=LoginBtn
2025-08-07 11:37:03.169 [Debug] Waiting for loading spinner to disappear.
2025-08-07 11:37:03.172 [Debug] Parsing selector: id=LoadingSpinner
2025-08-07 11:37:03.419 [Debug] Delaying step execution by 2000 ms.
2025-08-07 11:37:05.422 [Debug] Locating element with target: id=LoginBtn
2025-08-07 11:37:05.423 [Debug] Parsing selector: id=LoginBtn
2025-08-07 11:37:05.610 [Error] Error executing step: click on target: id=LoginBtn , CustomStatusToReportUponFailure: . Details: Can`t Find Element with target: id=LoginBtn 
2025-08-07 11:37:05.644 [Error] Error executing test case: Click Block card. Details: Can`t Find Element with target: id=LoginBtn 
2025-08-07 11:37:05.921 [Info] WebDriver instance quit.
2025-08-07 11:37:05.921 [Error] Error during test case execution: Click Block card. Details: Can`t Find Element with target: id=LoginBtn 
2025-08-07 11:37:09.917 [Info] Report generated as html
2025-08-07 11:37:14.559 [Info] Report generated as html
2025-08-07 11:37:19.003 [Info] Combined summary report generated successfully