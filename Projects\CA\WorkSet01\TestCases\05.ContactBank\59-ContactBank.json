{"TestCaseName": "Contact Bank", "TestCaseCode": "TC-59", "Steps": [{"TestCaseReference": {"Name": "<PERSON><PERSON>", "TestCasePath": "WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json", "ParamsReference": "WorkSet01/Params/LoginTestCaseParams.json"}}, {"Name": "Click on change later button on the password expiration popup message", "Command": "Click", "Target": "{{Selectors.popupCancel}}"}, {"Name": "Click on Reach Us option on the side menu", "Command": "Click", "Target": "{{Selectors.ReachUsButton}}"}, {"Name": "Click on Contact Us option", "Command": "Click", "Target": "{{Selectors.ContactUs}}"}, {"Name": "Click on Request Type", "Command": "Click", "Target": "{{Selectors.RequestType}}"}, {"Name": "Click on Request new product", "Command": "Click", "Target": "{{Selectors.RequestNewProduct}}"}, {"Name": "Type the request message", "Command": "type", "Target": "{{Selectors.MessageTextbox}}", "Value": "Testing the request feature"}, {"Name": "Click on submit button", "Command": "Click", "Target": "{{Selectors.Submit<PERSON>on}}"}, {"Name": "Assert error message: {{Selectors.AssertMessage}}", "Command": "assert", "Target": "{{Selectors.popupMessage}}", "Value": "{{Selectors.AssertMessage}}"}]}