================================ Start of LogFile ================================
2025-08-11 09:52:55.141 [Info] Checking for work directory: ..\..\..\Projects\CA
2025-08-11 09:52:55.183 [Info] Using work directory: D:\TFS\DevWork\R and D Work\TestAutomationFramework\TestAutomationFramework\Projects\CA
2025-08-11 09:52:55.183 [Info] No Args for SuitePaths, scanning: WorkSet01\TestSuites, WorkSet02\TestSuites, WorkSet03\TestSuites, WorkSet04\TestSuites, MainWorkSet\TestSuites
2025-08-11 09:52:55.185 [Info] Retrieving all test cases.
2025-08-11 09:52:55.186 [Info] Retrieving test suite paths.
2025-08-11 09:52:55.187 [Info] Retrieved 0 test suite paths.
2025-08-11 09:52:55.190 [Info] Retrieved 1 test suite paths.
2025-08-11 09:52:55.190 [Info] Retrieved 99 test suite paths.
2025-08-11 09:52:55.191 [Info] Retrieved 154 test suite paths.
2025-08-11 09:52:55.191 [Info] Retrieved 164 test suite paths.
2025-08-11 09:52:55.194 [Info] Loading RunningInstructions configuration...
2025-08-11 09:52:55.195 [Info] Attempting to load configuration file: RunningInstructions.json
2025-08-11 09:52:55.207 [Info] Successfully loaded configuration file: RunningInstructions.json
2025-08-11 09:52:55.222 [Info] Attempting to load configuration file: RunningInstructions.local.json
2025-08-11 09:52:55.222 [Info] Successfully loaded configuration file: RunningInstructions.local.json
2025-08-11 09:52:55.222 [Info] Merging local RunningInstructions with main configuration.
2025-08-11 09:52:55.223 [Info] Successfully merged local RunningInstructions with main configuration.
2025-08-11 09:52:55.283 [Debug] Applying suite filtering with regexes: .*workset01.*testsuites.*
2025-08-11 09:52:55.290 [Info] Loading test suite: WorkSet01\TestSuites\Sanity.json
2025-08-11 09:52:55.291 [Info] Attempting to load test suite: WorkSet01\TestSuites\Sanity.json
2025-08-11 09:52:55.316 [Info] Successfully loaded test suite: WorkSet01\TestSuites\Sanity.json
2025-08-11 09:52:55.317 [Debug] Loading test case: WorkSet01/TestCases/021.HappyPoints/29.json
2025-08-11 09:52:55.317 [Info] Loading test case from: WorkSet01/TestCases/021.HappyPoints/29.json
2025-08-11 09:52:55.317 [Info] Attempting to load configuration file: WorkSet01/TestCases/021.HappyPoints/29.json
2025-08-11 09:52:55.330 [Info] Successfully loaded configuration file: WorkSet01/TestCases/021.HappyPoints/29.json
2025-08-11 09:52:55.342 [Info] No filtering configuration found. Including test case 'Happy Points valid redemption'.
2025-08-11 09:52:55.345 [Info] Attempting to load configuration file: GlobalParams.json
2025-08-11 09:52:55.358 [Info] Successfully loaded configuration file: GlobalParams.json
2025-08-11 09:52:55.370 [Debug] Resolving parameters for test case: Happy Points valid redemption
2025-08-11 09:52:55.371 [Debug] Merging params
2025-08-11 09:52:55.371 [Debug] Merged basic params
2025-08-11 09:52:55.371 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-11 09:52:55.371 [Debug] Merged SpecialEnvParams params
2025-08-11 09:52:55.371 [Debug] Merged params
2025-08-11 09:52:55.371 [Debug] Loading parameters from reference file: WorkSet01/Params/HappyPointsParams.json
2025-08-11 09:52:55.371 [Info] Attempting to load configuration file: WorkSet01/Params/HappyPointsParams.json
2025-08-11 09:52:55.383 [Info] Successfully loaded configuration file: WorkSet01/Params/HappyPointsParams.json
2025-08-11 09:52:55.383 [Debug] Merging params
2025-08-11 09:52:55.383 [Debug] Merged basic params
2025-08-11 09:52:55.383 [Debug] Merged params
2025-08-11 09:52:55.387 [Debug] Merging params
2025-08-11 09:52:55.387 [Debug] Merging params
2025-08-11 09:52:55.387 [Debug] Resolving placeholders in test steps for test case: Happy Points valid redemption
2025-08-11 09:52:55.387 [Debug] Resolving TestCaseReference in step: 
2025-08-11 09:52:55.387 [Info] Loading test case from: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-11 09:52:55.387 [Info] Attempting to load configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-11 09:52:55.400 [Info] Successfully loaded configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-11 09:52:55.404 [Debug] Resolving parameters for test case: Login
2025-08-11 09:52:55.404 [Debug] Merging params
2025-08-11 09:52:55.404 [Debug] Merged basic params
2025-08-11 09:52:55.404 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-11 09:52:55.404 [Debug] Merged SpecialEnvParams params
2025-08-11 09:52:55.404 [Debug] Merged params
2025-08-11 09:52:55.404 [Debug] Loading parameters from reference file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-11 09:52:55.404 [Info] Attempting to load configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-11 09:52:55.411 [Info] Successfully loaded configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-11 09:52:55.411 [Debug] Merging params
2025-08-11 09:52:55.411 [Debug] Merged basic params
2025-08-11 09:52:55.411 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-11 09:52:55.411 [Debug] Merged SpecialEnvParams params
2025-08-11 09:52:55.411 [Debug] Merged params
2025-08-11 09:52:55.411 [Debug] Merging params
2025-08-11 09:52:55.411 [Debug] Merging params
2025-08-11 09:52:55.411 [Debug] Resolving placeholders in test steps for test case: Login
2025-08-11 09:52:55.413 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-11 09:52:55.413 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-11 09:52:55.413 [Debug] Resolving placeholders in string: {{Selectors.FinishButton}}
2025-08-11 09:52:55.413 [Debug] Resolved string: id=finish
2025-08-11 09:52:55.414 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-11 09:52:55.414 [Debug] Resolved string: Click on Login Button
2025-08-11 09:52:55.414 [Debug] Resolving placeholders in string: {{Selectors.LoginButton}}
2025-08-11 09:52:55.414 [Debug] Resolved string: id=LoginBtn
2025-08-11 09:52:55.414 [Debug] Resolving placeholders in string: Type the User Name
2025-08-11 09:52:55.414 [Debug] Resolved string: Type the User Name
2025-08-11 09:52:55.414 [Debug] Resolving placeholders in string: {{Selectors.UserNameField}}
2025-08-11 09:52:55.414 [Debug] Resolved string: id=UserName
2025-08-11 09:52:55.414 [Debug] Resolving placeholders in string: {{TestData.UserName}}
2025-08-11 09:52:55.414 [Debug] Resolved string: Mm_azmy
2025-08-11 09:52:55.414 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-11 09:52:55.414 [Debug] Resolved string: Click on Continue Button
2025-08-11 09:52:55.414 [Debug] Resolving placeholders in string: {{Selectors.ContinueButton}}
2025-08-11 09:52:55.414 [Debug] Resolved string: id=btn
2025-08-11 09:52:55.414 [Debug] Resolving placeholders in string: Type the Password
2025-08-11 09:52:55.414 [Debug] Resolved string: Type the Password
2025-08-11 09:52:55.414 [Debug] Resolving placeholders in string: {{Selectors.PasswordField}}
2025-08-11 09:52:55.414 [Debug] Resolved string: id=Password
2025-08-11 09:52:55.414 [Debug] Resolving placeholders in string: $('[{{Selectors.PasswordField}}]').val('{{TestData.Password}}')
2025-08-11 09:52:55.415 [Debug] Resolved string: $('[id=Password]').val('Asdf2025')
2025-08-11 09:52:55.415 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-11 09:52:55.415 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-11 09:52:55.415 [Debug] Resolving placeholders in string: {{Selectors.ChallengeQuestionField}}
2025-08-11 09:52:55.415 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-11 09:52:55.415 [Debug] Resolving placeholders in string: {{TestData.ChallengeAnswer}}
2025-08-11 09:52:55.415 [Debug] Resolved string: armada
2025-08-11 09:52:55.415 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-11 09:52:55.415 [Debug] Resolved string: Click on the Login Button
2025-08-11 09:52:55.415 [Debug] Resolving placeholders in string: {{Selectors.LoginAuthButton}}
2025-08-11 09:52:55.415 [Debug] Resolved string: id=LoginAuthBtn
2025-08-11 09:52:55.415 [Info] Successfully resolved parameters for test case: Login
2025-08-11 09:52:55.415 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-11 09:52:55.415 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-11 09:52:55.415 [Debug] Resolving placeholders in string: id=finish
2025-08-11 09:52:55.415 [Debug] Resolved string: id=finish
2025-08-11 09:52:55.415 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-11 09:52:55.415 [Debug] Resolved string: Click on Login Button
2025-08-11 09:52:55.415 [Debug] Resolving placeholders in string: id=LoginBtn
2025-08-11 09:52:55.415 [Debug] Resolved string: id=LoginBtn
2025-08-11 09:52:55.415 [Debug] Resolving placeholders in string: Type the User Name
2025-08-11 09:52:55.415 [Debug] Resolved string: Type the User Name
2025-08-11 09:52:55.415 [Debug] Resolving placeholders in string: id=UserName
2025-08-11 09:52:55.415 [Debug] Resolved string: id=UserName
2025-08-11 09:52:55.415 [Debug] Resolving placeholders in string: Mm_azmy
2025-08-11 09:52:55.415 [Debug] Resolved string: Mm_azmy
2025-08-11 09:52:55.415 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-11 09:52:55.415 [Debug] Resolved string: Click on Continue Button
2025-08-11 09:52:55.415 [Debug] Resolving placeholders in string: id=btn
2025-08-11 09:52:55.415 [Debug] Resolved string: id=btn
2025-08-11 09:52:55.415 [Debug] Resolving placeholders in string: Type the Password
2025-08-11 09:52:55.415 [Debug] Resolved string: Type the Password
2025-08-11 09:52:55.415 [Debug] Resolving placeholders in string: id=Password
2025-08-11 09:52:55.415 [Debug] Resolved string: id=Password
2025-08-11 09:52:55.415 [Debug] Resolving placeholders in string: $('[id=Password]').val('Asdf2025')
2025-08-11 09:52:55.415 [Debug] Resolved string: $('[id=Password]').val('Asdf2025')
2025-08-11 09:52:55.415 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-11 09:52:55.415 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-11 09:52:55.415 [Debug] Resolving placeholders in string: id=ChallengeQuestionAnswer
2025-08-11 09:52:55.415 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-11 09:52:55.415 [Debug] Resolving placeholders in string: armada
2025-08-11 09:52:55.415 [Debug] Resolved string: armada
2025-08-11 09:52:55.415 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-11 09:52:55.415 [Debug] Resolved string: Click on the Login Button
2025-08-11 09:52:55.415 [Debug] Resolving placeholders in string: id=LoginAuthBtn
2025-08-11 09:52:55.415 [Debug] Resolved string: id=LoginAuthBtn
2025-08-11 09:52:55.415 [Debug] Resolving placeholders in string: Click on change later button on the password expiration popup message
2025-08-11 09:52:55.415 [Debug] Resolved string: Click on change later button on the password expiration popup message
2025-08-11 09:52:55.415 [Debug] Resolving placeholders in string: {{Selectors.popupCancel}}
2025-08-11 09:52:55.415 [Debug] Resolved string: id=popup_cancel
2025-08-11 09:52:55.415 [Debug] Resolving placeholders in string: Click on biometric alert
2025-08-11 09:52:55.415 [Debug] Resolved string: Click on biometric alert
2025-08-11 09:52:55.415 [Debug] Resolving placeholders in string: {{Selectors.BioMetricAlert}}
2025-08-11 09:52:55.416 [Debug] Resolved string: css=#popbuttons > div > button.btn.Cancel.back_gradient2
2025-08-11 09:52:55.416 [Debug] Resolving placeholders in string: Click on Happy points option from side menu
2025-08-11 09:52:55.416 [Debug] Resolved string: Click on Happy points option from side menu
2025-08-11 09:52:55.416 [Debug] Resolving placeholders in string: {{Selectors.HappyPointsOption}}
2025-08-11 09:52:55.416 [Debug] Resolved string: xpath=//div[@class='mainmenuitems']//label[contains(text(), 'Happy Points')]
2025-08-11 09:52:55.416 [Debug] Resolving placeholders in string: Click on Redeem Online button
2025-08-11 09:52:55.416 [Debug] Resolved string: Click on Redeem Online button
2025-08-11 09:52:55.416 [Debug] Resolving placeholders in string: {{Selectors.RedeemOnline}}
2025-08-11 09:52:55.416 [Debug] Resolved string: css=#BuyOnlineBTN > div
2025-08-11 09:52:55.416 [Debug] Resolving placeholders in string: Choose a merchant
2025-08-11 09:52:55.416 [Debug] Resolved string: Choose a merchant
2025-08-11 09:52:55.416 [Debug] Resolving placeholders in string: {{Selectors.SelectMerchant}}
2025-08-11 09:52:55.416 [Debug] Resolved string: css=#RequestItem0
2025-08-11 09:52:55.416 [Debug] Resolving placeholders in string: Click Redeem button
2025-08-11 09:52:55.416 [Debug] Resolved string: Click Redeem button
2025-08-11 09:52:55.416 [Debug] Resolving placeholders in string: {{Selectors.RedeemButton}}
2025-08-11 09:52:55.416 [Debug] Resolved string: id=RedeemID
2025-08-11 09:52:55.416 [Debug] Resolving placeholders in string: Enter the number of points to redeem
2025-08-11 09:52:55.416 [Debug] Resolved string: Enter the number of points to redeem
2025-08-11 09:52:55.416 [Debug] Resolving placeholders in string: {{Selectors.PointsTextbox}}
2025-08-11 09:52:55.416 [Debug] Resolved string: id=redeempointsID
2025-08-11 09:52:55.416 [Debug] Resolving placeholders in string: 15000
2025-08-11 09:52:55.416 [Debug] Resolved string: 15000
2025-08-11 09:52:55.416 [Debug] Resolving placeholders in string: Click on create cupon button
2025-08-11 09:52:55.417 [Debug] Resolved string: Click on create cupon button
2025-08-11 09:52:55.417 [Debug] Resolving placeholders in string: {{Selectors.CreateCupon}}
2025-08-11 09:52:55.417 [Debug] Resolved string: id=CCoponID
2025-08-11 09:52:55.417 [Debug] Resolving placeholders in string: Click on Continue button
2025-08-11 09:52:55.417 [Debug] Resolved string: Click on Continue button
2025-08-11 09:52:55.417 [Debug] Resolving placeholders in string: {{Selectors.CuntinueConfirmCupon}}
2025-08-11 09:52:55.417 [Debug] Resolved string: id=RequestRedemptionConfrimationSbmtBtn
2025-08-11 09:52:55.417 [Debug] Resolving placeholders in string: Ensure that it redirected to the token page
2025-08-11 09:52:55.417 [Debug] Resolved string: Ensure that it redirected to the token page
2025-08-11 09:52:55.417 [Debug] Resolving placeholders in string: {{Selectors.OTPbox}}
2025-08-11 09:52:55.417 [Debug] Resolved string: id=OTPInpt
2025-08-11 09:52:55.417 [Info] Successfully resolved parameters for test case: Happy Points valid redemption
2025-08-11 09:52:55.418 [Debug] Loading test case: WorkSet01/TestCases/021.HappyPoints/31.json
2025-08-11 09:52:55.419 [Info] Loading test case from: WorkSet01/TestCases/021.HappyPoints/31.json
2025-08-11 09:52:55.419 [Info] Attempting to load configuration file: WorkSet01/TestCases/021.HappyPoints/31.json
2025-08-11 09:52:55.419 [Info] Successfully loaded configuration file: WorkSet01/TestCases/021.HappyPoints/31.json
2025-08-11 09:52:55.419 [Info] No filtering configuration found. Including test case 'Redeem less than 10000 points'.
2025-08-11 09:52:55.420 [Debug] Resolving parameters for test case: Redeem less than 10000 points
2025-08-11 09:52:55.420 [Debug] Merging params
2025-08-11 09:52:55.420 [Debug] Merged basic params
2025-08-11 09:52:55.420 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-11 09:52:55.420 [Debug] Merged SpecialEnvParams params
2025-08-11 09:52:55.420 [Debug] Merged params
2025-08-11 09:52:55.420 [Debug] Loading parameters from reference file: WorkSet01/Params/HappyPointsParams.json
2025-08-11 09:52:55.420 [Info] Attempting to load configuration file: WorkSet01/Params/HappyPointsParams.json
2025-08-11 09:52:55.420 [Info] Successfully loaded configuration file: WorkSet01/Params/HappyPointsParams.json
2025-08-11 09:52:55.420 [Debug] Merging params
2025-08-11 09:52:55.420 [Debug] Merged basic params
2025-08-11 09:52:55.420 [Debug] Merged params
2025-08-11 09:52:55.420 [Debug] Merging params
2025-08-11 09:52:55.420 [Debug] Merging params
2025-08-11 09:52:55.420 [Debug] Resolving placeholders in test steps for test case: Redeem less than 10000 points
2025-08-11 09:52:55.420 [Debug] Resolving TestCaseReference in step: 
2025-08-11 09:52:55.420 [Info] Loading test case from: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-11 09:52:55.420 [Info] Attempting to load configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-11 09:52:55.421 [Info] Successfully loaded configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-11 09:52:55.421 [Debug] Resolving parameters for test case: Login
2025-08-11 09:52:55.421 [Debug] Merging params
2025-08-11 09:52:55.421 [Debug] Merged basic params
2025-08-11 09:52:55.421 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-11 09:52:55.421 [Debug] Merged SpecialEnvParams params
2025-08-11 09:52:55.421 [Debug] Merged params
2025-08-11 09:52:55.421 [Debug] Loading parameters from reference file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-11 09:52:55.421 [Info] Attempting to load configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-11 09:52:55.421 [Info] Successfully loaded configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-11 09:52:55.422 [Debug] Merging params
2025-08-11 09:52:55.422 [Debug] Merged basic params
2025-08-11 09:52:55.422 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-11 09:52:55.422 [Debug] Merged SpecialEnvParams params
2025-08-11 09:52:55.422 [Debug] Merged params
2025-08-11 09:52:55.422 [Debug] Merging params
2025-08-11 09:52:55.422 [Debug] Merging params
2025-08-11 09:52:55.422 [Debug] Resolving placeholders in test steps for test case: Login
2025-08-11 09:52:55.422 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-11 09:52:55.422 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-11 09:52:55.422 [Debug] Resolving placeholders in string: {{Selectors.FinishButton}}
2025-08-11 09:52:55.422 [Debug] Resolved string: id=finish
2025-08-11 09:52:55.422 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-11 09:52:55.422 [Debug] Resolved string: Click on Login Button
2025-08-11 09:52:55.422 [Debug] Resolving placeholders in string: {{Selectors.LoginButton}}
2025-08-11 09:52:55.422 [Debug] Resolved string: id=LoginBtn
2025-08-11 09:52:55.422 [Debug] Resolving placeholders in string: Type the User Name
2025-08-11 09:52:55.422 [Debug] Resolved string: Type the User Name
2025-08-11 09:52:55.422 [Debug] Resolving placeholders in string: {{Selectors.UserNameField}}
2025-08-11 09:52:55.422 [Debug] Resolved string: id=UserName
2025-08-11 09:52:55.422 [Debug] Resolving placeholders in string: {{TestData.UserName}}
2025-08-11 09:52:55.422 [Debug] Resolved string: Mm_azmy
2025-08-11 09:52:55.422 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-11 09:52:55.422 [Debug] Resolved string: Click on Continue Button
2025-08-11 09:52:55.423 [Debug] Resolving placeholders in string: {{Selectors.ContinueButton}}
2025-08-11 09:52:55.423 [Debug] Resolved string: id=btn
2025-08-11 09:52:55.423 [Debug] Resolving placeholders in string: Type the Password
2025-08-11 09:52:55.423 [Debug] Resolved string: Type the Password
2025-08-11 09:52:55.423 [Debug] Resolving placeholders in string: {{Selectors.PasswordField}}
2025-08-11 09:52:55.423 [Debug] Resolved string: id=Password
2025-08-11 09:52:55.423 [Debug] Resolving placeholders in string: $('[{{Selectors.PasswordField}}]').val('{{TestData.Password}}')
2025-08-11 09:52:55.423 [Debug] Resolved string: $('[id=Password]').val('Asdf2025')
2025-08-11 09:52:55.423 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-11 09:52:55.423 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-11 09:52:55.423 [Debug] Resolving placeholders in string: {{Selectors.ChallengeQuestionField}}
2025-08-11 09:52:55.423 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-11 09:52:55.423 [Debug] Resolving placeholders in string: {{TestData.ChallengeAnswer}}
2025-08-11 09:52:55.423 [Debug] Resolved string: armada
2025-08-11 09:52:55.423 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-11 09:52:55.423 [Debug] Resolved string: Click on the Login Button
2025-08-11 09:52:55.423 [Debug] Resolving placeholders in string: {{Selectors.LoginAuthButton}}
2025-08-11 09:52:55.423 [Debug] Resolved string: id=LoginAuthBtn
2025-08-11 09:52:55.423 [Info] Successfully resolved parameters for test case: Login
2025-08-11 09:52:55.423 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-11 09:52:55.423 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-11 09:52:55.423 [Debug] Resolving placeholders in string: id=finish
2025-08-11 09:52:55.423 [Debug] Resolved string: id=finish
2025-08-11 09:52:55.424 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-11 09:52:55.424 [Debug] Resolved string: Click on Login Button
2025-08-11 09:52:55.424 [Debug] Resolving placeholders in string: id=LoginBtn
2025-08-11 09:52:55.424 [Debug] Resolved string: id=LoginBtn
2025-08-11 09:52:55.424 [Debug] Resolving placeholders in string: Type the User Name
2025-08-11 09:52:55.424 [Debug] Resolved string: Type the User Name
2025-08-11 09:52:55.424 [Debug] Resolving placeholders in string: id=UserName
2025-08-11 09:52:55.424 [Debug] Resolved string: id=UserName
2025-08-11 09:52:55.424 [Debug] Resolving placeholders in string: Mm_azmy
2025-08-11 09:52:55.425 [Debug] Resolved string: Mm_azmy
2025-08-11 09:52:55.425 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-11 09:52:55.425 [Debug] Resolved string: Click on Continue Button
2025-08-11 09:52:55.425 [Debug] Resolving placeholders in string: id=btn
2025-08-11 09:52:55.425 [Debug] Resolved string: id=btn
2025-08-11 09:52:55.425 [Debug] Resolving placeholders in string: Type the Password
2025-08-11 09:52:55.425 [Debug] Resolved string: Type the Password
2025-08-11 09:52:55.425 [Debug] Resolving placeholders in string: id=Password
2025-08-11 09:52:55.426 [Debug] Resolved string: id=Password
2025-08-11 09:52:55.426 [Debug] Resolving placeholders in string: $('[id=Password]').val('Asdf2025')
2025-08-11 09:52:55.426 [Debug] Resolved string: $('[id=Password]').val('Asdf2025')
2025-08-11 09:52:55.426 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-11 09:52:55.426 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-11 09:52:55.426 [Debug] Resolving placeholders in string: id=ChallengeQuestionAnswer
2025-08-11 09:52:55.426 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-11 09:52:55.426 [Debug] Resolving placeholders in string: armada
2025-08-11 09:52:55.426 [Debug] Resolved string: armada
2025-08-11 09:52:55.426 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-11 09:52:55.426 [Debug] Resolved string: Click on the Login Button
2025-08-11 09:52:55.426 [Debug] Resolving placeholders in string: id=LoginAuthBtn
2025-08-11 09:52:55.426 [Debug] Resolved string: id=LoginAuthBtn
2025-08-11 09:52:55.426 [Debug] Resolving placeholders in string: Click on Happy points option from side menu
2025-08-11 09:52:55.426 [Debug] Resolved string: Click on Happy points option from side menu
2025-08-11 09:52:55.426 [Debug] Resolving placeholders in string: {{Selectors.HappyPointsOption}}
2025-08-11 09:52:55.426 [Debug] Resolved string: xpath=//div[@class='mainmenuitems']//label[contains(text(), 'Happy Points')]
2025-08-11 09:52:55.426 [Debug] Resolving placeholders in string: Click on Redeem Online button
2025-08-11 09:52:55.426 [Debug] Resolved string: Click on Redeem Online button
2025-08-11 09:52:55.426 [Debug] Resolving placeholders in string: {{Selectors.RedeemOnline}}
2025-08-11 09:52:55.426 [Debug] Resolved string: css=#BuyOnlineBTN > div
2025-08-11 09:52:55.426 [Debug] Resolving placeholders in string: Choose a merchant
2025-08-11 09:52:55.426 [Debug] Resolved string: Choose a merchant
2025-08-11 09:52:55.426 [Debug] Resolving placeholders in string: {{Selectors.SelectMerchant}}
2025-08-11 09:52:55.426 [Debug] Resolved string: css=#RequestItem0
2025-08-11 09:52:55.428 [Debug] Resolving placeholders in string: Click Redeem button
2025-08-11 09:52:55.428 [Debug] Resolved string: Click Redeem button
2025-08-11 09:52:55.428 [Debug] Resolving placeholders in string: {{Selectors.RedeemButton}}
2025-08-11 09:52:55.428 [Debug] Resolved string: id=RedeemID
2025-08-11 09:52:55.428 [Debug] Resolving placeholders in string: Enter the number of points to redeem
2025-08-11 09:52:55.428 [Debug] Resolved string: Enter the number of points to redeem
2025-08-11 09:52:55.428 [Debug] Resolving placeholders in string: {{Selectors.PointsTextbox}}
2025-08-11 09:52:55.428 [Debug] Resolved string: id=redeempointsID
2025-08-11 09:52:55.428 [Debug] Resolving placeholders in string: 900
2025-08-11 09:52:55.428 [Debug] Resolved string: 900
2025-08-11 09:52:55.429 [Debug] Resolving placeholders in string: Click on create cupon button
2025-08-11 09:52:55.429 [Debug] Resolved string: Click on create cupon button
2025-08-11 09:52:55.429 [Debug] Resolving placeholders in string: {{Selectors.CreateCupon}}
2025-08-11 09:52:55.429 [Debug] Resolved string: id=CCoponID
2025-08-11 09:52:55.429 [Debug] Resolving placeholders in string: Click on Continue button
2025-08-11 09:52:55.429 [Debug] Resolved string: Click on Continue button
2025-08-11 09:52:55.429 [Debug] Resolving placeholders in string: {{Selectors.CuntinueConfirmCupon}}
2025-08-11 09:52:55.429 [Debug] Resolved string: id=RequestRedemptionConfrimationSbmtBtn
2025-08-11 09:52:55.429 [Debug] Resolving placeholders in string: Ensure that it redirected to the token page
2025-08-11 09:52:55.429 [Debug] Resolved string: Ensure that it redirected to the token page
2025-08-11 09:52:55.429 [Debug] Resolving placeholders in string: {{Selectors.OTPbox}}
2025-08-11 09:52:55.429 [Debug] Resolved string: id=OTPInpt
2025-08-11 09:52:55.429 [Info] Successfully resolved parameters for test case: Redeem less than 10000 points
2025-08-11 09:52:55.429 [Debug] Loading test case: WorkSet01/TestCases/021.HappyPoints/32.json
2025-08-11 09:52:55.429 [Info] Loading test case from: WorkSet01/TestCases/021.HappyPoints/32.json
2025-08-11 09:52:55.429 [Info] Attempting to load configuration file: WorkSet01/TestCases/021.HappyPoints/32.json
2025-08-11 09:52:55.429 [Info] Successfully loaded configuration file: WorkSet01/TestCases/021.HappyPoints/32.json
2025-08-11 09:52:55.430 [Info] No filtering configuration found. Including test case 'Verify That User Can't Redeem Happy Points Using Wrong SMS'.
2025-08-11 09:52:55.430 [Debug] Resolving parameters for test case: Verify That User Can't Redeem Happy Points Using Wrong SMS
2025-08-11 09:52:55.431 [Debug] Merging params
2025-08-11 09:52:55.431 [Debug] Merged basic params
2025-08-11 09:52:55.431 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-11 09:52:55.431 [Debug] Merged SpecialEnvParams params
2025-08-11 09:52:55.431 [Debug] Merged params
2025-08-11 09:52:55.431 [Debug] Loading parameters from reference file: WorkSet01/Params/HappyPointsParams.json
2025-08-11 09:52:55.431 [Info] Attempting to load configuration file: WorkSet01/Params/HappyPointsParams.json
2025-08-11 09:52:55.432 [Info] Successfully loaded configuration file: WorkSet01/Params/HappyPointsParams.json
2025-08-11 09:52:55.432 [Debug] Merging params
2025-08-11 09:52:55.432 [Debug] Merged basic params
2025-08-11 09:52:55.432 [Debug] Merged params
2025-08-11 09:52:55.432 [Debug] Merging params
2025-08-11 09:52:55.432 [Debug] Merging params
2025-08-11 09:52:55.432 [Debug] Resolving placeholders in test steps for test case: Verify That User Can't Redeem Happy Points Using Wrong SMS
2025-08-11 09:52:55.432 [Debug] Resolving TestCaseReference in step: 
2025-08-11 09:52:55.432 [Info] Loading test case from: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-11 09:52:55.432 [Info] Attempting to load configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-11 09:52:55.432 [Info] Successfully loaded configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-11 09:52:55.432 [Debug] Resolving parameters for test case: Login
2025-08-11 09:52:55.432 [Debug] Merging params
2025-08-11 09:52:55.433 [Debug] Merged basic params
2025-08-11 09:52:55.433 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-11 09:52:55.433 [Debug] Merged SpecialEnvParams params
2025-08-11 09:52:55.433 [Debug] Merged params
2025-08-11 09:52:55.433 [Debug] Loading parameters from reference file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-11 09:52:55.433 [Info] Attempting to load configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-11 09:52:55.434 [Info] Successfully loaded configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-11 09:52:55.434 [Debug] Merging params
2025-08-11 09:52:55.434 [Debug] Merged basic params
2025-08-11 09:52:55.434 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-11 09:52:55.434 [Debug] Merged SpecialEnvParams params
2025-08-11 09:52:55.434 [Debug] Merged params
2025-08-11 09:52:55.434 [Debug] Merging params
2025-08-11 09:52:55.434 [Debug] Merging params
2025-08-11 09:52:55.434 [Debug] Resolving placeholders in test steps for test case: Login
2025-08-11 09:52:55.434 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-11 09:52:55.434 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-11 09:52:55.434 [Debug] Resolving placeholders in string: {{Selectors.FinishButton}}
2025-08-11 09:52:55.434 [Debug] Resolved string: id=finish
2025-08-11 09:52:55.434 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-11 09:52:55.434 [Debug] Resolved string: Click on Login Button
2025-08-11 09:52:55.434 [Debug] Resolving placeholders in string: {{Selectors.LoginButton}}
2025-08-11 09:52:55.435 [Debug] Resolved string: id=LoginBtn
2025-08-11 09:52:55.435 [Debug] Resolving placeholders in string: Type the User Name
2025-08-11 09:52:55.435 [Debug] Resolved string: Type the User Name
2025-08-11 09:52:55.435 [Debug] Resolving placeholders in string: {{Selectors.UserNameField}}
2025-08-11 09:52:55.435 [Debug] Resolved string: id=UserName
2025-08-11 09:52:55.435 [Debug] Resolving placeholders in string: {{TestData.UserName}}
2025-08-11 09:52:55.435 [Debug] Resolved string: Mm_azmy
2025-08-11 09:52:55.435 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-11 09:52:55.435 [Debug] Resolved string: Click on Continue Button
2025-08-11 09:52:55.435 [Debug] Resolving placeholders in string: {{Selectors.ContinueButton}}
2025-08-11 09:52:55.436 [Debug] Resolved string: id=btn
2025-08-11 09:52:55.436 [Debug] Resolving placeholders in string: Type the Password
2025-08-11 09:52:55.436 [Debug] Resolved string: Type the Password
2025-08-11 09:52:55.436 [Debug] Resolving placeholders in string: {{Selectors.PasswordField}}
2025-08-11 09:52:55.436 [Debug] Resolved string: id=Password
2025-08-11 09:52:55.436 [Debug] Resolving placeholders in string: $('[{{Selectors.PasswordField}}]').val('{{TestData.Password}}')
2025-08-11 09:52:55.436 [Debug] Resolved string: $('[id=Password]').val('Asdf2025')
2025-08-11 09:52:55.436 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-11 09:52:55.436 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-11 09:52:55.436 [Debug] Resolving placeholders in string: {{Selectors.ChallengeQuestionField}}
2025-08-11 09:52:55.436 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-11 09:52:55.436 [Debug] Resolving placeholders in string: {{TestData.ChallengeAnswer}}
2025-08-11 09:52:55.436 [Debug] Resolved string: armada
2025-08-11 09:52:55.436 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-11 09:52:55.436 [Debug] Resolved string: Click on the Login Button
2025-08-11 09:52:55.436 [Debug] Resolving placeholders in string: {{Selectors.LoginAuthButton}}
2025-08-11 09:52:55.436 [Debug] Resolved string: id=LoginAuthBtn
2025-08-11 09:52:55.436 [Info] Successfully resolved parameters for test case: Login
2025-08-11 09:52:55.436 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-11 09:52:55.436 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-11 09:52:55.436 [Debug] Resolving placeholders in string: id=finish
2025-08-11 09:52:55.436 [Debug] Resolved string: id=finish
2025-08-11 09:52:55.436 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-11 09:52:55.436 [Debug] Resolved string: Click on Login Button
2025-08-11 09:52:55.436 [Debug] Resolving placeholders in string: id=LoginBtn
2025-08-11 09:52:55.437 [Debug] Resolved string: id=LoginBtn
2025-08-11 09:52:55.437 [Debug] Resolving placeholders in string: Type the User Name
2025-08-11 09:52:55.437 [Debug] Resolved string: Type the User Name
2025-08-11 09:52:55.437 [Debug] Resolving placeholders in string: id=UserName
2025-08-11 09:52:55.437 [Debug] Resolved string: id=UserName
2025-08-11 09:52:55.437 [Debug] Resolving placeholders in string: Mm_azmy
2025-08-11 09:52:55.437 [Debug] Resolved string: Mm_azmy
2025-08-11 09:52:55.438 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-11 09:52:55.438 [Debug] Resolved string: Click on Continue Button
2025-08-11 09:52:55.438 [Debug] Resolving placeholders in string: id=btn
2025-08-11 09:52:55.438 [Debug] Resolved string: id=btn
2025-08-11 09:52:55.438 [Debug] Resolving placeholders in string: Type the Password
2025-08-11 09:52:55.438 [Debug] Resolved string: Type the Password
2025-08-11 09:52:55.438 [Debug] Resolving placeholders in string: id=Password
2025-08-11 09:52:55.438 [Debug] Resolved string: id=Password
2025-08-11 09:52:55.438 [Debug] Resolving placeholders in string: $('[id=Password]').val('Asdf2025')
2025-08-11 09:52:55.438 [Debug] Resolved string: $('[id=Password]').val('Asdf2025')
2025-08-11 09:52:55.438 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-11 09:52:55.438 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-11 09:52:55.438 [Debug] Resolving placeholders in string: id=ChallengeQuestionAnswer
2025-08-11 09:52:55.438 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-11 09:52:55.438 [Debug] Resolving placeholders in string: armada
2025-08-11 09:52:55.439 [Debug] Resolved string: armada
2025-08-11 09:52:55.439 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-11 09:52:55.439 [Debug] Resolved string: Click on the Login Button
2025-08-11 09:52:55.439 [Debug] Resolving placeholders in string: id=LoginAuthBtn
2025-08-11 09:52:55.439 [Debug] Resolved string: id=LoginAuthBtn
2025-08-11 09:52:55.439 [Debug] Resolving placeholders in string: Click on Happy points option from side menu
2025-08-11 09:52:55.439 [Debug] Resolved string: Click on Happy points option from side menu
2025-08-11 09:52:55.439 [Debug] Resolving placeholders in string: {{Selectors.HappyPointsOption}}
2025-08-11 09:52:55.439 [Debug] Resolved string: xpath=//div[@class='mainmenuitems']//label[contains(text(), 'Happy Points')]
2025-08-11 09:52:55.439 [Debug] Resolving placeholders in string: Click on Redeem Online button
2025-08-11 09:52:55.439 [Debug] Resolved string: Click on Redeem Online button
2025-08-11 09:52:55.439 [Debug] Resolving placeholders in string: {{Selectors.RedeemOnline}}
2025-08-11 09:52:55.439 [Debug] Resolved string: css=#BuyOnlineBTN > div
2025-08-11 09:52:55.439 [Debug] Resolving placeholders in string: Choose a merchant
2025-08-11 09:52:55.439 [Debug] Resolved string: Choose a merchant
2025-08-11 09:52:55.439 [Debug] Resolving placeholders in string: {{Selectors.SelectMerchant}}
2025-08-11 09:52:55.439 [Debug] Resolved string: css=#RequestItem0
2025-08-11 09:52:55.439 [Debug] Resolving placeholders in string: Click Redeem button
2025-08-11 09:52:55.439 [Debug] Resolved string: Click Redeem button
2025-08-11 09:52:55.439 [Debug] Resolving placeholders in string: {{Selectors.RedeemButton}}
2025-08-11 09:52:55.439 [Debug] Resolved string: id=RedeemID
2025-08-11 09:52:55.439 [Debug] Resolving placeholders in string: Enter the number of points to redeem
2025-08-11 09:52:55.440 [Debug] Resolved string: Enter the number of points to redeem
2025-08-11 09:52:55.440 [Debug] Resolving placeholders in string: {{Selectors.PointsTextbox}}
2025-08-11 09:52:55.440 [Debug] Resolved string: id=redeempointsID
2025-08-11 09:52:55.440 [Debug] Resolving placeholders in string: 1000
2025-08-11 09:52:55.440 [Debug] Resolved string: 1000
2025-08-11 09:52:55.440 [Debug] Resolving placeholders in string: Click on create cupon button
2025-08-11 09:52:55.440 [Debug] Resolved string: Click on create cupon button
2025-08-11 09:52:55.440 [Debug] Resolving placeholders in string: {{Selectors.CreateCupon}}
2025-08-11 09:52:55.440 [Debug] Resolved string: id=CCoponID
2025-08-11 09:52:55.441 [Debug] Resolving placeholders in string: Click on Continue button
2025-08-11 09:52:55.441 [Debug] Resolved string: Click on Continue button
2025-08-11 09:52:55.441 [Debug] Resolving placeholders in string: {{Selectors.CuntinueConfirmCupon}}
2025-08-11 09:52:55.441 [Debug] Resolved string: id=RequestRedemptionConfrimationSbmtBtn
2025-08-11 09:52:55.441 [Debug] Resolving placeholders in string: Enter wrong OTP
2025-08-11 09:52:55.441 [Debug] Resolved string: Enter wrong OTP
2025-08-11 09:52:55.441 [Debug] Resolving placeholders in string: {{Selectors.OTPbox}}
2025-08-11 09:52:55.441 [Debug] Resolved string: id=OTPInpt
2025-08-11 09:52:55.441 [Debug] Resolving placeholders in string: 0000000
2025-08-11 09:52:55.441 [Debug] Resolved string: 0000000
2025-08-11 09:52:55.441 [Debug] Resolving placeholders in string: Click on Confirm OTP button
2025-08-11 09:52:55.441 [Debug] Resolved string: Click on Confirm OTP button
2025-08-11 09:52:55.441 [Debug] Resolving placeholders in string: {{Selectors.OTPconfirmButton}}
2025-08-11 09:52:55.442 [Debug] Resolved string: id=ConfirmSbmtBtn
2025-08-11 09:52:55.442 [Debug] Resolving placeholders in string: Assert the error message: 
2025-08-11 09:52:55.442 [Debug] Resolved string: Assert the error message: 
2025-08-11 09:52:55.442 [Debug] Resolving placeholders in string: {{Selectors.popupMessage}}
2025-08-11 09:52:55.442 [Debug] Resolved string: id=popup_message
2025-08-11 09:52:55.442 [Debug] Resolving placeholders in string: Wrong activation code
2025-08-11 09:52:55.442 [Debug] Resolved string: Wrong activation code
2025-08-11 09:52:55.442 [Info] Successfully resolved parameters for test case: Verify That User Can't Redeem Happy Points Using Wrong SMS
2025-08-11 09:52:55.442 [Debug] Loading test case: WorkSet01/TestCases/021.HappyPoints/157-MorePointsThanAvailable.json
2025-08-11 09:52:55.442 [Info] Loading test case from: WorkSet01/TestCases/021.HappyPoints/157-MorePointsThanAvailable.json
2025-08-11 09:52:55.442 [Info] Attempting to load configuration file: WorkSet01/TestCases/021.HappyPoints/157-MorePointsThanAvailable.json
2025-08-11 09:52:55.453 [Info] Successfully loaded configuration file: WorkSet01/TestCases/021.HappyPoints/157-MorePointsThanAvailable.json
2025-08-11 09:52:55.453 [Info] No filtering configuration found. Including test case 'Redeem Online - input points more than the available'.
2025-08-11 09:52:55.453 [Debug] Resolving parameters for test case: Redeem Online - input points more than the available
2025-08-11 09:52:55.453 [Debug] Merging params
2025-08-11 09:52:55.453 [Debug] Merged basic params
2025-08-11 09:52:55.453 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-11 09:52:55.453 [Debug] Merged SpecialEnvParams params
2025-08-11 09:52:55.453 [Debug] Merged params
2025-08-11 09:52:55.454 [Debug] Loading parameters from reference file: WorkSet01/Params/HappyPointsParams.json
2025-08-11 09:52:55.455 [Info] Attempting to load configuration file: WorkSet01/Params/HappyPointsParams.json
2025-08-11 09:52:55.455 [Info] Successfully loaded configuration file: WorkSet01/Params/HappyPointsParams.json
2025-08-11 09:52:55.455 [Debug] Merging params
2025-08-11 09:52:55.455 [Debug] Merged basic params
2025-08-11 09:52:55.455 [Debug] Merged params
2025-08-11 09:52:55.455 [Debug] Merging params
2025-08-11 09:52:55.455 [Debug] Merging params
2025-08-11 09:52:55.455 [Debug] Resolving placeholders in test steps for test case: Redeem Online - input points more than the available
2025-08-11 09:52:55.455 [Debug] Resolving TestCaseReference in step: 
2025-08-11 09:52:55.455 [Info] Loading test case from: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-11 09:52:55.455 [Info] Attempting to load configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-11 09:52:55.456 [Info] Successfully loaded configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-11 09:52:55.456 [Debug] Resolving parameters for test case: Login
2025-08-11 09:52:55.456 [Debug] Merging params
2025-08-11 09:52:55.456 [Debug] Merged basic params
2025-08-11 09:52:55.456 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-11 09:52:55.456 [Debug] Merged SpecialEnvParams params
2025-08-11 09:52:55.456 [Debug] Merged params
2025-08-11 09:52:55.456 [Debug] Loading parameters from reference file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-11 09:52:55.457 [Info] Attempting to load configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-11 09:52:55.457 [Info] Successfully loaded configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-11 09:52:55.458 [Debug] Merging params
2025-08-11 09:52:55.458 [Debug] Merged basic params
2025-08-11 09:52:55.458 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-11 09:52:55.458 [Debug] Merged SpecialEnvParams params
2025-08-11 09:52:55.458 [Debug] Merged params
2025-08-11 09:52:55.458 [Debug] Merging params
2025-08-11 09:52:55.458 [Debug] Merging params
2025-08-11 09:52:55.458 [Debug] Resolving placeholders in test steps for test case: Login
2025-08-11 09:52:55.458 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-11 09:52:55.458 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-11 09:52:55.458 [Debug] Resolving placeholders in string: {{Selectors.FinishButton}}
2025-08-11 09:52:55.458 [Debug] Resolved string: id=finish
2025-08-11 09:52:55.458 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-11 09:52:55.459 [Debug] Resolved string: Click on Login Button
2025-08-11 09:52:55.459 [Debug] Resolving placeholders in string: {{Selectors.LoginButton}}
2025-08-11 09:52:55.459 [Debug] Resolved string: id=LoginBtn
2025-08-11 09:52:55.459 [Debug] Resolving placeholders in string: Type the User Name
2025-08-11 09:52:55.459 [Debug] Resolved string: Type the User Name
2025-08-11 09:52:55.459 [Debug] Resolving placeholders in string: {{Selectors.UserNameField}}
2025-08-11 09:52:55.459 [Debug] Resolved string: id=UserName
2025-08-11 09:52:55.459 [Debug] Resolving placeholders in string: {{TestData.UserName}}
2025-08-11 09:52:55.459 [Debug] Resolved string: Mm_azmy
2025-08-11 09:52:55.459 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-11 09:52:55.460 [Debug] Resolved string: Click on Continue Button
2025-08-11 09:52:55.460 [Debug] Resolving placeholders in string: {{Selectors.ContinueButton}}
2025-08-11 09:52:55.460 [Debug] Resolved string: id=btn
2025-08-11 09:52:55.460 [Debug] Resolving placeholders in string: Type the Password
2025-08-11 09:52:55.460 [Debug] Resolved string: Type the Password
2025-08-11 09:52:55.460 [Debug] Resolving placeholders in string: {{Selectors.PasswordField}}
2025-08-11 09:52:55.460 [Debug] Resolved string: id=Password
2025-08-11 09:52:55.460 [Debug] Resolving placeholders in string: $('[{{Selectors.PasswordField}}]').val('{{TestData.Password}}')
2025-08-11 09:52:55.460 [Debug] Resolved string: $('[id=Password]').val('Asdf2025')
2025-08-11 09:52:55.461 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-11 09:52:55.461 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-11 09:52:55.461 [Debug] Resolving placeholders in string: {{Selectors.ChallengeQuestionField}}
2025-08-11 09:52:55.461 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-11 09:52:55.461 [Debug] Resolving placeholders in string: {{TestData.ChallengeAnswer}}
2025-08-11 09:52:55.461 [Debug] Resolved string: armada
2025-08-11 09:52:55.461 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-11 09:52:55.468 [Debug] Resolved string: Click on the Login Button
2025-08-11 09:52:55.468 [Debug] Resolving placeholders in string: {{Selectors.LoginAuthButton}}
2025-08-11 09:52:55.468 [Debug] Resolved string: id=LoginAuthBtn
2025-08-11 09:52:55.468 [Info] Successfully resolved parameters for test case: Login
2025-08-11 09:52:55.468 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-11 09:52:55.468 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-11 09:52:55.468 [Debug] Resolving placeholders in string: id=finish
2025-08-11 09:52:55.468 [Debug] Resolved string: id=finish
2025-08-11 09:52:55.468 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-11 09:52:55.468 [Debug] Resolved string: Click on Login Button
2025-08-11 09:52:55.468 [Debug] Resolving placeholders in string: id=LoginBtn
2025-08-11 09:52:55.468 [Debug] Resolved string: id=LoginBtn
2025-08-11 09:52:55.468 [Debug] Resolving placeholders in string: Type the User Name
2025-08-11 09:52:55.468 [Debug] Resolved string: Type the User Name
2025-08-11 09:52:55.468 [Debug] Resolving placeholders in string: id=UserName
2025-08-11 09:52:55.468 [Debug] Resolved string: id=UserName
2025-08-11 09:52:55.468 [Debug] Resolving placeholders in string: Mm_azmy
2025-08-11 09:52:55.468 [Debug] Resolved string: Mm_azmy
2025-08-11 09:52:55.468 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-11 09:52:55.468 [Debug] Resolved string: Click on Continue Button
2025-08-11 09:52:55.468 [Debug] Resolving placeholders in string: id=btn
2025-08-11 09:52:55.469 [Debug] Resolved string: id=btn
2025-08-11 09:52:55.469 [Debug] Resolving placeholders in string: Type the Password
2025-08-11 09:52:55.469 [Debug] Resolved string: Type the Password
2025-08-11 09:52:55.469 [Debug] Resolving placeholders in string: id=Password
2025-08-11 09:52:55.469 [Debug] Resolved string: id=Password
2025-08-11 09:52:55.469 [Debug] Resolving placeholders in string: $('[id=Password]').val('Asdf2025')
2025-08-11 09:52:55.469 [Debug] Resolved string: $('[id=Password]').val('Asdf2025')
2025-08-11 09:52:55.469 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-11 09:52:55.469 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-11 09:52:55.469 [Debug] Resolving placeholders in string: id=ChallengeQuestionAnswer
2025-08-11 09:52:55.470 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-11 09:52:55.470 [Debug] Resolving placeholders in string: armada
2025-08-11 09:52:55.470 [Debug] Resolved string: armada
2025-08-11 09:52:55.473 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-11 09:52:55.473 [Debug] Resolved string: Click on the Login Button
2025-08-11 09:52:55.473 [Debug] Resolving placeholders in string: id=LoginAuthBtn
2025-08-11 09:52:55.473 [Debug] Resolved string: id=LoginAuthBtn
2025-08-11 09:52:55.473 [Debug] Resolving placeholders in string: Click on Happy points option from side menu
2025-08-11 09:52:55.473 [Debug] Resolved string: Click on Happy points option from side menu
2025-08-11 09:52:55.473 [Debug] Resolving placeholders in string: {{Selectors.HappyPointsOption}}
2025-08-11 09:52:55.473 [Debug] Resolved string: xpath=//div[@class='mainmenuitems']//label[contains(text(), 'Happy Points')]
2025-08-11 09:52:55.473 [Debug] Resolving placeholders in string: Click on Redeem Online button
2025-08-11 09:52:55.473 [Debug] Resolved string: Click on Redeem Online button
2025-08-11 09:52:55.473 [Debug] Resolving placeholders in string: {{Selectors.RedeemOnline}}
2025-08-11 09:52:55.473 [Debug] Resolved string: css=#BuyOnlineBTN > div
2025-08-11 09:52:55.473 [Debug] Resolving placeholders in string: Choose a merchant
2025-08-11 09:52:55.473 [Debug] Resolved string: Choose a merchant
2025-08-11 09:52:55.473 [Debug] Resolving placeholders in string: {{Selectors.SelectMerchant}}
2025-08-11 09:52:55.473 [Debug] Resolved string: css=#RequestItem0
2025-08-11 09:52:55.473 [Debug] Resolving placeholders in string: Click Redeem button
2025-08-11 09:52:55.473 [Debug] Resolved string: Click Redeem button
2025-08-11 09:52:55.473 [Debug] Resolving placeholders in string: {{Selectors.RedeemButton}}
2025-08-11 09:52:55.473 [Debug] Resolved string: id=RedeemID
2025-08-11 09:52:55.473 [Debug] Resolving placeholders in string: Enter the number of points to redeem
2025-08-11 09:52:55.473 [Debug] Resolved string: Enter the number of points to redeem
2025-08-11 09:52:55.473 [Debug] Resolving placeholders in string: {{Selectors.PointsTextbox}}
2025-08-11 09:52:55.473 [Debug] Resolved string: id=redeempointsID
2025-08-11 09:52:55.473 [Debug] Resolving placeholders in string: 50000
2025-08-11 09:52:55.473 [Debug] Resolved string: 50000
2025-08-11 09:52:55.473 [Debug] Resolving placeholders in string: Click on create cupon button and show the error message
2025-08-11 09:52:55.473 [Debug] Resolved string: Click on create cupon button and show the error message
2025-08-11 09:52:55.473 [Debug] Resolving placeholders in string: {{Selectors.CreateCupon}}
2025-08-11 09:52:55.473 [Debug] Resolved string: id=CCoponID
2025-08-11 09:52:55.473 [Info] Successfully resolved parameters for test case: Redeem Online - input points more than the available
2025-08-11 09:52:55.473 [Debug] Loading test case: WorkSet01/TestCases/03.Beneficiaries/Add/TC-255 AddingOtherCAEaccount.json
2025-08-11 09:52:55.475 [Info] Loading test case from: WorkSet01/TestCases/03.Beneficiaries/Add/TC-255 AddingOtherCAEaccount.json
2025-08-11 09:52:55.475 [Info] Attempting to load configuration file: WorkSet01/TestCases/03.Beneficiaries/Add/TC-255 AddingOtherCAEaccount.json
2025-08-11 09:52:55.489 [Info] Successfully loaded configuration file: WorkSet01/TestCases/03.Beneficiaries/Add/TC-255 AddingOtherCAEaccount.json
2025-08-11 09:52:55.491 [Info] No filtering configuration found. Including test case 'Add new beneficiary account'.
2025-08-11 09:52:55.492 [Debug] Resolving parameters for test case: Add new beneficiary account
2025-08-11 09:52:55.497 [Debug] Merging params
2025-08-11 09:52:55.497 [Debug] Merged basic params
2025-08-11 09:52:55.498 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-11 09:52:55.498 [Debug] Merged SpecialEnvParams params
2025-08-11 09:52:55.498 [Debug] Merged params
2025-08-11 09:52:55.498 [Debug] Loading parameters from reference file: WorkSet01/Params/BeneficiariesParams.json
2025-08-11 09:52:55.498 [Info] Attempting to load configuration file: WorkSet01/Params/BeneficiariesParams.json
2025-08-11 09:52:55.498 [Info] Successfully loaded configuration file: WorkSet01/Params/BeneficiariesParams.json
2025-08-11 09:52:55.499 [Debug] Merging params
2025-08-11 09:52:55.499 [Debug] Merged basic params
2025-08-11 09:52:55.499 [Debug] Merged params
2025-08-11 09:52:55.499 [Debug] Merging params
2025-08-11 09:52:55.499 [Debug] Merging params
2025-08-11 09:52:55.499 [Debug] Resolving placeholders in test steps for test case: Add new beneficiary account
2025-08-11 09:52:55.499 [Debug] Resolving TestCaseReference in step: 
2025-08-11 09:52:55.499 [Info] Loading test case from: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-11 09:52:55.499 [Info] Attempting to load configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-11 09:52:55.499 [Info] Successfully loaded configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-11 09:52:55.500 [Debug] Resolving parameters for test case: Login
2025-08-11 09:52:55.500 [Debug] Merging params
2025-08-11 09:52:55.500 [Debug] Merged basic params
2025-08-11 09:52:55.500 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-11 09:52:55.500 [Debug] Merged SpecialEnvParams params
2025-08-11 09:52:55.500 [Debug] Merged params
2025-08-11 09:52:55.500 [Debug] Loading parameters from reference file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-11 09:52:55.500 [Info] Attempting to load configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-11 09:52:55.500 [Info] Successfully loaded configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-11 09:52:55.501 [Debug] Merging params
2025-08-11 09:52:55.501 [Debug] Merged basic params
2025-08-11 09:52:55.501 [Debug] Merged params
2025-08-11 09:52:55.502 [Debug] Merging params
2025-08-11 09:52:55.503 [Debug] Merging params
2025-08-11 09:52:55.504 [Debug] Resolving placeholders in test steps for test case: Login
2025-08-11 09:52:55.505 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-11 09:52:55.505 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-11 09:52:55.513 [Debug] Resolving placeholders in string: {{Selectors.FinishButton}}
2025-08-11 09:52:55.513 [Debug] Resolved string: id=finish
2025-08-11 09:52:55.513 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-11 09:52:55.513 [Debug] Resolved string: Click on Login Button
2025-08-11 09:52:55.513 [Debug] Resolving placeholders in string: {{Selectors.LoginButton}}
2025-08-11 09:52:55.513 [Debug] Resolved string: id=LoginBtn
2025-08-11 09:52:55.513 [Debug] Resolving placeholders in string: Type the User Name
2025-08-11 09:52:55.513 [Debug] Resolved string: Type the User Name
2025-08-11 09:52:55.513 [Debug] Resolving placeholders in string: {{Selectors.UserNameField}}
2025-08-11 09:52:55.513 [Debug] Resolved string: id=UserName
2025-08-11 09:52:55.513 [Debug] Resolving placeholders in string: {{TestData.UserName}}
2025-08-11 09:52:55.514 [Debug] Resolved string: mahmoudsayed022
2025-08-11 09:52:55.514 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-11 09:52:55.514 [Debug] Resolved string: Click on Continue Button
2025-08-11 09:52:55.514 [Debug] Resolving placeholders in string: {{Selectors.ContinueButton}}
2025-08-11 09:52:55.514 [Debug] Resolved string: id=btn
2025-08-11 09:52:55.514 [Debug] Resolving placeholders in string: Type the Password
2025-08-11 09:52:55.514 [Debug] Resolved string: Type the Password
2025-08-11 09:52:55.514 [Debug] Resolving placeholders in string: {{Selectors.PasswordField}}
2025-08-11 09:52:55.514 [Debug] Resolved string: id=Password
2025-08-11 09:52:55.514 [Debug] Resolving placeholders in string: $('[{{Selectors.PasswordField}}]').val('{{TestData.Password}}')
2025-08-11 09:52:55.514 [Debug] Resolved string: $('[id=Password]').val('Password1')
2025-08-11 09:52:55.514 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-11 09:52:55.514 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-11 09:52:55.514 [Debug] Resolving placeholders in string: {{Selectors.ChallengeQuestionField}}
2025-08-11 09:52:55.514 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-11 09:52:55.514 [Debug] Resolving placeholders in string: {{TestData.ChallengeAnswer}}
2025-08-11 09:52:55.514 [Debug] Resolved string: bmw
2025-08-11 09:52:55.514 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-11 09:52:55.515 [Debug] Resolved string: Click on the Login Button
2025-08-11 09:52:55.516 [Debug] Resolving placeholders in string: {{Selectors.LoginAuthButton}}
2025-08-11 09:52:55.516 [Debug] Resolved string: id=LoginAuthBtn
2025-08-11 09:52:55.517 [Info] Successfully resolved parameters for test case: Login
2025-08-11 09:52:55.518 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-11 09:52:55.525 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-11 09:52:55.525 [Debug] Resolving placeholders in string: id=finish
2025-08-11 09:52:55.525 [Debug] Resolved string: id=finish
2025-08-11 09:52:55.525 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-11 09:52:55.525 [Debug] Resolved string: Click on Login Button
2025-08-11 09:52:55.525 [Debug] Resolving placeholders in string: id=LoginBtn
2025-08-11 09:52:55.526 [Debug] Resolved string: id=LoginBtn
2025-08-11 09:52:55.526 [Debug] Resolving placeholders in string: Type the User Name
2025-08-11 09:52:55.526 [Debug] Resolved string: Type the User Name
2025-08-11 09:52:55.526 [Debug] Resolving placeholders in string: id=UserName
2025-08-11 09:52:55.526 [Debug] Resolved string: id=UserName
2025-08-11 09:52:55.526 [Debug] Resolving placeholders in string: mahmoudsayed022
2025-08-11 09:52:55.526 [Debug] Resolved string: mahmoudsayed022
2025-08-11 09:52:55.526 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-11 09:52:55.526 [Debug] Resolved string: Click on Continue Button
2025-08-11 09:52:55.526 [Debug] Resolving placeholders in string: id=btn
2025-08-11 09:52:55.526 [Debug] Resolved string: id=btn
2025-08-11 09:52:55.526 [Debug] Resolving placeholders in string: Type the Password
2025-08-11 09:52:55.526 [Debug] Resolved string: Type the Password
2025-08-11 09:52:55.526 [Debug] Resolving placeholders in string: id=Password
2025-08-11 09:52:55.526 [Debug] Resolved string: id=Password
2025-08-11 09:52:55.526 [Debug] Resolving placeholders in string: $('[id=Password]').val('Password1')
2025-08-11 09:52:55.526 [Debug] Resolved string: $('[id=Password]').val('Password1')
2025-08-11 09:52:55.526 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-11 09:52:55.526 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-11 09:52:55.526 [Debug] Resolving placeholders in string: id=ChallengeQuestionAnswer
2025-08-11 09:52:55.526 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-11 09:52:55.526 [Debug] Resolving placeholders in string: bmw
2025-08-11 09:52:55.528 [Debug] Resolved string: bmw
2025-08-11 09:52:55.528 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-11 09:52:55.529 [Debug] Resolved string: Click on the Login Button
2025-08-11 09:52:55.529 [Debug] Resolving placeholders in string: id=LoginAuthBtn
2025-08-11 09:52:55.529 [Debug] Resolved string: id=LoginAuthBtn
2025-08-11 09:52:55.533 [Debug] Resolving placeholders in string: Click on change later button on the password expiration popup message
2025-08-11 09:52:55.534 [Debug] Resolved string: Click on change later button on the password expiration popup message
2025-08-11 09:52:55.534 [Debug] Resolving placeholders in string: {{Selectors.popupCancel}}
2025-08-11 09:52:55.534 [Debug] Resolved string: id=popup_cancel
2025-08-11 09:52:55.534 [Debug] Resolving placeholders in string: Click on biometric alert
2025-08-11 09:52:55.535 [Debug] Resolved string: Click on biometric alert
2025-08-11 09:52:55.535 [Debug] Resolving placeholders in string: {{Selectors.BioMetricAlert}}
2025-08-11 09:52:55.535 [Debug] Resolved string: css=#popbuttons > div > button.btn.Cancel.back_gradient2
2025-08-11 09:52:55.535 [Debug] Resolving placeholders in string: Execute JS code to activate token
2025-08-11 09:52:55.535 [Debug] Resolved string: Execute JS code to activate token
2025-08-11 09:52:55.535 [Debug] Resolving placeholders in string: Globals.ActivationState = 'ACTIVATED';
2025-08-11 09:52:55.535 [Debug] Resolved string: Globals.ActivationState = 'ACTIVATED';
2025-08-11 09:52:55.535 [Debug] Resolving placeholders in string: Click on Beneficiaries
2025-08-11 09:52:55.535 [Debug] Resolved string: Click on Beneficiaries
2025-08-11 09:52:55.535 [Debug] Resolving placeholders in string: {{Selectors.BeneficiariesToButton}}
2025-08-11 09:52:55.535 [Debug] Resolved string: xpath=//div[@class='mainmenuitems']//label[contains(text(), 'Beneficiaries')]
2025-08-11 09:52:55.535 [Debug] Resolving placeholders in string: Click on Other CAE Accounts
2025-08-11 09:52:55.535 [Debug] Resolved string: Click on Other CAE Accounts
2025-08-11 09:52:55.535 [Debug] Resolving placeholders in string: {{Selectors.OtherCAEAccounts}}
2025-08-11 09:52:55.535 [Debug] Resolved string: xpath=//div[@class='submenuitem']//label[contains(text(), 'Other CAE Accounts')]
2025-08-11 09:52:55.535 [Debug] Resolving placeholders in string: Click on Add Beneficiary
2025-08-11 09:52:55.535 [Debug] Resolved string: Click on Add Beneficiary
2025-08-11 09:52:55.535 [Debug] Resolving placeholders in string: {{Selectors.AddNewBeneficiaryButtonOtheCAEaccounts}}
2025-08-11 09:52:55.535 [Debug] Resolved string: id=AddDigitalBankBeneficiary
2025-08-11 09:52:55.535 [Debug] Resolving placeholders in string: Type the Nickname
2025-08-11 09:52:55.535 [Debug] Resolved string: Type the Nickname
2025-08-11 09:52:55.539 [Debug] Resolving placeholders in string: {{Selectors.BeneficiaryNameTextbox}}
2025-08-11 09:52:55.539 [Debug] Resolved string: id=BeneficiaryName
2025-08-11 09:52:55.541 [Debug] Resolving placeholders in string: OtherCAE_account_2
2025-08-11 09:52:55.541 [Debug] Resolved string: OtherCAE_account_2
2025-08-11 09:52:55.542 [Debug] Resolving placeholders in string: Type Account number
2025-08-11 09:52:55.550 [Debug] Resolved string: Type Account number
2025-08-11 09:52:55.550 [Debug] Resolving placeholders in string: {{Selectors.BeneficiaryAccountNumber}}
2025-08-11 09:52:55.550 [Debug] Resolved string: id=BeneficiaryAccountNumber
2025-08-11 09:52:55.550 [Debug] Resolving placeholders in string: ************** 
2025-08-11 09:52:55.550 [Debug] Resolved string: ************** 
2025-08-11 09:52:55.550 [Debug] Resolving placeholders in string: Click on Save button
2025-08-11 09:52:55.550 [Debug] Resolved string: Click on Save button
2025-08-11 09:52:55.550 [Debug] Resolving placeholders in string: {{Selectors.SaveButton}}
2025-08-11 09:52:55.550 [Debug] Resolved string: id=Save
2025-08-11 09:52:55.550 [Debug] Resolving placeholders in string: Click on continue
2025-08-11 09:52:55.550 [Debug] Resolved string: Click on continue
2025-08-11 09:52:55.550 [Debug] Resolving placeholders in string: {{Selectors.ContinueToSaveBeneficiary}}
2025-08-11 09:52:55.550 [Debug] Resolved string: id=DigitalBankBenfContinueDeleteBtn
2025-08-11 09:52:55.551 [Debug] Resolving placeholders in string: Enter Token number
2025-08-11 09:52:55.551 [Debug] Resolved string: Enter Token number
2025-08-11 09:52:55.551 [Debug] Resolving placeholders in string: {{Selectors.TokenInput}}
2025-08-11 09:52:55.551 [Debug] Resolved string: id=TokenNUMBER
2025-08-11 09:52:55.551 [Debug] Resolving placeholders in string: 123456
2025-08-11 09:52:55.551 [Debug] Resolved string: 123456
2025-08-11 09:52:55.551 [Debug] Resolving placeholders in string: Click on confirm and show the confirmation page
2025-08-11 09:52:55.551 [Debug] Resolved string: Click on confirm and show the confirmation page
2025-08-11 09:52:55.551 [Debug] Resolving placeholders in string: {{Selectors.btnTokenConfirm}}
2025-08-11 09:52:55.551 [Debug] Resolved string: id=btnTokenConfirm
2025-08-11 09:52:55.551 [Info] Successfully resolved parameters for test case: Add new beneficiary account
2025-08-11 09:52:55.551 [Debug] Loading test case: WorkSet01/TestCases/03.Beneficiaries/Add/TC-256 AddingOtherCAEcard.json
2025-08-11 09:52:55.552 [Info] Loading test case from: WorkSet01/TestCases/03.Beneficiaries/Add/TC-256 AddingOtherCAEcard.json
2025-08-11 09:52:55.553 [Info] Attempting to load configuration file: WorkSet01/TestCases/03.Beneficiaries/Add/TC-256 AddingOtherCAEcard.json
2025-08-11 09:52:55.554 [Info] Successfully loaded configuration file: WorkSet01/TestCases/03.Beneficiaries/Add/TC-256 AddingOtherCAEcard.json
2025-08-11 09:52:55.555 [Info] No filtering configuration found. Including test case 'Add new beneficiary card'.
2025-08-11 09:52:55.556 [Debug] Resolving parameters for test case: Add new beneficiary card
2025-08-11 09:52:55.562 [Debug] Merging params
2025-08-11 09:52:55.562 [Debug] Merged basic params
2025-08-11 09:52:55.562 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-11 09:52:55.562 [Debug] Merged SpecialEnvParams params
2025-08-11 09:52:55.562 [Debug] Merged params
2025-08-11 09:52:55.562 [Debug] Loading parameters from reference file: WorkSet01/Params/BeneficiariesParams.json
2025-08-11 09:52:55.562 [Info] Attempting to load configuration file: WorkSet01/Params/BeneficiariesParams.json
2025-08-11 09:52:55.562 [Info] Successfully loaded configuration file: WorkSet01/Params/BeneficiariesParams.json
2025-08-11 09:52:55.563 [Debug] Merging params
2025-08-11 09:52:55.563 [Debug] Merged basic params
2025-08-11 09:52:55.563 [Debug] Merged params
2025-08-11 09:52:55.563 [Debug] Merging params
2025-08-11 09:52:55.563 [Debug] Merging params
2025-08-11 09:52:55.563 [Debug] Resolving placeholders in test steps for test case: Add new beneficiary card
2025-08-11 09:52:55.563 [Debug] Resolving TestCaseReference in step: 
2025-08-11 09:52:55.563 [Info] Loading test case from: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-11 09:52:55.563 [Info] Attempting to load configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-11 09:52:55.563 [Info] Successfully loaded configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-11 09:52:55.563 [Debug] Resolving parameters for test case: Login
2025-08-11 09:52:55.563 [Debug] Merging params
2025-08-11 09:52:55.563 [Debug] Merged basic params
2025-08-11 09:52:55.564 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-11 09:52:55.564 [Debug] Merged SpecialEnvParams params
2025-08-11 09:52:55.564 [Debug] Merged params
2025-08-11 09:52:55.565 [Debug] Loading parameters from reference file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-11 09:52:55.565 [Info] Attempting to load configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-11 09:52:55.566 [Info] Successfully loaded configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-11 09:52:55.567 [Debug] Merging params
2025-08-11 09:52:55.568 [Debug] Merged basic params
2025-08-11 09:52:55.572 [Debug] Merged params
2025-08-11 09:52:55.572 [Debug] Merging params
2025-08-11 09:52:55.572 [Debug] Merging params
2025-08-11 09:52:55.572 [Debug] Resolving placeholders in test steps for test case: Login
2025-08-11 09:52:55.572 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-11 09:52:55.572 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-11 09:52:55.572 [Debug] Resolving placeholders in string: {{Selectors.FinishButton}}
2025-08-11 09:52:55.572 [Debug] Resolved string: id=finish
2025-08-11 09:52:55.572 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-11 09:52:55.572 [Debug] Resolved string: Click on Login Button
2025-08-11 09:52:55.572 [Debug] Resolving placeholders in string: {{Selectors.LoginButton}}
2025-08-11 09:52:55.572 [Debug] Resolved string: id=LoginBtn
2025-08-11 09:52:55.572 [Debug] Resolving placeholders in string: Type the User Name
2025-08-11 09:52:55.572 [Debug] Resolved string: Type the User Name
2025-08-11 09:52:55.572 [Debug] Resolving placeholders in string: {{Selectors.UserNameField}}
2025-08-11 09:52:55.572 [Debug] Resolved string: id=UserName
2025-08-11 09:52:55.572 [Debug] Resolving placeholders in string: {{TestData.UserName}}
2025-08-11 09:52:55.572 [Debug] Resolved string: mahmoudsayed022
2025-08-11 09:52:55.573 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-11 09:52:55.573 [Debug] Resolved string: Click on Continue Button
2025-08-11 09:52:55.573 [Debug] Resolving placeholders in string: {{Selectors.ContinueButton}}
2025-08-11 09:52:55.573 [Debug] Resolved string: id=btn
2025-08-11 09:52:55.573 [Debug] Resolving placeholders in string: Type the Password
2025-08-11 09:52:55.573 [Debug] Resolved string: Type the Password
2025-08-11 09:52:55.573 [Debug] Resolving placeholders in string: {{Selectors.PasswordField}}
2025-08-11 09:52:55.574 [Debug] Resolved string: id=Password
2025-08-11 09:52:55.574 [Debug] Resolving placeholders in string: $('[{{Selectors.PasswordField}}]').val('{{TestData.Password}}')
2025-08-11 09:52:55.575 [Debug] Resolved string: $('[id=Password]').val('Password1')
2025-08-11 09:52:55.575 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-11 09:52:55.578 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-11 09:52:55.578 [Debug] Resolving placeholders in string: {{Selectors.ChallengeQuestionField}}
2025-08-11 09:52:55.578 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-11 09:52:55.578 [Debug] Resolving placeholders in string: {{TestData.ChallengeAnswer}}
2025-08-11 09:52:55.578 [Debug] Resolved string: bmw
2025-08-11 09:52:55.578 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-11 09:52:55.578 [Debug] Resolved string: Click on the Login Button
2025-08-11 09:52:55.578 [Debug] Resolving placeholders in string: {{Selectors.LoginAuthButton}}
2025-08-11 09:52:55.578 [Debug] Resolved string: id=LoginAuthBtn
2025-08-11 09:52:55.579 [Info] Successfully resolved parameters for test case: Login
2025-08-11 09:52:55.579 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-11 09:52:55.579 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-11 09:52:55.579 [Debug] Resolving placeholders in string: id=finish
2025-08-11 09:52:55.579 [Debug] Resolved string: id=finish
2025-08-11 09:52:55.579 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-11 09:52:55.579 [Debug] Resolved string: Click on Login Button
2025-08-11 09:52:55.579 [Debug] Resolving placeholders in string: id=LoginBtn
2025-08-11 09:52:55.579 [Debug] Resolved string: id=LoginBtn
2025-08-11 09:52:55.580 [Debug] Resolving placeholders in string: Type the User Name
2025-08-11 09:52:55.580 [Debug] Resolved string: Type the User Name
2025-08-11 09:52:55.580 [Debug] Resolving placeholders in string: id=UserName
2025-08-11 09:52:55.580 [Debug] Resolved string: id=UserName
2025-08-11 09:52:55.580 [Debug] Resolving placeholders in string: mahmoudsayed022
2025-08-11 09:52:55.580 [Debug] Resolved string: mahmoudsayed022
2025-08-11 09:52:55.581 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-11 09:52:55.581 [Debug] Resolved string: Click on Continue Button
2025-08-11 09:52:55.581 [Debug] Resolving placeholders in string: id=btn
2025-08-11 09:52:55.581 [Debug] Resolved string: id=btn
2025-08-11 09:52:55.581 [Debug] Resolving placeholders in string: Type the Password
2025-08-11 09:52:55.581 [Debug] Resolved string: Type the Password
2025-08-11 09:52:55.582 [Debug] Resolving placeholders in string: id=Password
2025-08-11 09:52:55.582 [Debug] Resolved string: id=Password
2025-08-11 09:52:55.582 [Debug] Resolving placeholders in string: $('[id=Password]').val('Password1')
2025-08-11 09:52:55.582 [Debug] Resolved string: $('[id=Password]').val('Password1')
2025-08-11 09:52:55.583 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-11 09:52:55.583 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-11 09:52:55.583 [Debug] Resolving placeholders in string: id=ChallengeQuestionAnswer
2025-08-11 09:52:55.583 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-11 09:52:55.583 [Debug] Resolving placeholders in string: bmw
2025-08-11 09:52:55.584 [Debug] Resolved string: bmw
2025-08-11 09:52:55.584 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-11 09:52:55.584 [Debug] Resolved string: Click on the Login Button
2025-08-11 09:52:55.584 [Debug] Resolving placeholders in string: id=LoginAuthBtn
2025-08-11 09:52:55.584 [Debug] Resolved string: id=LoginAuthBtn
2025-08-11 09:52:55.584 [Debug] Resolving placeholders in string: Click on change later button on the password expiration popup message
2025-08-11 09:52:55.584 [Debug] Resolved string: Click on change later button on the password expiration popup message
2025-08-11 09:52:55.585 [Debug] Resolving placeholders in string: {{Selectors.popupCancel}}
2025-08-11 09:52:55.585 [Debug] Resolved string: id=popup_cancel
2025-08-11 09:52:55.585 [Debug] Resolving placeholders in string: Click on biometric alert
2025-08-11 09:52:55.585 [Debug] Resolved string: Click on biometric alert
2025-08-11 09:52:55.585 [Debug] Resolving placeholders in string: {{Selectors.BioMetricAlert}}
2025-08-11 09:52:55.585 [Debug] Resolved string: css=#popbuttons > div > button.btn.Cancel.back_gradient2
2025-08-11 09:52:55.585 [Debug] Resolving placeholders in string: Execute JS code to activate token
2025-08-11 09:52:55.585 [Debug] Resolved string: Execute JS code to activate token
2025-08-11 09:52:55.585 [Debug] Resolving placeholders in string: Globals.ActivationState = 'ACTIVATED';
2025-08-11 09:52:55.585 [Debug] Resolved string: Globals.ActivationState = 'ACTIVATED';
2025-08-11 09:52:55.585 [Debug] Resolving placeholders in string: Click on Beneficiaries
2025-08-11 09:52:55.585 [Debug] Resolved string: Click on Beneficiaries
2025-08-11 09:52:55.585 [Debug] Resolving placeholders in string: {{Selectors.BeneficiariesToButton}}
2025-08-11 09:52:55.585 [Debug] Resolved string: xpath=//div[@class='mainmenuitems']//label[contains(text(), 'Beneficiaries')]
2025-08-11 09:52:55.585 [Debug] Resolving placeholders in string: Click on Other CAE Cards
2025-08-11 09:52:55.585 [Debug] Resolved string: Click on Other CAE Cards
2025-08-11 09:52:55.586 [Debug] Resolving placeholders in string: {{Selectors.OtherCAECards}}
2025-08-11 09:52:55.586 [Debug] Resolved string: xpath=//div[@class='submenuitem']//label[contains(text(), 'Other CAE Cards')]
2025-08-11 09:52:55.586 [Debug] Resolving placeholders in string: Click on Add Beneficiary
2025-08-11 09:52:55.586 [Debug] Resolved string: Click on Add Beneficiary
2025-08-11 09:52:55.586 [Debug] Resolving placeholders in string: xpath=//button[text()='Add Beneficiary']
2025-08-11 09:52:55.586 [Debug] Resolved string: xpath=//button[text()='Add Beneficiary']
2025-08-11 09:52:55.586 [Debug] Resolving placeholders in string: Type the Nickname
2025-08-11 09:52:55.586 [Debug] Resolved string: Type the Nickname
2025-08-11 09:52:55.586 [Debug] Resolving placeholders in string: {{Selectors.BeneficiaryNameTextbox}}
2025-08-11 09:52:55.586 [Debug] Resolved string: id=BeneficiaryName
2025-08-11 09:52:55.586 [Debug] Resolving placeholders in string: OtherCAE_card_2
2025-08-11 09:52:55.586 [Debug] Resolved string: OtherCAE_card_2
2025-08-11 09:52:55.586 [Debug] Resolving placeholders in string: Type Card number
2025-08-11 09:52:55.586 [Debug] Resolved string: Type Card number
2025-08-11 09:52:55.586 [Debug] Resolving placeholders in string: {{Selectors.BeneficiaryAccountNumber}}
2025-08-11 09:52:55.587 [Debug] Resolved string: id=BeneficiaryAccountNumber
2025-08-11 09:52:55.587 [Debug] Resolving placeholders in string: ****************
2025-08-11 09:52:55.587 [Debug] Resolved string: ****************
2025-08-11 09:52:55.587 [Debug] Resolving placeholders in string: Click on Save button
2025-08-11 09:52:55.587 [Debug] Resolved string: Click on Save button
2025-08-11 09:52:55.587 [Debug] Resolving placeholders in string: {{Selectors.SaveButton}}
2025-08-11 09:52:55.587 [Debug] Resolved string: id=Save
2025-08-11 09:52:55.587 [Debug] Resolving placeholders in string: Click on continue
2025-08-11 09:52:55.587 [Debug] Resolved string: Click on continue
2025-08-11 09:52:55.587 [Debug] Resolving placeholders in string: {{Selectors.ContinueToSaveBeneficiary}}
2025-08-11 09:52:55.587 [Debug] Resolved string: id=DigitalBankBenfContinueDeleteBtn
2025-08-11 09:52:55.587 [Debug] Resolving placeholders in string: Enter Token number
2025-08-11 09:52:55.587 [Debug] Resolved string: Enter Token number
2025-08-11 09:52:55.587 [Debug] Resolving placeholders in string: {{Selectors.TokenInput}}
2025-08-11 09:52:55.587 [Debug] Resolved string: id=TokenNUMBER
2025-08-11 09:52:55.587 [Debug] Resolving placeholders in string: 123456
2025-08-11 09:52:55.587 [Debug] Resolved string: 123456
2025-08-11 09:52:55.587 [Debug] Resolving placeholders in string: Click on confirm and show the confirmation page
2025-08-11 09:52:55.587 [Debug] Resolved string: Click on confirm and show the confirmation page
2025-08-11 09:52:55.587 [Debug] Resolving placeholders in string: {{Selectors.btnTokenConfirm}}
2025-08-11 09:52:55.587 [Debug] Resolved string: id=btnTokenConfirm
2025-08-11 09:52:55.587 [Info] Successfully resolved parameters for test case: Add new beneficiary card
2025-08-11 09:52:55.588 [Debug] Loading test case: WorkSet01/TestCases/03.Beneficiaries/Verify/251-Existing Account beneficiaries displayed.json
2025-08-11 09:52:55.588 [Info] Loading test case from: WorkSet01/TestCases/03.Beneficiaries/Verify/251-Existing Account beneficiaries displayed.json
2025-08-11 09:52:55.588 [Info] Attempting to load configuration file: WorkSet01/TestCases/03.Beneficiaries/Verify/251-Existing Account beneficiaries displayed.json
2025-08-11 09:52:55.593 [Info] Successfully loaded configuration file: WorkSet01/TestCases/03.Beneficiaries/Verify/251-Existing Account beneficiaries displayed.json
2025-08-11 09:52:55.594 [Info] No filtering configuration found. Including test case 'Existing Account beneficiaries displayed'.
2025-08-11 09:52:55.594 [Debug] Resolving parameters for test case: Existing Account beneficiaries displayed
2025-08-11 09:52:55.594 [Debug] Merging params
2025-08-11 09:52:55.594 [Debug] Merged basic params
2025-08-11 09:52:55.594 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-11 09:52:55.594 [Debug] Merged SpecialEnvParams params
2025-08-11 09:52:55.594 [Debug] Merged params
2025-08-11 09:52:55.594 [Debug] Loading parameters from reference file: WorkSet01/Params/BeneficiariesParams.json
2025-08-11 09:52:55.594 [Info] Attempting to load configuration file: WorkSet01/Params/BeneficiariesParams.json
2025-08-11 09:52:55.594 [Info] Successfully loaded configuration file: WorkSet01/Params/BeneficiariesParams.json
2025-08-11 09:52:55.595 [Debug] Merging params
2025-08-11 09:52:55.595 [Debug] Merged basic params
2025-08-11 09:52:55.595 [Debug] Merged params
2025-08-11 09:52:55.595 [Debug] Merging params
2025-08-11 09:52:55.595 [Debug] Merging params
2025-08-11 09:52:55.595 [Debug] Resolving placeholders in test steps for test case: Existing Account beneficiaries displayed
2025-08-11 09:52:55.595 [Debug] Resolving TestCaseReference in step: 
2025-08-11 09:52:55.595 [Info] Loading test case from: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-11 09:52:55.595 [Info] Attempting to load configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-11 09:52:55.595 [Info] Successfully loaded configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-11 09:52:55.603 [Debug] Resolving parameters for test case: Login
2025-08-11 09:52:55.603 [Debug] Merging params
2025-08-11 09:52:55.604 [Debug] Merged basic params
2025-08-11 09:52:55.604 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-11 09:52:55.604 [Debug] Merged SpecialEnvParams params
2025-08-11 09:52:55.604 [Debug] Merged params
2025-08-11 09:52:55.604 [Debug] Loading parameters from reference file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-11 09:52:55.604 [Info] Attempting to load configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-11 09:52:55.604 [Info] Successfully loaded configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-11 09:52:55.604 [Debug] Merging params
2025-08-11 09:52:55.604 [Debug] Merged basic params
2025-08-11 09:52:55.604 [Debug] Merged params
2025-08-11 09:52:55.604 [Debug] Merging params
2025-08-11 09:52:55.605 [Debug] Merging params
2025-08-11 09:52:55.605 [Debug] Resolving placeholders in test steps for test case: Login
2025-08-11 09:52:55.605 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-11 09:52:55.605 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-11 09:52:55.605 [Debug] Resolving placeholders in string: {{Selectors.FinishButton}}
2025-08-11 09:52:55.605 [Debug] Resolved string: id=finish
2025-08-11 09:52:55.605 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-11 09:52:55.608 [Debug] Resolved string: Click on Login Button
2025-08-11 09:52:55.608 [Debug] Resolving placeholders in string: {{Selectors.LoginButton}}
2025-08-11 09:52:55.608 [Debug] Resolved string: id=LoginBtn
2025-08-11 09:52:55.608 [Debug] Resolving placeholders in string: Type the User Name
2025-08-11 09:52:55.608 [Debug] Resolved string: Type the User Name
2025-08-11 09:52:55.608 [Debug] Resolving placeholders in string: {{Selectors.UserNameField}}
2025-08-11 09:52:55.608 [Debug] Resolved string: id=UserName
2025-08-11 09:52:55.608 [Debug] Resolving placeholders in string: {{TestData.UserName}}
2025-08-11 09:52:55.608 [Debug] Resolved string: mahmoudsayed022
2025-08-11 09:52:55.608 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-11 09:52:55.608 [Debug] Resolved string: Click on Continue Button
2025-08-11 09:52:55.608 [Debug] Resolving placeholders in string: {{Selectors.ContinueButton}}
2025-08-11 09:52:55.608 [Debug] Resolved string: id=btn
2025-08-11 09:52:55.608 [Debug] Resolving placeholders in string: Type the Password
2025-08-11 09:52:55.609 [Debug] Resolved string: Type the Password
2025-08-11 09:52:55.609 [Debug] Resolving placeholders in string: {{Selectors.PasswordField}}
2025-08-11 09:52:55.609 [Debug] Resolved string: id=Password
2025-08-11 09:52:55.609 [Debug] Resolving placeholders in string: $('[{{Selectors.PasswordField}}]').val('{{TestData.Password}}')
2025-08-11 09:52:55.609 [Debug] Resolved string: $('[id=Password]').val('Password1')
2025-08-11 09:52:55.609 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-11 09:52:55.611 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-11 09:52:55.612 [Debug] Resolving placeholders in string: {{Selectors.ChallengeQuestionField}}
2025-08-11 09:52:55.613 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-11 09:52:55.613 [Debug] Resolving placeholders in string: {{TestData.ChallengeAnswer}}
2025-08-11 09:52:55.614 [Debug] Resolved string: bmw
2025-08-11 09:52:55.614 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-11 09:52:55.614 [Debug] Resolved string: Click on the Login Button
2025-08-11 09:52:55.614 [Debug] Resolving placeholders in string: {{Selectors.LoginAuthButton}}
2025-08-11 09:52:55.614 [Debug] Resolved string: id=LoginAuthBtn
2025-08-11 09:52:55.615 [Info] Successfully resolved parameters for test case: Login
2025-08-11 09:52:55.615 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-11 09:52:55.615 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-11 09:52:55.615 [Debug] Resolving placeholders in string: id=finish
2025-08-11 09:52:55.615 [Debug] Resolved string: id=finish
2025-08-11 09:52:55.615 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-11 09:52:55.615 [Debug] Resolved string: Click on Login Button
2025-08-11 09:52:55.615 [Debug] Resolving placeholders in string: id=LoginBtn
2025-08-11 09:52:55.615 [Debug] Resolved string: id=LoginBtn
2025-08-11 09:52:55.616 [Debug] Resolving placeholders in string: Type the User Name
2025-08-11 09:52:55.616 [Debug] Resolved string: Type the User Name
2025-08-11 09:52:55.616 [Debug] Resolving placeholders in string: id=UserName
2025-08-11 09:52:55.616 [Debug] Resolved string: id=UserName
2025-08-11 09:52:55.616 [Debug] Resolving placeholders in string: mahmoudsayed022
2025-08-11 09:52:55.616 [Debug] Resolved string: mahmoudsayed022
2025-08-11 09:52:55.617 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-11 09:52:55.617 [Debug] Resolved string: Click on Continue Button
2025-08-11 09:52:55.617 [Debug] Resolving placeholders in string: id=btn
2025-08-11 09:52:55.617 [Debug] Resolved string: id=btn
2025-08-11 09:52:55.617 [Debug] Resolving placeholders in string: Type the Password
2025-08-11 09:52:55.618 [Debug] Resolved string: Type the Password
2025-08-11 09:52:55.618 [Debug] Resolving placeholders in string: id=Password
2025-08-11 09:52:55.618 [Debug] Resolved string: id=Password
2025-08-11 09:52:55.618 [Debug] Resolving placeholders in string: $('[id=Password]').val('Password1')
2025-08-11 09:52:55.618 [Debug] Resolved string: $('[id=Password]').val('Password1')
2025-08-11 09:52:55.618 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-11 09:52:55.618 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-11 09:52:55.618 [Debug] Resolving placeholders in string: id=ChallengeQuestionAnswer
2025-08-11 09:52:55.618 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-11 09:52:55.618 [Debug] Resolving placeholders in string: bmw
2025-08-11 09:52:55.618 [Debug] Resolved string: bmw
2025-08-11 09:52:55.618 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-11 09:52:55.618 [Debug] Resolved string: Click on the Login Button
2025-08-11 09:52:55.618 [Debug] Resolving placeholders in string: id=LoginAuthBtn
2025-08-11 09:52:55.618 [Debug] Resolved string: id=LoginAuthBtn
2025-08-11 09:52:55.618 [Debug] Resolving placeholders in string: Click on change later button on the password expiration popup message
2025-08-11 09:52:55.618 [Debug] Resolved string: Click on change later button on the password expiration popup message
2025-08-11 09:52:55.619 [Debug] Resolving placeholders in string: {{Selectors.popupCancel}}
2025-08-11 09:52:55.619 [Debug] Resolved string: id=popup_cancel
2025-08-11 09:52:55.619 [Debug] Resolving placeholders in string: Click on biometric alert
2025-08-11 09:52:55.619 [Debug] Resolved string: Click on biometric alert
2025-08-11 09:52:55.619 [Debug] Resolving placeholders in string: {{Selectors.BioMetricAlert}}
2025-08-11 09:52:55.619 [Debug] Resolved string: css=#popbuttons > div > button.btn.Cancel.back_gradient2
2025-08-11 09:52:55.620 [Debug] Resolving placeholders in string: Execute JS code to activate token
2025-08-11 09:52:55.620 [Debug] Resolved string: Execute JS code to activate token
2025-08-11 09:52:55.620 [Debug] Resolving placeholders in string: Globals.ActivationState = 'ACTIVATED';
2025-08-11 09:52:55.620 [Debug] Resolved string: Globals.ActivationState = 'ACTIVATED';
2025-08-11 09:52:55.623 [Debug] Resolving placeholders in string: Click on Beneficiaries
2025-08-11 09:52:55.623 [Debug] Resolved string: Click on Beneficiaries
2025-08-11 09:52:55.623 [Debug] Resolving placeholders in string: {{Selectors.BeneficiariesToButton}}
2025-08-11 09:52:55.623 [Debug] Resolved string: xpath=//div[@class='mainmenuitems']//label[contains(text(), 'Beneficiaries')]
2025-08-11 09:52:55.623 [Debug] Resolving placeholders in string: Click on Other CAE Accounts
2025-08-11 09:52:55.624 [Debug] Resolved string: Click on Other CAE Accounts
2025-08-11 09:52:55.624 [Debug] Resolving placeholders in string: {{Selectors.OtherCAEAccounts}}
2025-08-11 09:52:55.624 [Debug] Resolved string: xpath=//div[@class='submenuitem']//label[contains(text(), 'Other CAE Accounts')]
2025-08-11 09:52:55.624 [Debug] Resolving placeholders in string: Check if the Account List appears
2025-08-11 09:52:55.624 [Debug] Resolved string: Check if the Account List appears
2025-08-11 09:52:55.624 [Debug] Resolving placeholders in string: {{Selectors.BeneficiaryHeader}}
2025-08-11 09:52:55.624 [Debug] Resolved string: id=BeneficiaryHeader
2025-08-11 09:52:55.624 [Info] Successfully resolved parameters for test case: Existing Account beneficiaries displayed
2025-08-11 09:52:55.624 [Debug] Loading test case: WorkSet01/TestCases/03.Beneficiaries/Verify/252-Existing Card beneficiaries displayed.json
2025-08-11 09:52:55.624 [Info] Loading test case from: WorkSet01/TestCases/03.Beneficiaries/Verify/252-Existing Card beneficiaries displayed.json
2025-08-11 09:52:55.624 [Info] Attempting to load configuration file: WorkSet01/TestCases/03.Beneficiaries/Verify/252-Existing Card beneficiaries displayed.json
2025-08-11 09:52:55.625 [Info] Successfully loaded configuration file: WorkSet01/TestCases/03.Beneficiaries/Verify/252-Existing Card beneficiaries displayed.json
2025-08-11 09:52:55.625 [Info] No filtering configuration found. Including test case 'Existing Card beneficiaries displayed'.
2025-08-11 09:52:55.625 [Debug] Resolving parameters for test case: Existing Card beneficiaries displayed
2025-08-11 09:52:55.625 [Debug] Merging params
2025-08-11 09:52:55.625 [Debug] Merged basic params
2025-08-11 09:52:55.625 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-11 09:52:55.625 [Debug] Merged SpecialEnvParams params
2025-08-11 09:52:55.626 [Debug] Merged params
2025-08-11 09:52:55.627 [Debug] Loading parameters from reference file: WorkSet01/Params/BeneficiariesParams.json
2025-08-11 09:52:55.627 [Info] Attempting to load configuration file: WorkSet01/Params/BeneficiariesParams.json
2025-08-11 09:52:55.628 [Info] Successfully loaded configuration file: WorkSet01/Params/BeneficiariesParams.json
2025-08-11 09:52:55.628 [Debug] Merging params
2025-08-11 09:52:55.628 [Debug] Merged basic params
2025-08-11 09:52:55.628 [Debug] Merged params
2025-08-11 09:52:55.629 [Debug] Merging params
2025-08-11 09:52:55.629 [Debug] Merging params
2025-08-11 09:52:55.629 [Debug] Resolving placeholders in test steps for test case: Existing Card beneficiaries displayed
2025-08-11 09:52:55.629 [Debug] Resolving TestCaseReference in step: 
2025-08-11 09:52:55.629 [Info] Loading test case from: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-11 09:52:55.629 [Info] Attempting to load configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-11 09:52:55.630 [Info] Successfully loaded configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-11 09:52:55.631 [Debug] Resolving parameters for test case: Login
2025-08-11 09:52:55.631 [Debug] Merging params
2025-08-11 09:52:55.631 [Debug] Merged basic params
2025-08-11 09:52:55.631 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-11 09:52:55.631 [Debug] Merged SpecialEnvParams params
2025-08-11 09:52:55.631 [Debug] Merged params
2025-08-11 09:52:55.631 [Debug] Loading parameters from reference file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-11 09:52:55.631 [Info] Attempting to load configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-11 09:52:55.631 [Info] Successfully loaded configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-11 09:52:55.632 [Debug] Merging params
2025-08-11 09:52:55.632 [Debug] Merged basic params
2025-08-11 09:52:55.632 [Debug] Merged params
2025-08-11 09:52:55.632 [Debug] Merging params
2025-08-11 09:52:55.632 [Debug] Merging params
2025-08-11 09:52:55.632 [Debug] Resolving placeholders in test steps for test case: Login
2025-08-11 09:52:55.632 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-11 09:52:55.632 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-11 09:52:55.632 [Debug] Resolving placeholders in string: {{Selectors.FinishButton}}
2025-08-11 09:52:55.632 [Debug] Resolved string: id=finish
2025-08-11 09:52:55.632 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-11 09:52:55.633 [Debug] Resolved string: Click on Login Button
2025-08-11 09:52:55.633 [Debug] Resolving placeholders in string: {{Selectors.LoginButton}}
2025-08-11 09:52:55.633 [Debug] Resolved string: id=LoginBtn
2025-08-11 09:52:55.633 [Debug] Resolving placeholders in string: Type the User Name
2025-08-11 09:52:55.633 [Debug] Resolved string: Type the User Name
2025-08-11 09:52:55.633 [Debug] Resolving placeholders in string: {{Selectors.UserNameField}}
2025-08-11 09:52:55.633 [Debug] Resolved string: id=UserName
2025-08-11 09:52:55.633 [Debug] Resolving placeholders in string: {{TestData.UserName}}
2025-08-11 09:52:55.633 [Debug] Resolved string: mahmoudsayed022
2025-08-11 09:52:55.633 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-11 09:52:55.633 [Debug] Resolved string: Click on Continue Button
2025-08-11 09:52:55.633 [Debug] Resolving placeholders in string: {{Selectors.ContinueButton}}
2025-08-11 09:52:55.633 [Debug] Resolved string: id=btn
2025-08-11 09:52:55.633 [Debug] Resolving placeholders in string: Type the Password
2025-08-11 09:52:55.633 [Debug] Resolved string: Type the Password
2025-08-11 09:52:55.633 [Debug] Resolving placeholders in string: {{Selectors.PasswordField}}
2025-08-11 09:52:55.633 [Debug] Resolved string: id=Password
2025-08-11 09:52:55.633 [Debug] Resolving placeholders in string: $('[{{Selectors.PasswordField}}]').val('{{TestData.Password}}')
2025-08-11 09:52:55.634 [Debug] Resolved string: $('[id=Password]').val('Password1')
2025-08-11 09:52:55.634 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-11 09:52:55.634 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-11 09:52:55.634 [Debug] Resolving placeholders in string: {{Selectors.ChallengeQuestionField}}
2025-08-11 09:52:55.634 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-11 09:52:55.634 [Debug] Resolving placeholders in string: {{TestData.ChallengeAnswer}}
2025-08-11 09:52:55.635 [Debug] Resolved string: bmw
2025-08-11 09:52:55.635 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-11 09:52:55.635 [Debug] Resolved string: Click on the Login Button
2025-08-11 09:52:55.636 [Debug] Resolving placeholders in string: {{Selectors.LoginAuthButton}}
2025-08-11 09:52:55.636 [Debug] Resolved string: id=LoginAuthBtn
2025-08-11 09:52:55.636 [Info] Successfully resolved parameters for test case: Login
2025-08-11 09:52:55.636 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-11 09:52:55.636 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-11 09:52:55.636 [Debug] Resolving placeholders in string: id=finish
2025-08-11 09:52:55.636 [Debug] Resolved string: id=finish
2025-08-11 09:52:55.636 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-11 09:52:55.636 [Debug] Resolved string: Click on Login Button
2025-08-11 09:52:55.636 [Debug] Resolving placeholders in string: id=LoginBtn
2025-08-11 09:52:55.636 [Debug] Resolved string: id=LoginBtn
2025-08-11 09:52:55.636 [Debug] Resolving placeholders in string: Type the User Name
2025-08-11 09:52:55.636 [Debug] Resolved string: Type the User Name
2025-08-11 09:52:55.636 [Debug] Resolving placeholders in string: id=UserName
2025-08-11 09:52:55.636 [Debug] Resolved string: id=UserName
2025-08-11 09:52:55.636 [Debug] Resolving placeholders in string: mahmoudsayed022
2025-08-11 09:52:55.636 [Debug] Resolved string: mahmoudsayed022
2025-08-11 09:52:55.636 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-11 09:52:55.637 [Debug] Resolved string: Click on Continue Button
2025-08-11 09:52:55.637 [Debug] Resolving placeholders in string: id=btn
2025-08-11 09:52:55.637 [Debug] Resolved string: id=btn
2025-08-11 09:52:55.637 [Debug] Resolving placeholders in string: Type the Password
2025-08-11 09:52:55.637 [Debug] Resolved string: Type the Password
2025-08-11 09:52:55.637 [Debug] Resolving placeholders in string: id=Password
2025-08-11 09:52:55.637 [Debug] Resolved string: id=Password
2025-08-11 09:52:55.637 [Debug] Resolving placeholders in string: $('[id=Password]').val('Password1')
2025-08-11 09:52:55.637 [Debug] Resolved string: $('[id=Password]').val('Password1')
2025-08-11 09:52:55.637 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-11 09:52:55.638 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-11 09:52:55.638 [Debug] Resolving placeholders in string: id=ChallengeQuestionAnswer
2025-08-11 09:52:55.638 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-11 09:52:55.638 [Debug] Resolving placeholders in string: bmw
2025-08-11 09:52:55.638 [Debug] Resolved string: bmw
2025-08-11 09:52:55.638 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-11 09:52:55.638 [Debug] Resolved string: Click on the Login Button
2025-08-11 09:52:55.638 [Debug] Resolving placeholders in string: id=LoginAuthBtn
2025-08-11 09:52:55.638 [Debug] Resolved string: id=LoginAuthBtn
2025-08-11 09:52:55.638 [Debug] Resolving placeholders in string: Click on change later button on the password expiration popup message
2025-08-11 09:52:55.638 [Debug] Resolved string: Click on change later button on the password expiration popup message
2025-08-11 09:52:55.638 [Debug] Resolving placeholders in string: {{Selectors.popupCancel}}
2025-08-11 09:52:55.638 [Debug] Resolved string: id=popup_cancel
2025-08-11 09:52:55.638 [Debug] Resolving placeholders in string: Click on biometric alert
2025-08-11 09:52:55.638 [Debug] Resolved string: Click on biometric alert
2025-08-11 09:52:55.638 [Debug] Resolving placeholders in string: {{Selectors.BioMetricAlert}}
2025-08-11 09:52:55.638 [Debug] Resolved string: css=#popbuttons > div > button.btn.Cancel.back_gradient2
2025-08-11 09:52:55.638 [Debug] Resolving placeholders in string: Execute JS code to activate token
2025-08-11 09:52:55.639 [Debug] Resolved string: Execute JS code to activate token
2025-08-11 09:52:55.639 [Debug] Resolving placeholders in string: Globals.ActivationState = 'ACTIVATED';
2025-08-11 09:52:55.639 [Debug] Resolved string: Globals.ActivationState = 'ACTIVATED';
2025-08-11 09:52:55.639 [Debug] Resolving placeholders in string: Click on Beneficiaries
2025-08-11 09:52:55.640 [Debug] Resolved string: Click on Beneficiaries
2025-08-11 09:52:55.640 [Debug] Resolving placeholders in string: {{Selectors.BeneficiariesToButton}}
2025-08-11 09:52:55.640 [Debug] Resolved string: xpath=//div[@class='mainmenuitems']//label[contains(text(), 'Beneficiaries')]
2025-08-11 09:52:55.640 [Debug] Resolving placeholders in string: Click on Other CAE Cards
2025-08-11 09:52:55.641 [Debug] Resolved string: Click on Other CAE Cards
2025-08-11 09:52:55.641 [Debug] Resolving placeholders in string: {{Selectors.OtherCAECards}}
2025-08-11 09:52:55.641 [Debug] Resolved string: xpath=//div[@class='submenuitem']//label[contains(text(), 'Other CAE Cards')]
2025-08-11 09:52:55.641 [Debug] Resolving placeholders in string: Check if the Account List appears
2025-08-11 09:52:55.641 [Debug] Resolved string: Check if the Account List appears
2025-08-11 09:52:55.641 [Debug] Resolving placeholders in string: {{Selectors.BeneficiaryHeader}}
2025-08-11 09:52:55.641 [Debug] Resolved string: id=BeneficiaryHeader
2025-08-11 09:52:55.641 [Info] Successfully resolved parameters for test case: Existing Card beneficiaries displayed
2025-08-11 09:52:55.641 [Debug] Loading test case: WorkSet01/TestCases/03.Beneficiaries/Verify/TC-145 Existing bankinsideegypt beneficiaries displayed.json
2025-08-11 09:52:55.641 [Info] Loading test case from: WorkSet01/TestCases/03.Beneficiaries/Verify/TC-145 Existing bankinsideegypt beneficiaries displayed.json
2025-08-11 09:52:55.641 [Info] Attempting to load configuration file: WorkSet01/TestCases/03.Beneficiaries/Verify/TC-145 Existing bankinsideegypt beneficiaries displayed.json
2025-08-11 09:52:55.655 [Info] Successfully loaded configuration file: WorkSet01/TestCases/03.Beneficiaries/Verify/TC-145 Existing bankinsideegypt beneficiaries displayed.json
2025-08-11 09:52:55.655 [Info] No filtering configuration found. Including test case 'Existing Banks Inside Egypt beneficiaries displayed'.
2025-08-11 09:52:55.655 [Debug] Resolving parameters for test case: Existing Banks Inside Egypt beneficiaries displayed
2025-08-11 09:52:55.655 [Debug] Merging params
2025-08-11 09:52:55.655 [Debug] Merged basic params
2025-08-11 09:52:55.658 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-11 09:52:55.658 [Debug] Merged SpecialEnvParams params
2025-08-11 09:52:55.658 [Debug] Merged params
2025-08-11 09:52:55.658 [Debug] Loading parameters from reference file: WorkSet01/Params/BeneficiariesParams.json
2025-08-11 09:52:55.658 [Info] Attempting to load configuration file: WorkSet01/Params/BeneficiariesParams.json
2025-08-11 09:52:55.658 [Info] Successfully loaded configuration file: WorkSet01/Params/BeneficiariesParams.json
2025-08-11 09:52:55.659 [Debug] Merging params
2025-08-11 09:52:55.659 [Debug] Merged basic params
2025-08-11 09:52:55.659 [Debug] Merged params
2025-08-11 09:52:55.659 [Debug] Merging params
2025-08-11 09:52:55.659 [Debug] Merging params
2025-08-11 09:52:55.659 [Debug] Resolving placeholders in test steps for test case: Existing Banks Inside Egypt beneficiaries displayed
2025-08-11 09:52:55.659 [Debug] Resolving TestCaseReference in step: 
2025-08-11 09:52:55.659 [Info] Loading test case from: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-11 09:52:55.659 [Info] Attempting to load configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-11 09:52:55.660 [Info] Successfully loaded configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-11 09:52:55.660 [Debug] Resolving parameters for test case: Login
2025-08-11 09:52:55.660 [Debug] Merging params
2025-08-11 09:52:55.660 [Debug] Merged basic params
2025-08-11 09:52:55.660 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-11 09:52:55.660 [Debug] Merged SpecialEnvParams params
2025-08-11 09:52:55.660 [Debug] Merged params
2025-08-11 09:52:55.661 [Debug] Loading parameters from reference file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-11 09:52:55.661 [Info] Attempting to load configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-11 09:52:55.661 [Info] Successfully loaded configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-11 09:52:55.661 [Debug] Merging params
2025-08-11 09:52:55.661 [Debug] Merged basic params
2025-08-11 09:52:55.661 [Debug] Merged params
2025-08-11 09:52:55.661 [Debug] Merging params
2025-08-11 09:52:55.661 [Debug] Merging params
2025-08-11 09:52:55.661 [Debug] Resolving placeholders in test steps for test case: Login
2025-08-11 09:52:55.661 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-11 09:52:55.662 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-11 09:52:55.662 [Debug] Resolving placeholders in string: {{Selectors.FinishButton}}
2025-08-11 09:52:55.662 [Debug] Resolved string: id=finish
2025-08-11 09:52:55.662 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-11 09:52:55.662 [Debug] Resolved string: Click on Login Button
2025-08-11 09:52:55.662 [Debug] Resolving placeholders in string: {{Selectors.LoginButton}}
2025-08-11 09:52:55.663 [Debug] Resolved string: id=LoginBtn
2025-08-11 09:52:55.663 [Debug] Resolving placeholders in string: Type the User Name
2025-08-11 09:52:55.663 [Debug] Resolved string: Type the User Name
2025-08-11 09:52:55.663 [Debug] Resolving placeholders in string: {{Selectors.UserNameField}}
2025-08-11 09:52:55.663 [Debug] Resolved string: id=UserName
2025-08-11 09:52:55.664 [Debug] Resolving placeholders in string: {{TestData.UserName}}
2025-08-11 09:52:55.664 [Debug] Resolved string: mahmoudsayed022
2025-08-11 09:52:55.664 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-11 09:52:55.664 [Debug] Resolved string: Click on Continue Button
2025-08-11 09:52:55.664 [Debug] Resolving placeholders in string: {{Selectors.ContinueButton}}
2025-08-11 09:52:55.665 [Debug] Resolved string: id=btn
2025-08-11 09:52:55.665 [Debug] Resolving placeholders in string: Type the Password
2025-08-11 09:52:55.665 [Debug] Resolved string: Type the Password
2025-08-11 09:52:55.665 [Debug] Resolving placeholders in string: {{Selectors.PasswordField}}
2025-08-11 09:52:55.665 [Debug] Resolved string: id=Password
2025-08-11 09:52:55.666 [Debug] Resolving placeholders in string: $('[{{Selectors.PasswordField}}]').val('{{TestData.Password}}')
2025-08-11 09:52:55.666 [Debug] Resolved string: $('[id=Password]').val('Password1')
2025-08-11 09:52:55.666 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-11 09:52:55.667 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-11 09:52:55.674 [Debug] Resolving placeholders in string: {{Selectors.ChallengeQuestionField}}
2025-08-11 09:52:55.674 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-11 09:52:55.674 [Debug] Resolving placeholders in string: {{TestData.ChallengeAnswer}}
2025-08-11 09:52:55.674 [Debug] Resolved string: bmw
2025-08-11 09:52:55.674 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-11 09:52:55.674 [Debug] Resolved string: Click on the Login Button
2025-08-11 09:52:55.674 [Debug] Resolving placeholders in string: {{Selectors.LoginAuthButton}}
2025-08-11 09:52:55.675 [Debug] Resolved string: id=LoginAuthBtn
2025-08-11 09:52:55.675 [Info] Successfully resolved parameters for test case: Login
2025-08-11 09:52:55.675 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-11 09:52:55.675 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-11 09:52:55.675 [Debug] Resolving placeholders in string: id=finish
2025-08-11 09:52:55.675 [Debug] Resolved string: id=finish
2025-08-11 09:52:55.675 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-11 09:52:55.675 [Debug] Resolved string: Click on Login Button
2025-08-11 09:52:55.675 [Debug] Resolving placeholders in string: id=LoginBtn
2025-08-11 09:52:55.682 [Debug] Resolved string: id=LoginBtn
2025-08-11 09:52:55.682 [Debug] Resolving placeholders in string: Type the User Name
2025-08-11 09:52:55.682 [Debug] Resolved string: Type the User Name
2025-08-11 09:52:55.682 [Debug] Resolving placeholders in string: id=UserName
2025-08-11 09:52:55.682 [Debug] Resolved string: id=UserName
2025-08-11 09:52:55.682 [Debug] Resolving placeholders in string: mahmoudsayed022
2025-08-11 09:52:55.682 [Debug] Resolved string: mahmoudsayed022
2025-08-11 09:52:55.682 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-11 09:52:55.682 [Debug] Resolved string: Click on Continue Button
2025-08-11 09:52:55.682 [Debug] Resolving placeholders in string: id=btn
2025-08-11 09:52:55.682 [Debug] Resolved string: id=btn
2025-08-11 09:52:55.682 [Debug] Resolving placeholders in string: Type the Password
2025-08-11 09:52:55.683 [Debug] Resolved string: Type the Password
2025-08-11 09:52:55.683 [Debug] Resolving placeholders in string: id=Password
2025-08-11 09:52:55.683 [Debug] Resolved string: id=Password
2025-08-11 09:52:55.685 [Debug] Resolving placeholders in string: $('[id=Password]').val('Password1')
2025-08-11 09:52:55.686 [Debug] Resolved string: $('[id=Password]').val('Password1')
2025-08-11 09:52:55.687 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-11 09:52:55.689 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-11 09:52:55.689 [Debug] Resolving placeholders in string: id=ChallengeQuestionAnswer
2025-08-11 09:52:55.690 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-11 09:52:55.691 [Debug] Resolving placeholders in string: bmw
2025-08-11 09:52:55.691 [Debug] Resolved string: bmw
2025-08-11 09:52:55.691 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-11 09:52:55.691 [Debug] Resolved string: Click on the Login Button
2025-08-11 09:52:55.692 [Debug] Resolving placeholders in string: id=LoginAuthBtn
2025-08-11 09:52:55.692 [Debug] Resolved string: id=LoginAuthBtn
2025-08-11 09:52:55.692 [Debug] Resolving placeholders in string: Click on change later button on the password expiration popup message
2025-08-11 09:52:55.692 [Debug] Resolved string: Click on change later button on the password expiration popup message
2025-08-11 09:52:55.693 [Debug] Resolving placeholders in string: {{Selectors.popupCancel}}
2025-08-11 09:52:55.693 [Debug] Resolved string: id=popup_cancel
2025-08-11 09:52:55.693 [Debug] Resolving placeholders in string: Click on biometric alert
2025-08-11 09:52:55.694 [Debug] Resolved string: Click on biometric alert
2025-08-11 09:52:55.694 [Debug] Resolving placeholders in string: {{Selectors.BioMetricAlert}}
2025-08-11 09:52:55.694 [Debug] Resolved string: css=#popbuttons > div > button.btn.Cancel.back_gradient2
2025-08-11 09:52:55.694 [Debug] Resolving placeholders in string: Execute JS code to activate token
2025-08-11 09:52:55.695 [Debug] Resolved string: Execute JS code to activate token
2025-08-11 09:52:55.695 [Debug] Resolving placeholders in string: Globals.ActivationState = 'ACTIVATED';
2025-08-11 09:52:55.695 [Debug] Resolved string: Globals.ActivationState = 'ACTIVATED';
2025-08-11 09:52:55.696 [Debug] Resolving placeholders in string: Click on Beneficiaries
2025-08-11 09:52:55.696 [Debug] Resolved string: Click on Beneficiaries
2025-08-11 09:52:55.696 [Debug] Resolving placeholders in string: {{Selectors.BeneficiariesToButton}}
2025-08-11 09:52:55.696 [Debug] Resolved string: xpath=//div[@class='mainmenuitems']//label[contains(text(), 'Beneficiaries')]
2025-08-11 09:52:55.696 [Debug] Resolving placeholders in string: Click on Banks Inside Egypt
2025-08-11 09:52:55.697 [Debug] Resolved string: Click on Banks Inside Egypt
2025-08-11 09:52:55.697 [Debug] Resolving placeholders in string: {{Selectors.BanksInsideEgypt}}
2025-08-11 09:52:55.697 [Debug] Resolved string: xpath=//div[@class='submenuitem']//label[contains(text(), 'Banks Inside Egypt')]
2025-08-11 09:52:55.697 [Debug] Resolving placeholders in string: Check if the Account List appears
2025-08-11 09:52:55.698 [Debug] Resolved string: Check if the Account List appears
2025-08-11 09:52:55.698 [Debug] Resolving placeholders in string: {{Selectors.BeneficiaryHeader}}
2025-08-11 09:52:55.699 [Debug] Resolved string: id=BeneficiaryHeader
2025-08-11 09:52:55.699 [Info] Successfully resolved parameters for test case: Existing Banks Inside Egypt beneficiaries displayed
2025-08-11 09:52:55.699 [Debug] Loading test case: WorkSet01/TestCases/03.Beneficiaries/Delete/147-Delete Existing beneficiary.json
2025-08-11 09:52:55.699 [Info] Loading test case from: WorkSet01/TestCases/03.Beneficiaries/Delete/147-Delete Existing beneficiary.json
2025-08-11 09:52:55.699 [Info] Attempting to load configuration file: WorkSet01/TestCases/03.Beneficiaries/Delete/147-Delete Existing beneficiary.json
2025-08-11 09:52:55.700 [Info] Successfully loaded configuration file: WorkSet01/TestCases/03.Beneficiaries/Delete/147-Delete Existing beneficiary.json
2025-08-11 09:52:55.701 [Info] No filtering configuration found. Including test case 'Delete Existing beneficiary'.
2025-08-11 09:52:55.701 [Debug] Resolving parameters for test case: Delete Existing beneficiary
2025-08-11 09:52:55.701 [Debug] Merging params
2025-08-11 09:52:55.701 [Debug] Merged basic params
2025-08-11 09:52:55.701 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-11 09:52:55.701 [Debug] Merged SpecialEnvParams params
2025-08-11 09:52:55.701 [Debug] Merged params
2025-08-11 09:52:55.701 [Debug] Loading parameters from reference file: WorkSet01/Params/BeneficiariesParams.json
2025-08-11 09:52:55.701 [Info] Attempting to load configuration file: WorkSet01/Params/BeneficiariesParams.json
2025-08-11 09:52:55.701 [Info] Successfully loaded configuration file: WorkSet01/Params/BeneficiariesParams.json
2025-08-11 09:52:55.702 [Debug] Merging params
2025-08-11 09:52:55.702 [Debug] Merged basic params
2025-08-11 09:52:55.702 [Debug] Merged params
2025-08-11 09:52:55.702 [Debug] Merging params
2025-08-11 09:52:55.703 [Debug] Merging params
2025-08-11 09:52:55.703 [Debug] Resolving placeholders in test steps for test case: Delete Existing beneficiary
2025-08-11 09:52:55.703 [Debug] Resolving TestCaseReference in step: 
2025-08-11 09:52:55.703 [Info] Loading test case from: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-11 09:52:55.703 [Info] Attempting to load configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-11 09:52:55.703 [Info] Successfully loaded configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-11 09:52:55.704 [Debug] Resolving parameters for test case: Login
2025-08-11 09:52:55.704 [Debug] Merging params
2025-08-11 09:52:55.704 [Debug] Merged basic params
2025-08-11 09:52:55.704 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-11 09:52:55.704 [Debug] Merged SpecialEnvParams params
2025-08-11 09:52:55.704 [Debug] Merged params
2025-08-11 09:52:55.704 [Debug] Loading parameters from reference file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-11 09:52:55.704 [Info] Attempting to load configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-11 09:52:55.704 [Info] Successfully loaded configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-11 09:52:55.704 [Debug] Merging params
2025-08-11 09:52:55.705 [Debug] Merged basic params
2025-08-11 09:52:55.705 [Debug] Merged params
2025-08-11 09:52:55.705 [Debug] Merging params
2025-08-11 09:52:55.705 [Debug] Merging params
2025-08-11 09:52:55.705 [Debug] Resolving placeholders in test steps for test case: Login
2025-08-11 09:52:55.705 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-11 09:52:55.705 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-11 09:52:55.705 [Debug] Resolving placeholders in string: {{Selectors.FinishButton}}
2025-08-11 09:52:55.705 [Debug] Resolved string: id=finish
2025-08-11 09:52:55.705 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-11 09:52:55.705 [Debug] Resolved string: Click on Login Button
2025-08-11 09:52:55.705 [Debug] Resolving placeholders in string: {{Selectors.LoginButton}}
2025-08-11 09:52:55.705 [Debug] Resolved string: id=LoginBtn
2025-08-11 09:52:55.705 [Debug] Resolving placeholders in string: Type the User Name
2025-08-11 09:52:55.705 [Debug] Resolved string: Type the User Name
2025-08-11 09:52:55.705 [Debug] Resolving placeholders in string: {{Selectors.UserNameField}}
2025-08-11 09:52:55.706 [Debug] Resolved string: id=UserName
2025-08-11 09:52:55.706 [Debug] Resolving placeholders in string: {{TestData.UserName}}
2025-08-11 09:52:55.706 [Debug] Resolved string: mahmoudsayed022
2025-08-11 09:52:55.706 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-11 09:52:55.707 [Debug] Resolved string: Click on Continue Button
2025-08-11 09:52:55.707 [Debug] Resolving placeholders in string: {{Selectors.ContinueButton}}
2025-08-11 09:52:55.707 [Debug] Resolved string: id=btn
2025-08-11 09:52:55.707 [Debug] Resolving placeholders in string: Type the Password
2025-08-11 09:52:55.707 [Debug] Resolved string: Type the Password
2025-08-11 09:52:55.707 [Debug] Resolving placeholders in string: {{Selectors.PasswordField}}
2025-08-11 09:52:55.708 [Debug] Resolved string: id=Password
2025-08-11 09:52:55.708 [Debug] Resolving placeholders in string: $('[{{Selectors.PasswordField}}]').val('{{TestData.Password}}')
2025-08-11 09:52:55.708 [Debug] Resolved string: $('[id=Password]').val('Password1')
2025-08-11 09:52:55.708 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-11 09:52:55.709 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-11 09:52:55.709 [Debug] Resolving placeholders in string: {{Selectors.ChallengeQuestionField}}
2025-08-11 09:52:55.711 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-11 09:52:55.711 [Debug] Resolving placeholders in string: {{TestData.ChallengeAnswer}}
2025-08-11 09:52:55.711 [Debug] Resolved string: bmw
2025-08-11 09:52:55.711 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-11 09:52:55.711 [Debug] Resolved string: Click on the Login Button
2025-08-11 09:52:55.711 [Debug] Resolving placeholders in string: {{Selectors.LoginAuthButton}}
2025-08-11 09:52:55.711 [Debug] Resolved string: id=LoginAuthBtn
2025-08-11 09:52:55.711 [Info] Successfully resolved parameters for test case: Login
2025-08-11 09:52:55.711 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-11 09:52:55.712 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-11 09:52:55.712 [Debug] Resolving placeholders in string: id=finish
2025-08-11 09:52:55.712 [Debug] Resolved string: id=finish
2025-08-11 09:52:55.712 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-11 09:52:55.712 [Debug] Resolved string: Click on Login Button
2025-08-11 09:52:55.712 [Debug] Resolving placeholders in string: id=LoginBtn
2025-08-11 09:52:55.712 [Debug] Resolved string: id=LoginBtn
2025-08-11 09:52:55.713 [Debug] Resolving placeholders in string: Type the User Name
2025-08-11 09:52:55.713 [Debug] Resolved string: Type the User Name
2025-08-11 09:52:55.713 [Debug] Resolving placeholders in string: id=UserName
2025-08-11 09:52:55.713 [Debug] Resolved string: id=UserName
2025-08-11 09:52:55.713 [Debug] Resolving placeholders in string: mahmoudsayed022
2025-08-11 09:52:55.713 [Debug] Resolved string: mahmoudsayed022
2025-08-11 09:52:55.713 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-11 09:52:55.713 [Debug] Resolved string: Click on Continue Button
2025-08-11 09:52:55.713 [Debug] Resolving placeholders in string: id=btn
2025-08-11 09:52:55.713 [Debug] Resolved string: id=btn
2025-08-11 09:52:55.713 [Debug] Resolving placeholders in string: Type the Password
2025-08-11 09:52:55.713 [Debug] Resolved string: Type the Password
2025-08-11 09:52:55.713 [Debug] Resolving placeholders in string: id=Password
2025-08-11 09:52:55.713 [Debug] Resolved string: id=Password
2025-08-11 09:52:55.713 [Debug] Resolving placeholders in string: $('[id=Password]').val('Password1')
2025-08-11 09:52:55.713 [Debug] Resolved string: $('[id=Password]').val('Password1')
2025-08-11 09:52:55.713 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-11 09:52:55.714 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-11 09:52:55.714 [Debug] Resolving placeholders in string: id=ChallengeQuestionAnswer
2025-08-11 09:52:55.714 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-11 09:52:55.714 [Debug] Resolving placeholders in string: bmw
2025-08-11 09:52:55.714 [Debug] Resolved string: bmw
2025-08-11 09:52:55.714 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-11 09:52:55.714 [Debug] Resolved string: Click on the Login Button
2025-08-11 09:52:55.714 [Debug] Resolving placeholders in string: id=LoginAuthBtn
2025-08-11 09:52:55.714 [Debug] Resolved string: id=LoginAuthBtn
2025-08-11 09:52:55.714 [Debug] Resolving placeholders in string: Click on change later button on the password expiration popup message
2025-08-11 09:52:55.714 [Debug] Resolved string: Click on change later button on the password expiration popup message
2025-08-11 09:52:55.714 [Debug] Resolving placeholders in string: {{Selectors.popupCancel}}
2025-08-11 09:52:55.714 [Debug] Resolved string: id=popup_cancel
2025-08-11 09:52:55.714 [Debug] Resolving placeholders in string: Click on biometric alert
2025-08-11 09:52:55.714 [Debug] Resolved string: Click on biometric alert
2025-08-11 09:52:55.714 [Debug] Resolving placeholders in string: {{Selectors.BioMetricAlert}}
2025-08-11 09:52:55.714 [Debug] Resolved string: css=#popbuttons > div > button.btn.Cancel.back_gradient2
2025-08-11 09:52:55.714 [Debug] Resolving placeholders in string: Click on biometric alert
2025-08-11 09:52:55.715 [Debug] Resolved string: Click on biometric alert
2025-08-11 09:52:55.715 [Debug] Resolving placeholders in string: {{Selectors.KYCskip}}
2025-08-11 09:52:55.715 [Debug] Resolved string: {{Selectors.KYCskip}}
2025-08-11 09:52:55.715 [Debug] Resolving placeholders in string: Execute JS code to activate token
2025-08-11 09:52:55.715 [Debug] Resolved string: Execute JS code to activate token
2025-08-11 09:52:55.715 [Debug] Resolving placeholders in string: Globals.ActivationState = 'ACTIVATED';
2025-08-11 09:52:55.715 [Debug] Resolved string: Globals.ActivationState = 'ACTIVATED';
2025-08-11 09:52:55.715 [Debug] Resolving placeholders in string: Click on Beneficiaries
2025-08-11 09:52:55.715 [Debug] Resolved string: Click on Beneficiaries
2025-08-11 09:52:55.715 [Debug] Resolving placeholders in string: {{Selectors.BeneficiariesToButton}}
2025-08-11 09:52:55.715 [Debug] Resolved string: xpath=//div[@class='mainmenuitems']//label[contains(text(), 'Beneficiaries')]
2025-08-11 09:52:55.715 [Debug] Resolving placeholders in string: Click on Local Transfer option
2025-08-11 09:52:55.715 [Debug] Resolved string: Click on Local Transfer option
2025-08-11 09:52:55.716 [Debug] Resolving placeholders in string: {{Selectors.LocalTransfer}}
2025-08-11 09:52:55.716 [Debug] Resolved string: xpath=//div[@class='submenuitem']//label[contains(text(), 'Local Transfer')]
2025-08-11 09:52:55.717 [Debug] Resolving placeholders in string: Click on Delete button
2025-08-11 09:52:55.717 [Debug] Resolved string: Click on Delete button
2025-08-11 09:52:55.718 [Debug] Resolving placeholders in string: id=DeleteBtn
2025-08-11 09:52:55.718 [Debug] Resolved string: id=DeleteBtn
2025-08-11 09:52:55.719 [Debug] Resolving placeholders in string: Click on continue button
2025-08-11 09:52:55.719 [Debug] Resolved string: Click on continue button
2025-08-11 09:52:55.719 [Debug] Resolving placeholders in string: id=DelLocBenSumContinue
2025-08-11 09:52:55.720 [Debug] Resolved string: id=DelLocBenSumContinue
2025-08-11 09:52:55.720 [Info] Successfully resolved parameters for test case: Delete Existing beneficiary
2025-08-11 09:52:55.721 [Debug] Loading test case: WorkSet01/TestCases/03.Beneficiaries/Delete/TC-254 Delete an Existing Card beneficiary.json
2025-08-11 09:52:55.721 [Info] Loading test case from: WorkSet01/TestCases/03.Beneficiaries/Delete/TC-254 Delete an Existing Card beneficiary.json
2025-08-11 09:52:55.721 [Info] Attempting to load configuration file: WorkSet01/TestCases/03.Beneficiaries/Delete/TC-254 Delete an Existing Card beneficiary.json
2025-08-11 09:52:55.731 [Info] Successfully loaded configuration file: WorkSet01/TestCases/03.Beneficiaries/Delete/TC-254 Delete an Existing Card beneficiary.json
2025-08-11 09:52:55.731 [Info] No filtering configuration found. Including test case 'Delete an Existing Card beneficiary'.
2025-08-11 09:52:55.731 [Debug] Resolving parameters for test case: Delete an Existing Card beneficiary
2025-08-11 09:52:55.731 [Debug] Merging params
2025-08-11 09:52:55.731 [Debug] Merged basic params
2025-08-11 09:52:55.731 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-11 09:52:55.731 [Debug] Merged SpecialEnvParams params
2025-08-11 09:52:55.732 [Debug] Merged params
2025-08-11 09:52:55.732 [Debug] Loading parameters from reference file: WorkSet01/Params/BeneficiariesParams.json
2025-08-11 09:52:55.732 [Info] Attempting to load configuration file: WorkSet01/Params/BeneficiariesParams.json
2025-08-11 09:52:55.732 [Info] Successfully loaded configuration file: WorkSet01/Params/BeneficiariesParams.json
2025-08-11 09:52:55.733 [Debug] Merging params
2025-08-11 09:52:55.733 [Debug] Merged basic params
2025-08-11 09:52:55.733 [Debug] Merged params
2025-08-11 09:52:55.734 [Debug] Merging params
2025-08-11 09:52:55.742 [Debug] Merging params
2025-08-11 09:52:55.742 [Debug] Resolving placeholders in test steps for test case: Delete an Existing Card beneficiary
2025-08-11 09:52:55.742 [Debug] Resolving TestCaseReference in step: 
2025-08-11 09:52:55.742 [Info] Loading test case from: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-11 09:52:55.742 [Info] Attempting to load configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-11 09:52:55.743 [Info] Successfully loaded configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-11 09:52:55.743 [Debug] Resolving parameters for test case: Login
2025-08-11 09:52:55.744 [Debug] Merging params
2025-08-11 09:52:55.744 [Debug] Merged basic params
2025-08-11 09:52:55.744 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-11 09:52:55.744 [Debug] Merged SpecialEnvParams params
2025-08-11 09:52:55.744 [Debug] Merged params
2025-08-11 09:52:55.744 [Debug] Loading parameters from reference file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-11 09:52:55.750 [Info] Attempting to load configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-11 09:52:55.750 [Info] Successfully loaded configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-11 09:52:55.750 [Debug] Merging params
2025-08-11 09:52:55.750 [Debug] Merged basic params
2025-08-11 09:52:55.750 [Debug] Merged params
2025-08-11 09:52:55.750 [Debug] Merging params
2025-08-11 09:52:55.750 [Debug] Merging params
2025-08-11 09:52:55.750 [Debug] Resolving placeholders in test steps for test case: Login
2025-08-11 09:52:55.750 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-11 09:52:55.750 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-11 09:52:55.751 [Debug] Resolving placeholders in string: {{Selectors.FinishButton}}
2025-08-11 09:52:55.751 [Debug] Resolved string: id=finish
2025-08-11 09:52:55.751 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-11 09:52:55.754 [Debug] Resolved string: Click on Login Button
2025-08-11 09:52:55.754 [Debug] Resolving placeholders in string: {{Selectors.LoginButton}}
2025-08-11 09:52:55.754 [Debug] Resolved string: id=LoginBtn
2025-08-11 09:52:55.754 [Debug] Resolving placeholders in string: Type the User Name
2025-08-11 09:52:55.754 [Debug] Resolved string: Type the User Name
2025-08-11 09:52:55.754 [Debug] Resolving placeholders in string: {{Selectors.UserNameField}}
2025-08-11 09:52:55.754 [Debug] Resolved string: id=UserName
2025-08-11 09:52:55.755 [Debug] Resolving placeholders in string: {{TestData.UserName}}
2025-08-11 09:52:55.755 [Debug] Resolved string: mahmoudsayed022
2025-08-11 09:52:55.755 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-11 09:52:55.755 [Debug] Resolved string: Click on Continue Button
2025-08-11 09:52:55.755 [Debug] Resolving placeholders in string: {{Selectors.ContinueButton}}
2025-08-11 09:52:55.755 [Debug] Resolved string: id=btn
2025-08-11 09:52:55.756 [Debug] Resolving placeholders in string: Type the Password
2025-08-11 09:52:55.757 [Debug] Resolved string: Type the Password
2025-08-11 09:52:55.758 [Debug] Resolving placeholders in string: {{Selectors.PasswordField}}
2025-08-11 09:52:55.758 [Debug] Resolved string: id=Password
2025-08-11 09:52:55.759 [Debug] Resolving placeholders in string: $('[{{Selectors.PasswordField}}]').val('{{TestData.Password}}')
2025-08-11 09:52:55.760 [Debug] Resolved string: $('[id=Password]').val('Password1')
2025-08-11 09:52:55.760 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-11 09:52:55.761 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-11 09:52:55.761 [Debug] Resolving placeholders in string: {{Selectors.ChallengeQuestionField}}
2025-08-11 09:52:55.761 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-11 09:52:55.761 [Debug] Resolving placeholders in string: {{TestData.ChallengeAnswer}}
2025-08-11 09:52:55.762 [Debug] Resolved string: bmw
2025-08-11 09:52:55.762 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-11 09:52:55.762 [Debug] Resolved string: Click on the Login Button
2025-08-11 09:52:55.762 [Debug] Resolving placeholders in string: {{Selectors.LoginAuthButton}}
2025-08-11 09:52:55.762 [Debug] Resolved string: id=LoginAuthBtn
2025-08-11 09:52:55.763 [Info] Successfully resolved parameters for test case: Login
2025-08-11 09:52:55.763 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-11 09:52:55.763 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-11 09:52:55.763 [Debug] Resolving placeholders in string: id=finish
2025-08-11 09:52:55.764 [Debug] Resolved string: id=finish
2025-08-11 09:52:55.764 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-11 09:52:55.764 [Debug] Resolved string: Click on Login Button
2025-08-11 09:52:55.764 [Debug] Resolving placeholders in string: id=LoginBtn
2025-08-11 09:52:55.765 [Debug] Resolved string: id=LoginBtn
2025-08-11 09:52:55.765 [Debug] Resolving placeholders in string: Type the User Name
2025-08-11 09:52:55.765 [Debug] Resolved string: Type the User Name
2025-08-11 09:52:55.765 [Debug] Resolving placeholders in string: id=UserName
2025-08-11 09:52:55.765 [Debug] Resolved string: id=UserName
2025-08-11 09:52:55.766 [Debug] Resolving placeholders in string: mahmoudsayed022
2025-08-11 09:52:55.766 [Debug] Resolved string: mahmoudsayed022
2025-08-11 09:52:55.766 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-11 09:52:55.766 [Debug] Resolved string: Click on Continue Button
2025-08-11 09:52:55.767 [Debug] Resolving placeholders in string: id=btn
2025-08-11 09:52:55.767 [Debug] Resolved string: id=btn
2025-08-11 09:52:55.767 [Debug] Resolving placeholders in string: Type the Password
2025-08-11 09:52:55.767 [Debug] Resolved string: Type the Password
2025-08-11 09:52:55.768 [Debug] Resolving placeholders in string: id=Password
2025-08-11 09:52:55.768 [Debug] Resolved string: id=Password
2025-08-11 09:52:55.768 [Debug] Resolving placeholders in string: $('[id=Password]').val('Password1')
2025-08-11 09:52:55.768 [Debug] Resolved string: $('[id=Password]').val('Password1')
2025-08-11 09:52:55.768 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-11 09:52:55.769 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-11 09:52:55.769 [Debug] Resolving placeholders in string: id=ChallengeQuestionAnswer
2025-08-11 09:52:55.769 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-11 09:52:55.769 [Debug] Resolving placeholders in string: bmw
2025-08-11 09:52:55.770 [Debug] Resolved string: bmw
2025-08-11 09:52:55.770 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-11 09:52:55.770 [Debug] Resolved string: Click on the Login Button
2025-08-11 09:52:55.770 [Debug] Resolving placeholders in string: id=LoginAuthBtn
2025-08-11 09:52:55.770 [Debug] Resolved string: id=LoginAuthBtn
2025-08-11 09:52:55.771 [Debug] Resolving placeholders in string: Click on change later button on the password expiration popup message
2025-08-11 09:52:55.771 [Debug] Resolved string: Click on change later button on the password expiration popup message
2025-08-11 09:52:55.771 [Debug] Resolving placeholders in string: {{Selectors.popupCancel}}
2025-08-11 09:52:55.773 [Debug] Resolved string: id=popup_cancel
2025-08-11 09:52:55.773 [Debug] Resolving placeholders in string: Click on biometric alert
2025-08-11 09:52:55.773 [Debug] Resolved string: Click on biometric alert
2025-08-11 09:52:55.773 [Debug] Resolving placeholders in string: {{Selectors.BioMetricAlert}}
2025-08-11 09:52:55.773 [Debug] Resolved string: css=#popbuttons > div > button.btn.Cancel.back_gradient2
2025-08-11 09:52:55.773 [Debug] Resolving placeholders in string: Click on biometric alert
2025-08-11 09:52:55.773 [Debug] Resolved string: Click on biometric alert
2025-08-11 09:52:55.773 [Debug] Resolving placeholders in string: {{Selectors.KYCskip}}
2025-08-11 09:52:55.773 [Debug] Resolved string: {{Selectors.KYCskip}}
2025-08-11 09:52:55.773 [Debug] Resolving placeholders in string: Execute JS code to activate token
2025-08-11 09:52:55.773 [Debug] Resolved string: Execute JS code to activate token
2025-08-11 09:52:55.774 [Debug] Resolving placeholders in string: Globals.ActivationState = 'ACTIVATED';
2025-08-11 09:52:55.774 [Debug] Resolved string: Globals.ActivationState = 'ACTIVATED';
2025-08-11 09:52:55.774 [Debug] Resolving placeholders in string: Click on Beneficiaries
2025-08-11 09:52:55.774 [Debug] Resolved string: Click on Beneficiaries
2025-08-11 09:52:55.774 [Debug] Resolving placeholders in string: {{Selectors.BeneficiariesToButton}}
2025-08-11 09:52:55.774 [Debug] Resolved string: xpath=//div[@class='mainmenuitems']//label[contains(text(), 'Beneficiaries')]
2025-08-11 09:52:55.774 [Debug] Resolving placeholders in string: Click on Other CAE Cards
2025-08-11 09:52:55.775 [Debug] Resolved string: Click on Other CAE Cards
2025-08-11 09:52:55.775 [Debug] Resolving placeholders in string: {{Selectors.OtherCAECards}}
2025-08-11 09:52:55.775 [Debug] Resolved string: xpath=//div[@class='submenuitem']//label[contains(text(), 'Other CAE Cards')]
2025-08-11 09:52:55.775 [Debug] Resolving placeholders in string: Click on Delete button
2025-08-11 09:52:55.775 [Debug] Resolved string: Click on Delete button
2025-08-11 09:52:55.775 [Debug] Resolving placeholders in string: id=DeleteBeneficiary
2025-08-11 09:52:55.775 [Debug] Resolved string: id=DeleteBeneficiary
2025-08-11 09:52:55.775 [Debug] Resolving placeholders in string: Click on continue button and show confirmation
2025-08-11 09:52:55.775 [Debug] Resolved string: Click on continue button and show confirmation
2025-08-11 09:52:55.775 [Debug] Resolving placeholders in string: id=DigitalBankBenfContinueDeleteBtn
2025-08-11 09:52:55.775 [Debug] Resolved string: id=DigitalBankBenfContinueDeleteBtn
2025-08-11 09:52:55.775 [Info] Successfully resolved parameters for test case: Delete an Existing Card beneficiary
2025-08-11 09:52:55.775 [Debug] Loading test case: WorkSet01/TestCases/03.Beneficiaries/Delete/TC-253 Delete an Existing Account beneficiary.json
2025-08-11 09:52:55.775 [Info] Loading test case from: WorkSet01/TestCases/03.Beneficiaries/Delete/TC-253 Delete an Existing Account beneficiary.json
2025-08-11 09:52:55.775 [Info] Attempting to load configuration file: WorkSet01/TestCases/03.Beneficiaries/Delete/TC-253 Delete an Existing Account beneficiary.json
2025-08-11 09:52:55.776 [Info] Successfully loaded configuration file: WorkSet01/TestCases/03.Beneficiaries/Delete/TC-253 Delete an Existing Account beneficiary.json
2025-08-11 09:52:55.776 [Info] No filtering configuration found. Including test case 'Delete an Existing Account beneficiary'.
2025-08-11 09:52:55.776 [Debug] Resolving parameters for test case: Delete an Existing Account beneficiary
2025-08-11 09:52:55.776 [Debug] Merging params
2025-08-11 09:52:55.777 [Debug] Merged basic params
2025-08-11 09:52:55.777 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-11 09:52:55.777 [Debug] Merged SpecialEnvParams params
2025-08-11 09:52:55.777 [Debug] Merged params
2025-08-11 09:52:55.777 [Debug] Loading parameters from reference file: WorkSet01/Params/BeneficiariesParams.json
2025-08-11 09:52:55.778 [Info] Attempting to load configuration file: WorkSet01/Params/BeneficiariesParams.json
2025-08-11 09:52:55.778 [Info] Successfully loaded configuration file: WorkSet01/Params/BeneficiariesParams.json
2025-08-11 09:52:55.779 [Debug] Merging params
2025-08-11 09:52:55.779 [Debug] Merged basic params
2025-08-11 09:52:55.779 [Debug] Merged params
2025-08-11 09:52:55.780 [Debug] Merging params
2025-08-11 09:52:55.780 [Debug] Merging params
2025-08-11 09:52:55.780 [Debug] Resolving placeholders in test steps for test case: Delete an Existing Account beneficiary
2025-08-11 09:52:55.780 [Debug] Resolving TestCaseReference in step: 
2025-08-11 09:52:55.780 [Info] Loading test case from: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-11 09:52:55.780 [Info] Attempting to load configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-11 09:52:55.780 [Info] Successfully loaded configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-11 09:52:55.781 [Debug] Resolving parameters for test case: Login
2025-08-11 09:52:55.781 [Debug] Merging params
2025-08-11 09:52:55.781 [Debug] Merged basic params
2025-08-11 09:52:55.782 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-11 09:52:55.782 [Debug] Merged SpecialEnvParams params
2025-08-11 09:52:55.782 [Debug] Merged params
2025-08-11 09:52:55.782 [Debug] Loading parameters from reference file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-11 09:52:55.782 [Info] Attempting to load configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-11 09:52:55.782 [Info] Successfully loaded configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-11 09:52:55.783 [Debug] Merging params
2025-08-11 09:52:55.783 [Debug] Merged basic params
2025-08-11 09:52:55.783 [Debug] Merged params
2025-08-11 09:52:55.783 [Debug] Merging params
2025-08-11 09:52:55.783 [Debug] Merging params
2025-08-11 09:52:55.783 [Debug] Resolving placeholders in test steps for test case: Login
2025-08-11 09:52:55.783 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-11 09:52:55.783 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-11 09:52:55.783 [Debug] Resolving placeholders in string: {{Selectors.FinishButton}}
2025-08-11 09:52:55.783 [Debug] Resolved string: id=finish
2025-08-11 09:52:55.783 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-11 09:52:55.784 [Debug] Resolved string: Click on Login Button
2025-08-11 09:52:55.784 [Debug] Resolving placeholders in string: {{Selectors.LoginButton}}
2025-08-11 09:52:55.784 [Debug] Resolved string: id=LoginBtn
2025-08-11 09:52:55.784 [Debug] Resolving placeholders in string: Type the User Name
2025-08-11 09:52:55.784 [Debug] Resolved string: Type the User Name
2025-08-11 09:52:55.784 [Debug] Resolving placeholders in string: {{Selectors.UserNameField}}
2025-08-11 09:52:55.784 [Debug] Resolved string: id=UserName
2025-08-11 09:52:55.784 [Debug] Resolving placeholders in string: {{TestData.UserName}}
2025-08-11 09:52:55.784 [Debug] Resolved string: mahmoudsayed022
2025-08-11 09:52:55.785 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-11 09:52:55.785 [Debug] Resolved string: Click on Continue Button
2025-08-11 09:52:55.785 [Debug] Resolving placeholders in string: {{Selectors.ContinueButton}}
2025-08-11 09:52:55.785 [Debug] Resolved string: id=btn
2025-08-11 09:52:55.786 [Debug] Resolving placeholders in string: Type the Password
2025-08-11 09:52:55.786 [Debug] Resolved string: Type the Password
2025-08-11 09:52:55.787 [Debug] Resolving placeholders in string: {{Selectors.PasswordField}}
2025-08-11 09:52:55.787 [Debug] Resolved string: id=Password
2025-08-11 09:52:55.787 [Debug] Resolving placeholders in string: $('[{{Selectors.PasswordField}}]').val('{{TestData.Password}}')
2025-08-11 09:52:55.787 [Debug] Resolved string: $('[id=Password]').val('Password1')
2025-08-11 09:52:55.788 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-11 09:52:55.788 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-11 09:52:55.788 [Debug] Resolving placeholders in string: {{Selectors.ChallengeQuestionField}}
2025-08-11 09:52:55.788 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-11 09:52:55.788 [Debug] Resolving placeholders in string: {{TestData.ChallengeAnswer}}
2025-08-11 09:52:55.788 [Debug] Resolved string: bmw
2025-08-11 09:52:55.788 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-11 09:52:55.788 [Debug] Resolved string: Click on the Login Button
2025-08-11 09:52:55.788 [Debug] Resolving placeholders in string: {{Selectors.LoginAuthButton}}
2025-08-11 09:52:55.788 [Debug] Resolved string: id=LoginAuthBtn
2025-08-11 09:52:55.788 [Info] Successfully resolved parameters for test case: Login
2025-08-11 09:52:55.788 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-11 09:52:55.788 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-11 09:52:55.789 [Debug] Resolving placeholders in string: id=finish
2025-08-11 09:52:55.789 [Debug] Resolved string: id=finish
2025-08-11 09:52:55.789 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-11 09:52:55.790 [Debug] Resolved string: Click on Login Button
2025-08-11 09:52:55.790 [Debug] Resolving placeholders in string: id=LoginBtn
2025-08-11 09:52:55.790 [Debug] Resolved string: id=LoginBtn
2025-08-11 09:52:55.790 [Debug] Resolving placeholders in string: Type the User Name
2025-08-11 09:52:55.790 [Debug] Resolved string: Type the User Name
2025-08-11 09:52:55.790 [Debug] Resolving placeholders in string: id=UserName
2025-08-11 09:52:55.790 [Debug] Resolved string: id=UserName
2025-08-11 09:52:55.790 [Debug] Resolving placeholders in string: mahmoudsayed022
2025-08-11 09:52:55.790 [Debug] Resolved string: mahmoudsayed022
2025-08-11 09:52:55.790 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-11 09:52:55.790 [Debug] Resolved string: Click on Continue Button
2025-08-11 09:52:55.791 [Debug] Resolving placeholders in string: id=btn
2025-08-11 09:52:55.791 [Debug] Resolved string: id=btn
2025-08-11 09:52:55.791 [Debug] Resolving placeholders in string: Type the Password
2025-08-11 09:52:55.791 [Debug] Resolved string: Type the Password
2025-08-11 09:52:55.791 [Debug] Resolving placeholders in string: id=Password
2025-08-11 09:52:55.791 [Debug] Resolved string: id=Password
2025-08-11 09:52:55.791 [Debug] Resolving placeholders in string: $('[id=Password]').val('Password1')
2025-08-11 09:52:55.791 [Debug] Resolved string: $('[id=Password]').val('Password1')
2025-08-11 09:52:55.791 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-11 09:52:55.791 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-11 09:52:55.791 [Debug] Resolving placeholders in string: id=ChallengeQuestionAnswer
2025-08-11 09:52:55.792 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-11 09:52:55.792 [Debug] Resolving placeholders in string: bmw
2025-08-11 09:52:55.792 [Debug] Resolved string: bmw
2025-08-11 09:52:55.792 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-11 09:52:55.792 [Debug] Resolved string: Click on the Login Button
2025-08-11 09:52:55.792 [Debug] Resolving placeholders in string: id=LoginAuthBtn
2025-08-11 09:52:55.792 [Debug] Resolved string: id=LoginAuthBtn
2025-08-11 09:52:55.792 [Debug] Resolving placeholders in string: Click on change later button on the password expiration popup message
2025-08-11 09:52:55.792 [Debug] Resolved string: Click on change later button on the password expiration popup message
2025-08-11 09:52:55.793 [Debug] Resolving placeholders in string: {{Selectors.popupCancel}}
2025-08-11 09:52:55.793 [Debug] Resolved string: id=popup_cancel
2025-08-11 09:52:55.793 [Debug] Resolving placeholders in string: Click on biometric alert
2025-08-11 09:52:55.793 [Debug] Resolved string: Click on biometric alert
2025-08-11 09:52:55.794 [Debug] Resolving placeholders in string: {{Selectors.BioMetricAlert}}
2025-08-11 09:52:55.794 [Debug] Resolved string: css=#popbuttons > div > button.btn.Cancel.back_gradient2
2025-08-11 09:52:55.794 [Debug] Resolving placeholders in string: Click on biometric alert
2025-08-11 09:52:55.795 [Debug] Resolved string: Click on biometric alert
2025-08-11 09:52:55.795 [Debug] Resolving placeholders in string: {{Selectors.KYCskip}}
2025-08-11 09:52:55.795 [Debug] Resolved string: {{Selectors.KYCskip}}
2025-08-11 09:52:55.795 [Debug] Resolving placeholders in string: Execute JS code to activate token
2025-08-11 09:52:55.796 [Debug] Resolved string: Execute JS code to activate token
2025-08-11 09:52:55.796 [Debug] Resolving placeholders in string: Globals.ActivationState = 'ACTIVATED';
2025-08-11 09:52:55.796 [Debug] Resolved string: Globals.ActivationState = 'ACTIVATED';
2025-08-11 09:52:55.797 [Debug] Resolving placeholders in string: Click on Beneficiaries
2025-08-11 09:52:55.797 [Debug] Resolved string: Click on Beneficiaries
2025-08-11 09:52:55.797 [Debug] Resolving placeholders in string: {{Selectors.BeneficiariesToButton}}
2025-08-11 09:52:55.798 [Debug] Resolved string: xpath=//div[@class='mainmenuitems']//label[contains(text(), 'Beneficiaries')]
2025-08-11 09:52:55.798 [Debug] Resolving placeholders in string: Click on Other CAE Accounts
2025-08-11 09:52:55.798 [Debug] Resolved string: Click on Other CAE Accounts
2025-08-11 09:52:55.799 [Debug] Resolving placeholders in string: {{Selectors.OtherCAEAccounts}}
2025-08-11 09:52:55.799 [Debug] Resolved string: xpath=//div[@class='submenuitem']//label[contains(text(), 'Other CAE Accounts')]
2025-08-11 09:52:55.800 [Debug] Resolving placeholders in string: Click on Delete button
2025-08-11 09:52:55.800 [Debug] Resolved string: Click on Delete button
2025-08-11 09:52:55.801 [Debug] Resolving placeholders in string: id=DeleteBeneficiary
2025-08-11 09:52:55.801 [Debug] Resolved string: id=DeleteBeneficiary
2025-08-11 09:52:55.801 [Debug] Resolving placeholders in string: Click on continue button and show confirmation
2025-08-11 09:52:55.801 [Debug] Resolved string: Click on continue button and show confirmation
2025-08-11 09:52:55.801 [Debug] Resolving placeholders in string: id=DigitalBankBenfContinueDeleteBtn
2025-08-11 09:52:55.801 [Debug] Resolved string: id=DigitalBankBenfContinueDeleteBtn
2025-08-11 09:52:55.801 [Info] Successfully resolved parameters for test case: Delete an Existing Account beneficiary
2025-08-11 09:52:55.801 [Debug] Loading test case: WorkSet01/TestCases/04.ATMdispute/Request Status/TC-430 Ensure that all request displayed Dispute Request.json
2025-08-11 09:52:55.801 [Info] Loading test case from: WorkSet01/TestCases/04.ATMdispute/Request Status/TC-430 Ensure that all request displayed Dispute Request.json
2025-08-11 09:52:55.801 [Info] Attempting to load configuration file: WorkSet01/TestCases/04.ATMdispute/Request Status/TC-430 Ensure that all request displayed Dispute Request.json
2025-08-11 09:52:55.804 [Info] Successfully loaded configuration file: WorkSet01/TestCases/04.ATMdispute/Request Status/TC-430 Ensure that all request displayed Dispute Request.json
2025-08-11 09:52:55.804 [Info] No filtering configuration found. Including test case ' Ensure that all request displayed -Dispute Request-'.
2025-08-11 09:52:55.804 [Debug] Resolving parameters for test case:  Ensure that all request displayed -Dispute Request-
2025-08-11 09:52:55.805 [Debug] Merging params
2025-08-11 09:52:55.810 [Debug] Merged basic params
2025-08-11 09:52:55.810 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-11 09:52:55.810 [Debug] Merged SpecialEnvParams params
2025-08-11 09:52:55.810 [Debug] Merged params
2025-08-11 09:52:55.810 [Debug] Loading parameters from reference file: WorkSet01/Params/CardServicesParams.json
2025-08-11 09:52:55.811 [Info] Attempting to load configuration file: WorkSet01/Params/CardServicesParams.json
2025-08-11 09:52:55.819 [Info] Successfully loaded configuration file: WorkSet01/Params/CardServicesParams.json
2025-08-11 09:52:55.819 [Debug] Merging params
2025-08-11 09:52:55.819 [Debug] Merged basic params
2025-08-11 09:52:55.819 [Debug] Merged params
2025-08-11 09:52:55.819 [Debug] Merging params
2025-08-11 09:52:55.823 [Debug] Merging params
2025-08-11 09:52:55.824 [Debug] Resolving placeholders in test steps for test case:  Ensure that all request displayed -Dispute Request-
2025-08-11 09:52:55.824 [Debug] Resolving TestCaseReference in step: 
2025-08-11 09:52:55.824 [Info] Loading test case from: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-11 09:52:55.824 [Info] Attempting to load configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-11 09:52:55.824 [Info] Successfully loaded configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-11 09:52:55.824 [Debug] Resolving parameters for test case: Login
2025-08-11 09:52:55.824 [Debug] Merging params
2025-08-11 09:52:55.824 [Debug] Merged basic params
2025-08-11 09:52:55.824 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-11 09:52:55.824 [Debug] Merged SpecialEnvParams params
2025-08-11 09:52:55.829 [Debug] Merged params
2025-08-11 09:52:55.829 [Debug] Loading parameters from reference file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-11 09:52:55.829 [Info] Attempting to load configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-11 09:52:55.830 [Info] Successfully loaded configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-11 09:52:55.833 [Debug] Merging params
2025-08-11 09:52:55.833 [Debug] Merged basic params
2025-08-11 09:52:55.833 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-11 09:52:55.833 [Debug] Merged SpecialEnvParams params
2025-08-11 09:52:55.833 [Debug] Merged params
2025-08-11 09:52:55.833 [Debug] Merging params
2025-08-11 09:52:55.834 [Debug] Merging params
2025-08-11 09:52:55.836 [Debug] Resolving placeholders in test steps for test case: Login
2025-08-11 09:52:55.837 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-11 09:52:55.838 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-11 09:52:55.839 [Debug] Resolving placeholders in string: {{Selectors.FinishButton}}
2025-08-11 09:52:55.840 [Debug] Resolved string: id=finish
2025-08-11 09:52:55.841 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-11 09:52:55.841 [Debug] Resolved string: Click on Login Button
2025-08-11 09:52:55.843 [Debug] Resolving placeholders in string: {{Selectors.LoginButton}}
2025-08-11 09:52:55.843 [Debug] Resolved string: id=LoginBtn
2025-08-11 09:52:55.844 [Debug] Resolving placeholders in string: Type the User Name
2025-08-11 09:52:55.844 [Debug] Resolved string: Type the User Name
2025-08-11 09:52:55.844 [Debug] Resolving placeholders in string: {{Selectors.UserNameField}}
2025-08-11 09:52:55.845 [Debug] Resolved string: id=UserName
2025-08-11 09:52:55.845 [Debug] Resolving placeholders in string: {{TestData.UserName}}
2025-08-11 09:52:55.845 [Debug] Resolved string: Mm_azmy
2025-08-11 09:52:55.846 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-11 09:52:55.847 [Debug] Resolved string: Click on Continue Button
2025-08-11 09:52:55.847 [Debug] Resolving placeholders in string: {{Selectors.ContinueButton}}
2025-08-11 09:52:55.847 [Debug] Resolved string: id=btn
2025-08-11 09:52:55.848 [Debug] Resolving placeholders in string: Type the Password
2025-08-11 09:52:55.848 [Debug] Resolved string: Type the Password
2025-08-11 09:52:55.848 [Debug] Resolving placeholders in string: {{Selectors.PasswordField}}
2025-08-11 09:52:55.848 [Debug] Resolved string: id=Password
2025-08-11 09:52:55.848 [Debug] Resolving placeholders in string: $('[{{Selectors.PasswordField}}]').val('{{TestData.Password}}')
2025-08-11 09:52:55.848 [Debug] Resolved string: $('[id=Password]').val('Asdf2025')
2025-08-11 09:52:55.848 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-11 09:52:55.848 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-11 09:52:55.848 [Debug] Resolving placeholders in string: {{Selectors.ChallengeQuestionField}}
2025-08-11 09:52:55.848 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-11 09:52:55.849 [Debug] Resolving placeholders in string: {{TestData.ChallengeAnswer}}
2025-08-11 09:52:55.849 [Debug] Resolved string: armada
2025-08-11 09:52:55.849 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-11 09:52:55.849 [Debug] Resolved string: Click on the Login Button
2025-08-11 09:52:55.849 [Debug] Resolving placeholders in string: {{Selectors.LoginAuthButton}}
2025-08-11 09:52:55.849 [Debug] Resolved string: id=LoginAuthBtn
2025-08-11 09:52:55.849 [Info] Successfully resolved parameters for test case: Login
2025-08-11 09:52:55.849 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-11 09:52:55.849 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-11 09:52:55.849 [Debug] Resolving placeholders in string: id=finish
2025-08-11 09:52:55.850 [Debug] Resolved string: id=finish
2025-08-11 09:52:55.850 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-11 09:52:55.850 [Debug] Resolved string: Click on Login Button
2025-08-11 09:52:55.850 [Debug] Resolving placeholders in string: id=LoginBtn
2025-08-11 09:52:55.850 [Debug] Resolved string: id=LoginBtn
2025-08-11 09:52:55.851 [Debug] Resolving placeholders in string: Type the User Name
2025-08-11 09:52:55.851 [Debug] Resolved string: Type the User Name
2025-08-11 09:52:55.851 [Debug] Resolving placeholders in string: id=UserName
2025-08-11 09:52:55.852 [Debug] Resolved string: id=UserName
2025-08-11 09:52:55.852 [Debug] Resolving placeholders in string: Mm_azmy
2025-08-11 09:52:55.852 [Debug] Resolved string: Mm_azmy
2025-08-11 09:52:55.852 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-11 09:52:55.853 [Debug] Resolved string: Click on Continue Button
2025-08-11 09:52:55.853 [Debug] Resolving placeholders in string: id=btn
2025-08-11 09:52:55.853 [Debug] Resolved string: id=btn
2025-08-11 09:52:55.854 [Debug] Resolving placeholders in string: Type the Password
2025-08-11 09:52:55.854 [Debug] Resolved string: Type the Password
2025-08-11 09:52:55.854 [Debug] Resolving placeholders in string: id=Password
2025-08-11 09:52:55.854 [Debug] Resolved string: id=Password
2025-08-11 09:52:55.854 [Debug] Resolving placeholders in string: $('[id=Password]').val('Asdf2025')
2025-08-11 09:52:55.854 [Debug] Resolved string: $('[id=Password]').val('Asdf2025')
2025-08-11 09:52:55.854 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-11 09:52:55.854 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-11 09:52:55.854 [Debug] Resolving placeholders in string: id=ChallengeQuestionAnswer
2025-08-11 09:52:55.854 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-11 09:52:55.854 [Debug] Resolving placeholders in string: armada
2025-08-11 09:52:55.854 [Debug] Resolved string: armada
2025-08-11 09:52:55.854 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-11 09:52:55.855 [Debug] Resolved string: Click on the Login Button
2025-08-11 09:52:55.855 [Debug] Resolving placeholders in string: id=LoginAuthBtn
2025-08-11 09:52:55.855 [Debug] Resolved string: id=LoginAuthBtn
2025-08-11 09:52:55.855 [Debug] Resolving placeholders in string: Click on Cards option on the side menu
2025-08-11 09:52:55.855 [Debug] Resolved string: Click on Cards option on the side menu
2025-08-11 09:52:55.855 [Debug] Resolving placeholders in string: {{Selectors.CardsButton}}
2025-08-11 09:52:55.855 [Debug] Resolved string: xpath=//div[@class='mainmenuitems']//label[contains(text(), 'Cards')]
2025-08-11 09:52:55.855 [Debug] Resolving placeholders in string: Click on Card Services
2025-08-11 09:52:55.855 [Debug] Resolved string: Click on Card Services
2025-08-11 09:52:55.855 [Debug] Resolving placeholders in string: {{Selectors.CardServices}}
2025-08-11 09:52:55.855 [Debug] Resolved string: xpath=//div[@class='submenuitem']//label[contains(text(), 'Card services')]
2025-08-11 09:52:55.856 [Debug] Resolving placeholders in string: Click on request status and Ensure that all request displayed
2025-08-11 09:52:55.856 [Debug] Resolved string: Click on request status and Ensure that all request displayed
2025-08-11 09:52:55.856 [Debug] Resolving placeholders in string: {{Selectors.RequestStatues}}
2025-08-11 09:52:55.856 [Debug] Resolved string: id=RequestStatus
2025-08-11 09:52:55.856 [Info] Successfully resolved parameters for test case:  Ensure that all request displayed -Dispute Request-
2025-08-11 09:52:55.856 [Debug] Loading test case: WorkSet01/TestCases/04.ATMdispute/128-ATM Dispute, click View Details.json
2025-08-11 09:52:55.856 [Info] Loading test case from: WorkSet01/TestCases/04.ATMdispute/128-ATM Dispute, click View Details.json
2025-08-11 09:52:55.856 [Info] Attempting to load configuration file: WorkSet01/TestCases/04.ATMdispute/128-ATM Dispute, click View Details.json
2025-08-11 09:52:55.865 [Info] Successfully loaded configuration file: WorkSet01/TestCases/04.ATMdispute/128-ATM Dispute, click View Details.json
2025-08-11 09:52:55.866 [Info] No filtering configuration found. Including test case 'ATM Dispute, click View Details'.
2025-08-11 09:52:55.866 [Debug] Resolving parameters for test case: ATM Dispute, click View Details
2025-08-11 09:52:55.866 [Debug] Merging params
2025-08-11 09:52:55.866 [Debug] Merged basic params
2025-08-11 09:52:55.866 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-11 09:52:55.866 [Debug] Merged SpecialEnvParams params
2025-08-11 09:52:55.866 [Debug] Merged params
2025-08-11 09:52:55.866 [Debug] Loading parameters from reference file: WorkSet01/Params/ATMdisputeParams.json
2025-08-11 09:52:55.866 [Info] Attempting to load configuration file: WorkSet01/Params/ATMdisputeParams.json
2025-08-11 09:52:55.881 [Info] Successfully loaded configuration file: WorkSet01/Params/ATMdisputeParams.json
2025-08-11 09:52:55.881 [Debug] Merging params
2025-08-11 09:52:55.881 [Debug] Merged basic params
2025-08-11 09:52:55.881 [Debug] Merged params
2025-08-11 09:52:55.882 [Debug] Merging params
2025-08-11 09:52:55.882 [Debug] Merging params
2025-08-11 09:52:55.882 [Debug] Resolving placeholders in test steps for test case: ATM Dispute, click View Details
2025-08-11 09:52:55.882 [Debug] Resolving TestCaseReference in step: 
2025-08-11 09:52:55.882 [Info] Loading test case from: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-11 09:52:55.882 [Info] Attempting to load configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-11 09:52:55.882 [Info] Successfully loaded configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-11 09:52:55.882 [Debug] Resolving parameters for test case: Login
2025-08-11 09:52:55.882 [Debug] Merging params
2025-08-11 09:52:55.882 [Debug] Merged basic params
2025-08-11 09:52:55.883 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-11 09:52:55.883 [Debug] Merged SpecialEnvParams params
2025-08-11 09:52:55.883 [Debug] Merged params
2025-08-11 09:52:55.883 [Debug] Loading parameters from reference file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-11 09:52:55.883 [Info] Attempting to load configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-11 09:52:55.884 [Info] Successfully loaded configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-11 09:52:55.885 [Debug] Merging params
2025-08-11 09:52:55.885 [Debug] Merged basic params
2025-08-11 09:52:55.885 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-11 09:52:55.885 [Debug] Merged SpecialEnvParams params
2025-08-11 09:52:55.885 [Debug] Merged params
2025-08-11 09:52:55.885 [Debug] Merging params
2025-08-11 09:52:55.885 [Debug] Merging params
2025-08-11 09:52:55.885 [Debug] Resolving placeholders in test steps for test case: Login
2025-08-11 09:52:55.885 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-11 09:52:55.885 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-11 09:52:55.885 [Debug] Resolving placeholders in string: {{Selectors.FinishButton}}
2025-08-11 09:52:55.886 [Debug] Resolved string: id=finish
2025-08-11 09:52:55.886 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-11 09:52:55.886 [Debug] Resolved string: Click on Login Button
2025-08-11 09:52:55.886 [Debug] Resolving placeholders in string: {{Selectors.LoginButton}}
2025-08-11 09:52:55.886 [Debug] Resolved string: id=LoginBtn
2025-08-11 09:52:55.886 [Debug] Resolving placeholders in string: Type the User Name
2025-08-11 09:52:55.886 [Debug] Resolved string: Type the User Name
2025-08-11 09:52:55.886 [Debug] Resolving placeholders in string: {{Selectors.UserNameField}}
2025-08-11 09:52:55.886 [Debug] Resolved string: id=UserName
2025-08-11 09:52:55.886 [Debug] Resolving placeholders in string: {{TestData.UserName}}
2025-08-11 09:52:55.886 [Debug] Resolved string: Mm_azmy
2025-08-11 09:52:55.886 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-11 09:52:55.886 [Debug] Resolved string: Click on Continue Button
2025-08-11 09:52:55.886 [Debug] Resolving placeholders in string: {{Selectors.ContinueButton}}
2025-08-11 09:52:55.886 [Debug] Resolved string: id=btn
2025-08-11 09:52:55.886 [Debug] Resolving placeholders in string: Type the Password
2025-08-11 09:52:55.886 [Debug] Resolved string: Type the Password
2025-08-11 09:52:55.886 [Debug] Resolving placeholders in string: {{Selectors.PasswordField}}
2025-08-11 09:52:55.887 [Debug] Resolved string: id=Password
2025-08-11 09:52:55.887 [Debug] Resolving placeholders in string: $('[{{Selectors.PasswordField}}]').val('{{TestData.Password}}')
2025-08-11 09:52:55.887 [Debug] Resolved string: $('[id=Password]').val('Asdf2025')
2025-08-11 09:52:55.887 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-11 09:52:55.887 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-11 09:52:55.888 [Debug] Resolving placeholders in string: {{Selectors.ChallengeQuestionField}}
2025-08-11 09:52:55.888 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-11 09:52:55.888 [Debug] Resolving placeholders in string: {{TestData.ChallengeAnswer}}
2025-08-11 09:52:55.889 [Debug] Resolved string: armada
2025-08-11 09:52:55.889 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-11 09:52:55.889 [Debug] Resolved string: Click on the Login Button
2025-08-11 09:52:55.889 [Debug] Resolving placeholders in string: {{Selectors.LoginAuthButton}}
2025-08-11 09:52:55.889 [Debug] Resolved string: id=LoginAuthBtn
2025-08-11 09:52:55.889 [Info] Successfully resolved parameters for test case: Login
2025-08-11 09:52:55.889 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-11 09:52:55.889 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-11 09:52:55.889 [Debug] Resolving placeholders in string: id=finish
2025-08-11 09:52:55.890 [Debug] Resolved string: id=finish
2025-08-11 09:52:55.890 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-11 09:52:55.890 [Debug] Resolved string: Click on Login Button
2025-08-11 09:52:55.890 [Debug] Resolving placeholders in string: id=LoginBtn
2025-08-11 09:52:55.890 [Debug] Resolved string: id=LoginBtn
2025-08-11 09:52:55.890 [Debug] Resolving placeholders in string: Type the User Name
2025-08-11 09:52:55.890 [Debug] Resolved string: Type the User Name
2025-08-11 09:52:55.890 [Debug] Resolving placeholders in string: id=UserName
2025-08-11 09:52:55.890 [Debug] Resolved string: id=UserName
2025-08-11 09:52:55.891 [Debug] Resolving placeholders in string: Mm_azmy
2025-08-11 09:52:55.891 [Debug] Resolved string: Mm_azmy
2025-08-11 09:52:55.891 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-11 09:52:55.891 [Debug] Resolved string: Click on Continue Button
2025-08-11 09:52:55.891 [Debug] Resolving placeholders in string: id=btn
2025-08-11 09:52:55.891 [Debug] Resolved string: id=btn
2025-08-11 09:52:55.891 [Debug] Resolving placeholders in string: Type the Password
2025-08-11 09:52:55.891 [Debug] Resolved string: Type the Password
2025-08-11 09:52:55.891 [Debug] Resolving placeholders in string: id=Password
2025-08-11 09:52:55.891 [Debug] Resolved string: id=Password
2025-08-11 09:52:55.891 [Debug] Resolving placeholders in string: $('[id=Password]').val('Asdf2025')
2025-08-11 09:52:55.891 [Debug] Resolved string: $('[id=Password]').val('Asdf2025')
2025-08-11 09:52:55.891 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-11 09:52:55.891 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-11 09:52:55.891 [Debug] Resolving placeholders in string: id=ChallengeQuestionAnswer
2025-08-11 09:52:55.891 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-11 09:52:55.891 [Debug] Resolving placeholders in string: armada
2025-08-11 09:52:55.891 [Debug] Resolved string: armada
2025-08-11 09:52:55.892 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-11 09:52:55.892 [Debug] Resolved string: Click on the Login Button
2025-08-11 09:52:55.892 [Debug] Resolving placeholders in string: id=LoginAuthBtn
2025-08-11 09:52:55.892 [Debug] Resolved string: id=LoginAuthBtn
2025-08-11 09:52:55.893 [Debug] Resolving placeholders in string: Click on Cards option on the side menu
2025-08-11 09:52:55.894 [Debug] Resolved string: Click on Cards option on the side menu
2025-08-11 09:52:55.894 [Debug] Resolving placeholders in string: {{Selectors.CardsButton}}
2025-08-11 09:52:55.894 [Debug] Resolved string: xpath=//div[@class='mainmenuitems']//label[contains(text(), 'Cards')]
2025-08-11 09:52:55.894 [Debug] Resolving placeholders in string: Click on Card Services
2025-08-11 09:52:55.894 [Debug] Resolved string: Click on Card Services
2025-08-11 09:52:55.894 [Debug] Resolving placeholders in string: {{Selectors.CardServices}}
2025-08-11 09:52:55.895 [Debug] Resolved string: xpath=//div[@class='submenuitem']//label[contains(text(), 'Card services')]
2025-08-11 09:52:55.895 [Debug] Resolving placeholders in string: Click on ATM dispute
2025-08-11 09:52:55.895 [Debug] Resolved string: Click on ATM dispute
2025-08-11 09:52:55.895 [Debug] Resolving placeholders in string: {{Selectors.ATMdisputeButton}}
2025-08-11 09:52:55.895 [Debug] Resolved string: id=ATMDispute
2025-08-11 09:52:55.895 [Debug] Resolving placeholders in string: Click on dispute type
2025-08-11 09:52:55.896 [Debug] Resolved string: Click on dispute type
2025-08-11 09:52:55.896 [Debug] Resolving placeholders in string: {{Selectors.SelectDisputeType}}
2025-08-11 09:52:55.896 [Debug] Resolved string: xpath=//div[@class='details_container']//span[normalize-space(text())='Select...']
2025-08-11 09:52:55.896 [Debug] Resolving placeholders in string: Choose dispute type
2025-08-11 09:52:55.896 [Debug] Resolved string: Choose dispute type
2025-08-11 09:52:55.896 [Debug] Resolving placeholders in string: {{Selectors.DisputeType}}
2025-08-11 09:52:55.896 [Debug] Resolved string: xpath=//div[@class='details_container']//span[normalize-space(text())='Withdrawal did not dispense']
2025-08-11 09:52:55.896 [Debug] Resolving placeholders in string: Click on Select Cards button
2025-08-11 09:52:55.896 [Debug] Resolved string: Click on Select Cards button
2025-08-11 09:52:55.896 [Debug] Resolving placeholders in string: {{Selectors.SelectButton}}
2025-08-11 09:52:55.896 [Debug] Resolved string: xpath=//div[@class='details_container']//button[normalize-space(text())='Select']
2025-08-11 09:52:55.896 [Debug] Resolving placeholders in string: Choose a card with ATM transactions
2025-08-11 09:52:55.896 [Debug] Resolved string: Choose a card with ATM transactions
2025-08-11 09:52:55.896 [Debug] Resolving placeholders in string: {{Selectors.CardWithTransactions}}
2025-08-11 09:52:55.896 [Debug] Resolved string: xpath=//div[@class='module_list_container cardData']//h4[normalize-space(text())='4204XXXXXXXX2562']
2025-08-11 09:52:55.896 [Debug] Resolving placeholders in string: Click on select a transaction
2025-08-11 09:52:55.897 [Debug] Resolved string: Click on select a transaction
2025-08-11 09:52:55.897 [Debug] Resolving placeholders in string: {{Selectors.SelectTransactionButton}}
2025-08-11 09:52:55.897 [Debug] Resolved string: xpath=//div[@class='details_container']//button[normalize-space(text())='Select Transaction']
2025-08-11 09:52:55.897 [Debug] Resolving placeholders in string: Choose a transaction
2025-08-11 09:52:55.897 [Debug] Resolved string: Choose a transaction
2025-08-11 09:52:55.897 [Debug] Resolving placeholders in string: {{Selectors.Transaction}}
2025-08-11 09:52:55.897 [Debug] Resolved string: css=#Transaction > div:nth-child(1) > div.list_header
2025-08-11 09:52:55.897 [Debug] Resolving placeholders in string: Click on continue
2025-08-11 09:52:55.897 [Debug] Resolved string: Click on continue
2025-08-11 09:52:55.897 [Debug] Resolving placeholders in string: {{Selectors.ContinueButtonPilot1}}
2025-08-11 09:52:55.897 [Debug] Resolved string: xpath=//div[@class='details_container']//button[normalize-space(text())='Continue']
2025-08-11 09:52:55.897 [Debug] Resolving placeholders in string: Enter the disputed amount
2025-08-11 09:52:55.898 [Debug] Resolved string: Enter the disputed amount
2025-08-11 09:52:55.898 [Debug] Resolving placeholders in string: {{Selectors.DisputedAmmountField}}
2025-08-11 09:52:55.899 [Debug] Resolved string: xpath=//div[@class='details_container']//input[normalize-space(@placeholder)='Enter Disputed amount']
2025-08-11 09:52:55.899 [Debug] Resolving placeholders in string: 0
2025-08-11 09:52:55.899 [Debug] Resolved string: 0
2025-08-11 09:52:55.899 [Debug] Resolving placeholders in string: Enter the dispute note
2025-08-11 09:52:55.900 [Debug] Resolved string: Enter the dispute note
2025-08-11 09:52:55.900 [Debug] Resolving placeholders in string: {{Selectors.DisputeNoteField}}
2025-08-11 09:52:55.900 [Debug] Resolved string: xpath=//div[@class='details_container']//textarea[normalize-space(@placeholder)='']
2025-08-11 09:52:55.900 [Debug] Resolving placeholders in string: This is a test for the feature by the dev team. Please ignore this request.
2025-08-11 09:52:55.900 [Debug] Resolved string: This is a test for the feature by the dev team. Please ignore this request.
2025-08-11 09:52:55.900 [Debug] Resolving placeholders in string: Click on the agree terms checkbox
2025-08-11 09:52:55.900 [Debug] Resolved string: Click on the agree terms checkbox
2025-08-11 09:52:55.900 [Debug] Resolving placeholders in string: {{Selectors.TermsCheckbox}}
2025-08-11 09:52:55.900 [Debug] Resolved string: xpath=//div[@class='details_container']//input[@type='checkbox']
2025-08-11 09:52:55.901 [Debug] Resolving placeholders in string: Click on continue button
2025-08-11 09:52:55.901 [Debug] Resolved string: Click on continue button
2025-08-11 09:52:55.901 [Debug] Resolving placeholders in string: {{Selectors.ContinueButtonPilot2}}
2025-08-11 09:52:55.901 [Debug] Resolved string: xpath=//div[@class='details_container']//button[normalize-space(text())='Confirm']
2025-08-11 09:52:55.901 [Debug] Resolving placeholders in string: Click on confirm button
2025-08-11 09:52:55.901 [Debug] Resolved string: Click on confirm button
2025-08-11 09:52:55.901 [Debug] Resolving placeholders in string: {{Selectors.ConfirmDispute}}
2025-08-11 09:52:55.901 [Debug] Resolved string: id=DisputePreConfirmationBtn
2025-08-11 09:52:55.901 [Debug] Resolving placeholders in string: Click on Request Details
2025-08-11 09:52:55.902 [Debug] Resolved string: Click on Request Details
2025-08-11 09:52:55.902 [Debug] Resolving placeholders in string: {{Selectors.SubmitProdRequestDetailsBtn}}
2025-08-11 09:52:55.902 [Debug] Resolved string: id=SubmitProdRequestDetailsBtn
2025-08-11 09:52:55.902 [Info] Successfully resolved parameters for test case: ATM Dispute, click View Details
2025-08-11 09:52:55.902 [Debug] Loading test case: WorkSet01/TestCases/012.CardServices/DirectDebit/130-DirectDebitRequest.json
2025-08-11 09:52:55.902 [Info] Loading test case from: WorkSet01/TestCases/012.CardServices/DirectDebit/130-DirectDebitRequest.json
2025-08-11 09:52:55.902 [Info] Attempting to load configuration file: WorkSet01/TestCases/012.CardServices/DirectDebit/130-DirectDebitRequest.json
2025-08-11 09:52:55.904 [Info] Successfully loaded configuration file: WorkSet01/TestCases/012.CardServices/DirectDebit/130-DirectDebitRequest.json
2025-08-11 09:52:55.909 [Info] No filtering configuration found. Including test case 'Direct Debit, standard users'.
2025-08-11 09:52:55.909 [Debug] Resolving parameters for test case: Direct Debit, standard users
2025-08-11 09:52:55.909 [Debug] Merging params
2025-08-11 09:52:55.909 [Debug] Merged basic params
2025-08-11 09:52:55.909 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-11 09:52:55.909 [Debug] Merged SpecialEnvParams params
2025-08-11 09:52:55.909 [Debug] Merged params
2025-08-11 09:52:55.909 [Debug] Loading parameters from reference file: WorkSet01/Params/BlockCardParams.json
2025-08-11 09:52:55.909 [Info] Attempting to load configuration file: WorkSet01/Params/BlockCardParams.json
2025-08-11 09:52:55.922 [Info] Successfully loaded configuration file: WorkSet01/Params/BlockCardParams.json
2025-08-11 09:52:55.922 [Debug] Merging params
2025-08-11 09:52:55.923 [Debug] Merged basic params
2025-08-11 09:52:55.923 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-11 09:52:55.923 [Debug] Merged SpecialEnvParams params
2025-08-11 09:52:55.923 [Debug] Merged params
2025-08-11 09:52:55.923 [Debug] Merging params
2025-08-11 09:52:55.923 [Debug] Merging params
2025-08-11 09:52:55.923 [Debug] Resolving placeholders in test steps for test case: Direct Debit, standard users
2025-08-11 09:52:55.923 [Debug] Resolving TestCaseReference in step: 
2025-08-11 09:52:55.923 [Info] Loading test case from: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-11 09:52:55.923 [Info] Attempting to load configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-11 09:52:55.923 [Info] Successfully loaded configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-11 09:52:55.924 [Debug] Resolving parameters for test case: Login
2025-08-11 09:52:55.924 [Debug] Merging params
2025-08-11 09:52:55.924 [Debug] Merged basic params
2025-08-11 09:52:55.924 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-11 09:52:55.924 [Debug] Merged SpecialEnvParams params
2025-08-11 09:52:55.924 [Debug] Merged params
2025-08-11 09:52:55.925 [Debug] Loading parameters from reference file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-11 09:52:55.925 [Info] Attempting to load configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-11 09:52:55.926 [Info] Successfully loaded configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-11 09:52:55.926 [Debug] Merging params
2025-08-11 09:52:55.926 [Debug] Merged basic params
2025-08-11 09:52:55.926 [Debug] Merged params
2025-08-11 09:52:55.926 [Debug] Merging params
2025-08-11 09:52:55.926 [Debug] Merging params
2025-08-11 09:52:55.926 [Debug] Resolving placeholders in test steps for test case: Login
2025-08-11 09:52:55.926 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-11 09:52:55.927 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-11 09:52:55.927 [Debug] Resolving placeholders in string: {{Selectors.FinishButton}}
2025-08-11 09:52:55.927 [Debug] Resolved string: id=finish
2025-08-11 09:52:55.927 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-11 09:52:55.927 [Debug] Resolved string: Click on Login Button
2025-08-11 09:52:55.927 [Debug] Resolving placeholders in string: {{Selectors.LoginButton}}
2025-08-11 09:52:55.927 [Debug] Resolved string: id=LoginBtn
2025-08-11 09:52:55.928 [Debug] Resolving placeholders in string: Type the User Name
2025-08-11 09:52:55.928 [Debug] Resolved string: Type the User Name
2025-08-11 09:52:55.928 [Debug] Resolving placeholders in string: {{Selectors.UserNameField}}
2025-08-11 09:52:55.928 [Debug] Resolved string: id=UserName
2025-08-11 09:52:55.928 [Debug] Resolving placeholders in string: {{TestData.UserName}}
2025-08-11 09:52:55.928 [Debug] Resolved string: mahmoudsayed022
2025-08-11 09:52:55.928 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-11 09:52:55.928 [Debug] Resolved string: Click on Continue Button
2025-08-11 09:52:55.928 [Debug] Resolving placeholders in string: {{Selectors.ContinueButton}}
2025-08-11 09:52:55.928 [Debug] Resolved string: id=btn
2025-08-11 09:52:55.928 [Debug] Resolving placeholders in string: Type the Password
2025-08-11 09:52:55.928 [Debug] Resolved string: Type the Password
2025-08-11 09:52:55.928 [Debug] Resolving placeholders in string: {{Selectors.PasswordField}}
2025-08-11 09:52:55.928 [Debug] Resolved string: id=Password
2025-08-11 09:52:55.928 [Debug] Resolving placeholders in string: $('[{{Selectors.PasswordField}}]').val('{{TestData.Password}}')
2025-08-11 09:52:55.929 [Debug] Resolved string: $('[id=Password]').val('Password1')
2025-08-11 09:52:55.929 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-11 09:52:55.929 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-11 09:52:55.930 [Debug] Resolving placeholders in string: {{Selectors.ChallengeQuestionField}}
2025-08-11 09:52:55.930 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-11 09:52:55.931 [Debug] Resolving placeholders in string: {{TestData.ChallengeAnswer}}
2025-08-11 09:52:55.931 [Debug] Resolved string: bmw
2025-08-11 09:52:55.931 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-11 09:52:55.931 [Debug] Resolved string: Click on the Login Button
2025-08-11 09:52:55.931 [Debug] Resolving placeholders in string: {{Selectors.LoginAuthButton}}
2025-08-11 09:52:55.931 [Debug] Resolved string: id=LoginAuthBtn
2025-08-11 09:52:55.931 [Info] Successfully resolved parameters for test case: Login
2025-08-11 09:52:55.932 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-11 09:52:55.932 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-11 09:52:55.932 [Debug] Resolving placeholders in string: id=finish
2025-08-11 09:52:55.932 [Debug] Resolved string: id=finish
2025-08-11 09:52:55.932 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-11 09:52:55.932 [Debug] Resolved string: Click on Login Button
2025-08-11 09:52:55.932 [Debug] Resolving placeholders in string: id=LoginBtn
2025-08-11 09:52:55.933 [Debug] Resolved string: id=LoginBtn
2025-08-11 09:52:55.933 [Debug] Resolving placeholders in string: Type the User Name
2025-08-11 09:52:55.933 [Debug] Resolved string: Type the User Name
2025-08-11 09:52:55.933 [Debug] Resolving placeholders in string: id=UserName
2025-08-11 09:52:55.933 [Debug] Resolved string: id=UserName
2025-08-11 09:52:55.933 [Debug] Resolving placeholders in string: mahmoudsayed022
2025-08-11 09:52:55.933 [Debug] Resolved string: mahmoudsayed022
2025-08-11 09:52:55.933 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-11 09:52:55.933 [Debug] Resolved string: Click on Continue Button
2025-08-11 09:52:55.933 [Debug] Resolving placeholders in string: id=btn
2025-08-11 09:52:55.933 [Debug] Resolved string: id=btn
2025-08-11 09:52:55.933 [Debug] Resolving placeholders in string: Type the Password
2025-08-11 09:52:55.933 [Debug] Resolved string: Type the Password
2025-08-11 09:52:55.933 [Debug] Resolving placeholders in string: id=Password
2025-08-11 09:52:55.934 [Debug] Resolved string: id=Password
2025-08-11 09:52:55.934 [Debug] Resolving placeholders in string: $('[id=Password]').val('Password1')
2025-08-11 09:52:55.934 [Debug] Resolved string: $('[id=Password]').val('Password1')
2025-08-11 09:52:55.934 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-11 09:52:55.934 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-11 09:52:55.934 [Debug] Resolving placeholders in string: id=ChallengeQuestionAnswer
2025-08-11 09:52:55.934 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-11 09:52:55.934 [Debug] Resolving placeholders in string: bmw
2025-08-11 09:52:55.934 [Debug] Resolved string: bmw
2025-08-11 09:52:55.935 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-11 09:52:55.935 [Debug] Resolved string: Click on the Login Button
2025-08-11 09:52:55.936 [Debug] Resolving placeholders in string: id=LoginAuthBtn
2025-08-11 09:52:55.937 [Debug] Resolved string: id=LoginAuthBtn
2025-08-11 09:52:55.937 [Debug] Resolving placeholders in string: Click on change later button on the password expiration popup message
2025-08-11 09:52:55.937 [Debug] Resolved string: Click on change later button on the password expiration popup message
2025-08-11 09:52:55.937 [Debug] Resolving placeholders in string: {{Selectors.popupCancel}}
2025-08-11 09:52:55.937 [Debug] Resolved string: id=popup_cancel
2025-08-11 09:52:55.937 [Debug] Resolving placeholders in string: Click on biometric alert
2025-08-11 09:52:55.937 [Debug] Resolved string: Click on biometric alert
2025-08-11 09:52:55.938 [Debug] Resolving placeholders in string: {{Selectors.BioMetricAlert}}
2025-08-11 09:52:55.938 [Debug] Resolved string: css=#popbuttons > div > button.btn.Cancel.back_gradient2
2025-08-11 09:52:55.938 [Debug] Resolving placeholders in string: Execute JS code to activate token
2025-08-11 09:52:55.938 [Debug] Resolved string: Execute JS code to activate token
2025-08-11 09:52:55.938 [Debug] Resolving placeholders in string: Globals.ActivationState = 'ACTIVATED';
2025-08-11 09:52:55.939 [Debug] Resolved string: Globals.ActivationState = 'ACTIVATED';
2025-08-11 09:52:55.939 [Debug] Resolving placeholders in string: Click on Cards button from side menu
2025-08-11 09:52:55.939 [Debug] Resolved string: Click on Cards button from side menu
2025-08-11 09:52:55.939 [Debug] Resolving placeholders in string: {{Selectors.CardsButton}}
2025-08-11 09:52:55.939 [Debug] Resolved string: xpath=//div[@class='mainmenuitems']//label[contains(text(), 'Cards')]
2025-08-11 09:52:55.939 [Debug] Resolving placeholders in string: Click on Cards Services button from side menu
2025-08-11 09:52:55.939 [Debug] Resolved string: Click on Cards Services button from side menu
2025-08-11 09:52:55.939 [Debug] Resolving placeholders in string: {{Selectors.CardServices}}
2025-08-11 09:52:55.939 [Debug] Resolved string: xpath=//div[@class='submenuitem']//label[contains(text(), 'Card services')]
2025-08-11 09:52:55.939 [Debug] Resolving placeholders in string: Click on Direct Debit
2025-08-11 09:52:55.939 [Debug] Resolved string: Click on Direct Debit
2025-08-11 09:52:55.939 [Debug] Resolving placeholders in string: {{Selectors.DirectDebitBtn}}
2025-08-11 09:52:55.939 [Debug] Resolved string: id=DirectDebit
2025-08-11 09:52:55.939 [Debug] Resolving placeholders in string: select card 
2025-08-11 09:52:55.940 [Debug] Resolved string: select card 
2025-08-11 09:52:55.940 [Debug] Resolving placeholders in string: {{Selectors.CardSelectedinDirectDebit}}
2025-08-11 09:52:55.940 [Debug] Resolved string: xpath=//div[@class='module_list_container cardData']/*[1]
2025-08-11 09:52:55.940 [Debug] Resolving placeholders in string: click account button 
2025-08-11 09:52:55.940 [Debug] Resolved string: click account button 
2025-08-11 09:52:55.940 [Debug] Resolving placeholders in string: {{Selectors.SelectAccountBtn}}
2025-08-11 09:52:55.940 [Debug] Resolved string: id=SelectAccountBtn
2025-08-11 09:52:55.940 [Debug] Resolving placeholders in string: select account
2025-08-11 09:52:55.940 [Debug] Resolved string: select account
2025-08-11 09:52:55.940 [Debug] Resolving placeholders in string: {{Selectors.DebitAccount}}
2025-08-11 09:52:55.940 [Debug] Resolved string: xpath=//div[@class='module_list_container']//h4[contains(text(), '**************')]
2025-08-11 09:52:55.940 [Debug] Resolving placeholders in string: click Debited Percentage select
2025-08-11 09:52:55.940 [Debug] Resolved string: click Debited Percentage select
2025-08-11 09:52:55.940 [Debug] Resolving placeholders in string: {{Selectors.DebitedPercentageSelect}}
2025-08-11 09:52:55.941 [Debug] Resolved string: css=#CardsSelectionErrorHandle > div.select
2025-08-11 09:52:55.943 [Debug] Resolving placeholders in string: click Debited Percentage select option
2025-08-11 09:52:55.944 [Debug] Resolved string: click Debited Percentage select option
2025-08-11 09:52:55.944 [Debug] Resolving placeholders in string: {{Selectors.DebitedPercentageSelectOption}}
2025-08-11 09:52:55.944 [Debug] Resolved string: css=#CardsSelectionErrorHandle > div.options.back_white_solid.dropdown-active.d-block > span:nth-child(1)
2025-08-11 09:52:55.944 [Debug] Resolving placeholders in string: click ageree checkbox
2025-08-11 09:52:55.944 [Debug] Resolved string: click ageree checkbox
2025-08-11 09:52:55.944 [Debug] Resolving placeholders in string: {{Selectors.AgreeCheckBoxinDebit}}
2025-08-11 09:52:55.944 [Debug] Resolved string: css=#wginsDirectDebit_Default > div > div.section_body > div > div > div.terms.AgreeTermsAndConditions > input[type=checkbox]
2025-08-11 09:52:55.944 [Debug] Resolving placeholders in string: click on continue button
2025-08-11 09:52:55.944 [Debug] Resolved string: click on continue button
2025-08-11 09:52:55.945 [Debug] Resolving placeholders in string: {{Selectors.SubmitDirectDebitReq}}
2025-08-11 09:52:55.945 [Debug] Resolved string: id=SubmitDirectDebitReq
2025-08-11 09:52:55.945 [Debug] Resolving placeholders in string: click on confirm button
2025-08-11 09:52:55.945 [Debug] Resolved string: click on confirm button
2025-08-11 09:52:55.945 [Debug] Resolving placeholders in string: {{Selectors.DirectDebitConfirm}}
2025-08-11 09:52:55.945 [Debug] Resolved string: id=DirectDebitConfirm
2025-08-11 09:52:55.945 [Debug] Resolving placeholders in string: Enter Token number
2025-08-11 09:52:55.945 [Debug] Resolved string: Enter Token number
2025-08-11 09:52:55.946 [Debug] Resolving placeholders in string: {{Selectors.TokenInput}}
2025-08-11 09:52:55.946 [Debug] Resolved string: id=TokenNUMBER
2025-08-11 09:52:55.946 [Debug] Resolving placeholders in string: 123456
2025-08-11 09:52:55.946 [Debug] Resolved string: 123456
2025-08-11 09:52:55.946 [Debug] Resolving placeholders in string: Click on confirm and show the confirmation page
2025-08-11 09:52:55.946 [Debug] Resolved string: Click on confirm and show the confirmation page
2025-08-11 09:52:55.946 [Debug] Resolving placeholders in string: {{Selectors.btnTokenConfirm}}
2025-08-11 09:52:55.946 [Debug] Resolved string: id=btnTokenConfirm
2025-08-11 09:52:55.946 [Info] Successfully resolved parameters for test case: Direct Debit, standard users
2025-08-11 09:52:55.946 [Debug] Loading test case: WorkSet01/TestCases/012.CardServices/ActivateCard/121-Activate Card, check that inactive card in backend are displayed.json
2025-08-11 09:52:55.946 [Info] Loading test case from: WorkSet01/TestCases/012.CardServices/ActivateCard/121-Activate Card, check that inactive card in backend are displayed.json
2025-08-11 09:52:55.946 [Info] Attempting to load configuration file: WorkSet01/TestCases/012.CardServices/ActivateCard/121-Activate Card, check that inactive card in backend are displayed.json
2025-08-11 09:52:55.952 [Info] Successfully loaded configuration file: WorkSet01/TestCases/012.CardServices/ActivateCard/121-Activate Card, check that inactive card in backend are displayed.json
2025-08-11 09:52:55.952 [Info] No filtering configuration found. Including test case 'Activate Card, check that inactive card in backend are displayed'.
2025-08-11 09:52:55.953 [Debug] Resolving parameters for test case: Activate Card, check that inactive card in backend are displayed
2025-08-11 09:52:55.953 [Debug] Merging params
2025-08-11 09:52:55.953 [Debug] Merged basic params
2025-08-11 09:52:55.953 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-11 09:52:55.953 [Debug] Merged SpecialEnvParams params
2025-08-11 09:52:55.953 [Debug] Merged params
2025-08-11 09:52:55.953 [Debug] Loading parameters from reference file: WorkSet01/Params/ActivateCardParm.json
2025-08-11 09:52:55.953 [Info] Attempting to load configuration file: WorkSet01/Params/ActivateCardParm.json
2025-08-11 09:52:55.965 [Info] Successfully loaded configuration file: WorkSet01/Params/ActivateCardParm.json
2025-08-11 09:52:55.965 [Debug] Merging params
2025-08-11 09:52:55.965 [Debug] Merged basic params
2025-08-11 09:52:55.966 [Debug] Merged params
2025-08-11 09:52:55.966 [Debug] Merging params
2025-08-11 09:52:55.966 [Debug] Merging params
2025-08-11 09:52:55.966 [Debug] Resolving placeholders in test steps for test case: Activate Card, check that inactive card in backend are displayed
2025-08-11 09:52:55.966 [Debug] Resolving TestCaseReference in step: 
2025-08-11 09:52:55.966 [Info] Loading test case from: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-11 09:52:55.967 [Info] Attempting to load configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-11 09:52:55.968 [Info] Successfully loaded configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-11 09:52:55.968 [Debug] Resolving parameters for test case: Login
2025-08-11 09:52:55.969 [Debug] Merging params
2025-08-11 09:52:55.969 [Debug] Merged basic params
2025-08-11 09:52:55.969 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-11 09:52:55.969 [Debug] Merged SpecialEnvParams params
2025-08-11 09:52:55.970 [Debug] Merged params
2025-08-11 09:52:55.970 [Debug] Loading parameters from reference file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-11 09:52:55.970 [Info] Attempting to load configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-11 09:52:55.971 [Info] Successfully loaded configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-11 09:52:55.971 [Debug] Merging params
2025-08-11 09:52:55.972 [Debug] Merged basic params
2025-08-11 09:52:55.972 [Debug] Merged params
2025-08-11 09:52:55.973 [Debug] Merging params
2025-08-11 09:52:55.973 [Debug] Merging params
2025-08-11 09:52:55.973 [Debug] Resolving placeholders in test steps for test case: Login
2025-08-11 09:52:55.973 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-11 09:52:55.973 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-11 09:52:55.973 [Debug] Resolving placeholders in string: {{Selectors.FinishButton}}
2025-08-11 09:52:55.973 [Debug] Resolved string: id=finish
2025-08-11 09:52:55.973 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-11 09:52:55.973 [Debug] Resolved string: Click on Login Button
2025-08-11 09:52:55.973 [Debug] Resolving placeholders in string: {{Selectors.LoginButton}}
2025-08-11 09:52:55.973 [Debug] Resolved string: id=LoginBtn
2025-08-11 09:52:55.974 [Debug] Resolving placeholders in string: Type the User Name
2025-08-11 09:52:55.974 [Debug] Resolved string: Type the User Name
2025-08-11 09:52:55.974 [Debug] Resolving placeholders in string: {{Selectors.UserNameField}}
2025-08-11 09:52:55.975 [Debug] Resolved string: id=UserName
2025-08-11 09:52:55.975 [Debug] Resolving placeholders in string: {{TestData.UserName}}
2025-08-11 09:52:55.975 [Debug] Resolved string: mahmoudsayed022
2025-08-11 09:52:55.975 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-11 09:52:55.975 [Debug] Resolved string: Click on Continue Button
2025-08-11 09:52:55.975 [Debug] Resolving placeholders in string: {{Selectors.ContinueButton}}
2025-08-11 09:52:55.975 [Debug] Resolved string: id=btn
2025-08-11 09:52:55.975 [Debug] Resolving placeholders in string: Type the Password
2025-08-11 09:52:55.976 [Debug] Resolved string: Type the Password
2025-08-11 09:52:55.976 [Debug] Resolving placeholders in string: {{Selectors.PasswordField}}
2025-08-11 09:52:55.976 [Debug] Resolved string: id=Password
2025-08-11 09:52:55.977 [Debug] Resolving placeholders in string: $('[{{Selectors.PasswordField}}]').val('{{TestData.Password}}')
2025-08-11 09:52:55.977 [Debug] Resolved string: $('[id=Password]').val('Password1')
2025-08-11 09:52:55.978 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-11 09:52:55.978 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-11 09:52:55.978 [Debug] Resolving placeholders in string: {{Selectors.ChallengeQuestionField}}
2025-08-11 09:52:55.979 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-11 09:52:55.979 [Debug] Resolving placeholders in string: {{TestData.ChallengeAnswer}}
2025-08-11 09:52:55.980 [Debug] Resolved string: bmw
2025-08-11 09:52:55.980 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-11 09:52:55.980 [Debug] Resolved string: Click on the Login Button
2025-08-11 09:52:55.980 [Debug] Resolving placeholders in string: {{Selectors.LoginAuthButton}}
2025-08-11 09:52:55.980 [Debug] Resolved string: id=LoginAuthBtn
2025-08-11 09:52:55.980 [Info] Successfully resolved parameters for test case: Login
2025-08-11 09:52:55.980 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-11 09:52:55.981 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-11 09:52:55.981 [Debug] Resolving placeholders in string: id=finish
2025-08-11 09:52:55.981 [Debug] Resolved string: id=finish
2025-08-11 09:52:55.981 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-11 09:52:55.981 [Debug] Resolved string: Click on Login Button
2025-08-11 09:52:55.982 [Debug] Resolving placeholders in string: id=LoginBtn
2025-08-11 09:52:55.982 [Debug] Resolved string: id=LoginBtn
2025-08-11 09:52:55.982 [Debug] Resolving placeholders in string: Type the User Name
2025-08-11 09:52:55.982 [Debug] Resolved string: Type the User Name
2025-08-11 09:52:55.982 [Debug] Resolving placeholders in string: id=UserName
2025-08-11 09:52:55.982 [Debug] Resolved string: id=UserName
2025-08-11 09:52:55.982 [Debug] Resolving placeholders in string: mahmoudsayed022
2025-08-11 09:52:55.982 [Debug] Resolved string: mahmoudsayed022
2025-08-11 09:52:55.982 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-11 09:52:55.983 [Debug] Resolved string: Click on Continue Button
2025-08-11 09:52:55.983 [Debug] Resolving placeholders in string: id=btn
2025-08-11 09:52:55.983 [Debug] Resolved string: id=btn
2025-08-11 09:52:55.983 [Debug] Resolving placeholders in string: Type the Password
2025-08-11 09:52:55.983 [Debug] Resolved string: Type the Password
2025-08-11 09:52:55.983 [Debug] Resolving placeholders in string: id=Password
2025-08-11 09:52:55.983 [Debug] Resolved string: id=Password
2025-08-11 09:52:55.983 [Debug] Resolving placeholders in string: $('[id=Password]').val('Password1')
2025-08-11 09:52:55.983 [Debug] Resolved string: $('[id=Password]').val('Password1')
2025-08-11 09:52:55.984 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-11 09:52:55.984 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-11 09:52:55.984 [Debug] Resolving placeholders in string: id=ChallengeQuestionAnswer
2025-08-11 09:52:55.985 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-11 09:52:55.985 [Debug] Resolving placeholders in string: bmw
2025-08-11 09:52:55.985 [Debug] Resolved string: bmw
2025-08-11 09:52:55.985 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-11 09:52:55.985 [Debug] Resolved string: Click on the Login Button
2025-08-11 09:52:55.985 [Debug] Resolving placeholders in string: id=LoginAuthBtn
2025-08-11 09:52:55.985 [Debug] Resolved string: id=LoginAuthBtn
2025-08-11 09:52:55.985 [Debug] Resolving placeholders in string: Click on change later button on the password expiration popup message
2025-08-11 09:52:55.985 [Debug] Resolved string: Click on change later button on the password expiration popup message
2025-08-11 09:52:55.986 [Debug] Resolving placeholders in string: {{Selectors.popupCancel}}
2025-08-11 09:52:55.986 [Debug] Resolved string: id=popup_cancel
2025-08-11 09:52:55.986 [Debug] Resolving placeholders in string: Click on biometric alert
2025-08-11 09:52:55.986 [Debug] Resolved string: Click on biometric alert
2025-08-11 09:52:55.986 [Debug] Resolving placeholders in string: {{Selectors.BioMetricAlert}}
2025-08-11 09:52:55.987 [Debug] Resolved string: css=#popbuttons > div > button.btn.Cancel.back_gradient2
2025-08-11 09:52:55.987 [Debug] Resolving placeholders in string: Click on Cards button from side menu
2025-08-11 09:52:55.987 [Debug] Resolved string: Click on Cards button from side menu
2025-08-11 09:52:55.987 [Debug] Resolving placeholders in string: {{Selectors.CardsButton}}
2025-08-11 09:52:55.987 [Debug] Resolved string: xpath=//div[@class='mainmenuitems']//label[contains(text(), 'Cards')]
2025-08-11 09:52:55.987 [Debug] Resolving placeholders in string: Click on Cards Services button from side menu
2025-08-11 09:52:55.987 [Debug] Resolved string: Click on Cards Services button from side menu
2025-08-11 09:52:55.987 [Debug] Resolving placeholders in string: {{Selectors.CardServices}}
2025-08-11 09:52:55.987 [Debug] Resolved string: xpath=//div[@class='submenuitem']//label[contains(text(), 'Card services')]
2025-08-11 09:52:55.988 [Debug] Resolving placeholders in string: Click on Activate Card and show the cards list
2025-08-11 09:52:55.988 [Debug] Resolved string: Click on Activate Card and show the cards list
2025-08-11 09:52:55.988 [Debug] Resolving placeholders in string: {{Selectors.ActivateCard}}
2025-08-11 09:52:55.988 [Debug] Resolved string: id=ActivateCard
2025-08-11 09:52:55.988 [Debug] Resolving placeholders in string: Check if the cards list appear
2025-08-11 09:52:55.989 [Debug] Resolved string: Check if the cards list appear
2025-08-11 09:52:55.989 [Debug] Resolving placeholders in string: {{Selectors.CardList}}
2025-08-11 09:52:55.989 [Debug] Resolved string: css=#CardsSelectionErrorHandle
2025-08-11 09:52:55.989 [Info] Successfully resolved parameters for test case: Activate Card, check that inactive card in backend are displayed
2025-08-11 09:52:55.989 [Debug] Loading test case: WorkSet01/TestCases/012.CardServices/BlockCard/120-ClickBlockCardInquiry.json
2025-08-11 09:52:55.989 [Info] Loading test case from: WorkSet01/TestCases/012.CardServices/BlockCard/120-ClickBlockCardInquiry.json
2025-08-11 09:52:55.990 [Info] Attempting to load configuration file: WorkSet01/TestCases/012.CardServices/BlockCard/120-ClickBlockCardInquiry.json
2025-08-11 09:52:56.001 [Info] Successfully loaded configuration file: WorkSet01/TestCases/012.CardServices/BlockCard/120-ClickBlockCardInquiry.json
2025-08-11 09:52:56.001 [Info] No filtering configuration found. Including test case 'Click Block card'.
2025-08-11 09:52:56.001 [Debug] Resolving parameters for test case: Click Block card
2025-08-11 09:52:56.001 [Debug] Merging params
2025-08-11 09:52:56.001 [Debug] Merged basic params
2025-08-11 09:52:56.001 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-11 09:52:56.002 [Debug] Merged SpecialEnvParams params
2025-08-11 09:52:56.002 [Debug] Merged params
2025-08-11 09:52:56.002 [Debug] Loading parameters from reference file: WorkSet01/Params/BlockCardParams.json
2025-08-11 09:52:56.002 [Info] Attempting to load configuration file: WorkSet01/Params/BlockCardParams.json
2025-08-11 09:52:56.003 [Info] Successfully loaded configuration file: WorkSet01/Params/BlockCardParams.json
2025-08-11 09:52:56.003 [Debug] Merging params
2025-08-11 09:52:56.003 [Debug] Merged basic params
2025-08-11 09:52:56.003 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-11 09:52:56.003 [Debug] Merged SpecialEnvParams params
2025-08-11 09:52:56.003 [Debug] Merged params
2025-08-11 09:52:56.003 [Debug] Merging params
2025-08-11 09:52:56.003 [Debug] Merging params
2025-08-11 09:52:56.003 [Debug] Resolving placeholders in test steps for test case: Click Block card
2025-08-11 09:52:56.004 [Debug] Resolving TestCaseReference in step: 
2025-08-11 09:52:56.004 [Info] Loading test case from: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-11 09:52:56.004 [Info] Attempting to load configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-11 09:52:56.004 [Info] Successfully loaded configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-11 09:52:56.004 [Debug] Resolving parameters for test case: Login
2025-08-11 09:52:56.004 [Debug] Merging params
2025-08-11 09:52:56.004 [Debug] Merged basic params
2025-08-11 09:52:56.005 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-11 09:52:56.005 [Debug] Merged SpecialEnvParams params
2025-08-11 09:52:56.005 [Debug] Merged params
2025-08-11 09:52:56.006 [Debug] Loading parameters from reference file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-11 09:52:56.006 [Info] Attempting to load configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-11 09:52:56.007 [Info] Successfully loaded configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-11 09:52:56.007 [Debug] Merging params
2025-08-11 09:52:56.008 [Debug] Merged basic params
2025-08-11 09:52:56.008 [Debug] Merged params
2025-08-11 09:52:56.008 [Debug] Merging params
2025-08-11 09:52:56.008 [Debug] Merging params
2025-08-11 09:52:56.008 [Debug] Resolving placeholders in test steps for test case: Login
2025-08-11 09:52:56.008 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-11 09:52:56.008 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-11 09:52:56.008 [Debug] Resolving placeholders in string: {{Selectors.FinishButton}}
2025-08-11 09:52:56.009 [Debug] Resolved string: id=finish
2025-08-11 09:52:56.009 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-11 09:52:56.009 [Debug] Resolved string: Click on Login Button
2025-08-11 09:52:56.009 [Debug] Resolving placeholders in string: {{Selectors.LoginButton}}
2025-08-11 09:52:56.009 [Debug] Resolved string: id=LoginBtn
2025-08-11 09:52:56.009 [Debug] Resolving placeholders in string: Type the User Name
2025-08-11 09:52:56.009 [Debug] Resolved string: Type the User Name
2025-08-11 09:52:56.009 [Debug] Resolving placeholders in string: {{Selectors.UserNameField}}
2025-08-11 09:52:56.009 [Debug] Resolved string: id=UserName
2025-08-11 09:52:56.009 [Debug] Resolving placeholders in string: {{TestData.UserName}}
2025-08-11 09:52:56.009 [Debug] Resolved string: mahmoudsayed022
2025-08-11 09:52:56.010 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-11 09:52:56.010 [Debug] Resolved string: Click on Continue Button
2025-08-11 09:52:56.010 [Debug] Resolving placeholders in string: {{Selectors.ContinueButton}}
2025-08-11 09:52:56.010 [Debug] Resolved string: id=btn
2025-08-11 09:52:56.010 [Debug] Resolving placeholders in string: Type the Password
2025-08-11 09:52:56.010 [Debug] Resolved string: Type the Password
2025-08-11 09:52:56.010 [Debug] Resolving placeholders in string: {{Selectors.PasswordField}}
2025-08-11 09:52:56.010 [Debug] Resolved string: id=Password
2025-08-11 09:52:56.010 [Debug] Resolving placeholders in string: $('[{{Selectors.PasswordField}}]').val('{{TestData.Password}}')
2025-08-11 09:52:56.011 [Debug] Resolved string: $('[id=Password]').val('Password1')
2025-08-11 09:52:56.011 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-11 09:52:56.011 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-11 09:52:56.011 [Debug] Resolving placeholders in string: {{Selectors.ChallengeQuestionField}}
2025-08-11 09:52:56.012 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-11 09:52:56.012 [Debug] Resolving placeholders in string: {{TestData.ChallengeAnswer}}
2025-08-11 09:52:56.012 [Debug] Resolved string: bmw
2025-08-11 09:52:56.012 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-11 09:52:56.012 [Debug] Resolved string: Click on the Login Button
2025-08-11 09:52:56.012 [Debug] Resolving placeholders in string: {{Selectors.LoginAuthButton}}
2025-08-11 09:52:56.013 [Debug] Resolved string: id=LoginAuthBtn
2025-08-11 09:52:56.013 [Info] Successfully resolved parameters for test case: Login
2025-08-11 09:52:56.013 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-11 09:52:56.013 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-11 09:52:56.013 [Debug] Resolving placeholders in string: id=finish
2025-08-11 09:52:56.013 [Debug] Resolved string: id=finish
2025-08-11 09:52:56.013 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-11 09:52:56.013 [Debug] Resolved string: Click on Login Button
2025-08-11 09:52:56.013 [Debug] Resolving placeholders in string: id=LoginBtn
2025-08-11 09:52:56.014 [Debug] Resolved string: id=LoginBtn
2025-08-11 09:52:56.014 [Debug] Resolving placeholders in string: Type the User Name
2025-08-11 09:52:56.014 [Debug] Resolved string: Type the User Name
2025-08-11 09:52:56.014 [Debug] Resolving placeholders in string: id=UserName
2025-08-11 09:52:56.014 [Debug] Resolved string: id=UserName
2025-08-11 09:52:56.014 [Debug] Resolving placeholders in string: mahmoudsayed022
2025-08-11 09:52:56.014 [Debug] Resolved string: mahmoudsayed022
2025-08-11 09:52:56.015 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-11 09:52:56.015 [Debug] Resolved string: Click on Continue Button
2025-08-11 09:52:56.015 [Debug] Resolving placeholders in string: id=btn
2025-08-11 09:52:56.015 [Debug] Resolved string: id=btn
2025-08-11 09:52:56.015 [Debug] Resolving placeholders in string: Type the Password
2025-08-11 09:52:56.015 [Debug] Resolved string: Type the Password
2025-08-11 09:52:56.015 [Debug] Resolving placeholders in string: id=Password
2025-08-11 09:52:56.015 [Debug] Resolved string: id=Password
2025-08-11 09:52:56.015 [Debug] Resolving placeholders in string: $('[id=Password]').val('Password1')
2025-08-11 09:52:56.015 [Debug] Resolved string: $('[id=Password]').val('Password1')
2025-08-11 09:52:56.015 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-11 09:52:56.015 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-11 09:52:56.016 [Debug] Resolving placeholders in string: id=ChallengeQuestionAnswer
2025-08-11 09:52:56.016 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-11 09:52:56.016 [Debug] Resolving placeholders in string: bmw
2025-08-11 09:52:56.016 [Debug] Resolved string: bmw
2025-08-11 09:52:56.016 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-11 09:52:56.016 [Debug] Resolved string: Click on the Login Button
2025-08-11 09:52:56.016 [Debug] Resolving placeholders in string: id=LoginAuthBtn
2025-08-11 09:52:56.016 [Debug] Resolved string: id=LoginAuthBtn
2025-08-11 09:52:56.017 [Debug] Resolving placeholders in string: Click on change later button on the password expiration popup message
2025-08-11 09:52:56.018 [Debug] Resolved string: Click on change later button on the password expiration popup message
2025-08-11 09:52:56.018 [Debug] Resolving placeholders in string: {{Selectors.popupCancel}}
2025-08-11 09:52:56.018 [Debug] Resolved string: id=popup_cancel
2025-08-11 09:52:56.018 [Debug] Resolving placeholders in string: Click on biometric alert
2025-08-11 09:52:56.019 [Debug] Resolved string: Click on biometric alert
2025-08-11 09:52:56.019 [Debug] Resolving placeholders in string: {{Selectors.BioMetricAlert}}
2025-08-11 09:52:56.019 [Debug] Resolved string: css=#popbuttons > div > button.btn.Cancel.back_gradient2
2025-08-11 09:52:56.019 [Debug] Resolving placeholders in string: Click on biometric alert
2025-08-11 09:52:56.019 [Debug] Resolved string: Click on biometric alert
2025-08-11 09:52:56.019 [Debug] Resolving placeholders in string: {{Selectors.KYCskip}}
2025-08-11 09:52:56.020 [Debug] Resolved string: css=#popbuttons > div > button.btn.Cancel.back_gradient2
2025-08-11 09:52:56.020 [Debug] Resolving placeholders in string: Execute JS code to activate token
2025-08-11 09:52:56.020 [Debug] Resolved string: Execute JS code to activate token
2025-08-11 09:52:56.020 [Debug] Resolving placeholders in string: Globals.ActivationState = 'ACTIVATED';
2025-08-11 09:52:56.020 [Debug] Resolved string: Globals.ActivationState = 'ACTIVATED';
2025-08-11 09:52:56.020 [Debug] Resolving placeholders in string: Click on Cards button from side menu
2025-08-11 09:52:56.020 [Debug] Resolved string: Click on Cards button from side menu
2025-08-11 09:52:56.020 [Debug] Resolving placeholders in string: {{Selectors.CardsButton}}
2025-08-11 09:52:56.021 [Debug] Resolved string: xpath=//div[@class='mainmenuitems']//label[contains(text(), 'Cards')]
2025-08-11 09:52:56.021 [Debug] Resolving placeholders in string: Click on Card Inquiry and check cards details
2025-08-11 09:52:56.021 [Debug] Resolved string: Click on Card Inquiry and check cards details
2025-08-11 09:52:56.021 [Debug] Resolving placeholders in string: {{Selectors.CardInquiry}}
2025-08-11 09:52:56.021 [Debug] Resolved string: xpath=//div[@class='submenuitem']//label[contains(text(), 'Card Inquiry')]
2025-08-11 09:52:56.021 [Debug] Resolving placeholders in string: Click on a card to block it
2025-08-11 09:52:56.021 [Debug] Resolved string: Click on a card to block it
2025-08-11 09:52:56.021 [Debug] Resolving placeholders in string: {{Selectors.CardToBlockFromInquiry}}
2025-08-11 09:52:56.022 [Debug] Resolved string: xpath=//div[@id='CardsListWorking']//h3[contains(text(), '4023XXXXXXXX0007')]
2025-08-11 09:52:56.022 [Debug] Resolving placeholders in string: Click on Block Button
2025-08-11 09:52:56.022 [Debug] Resolved string: Click on Block Button
2025-08-11 09:52:56.022 [Debug] Resolving placeholders in string: {{Selectors.BlockCardOptionInquiry}}
2025-08-11 09:52:56.022 [Debug] Resolved string: id=Stopbtn
2025-08-11 09:52:56.023 [Debug] Resolving placeholders in string: Click on block card button
2025-08-11 09:52:56.023 [Debug] Resolved string: Click on block card button
2025-08-11 09:52:56.023 [Debug] Resolving placeholders in string: {{Selectors.BlockCardBtnSubmit}}
2025-08-11 09:52:56.023 [Debug] Resolved string: id=SubmitBlockCardReq
2025-08-11 09:52:56.023 [Debug] Resolving placeholders in string: Click on block card confirm
2025-08-11 09:52:56.023 [Debug] Resolved string: Click on block card confirm
2025-08-11 09:52:56.023 [Debug] Resolving placeholders in string: {{Selectors.BlockCardConfirm}}
2025-08-11 09:52:56.024 [Debug] Resolved string: id=BlockCardConfirm
2025-08-11 09:52:56.024 [Info] Successfully resolved parameters for test case: Click Block card
2025-08-11 09:52:56.024 [Debug] Loading test case: WorkSet01/TestCases/012.CardServices/BlockCard/125-CheckBlockedCardsList.json
2025-08-11 09:52:56.024 [Info] Loading test case from: WorkSet01/TestCases/012.CardServices/BlockCard/125-CheckBlockedCardsList.json
2025-08-11 09:52:56.024 [Info] Attempting to load configuration file: WorkSet01/TestCases/012.CardServices/BlockCard/125-CheckBlockedCardsList.json
2025-08-11 09:52:56.025 [Info] Successfully loaded configuration file: WorkSet01/TestCases/012.CardServices/BlockCard/125-CheckBlockedCardsList.json
2025-08-11 09:52:56.026 [Info] No filtering configuration found. Including test case 'Check Blocked Cards List'.
2025-08-11 09:52:56.026 [Debug] Resolving parameters for test case: Check Blocked Cards List
2025-08-11 09:52:56.026 [Debug] Merging params
2025-08-11 09:52:56.026 [Debug] Merged basic params
2025-08-11 09:52:56.026 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-11 09:52:56.027 [Debug] Merged SpecialEnvParams params
2025-08-11 09:52:56.027 [Debug] Merged params
2025-08-11 09:52:56.027 [Debug] Loading parameters from reference file: WorkSet01/Params/BlockCardParams.json
2025-08-11 09:52:56.027 [Info] Attempting to load configuration file: WorkSet01/Params/BlockCardParams.json
2025-08-11 09:52:56.027 [Info] Successfully loaded configuration file: WorkSet01/Params/BlockCardParams.json
2025-08-11 09:52:56.028 [Debug] Merging params
2025-08-11 09:52:56.028 [Debug] Merged basic params
2025-08-11 09:52:56.028 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-11 09:52:56.028 [Debug] Merged SpecialEnvParams params
2025-08-11 09:52:56.028 [Debug] Merged params
2025-08-11 09:52:56.028 [Debug] Merging params
2025-08-11 09:52:56.028 [Debug] Merging params
2025-08-11 09:52:56.028 [Debug] Resolving placeholders in test steps for test case: Check Blocked Cards List
2025-08-11 09:52:56.028 [Debug] Resolving TestCaseReference in step: 
2025-08-11 09:52:56.028 [Info] Loading test case from: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-11 09:52:56.028 [Info] Attempting to load configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-11 09:52:56.029 [Info] Successfully loaded configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-11 09:52:56.030 [Debug] Resolving parameters for test case: Login
2025-08-11 09:52:56.030 [Debug] Merging params
2025-08-11 09:52:56.030 [Debug] Merged basic params
2025-08-11 09:52:56.031 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-11 09:52:56.032 [Debug] Merged SpecialEnvParams params
2025-08-11 09:52:56.032 [Debug] Merged params
2025-08-11 09:52:56.032 [Debug] Loading parameters from reference file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-11 09:52:56.033 [Info] Attempting to load configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-11 09:52:56.033 [Info] Successfully loaded configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-11 09:52:56.033 [Debug] Merging params
2025-08-11 09:52:56.033 [Debug] Merged basic params
2025-08-11 09:52:56.033 [Debug] Merged params
2025-08-11 09:52:56.034 [Debug] Merging params
2025-08-11 09:52:56.034 [Debug] Merging params
2025-08-11 09:52:56.034 [Debug] Resolving placeholders in test steps for test case: Login
2025-08-11 09:52:56.034 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-11 09:52:56.034 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-11 09:52:56.035 [Debug] Resolving placeholders in string: {{Selectors.FinishButton}}
2025-08-11 09:52:56.035 [Debug] Resolved string: id=finish
2025-08-11 09:52:56.035 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-11 09:52:56.035 [Debug] Resolved string: Click on Login Button
2025-08-11 09:52:56.035 [Debug] Resolving placeholders in string: {{Selectors.LoginButton}}
2025-08-11 09:52:56.035 [Debug] Resolved string: id=LoginBtn
2025-08-11 09:52:56.035 [Debug] Resolving placeholders in string: Type the User Name
2025-08-11 09:52:56.035 [Debug] Resolved string: Type the User Name
2025-08-11 09:52:56.035 [Debug] Resolving placeholders in string: {{Selectors.UserNameField}}
2025-08-11 09:52:56.035 [Debug] Resolved string: id=UserName
2025-08-11 09:52:56.035 [Debug] Resolving placeholders in string: {{TestData.UserName}}
2025-08-11 09:52:56.035 [Debug] Resolved string: mahmoudsayed022
2025-08-11 09:52:56.036 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-11 09:52:56.036 [Debug] Resolved string: Click on Continue Button
2025-08-11 09:52:56.036 [Debug] Resolving placeholders in string: {{Selectors.ContinueButton}}
2025-08-11 09:52:56.036 [Debug] Resolved string: id=btn
2025-08-11 09:52:56.036 [Debug] Resolving placeholders in string: Type the Password
2025-08-11 09:52:56.036 [Debug] Resolved string: Type the Password
2025-08-11 09:52:56.036 [Debug] Resolving placeholders in string: {{Selectors.PasswordField}}
2025-08-11 09:52:56.036 [Debug] Resolved string: id=Password
2025-08-11 09:52:56.036 [Debug] Resolving placeholders in string: $('[{{Selectors.PasswordField}}]').val('{{TestData.Password}}')
2025-08-11 09:52:56.036 [Debug] Resolved string: $('[id=Password]').val('Password1')
2025-08-11 09:52:56.036 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-11 09:52:56.036 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-11 09:52:56.036 [Debug] Resolving placeholders in string: {{Selectors.ChallengeQuestionField}}
2025-08-11 09:52:56.037 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-11 09:52:56.037 [Debug] Resolving placeholders in string: {{TestData.ChallengeAnswer}}
2025-08-11 09:52:56.037 [Debug] Resolved string: bmw
2025-08-11 09:52:56.037 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-11 09:52:56.038 [Debug] Resolved string: Click on the Login Button
2025-08-11 09:52:56.038 [Debug] Resolving placeholders in string: {{Selectors.LoginAuthButton}}
2025-08-11 09:52:56.039 [Debug] Resolved string: id=LoginAuthBtn
2025-08-11 09:52:56.039 [Info] Successfully resolved parameters for test case: Login
2025-08-11 09:52:56.040 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-11 09:52:56.040 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-11 09:52:56.040 [Debug] Resolving placeholders in string: id=finish
2025-08-11 09:52:56.041 [Debug] Resolved string: id=finish
2025-08-11 09:52:56.041 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-11 09:52:56.042 [Debug] Resolved string: Click on Login Button
2025-08-11 09:52:56.042 [Debug] Resolving placeholders in string: id=LoginBtn
2025-08-11 09:52:56.043 [Debug] Resolved string: id=LoginBtn
2025-08-11 09:52:56.043 [Debug] Resolving placeholders in string: Type the User Name
2025-08-11 09:52:56.044 [Debug] Resolved string: Type the User Name
2025-08-11 09:52:56.045 [Debug] Resolving placeholders in string: id=UserName
2025-08-11 09:52:56.045 [Debug] Resolved string: id=UserName
2025-08-11 09:52:56.045 [Debug] Resolving placeholders in string: mahmoudsayed022
2025-08-11 09:52:56.045 [Debug] Resolved string: mahmoudsayed022
2025-08-11 09:52:56.045 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-11 09:52:56.046 [Debug] Resolved string: Click on Continue Button
2025-08-11 09:52:56.046 [Debug] Resolving placeholders in string: id=btn
2025-08-11 09:52:56.046 [Debug] Resolved string: id=btn
2025-08-11 09:52:56.046 [Debug] Resolving placeholders in string: Type the Password
2025-08-11 09:52:56.046 [Debug] Resolved string: Type the Password
2025-08-11 09:52:56.047 [Debug] Resolving placeholders in string: id=Password
2025-08-11 09:52:56.059 [Debug] Resolved string: id=Password
2025-08-11 09:52:56.059 [Debug] Resolving placeholders in string: $('[id=Password]').val('Password1')
2025-08-11 09:52:56.059 [Debug] Resolved string: $('[id=Password]').val('Password1')
2025-08-11 09:52:56.059 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-11 09:52:56.059 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-11 09:52:56.059 [Debug] Resolving placeholders in string: id=ChallengeQuestionAnswer
2025-08-11 09:52:56.061 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-11 09:52:56.061 [Debug] Resolving placeholders in string: bmw
2025-08-11 09:52:56.062 [Debug] Resolved string: bmw
2025-08-11 09:52:56.062 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-11 09:52:56.064 [Debug] Resolved string: Click on the Login Button
2025-08-11 09:52:56.065 [Debug] Resolving placeholders in string: id=LoginAuthBtn
2025-08-11 09:52:56.065 [Debug] Resolved string: id=LoginAuthBtn
2025-08-11 09:52:56.066 [Debug] Resolving placeholders in string: Click on change later button on the password expiration popup message
2025-08-11 09:52:56.066 [Debug] Resolved string: Click on change later button on the password expiration popup message
2025-08-11 09:52:56.066 [Debug] Resolving placeholders in string: {{Selectors.popupCancel}}
2025-08-11 09:52:56.067 [Debug] Resolved string: id=popup_cancel
2025-08-11 09:52:56.067 [Debug] Resolving placeholders in string: Click on biometric alert
2025-08-11 09:52:56.067 [Debug] Resolved string: Click on biometric alert
2025-08-11 09:52:56.068 [Debug] Resolving placeholders in string: {{Selectors.BioMetricAlert}}
2025-08-11 09:52:56.068 [Debug] Resolved string: css=#popbuttons > div > button.btn.Cancel.back_gradient2
2025-08-11 09:52:56.069 [Debug] Resolving placeholders in string: Execute JS code to activate token
2025-08-11 09:52:56.069 [Debug] Resolved string: Execute JS code to activate token
2025-08-11 09:52:56.070 [Debug] Resolving placeholders in string: Globals.ActivationState = 'ACTIVATED';
2025-08-11 09:52:56.070 [Debug] Resolved string: Globals.ActivationState = 'ACTIVATED';
2025-08-11 09:52:56.071 [Debug] Resolving placeholders in string: Click on Cards option on the side menu
2025-08-11 09:52:56.071 [Debug] Resolved string: Click on Cards option on the side menu
2025-08-11 09:52:56.071 [Debug] Resolving placeholders in string: {{Selectors.CardsButton}}
2025-08-11 09:52:56.072 [Debug] Resolved string: xpath=//div[@class='mainmenuitems']//label[contains(text(), 'Cards')]
2025-08-11 09:52:56.072 [Debug] Resolving placeholders in string: Click on Card Services
2025-08-11 09:52:56.072 [Debug] Resolved string: Click on Card Services
2025-08-11 09:52:56.072 [Debug] Resolving placeholders in string: {{Selectors.CardServices}}
2025-08-11 09:52:56.072 [Debug] Resolved string: xpath=//div[@class='submenuitem']//label[contains(text(), 'Card services')]
2025-08-11 09:52:56.072 [Debug] Resolving placeholders in string: Click on UnBlock Btn
2025-08-11 09:52:56.072 [Debug] Resolved string: Click on UnBlock Btn
2025-08-11 09:52:56.072 [Debug] Resolving placeholders in string: {{Selectors.UnblockCard}}
2025-08-11 09:52:56.073 [Debug] Resolved string: id=UnblockCard
2025-08-11 09:52:56.073 [Debug] Resolving placeholders in string: Click on Select Card Button and show the blocked cards list
2025-08-11 09:52:56.078 [Debug] Resolved string: Click on Select Card Button and show the blocked cards list
2025-08-11 09:52:56.079 [Debug] Resolving placeholders in string: {{Selectors.SelectCardButton}}
2025-08-11 09:52:56.079 [Debug] Resolved string: id=SelectCardBtn
2025-08-11 09:52:56.079 [Debug] Resolving placeholders in string: Check if the blocked cards appear in the card list.
2025-08-11 09:52:56.080 [Debug] Resolved string: Check if the blocked cards appear in the card list.
2025-08-11 09:52:56.080 [Debug] Resolving placeholders in string: css=#CardsSelectionErrorHandle > div:nth-child(1)
2025-08-11 09:52:56.080 [Debug] Resolved string: css=#CardsSelectionErrorHandle > div:nth-child(1)
2025-08-11 09:52:56.080 [Info] Successfully resolved parameters for test case: Check Blocked Cards List
2025-08-11 09:52:56.083 [Debug] Loading test case: WorkSet01/TestCases/012.CardServices/BlockCard/114-CardInquiry2Widgets.json
2025-08-11 09:52:56.084 [Info] Loading test case from: WorkSet01/TestCases/012.CardServices/BlockCard/114-CardInquiry2Widgets.json
2025-08-11 09:52:56.084 [Info] Attempting to load configuration file: WorkSet01/TestCases/012.CardServices/BlockCard/114-CardInquiry2Widgets.json
2025-08-11 09:52:56.093 [Info] Successfully loaded configuration file: WorkSet01/TestCases/012.CardServices/BlockCard/114-CardInquiry2Widgets.json
2025-08-11 09:52:56.094 [Info] No filtering configuration found. Including test case 'Credit cards and prepaid cards displayed on 2 widgets'.
2025-08-11 09:52:56.094 [Debug] Resolving parameters for test case: Credit cards and prepaid cards displayed on 2 widgets
2025-08-11 09:52:56.094 [Debug] Merging params
2025-08-11 09:52:56.096 [Debug] Merged basic params
2025-08-11 09:52:56.097 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-11 09:52:56.098 [Debug] Merged SpecialEnvParams params
2025-08-11 09:52:56.099 [Debug] Merged params
2025-08-11 09:52:56.100 [Debug] Loading parameters from reference file: WorkSet01/Params/CardServicesParams.json
2025-08-11 09:52:56.101 [Info] Attempting to load configuration file: WorkSet01/Params/CardServicesParams.json
2025-08-11 09:52:56.102 [Info] Successfully loaded configuration file: WorkSet01/Params/CardServicesParams.json
2025-08-11 09:52:56.102 [Debug] Merging params
2025-08-11 09:52:56.103 [Debug] Merged basic params
2025-08-11 09:52:56.103 [Debug] Merged params
2025-08-11 09:52:56.103 [Debug] Merging params
2025-08-11 09:52:56.104 [Debug] Merging params
2025-08-11 09:52:56.104 [Debug] Resolving placeholders in test steps for test case: Credit cards and prepaid cards displayed on 2 widgets
2025-08-11 09:52:56.104 [Debug] Resolving TestCaseReference in step: 
2025-08-11 09:52:56.105 [Info] Loading test case from: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-11 09:52:56.105 [Info] Attempting to load configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-11 09:52:56.106 [Info] Successfully loaded configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-11 09:52:56.107 [Debug] Resolving parameters for test case: Login
2025-08-11 09:52:56.107 [Debug] Merging params
2025-08-11 09:52:56.107 [Debug] Merged basic params
2025-08-11 09:52:56.108 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-11 09:52:56.108 [Debug] Merged SpecialEnvParams params
2025-08-11 09:52:56.109 [Debug] Merged params
2025-08-11 09:52:56.109 [Debug] Loading parameters from reference file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-11 09:52:56.109 [Info] Attempting to load configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-11 09:52:56.111 [Info] Successfully loaded configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-11 09:52:56.111 [Debug] Merging params
2025-08-11 09:52:56.111 [Debug] Merged basic params
2025-08-11 09:52:56.112 [Debug] Merged params
2025-08-11 09:52:56.112 [Debug] Merging params
2025-08-11 09:52:56.112 [Debug] Merging params
2025-08-11 09:52:56.112 [Debug] Resolving placeholders in test steps for test case: Login
2025-08-11 09:52:56.112 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-11 09:52:56.112 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-11 09:52:56.112 [Debug] Resolving placeholders in string: {{Selectors.FinishButton}}
2025-08-11 09:52:56.113 [Debug] Resolved string: id=finish
2025-08-11 09:52:56.113 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-11 09:52:56.113 [Debug] Resolved string: Click on Login Button
2025-08-11 09:52:56.113 [Debug] Resolving placeholders in string: {{Selectors.LoginButton}}
2025-08-11 09:52:56.113 [Debug] Resolved string: id=LoginBtn
2025-08-11 09:52:56.113 [Debug] Resolving placeholders in string: Type the User Name
2025-08-11 09:52:56.113 [Debug] Resolved string: Type the User Name
2025-08-11 09:52:56.113 [Debug] Resolving placeholders in string: {{Selectors.UserNameField}}
2025-08-11 09:52:56.113 [Debug] Resolved string: id=UserName
2025-08-11 09:52:56.113 [Debug] Resolving placeholders in string: {{TestData.UserName}}
2025-08-11 09:52:56.114 [Debug] Resolved string: mahmoudsayed022
2025-08-11 09:52:56.114 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-11 09:52:56.114 [Debug] Resolved string: Click on Continue Button
2025-08-11 09:52:56.114 [Debug] Resolving placeholders in string: {{Selectors.ContinueButton}}
2025-08-11 09:52:56.114 [Debug] Resolved string: id=btn
2025-08-11 09:52:56.114 [Debug] Resolving placeholders in string: Type the Password
2025-08-11 09:52:56.114 [Debug] Resolved string: Type the Password
2025-08-11 09:52:56.114 [Debug] Resolving placeholders in string: {{Selectors.PasswordField}}
2025-08-11 09:52:56.114 [Debug] Resolved string: id=Password
2025-08-11 09:52:56.114 [Debug] Resolving placeholders in string: $('[{{Selectors.PasswordField}}]').val('{{TestData.Password}}')
2025-08-11 09:52:56.115 [Debug] Resolved string: $('[id=Password]').val('Password1')
2025-08-11 09:52:56.115 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-11 09:52:56.115 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-11 09:52:56.116 [Debug] Resolving placeholders in string: {{Selectors.ChallengeQuestionField}}
2025-08-11 09:52:56.116 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-11 09:52:56.116 [Debug] Resolving placeholders in string: {{TestData.ChallengeAnswer}}
2025-08-11 09:52:56.116 [Debug] Resolved string: bmw
2025-08-11 09:52:56.116 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-11 09:52:56.117 [Debug] Resolved string: Click on the Login Button
2025-08-11 09:52:56.117 [Debug] Resolving placeholders in string: {{Selectors.LoginAuthButton}}
2025-08-11 09:52:56.117 [Debug] Resolved string: id=LoginAuthBtn
2025-08-11 09:52:56.117 [Info] Successfully resolved parameters for test case: Login
2025-08-11 09:52:56.117 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-11 09:52:56.117 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-11 09:52:56.118 [Debug] Resolving placeholders in string: id=finish
2025-08-11 09:52:56.118 [Debug] Resolved string: id=finish
2025-08-11 09:52:56.118 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-11 09:52:56.118 [Debug] Resolved string: Click on Login Button
2025-08-11 09:52:56.118 [Debug] Resolving placeholders in string: id=LoginBtn
2025-08-11 09:52:56.118 [Debug] Resolved string: id=LoginBtn
2025-08-11 09:52:56.118 [Debug] Resolving placeholders in string: Type the User Name
2025-08-11 09:52:56.118 [Debug] Resolved string: Type the User Name
2025-08-11 09:52:56.118 [Debug] Resolving placeholders in string: id=UserName
2025-08-11 09:52:56.118 [Debug] Resolved string: id=UserName
2025-08-11 09:52:56.118 [Debug] Resolving placeholders in string: mahmoudsayed022
2025-08-11 09:52:56.119 [Debug] Resolved string: mahmoudsayed022
2025-08-11 09:52:56.119 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-11 09:52:56.119 [Debug] Resolved string: Click on Continue Button
2025-08-11 09:52:56.119 [Debug] Resolving placeholders in string: id=btn
2025-08-11 09:52:56.119 [Debug] Resolved string: id=btn
2025-08-11 09:52:56.119 [Debug] Resolving placeholders in string: Type the Password
2025-08-11 09:52:56.119 [Debug] Resolved string: Type the Password
2025-08-11 09:52:56.119 [Debug] Resolving placeholders in string: id=Password
2025-08-11 09:52:56.119 [Debug] Resolved string: id=Password
2025-08-11 09:52:56.120 [Debug] Resolving placeholders in string: $('[id=Password]').val('Password1')
2025-08-11 09:52:56.121 [Debug] Resolved string: $('[id=Password]').val('Password1')
2025-08-11 09:52:56.121 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-11 09:52:56.121 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-11 09:52:56.121 [Debug] Resolving placeholders in string: id=ChallengeQuestionAnswer
2025-08-11 09:52:56.122 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-11 09:52:56.122 [Debug] Resolving placeholders in string: bmw
2025-08-11 09:52:56.122 [Debug] Resolved string: bmw
2025-08-11 09:52:56.122 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-11 09:52:56.122 [Debug] Resolved string: Click on the Login Button
2025-08-11 09:52:56.122 [Debug] Resolving placeholders in string: id=LoginAuthBtn
2025-08-11 09:52:56.122 [Debug] Resolved string: id=LoginAuthBtn
2025-08-11 09:52:56.123 [Debug] Resolving placeholders in string: Click on change later button on the password expiration popup message
2025-08-11 09:52:56.123 [Debug] Resolved string: Click on change later button on the password expiration popup message
2025-08-11 09:52:56.123 [Debug] Resolving placeholders in string: {{Selectors.popupCancel}}
2025-08-11 09:52:56.123 [Debug] Resolved string: id=popup_cancel
2025-08-11 09:52:56.123 [Debug] Resolving placeholders in string: Execute JS code to activate token
2025-08-11 09:52:56.123 [Debug] Resolved string: Execute JS code to activate token
2025-08-11 09:52:56.123 [Debug] Resolving placeholders in string: Globals.ActivationState = 'ACTIVATED';
2025-08-11 09:52:56.123 [Debug] Resolved string: Globals.ActivationState = 'ACTIVATED';
2025-08-11 09:52:56.123 [Debug] Resolving placeholders in string: Click on Cards option on the side menu
2025-08-11 09:52:56.124 [Debug] Resolved string: Click on Cards option on the side menu
2025-08-11 09:52:56.124 [Debug] Resolving placeholders in string: {{Selectors.CardsButton}}
2025-08-11 09:52:56.124 [Debug] Resolved string: xpath=//div[@class='mainmenuitems']//label[contains(text(), 'Cards')]
2025-08-11 09:52:56.124 [Debug] Resolving placeholders in string: Click on Card Inquiry
2025-08-11 09:52:56.124 [Debug] Resolved string: Click on Card Inquiry
2025-08-11 09:52:56.124 [Debug] Resolving placeholders in string: {{Selectors.CardInquiry}}
2025-08-11 09:52:56.124 [Debug] Resolved string: xpath=//div[@class='submenuitem']//label[contains(text(), 'Card Inquiry')]
2025-08-11 09:52:56.124 [Debug] Resolving placeholders in string: check Credit cards and prepaid cards displayed on 2 widgets
2025-08-11 09:52:56.124 [Debug] Resolved string: check Credit cards and prepaid cards displayed on 2 widgets
2025-08-11 09:52:56.124 [Debug] Resolving placeholders in string: id=CreditCard
2025-08-11 09:52:56.125 [Debug] Resolved string: id=CreditCard
2025-08-11 09:52:56.125 [Info] Successfully resolved parameters for test case: Credit cards and prepaid cards displayed on 2 widgets
2025-08-11 09:52:56.125 [Debug] Loading test case: WorkSet01/TestCases/012.CardServices/BlockCard/113-CardInquiry.json
2025-08-11 09:52:56.125 [Info] Loading test case from: WorkSet01/TestCases/012.CardServices/BlockCard/113-CardInquiry.json
2025-08-11 09:52:56.126 [Info] Attempting to load configuration file: WorkSet01/TestCases/012.CardServices/BlockCard/113-CardInquiry.json
2025-08-11 09:52:56.126 [Info] Successfully loaded configuration file: WorkSet01/TestCases/012.CardServices/BlockCard/113-CardInquiry.json
2025-08-11 09:52:56.127 [Info] No filtering configuration found. Including test case 'All users Cards are displayed successfully'.
2025-08-11 09:52:56.127 [Debug] Resolving parameters for test case: All users Cards are displayed successfully
2025-08-11 09:52:56.127 [Debug] Merging params
2025-08-11 09:52:56.127 [Debug] Merged basic params
2025-08-11 09:52:56.127 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-11 09:52:56.127 [Debug] Merged SpecialEnvParams params
2025-08-11 09:52:56.127 [Debug] Merged params
2025-08-11 09:52:56.128 [Debug] Loading parameters from reference file: WorkSet01/Params/CardServicesParams.json
2025-08-11 09:52:56.128 [Info] Attempting to load configuration file: WorkSet01/Params/CardServicesParams.json
2025-08-11 09:52:56.128 [Info] Successfully loaded configuration file: WorkSet01/Params/CardServicesParams.json
2025-08-11 09:52:56.129 [Debug] Merging params
2025-08-11 09:52:56.129 [Debug] Merged basic params
2025-08-11 09:52:56.129 [Debug] Merged params
2025-08-11 09:52:56.129 [Debug] Merging params
2025-08-11 09:52:56.129 [Debug] Merging params
2025-08-11 09:52:56.129 [Debug] Resolving placeholders in test steps for test case: All users Cards are displayed successfully
2025-08-11 09:52:56.129 [Debug] Resolving TestCaseReference in step: 
2025-08-11 09:52:56.129 [Info] Loading test case from: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-11 09:52:56.130 [Info] Attempting to load configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-11 09:52:56.130 [Info] Successfully loaded configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-11 09:52:56.130 [Debug] Resolving parameters for test case: Login
2025-08-11 09:52:56.130 [Debug] Merging params
2025-08-11 09:52:56.131 [Debug] Merged basic params
2025-08-11 09:52:56.131 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-11 09:52:56.131 [Debug] Merged SpecialEnvParams params
2025-08-11 09:52:56.131 [Debug] Merged params
2025-08-11 09:52:56.132 [Debug] Loading parameters from reference file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-11 09:52:56.132 [Info] Attempting to load configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-11 09:52:56.132 [Info] Successfully loaded configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-11 09:52:56.132 [Debug] Merging params
2025-08-11 09:52:56.133 [Debug] Merged basic params
2025-08-11 09:52:56.133 [Debug] Merged params
2025-08-11 09:52:56.133 [Debug] Merging params
2025-08-11 09:52:56.133 [Debug] Merging params
2025-08-11 09:52:56.133 [Debug] Resolving placeholders in test steps for test case: Login
2025-08-11 09:52:56.133 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-11 09:52:56.133 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-11 09:52:56.134 [Debug] Resolving placeholders in string: {{Selectors.FinishButton}}
2025-08-11 09:52:56.134 [Debug] Resolved string: id=finish
2025-08-11 09:52:56.134 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-11 09:52:56.134 [Debug] Resolved string: Click on Login Button
2025-08-11 09:52:56.134 [Debug] Resolving placeholders in string: {{Selectors.LoginButton}}
2025-08-11 09:52:56.134 [Debug] Resolved string: id=LoginBtn
2025-08-11 09:52:56.134 [Debug] Resolving placeholders in string: Type the User Name
2025-08-11 09:52:56.135 [Debug] Resolved string: Type the User Name
2025-08-11 09:52:56.135 [Debug] Resolving placeholders in string: {{Selectors.UserNameField}}
2025-08-11 09:52:56.135 [Debug] Resolved string: id=UserName
2025-08-11 09:52:56.135 [Debug] Resolving placeholders in string: {{TestData.UserName}}
2025-08-11 09:52:56.135 [Debug] Resolved string: mahmoudsayed022
2025-08-11 09:52:56.135 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-11 09:52:56.135 [Debug] Resolved string: Click on Continue Button
2025-08-11 09:52:56.135 [Debug] Resolving placeholders in string: {{Selectors.ContinueButton}}
2025-08-11 09:52:56.136 [Debug] Resolved string: id=btn
2025-08-11 09:52:56.136 [Debug] Resolving placeholders in string: Type the Password
2025-08-11 09:52:56.137 [Debug] Resolved string: Type the Password
2025-08-11 09:52:56.137 [Debug] Resolving placeholders in string: {{Selectors.PasswordField}}
2025-08-11 09:52:56.138 [Debug] Resolved string: id=Password
2025-08-11 09:52:56.138 [Debug] Resolving placeholders in string: $('[{{Selectors.PasswordField}}]').val('{{TestData.Password}}')
2025-08-11 09:52:56.138 [Debug] Resolved string: $('[id=Password]').val('Password1')
2025-08-11 09:52:56.138 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-11 09:52:56.138 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-11 09:52:56.138 [Debug] Resolving placeholders in string: {{Selectors.ChallengeQuestionField}}
2025-08-11 09:52:56.138 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-11 09:52:56.138 [Debug] Resolving placeholders in string: {{TestData.ChallengeAnswer}}
2025-08-11 09:52:56.139 [Debug] Resolved string: bmw
2025-08-11 09:52:56.139 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-11 09:52:56.139 [Debug] Resolved string: Click on the Login Button
2025-08-11 09:52:56.139 [Debug] Resolving placeholders in string: {{Selectors.LoginAuthButton}}
2025-08-11 09:52:56.139 [Debug] Resolved string: id=LoginAuthBtn
2025-08-11 09:52:56.140 [Info] Successfully resolved parameters for test case: Login
2025-08-11 09:52:56.140 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-11 09:52:56.140 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-11 09:52:56.140 [Debug] Resolving placeholders in string: id=finish
2025-08-11 09:52:56.140 [Debug] Resolved string: id=finish
2025-08-11 09:52:56.140 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-11 09:52:56.140 [Debug] Resolved string: Click on Login Button
2025-08-11 09:52:56.140 [Debug] Resolving placeholders in string: id=LoginBtn
2025-08-11 09:52:56.140 [Debug] Resolved string: id=LoginBtn
2025-08-11 09:52:56.140 [Debug] Resolving placeholders in string: Type the User Name
2025-08-11 09:52:56.140 [Debug] Resolved string: Type the User Name
2025-08-11 09:52:56.141 [Debug] Resolving placeholders in string: id=UserName
2025-08-11 09:52:56.141 [Debug] Resolved string: id=UserName
2025-08-11 09:52:56.141 [Debug] Resolving placeholders in string: mahmoudsayed022
2025-08-11 09:52:56.141 [Debug] Resolved string: mahmoudsayed022
2025-08-11 09:52:56.141 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-11 09:52:56.141 [Debug] Resolved string: Click on Continue Button
2025-08-11 09:52:56.141 [Debug] Resolving placeholders in string: id=btn
2025-08-11 09:52:56.142 [Debug] Resolved string: id=btn
2025-08-11 09:52:56.142 [Debug] Resolving placeholders in string: Type the Password
2025-08-11 09:52:56.143 [Debug] Resolved string: Type the Password
2025-08-11 09:52:56.143 [Debug] Resolving placeholders in string: id=Password
2025-08-11 09:52:56.144 [Debug] Resolved string: id=Password
2025-08-11 09:52:56.144 [Debug] Resolving placeholders in string: $('[id=Password]').val('Password1')
2025-08-11 09:52:56.144 [Debug] Resolved string: $('[id=Password]').val('Password1')
2025-08-11 09:52:56.144 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-11 09:52:56.144 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-11 09:52:56.144 [Debug] Resolving placeholders in string: id=ChallengeQuestionAnswer
2025-08-11 09:52:56.144 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-11 09:52:56.144 [Debug] Resolving placeholders in string: bmw
2025-08-11 09:52:56.145 [Debug] Resolved string: bmw
2025-08-11 09:52:56.145 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-11 09:52:56.145 [Debug] Resolved string: Click on the Login Button
2025-08-11 09:52:56.145 [Debug] Resolving placeholders in string: id=LoginAuthBtn
2025-08-11 09:52:56.145 [Debug] Resolved string: id=LoginAuthBtn
2025-08-11 09:52:56.145 [Debug] Resolving placeholders in string: Click on change later button on the password expiration popup message
2025-08-11 09:52:56.146 [Debug] Resolved string: Click on change later button on the password expiration popup message
2025-08-11 09:52:56.146 [Debug] Resolving placeholders in string: {{Selectors.popupCancel}}
2025-08-11 09:52:56.146 [Debug] Resolved string: id=popup_cancel
2025-08-11 09:52:56.146 [Debug] Resolving placeholders in string: Execute JS code to activate token
2025-08-11 09:52:56.146 [Debug] Resolved string: Execute JS code to activate token
2025-08-11 09:52:56.146 [Debug] Resolving placeholders in string: Globals.ActivationState = 'ACTIVATED';
2025-08-11 09:52:56.146 [Debug] Resolved string: Globals.ActivationState = 'ACTIVATED';
2025-08-11 09:52:56.146 [Debug] Resolving placeholders in string: Click on Cards option on the side menu
2025-08-11 09:52:56.146 [Debug] Resolved string: Click on Cards option on the side menu
2025-08-11 09:52:56.146 [Debug] Resolving placeholders in string: {{Selectors.CardsButton}}
2025-08-11 09:52:56.146 [Debug] Resolved string: xpath=//div[@class='mainmenuitems']//label[contains(text(), 'Cards')]
2025-08-11 09:52:56.147 [Debug] Resolving placeholders in string: Click on Card Inquiry and check cards details
2025-08-11 09:52:56.147 [Debug] Resolved string: Click on Card Inquiry and check cards details
2025-08-11 09:52:56.147 [Debug] Resolving placeholders in string: {{Selectors.CardInquiry}}
2025-08-11 09:52:56.147 [Debug] Resolved string: xpath=//div[@class='submenuitem']//label[contains(text(), 'Card Inquiry')]
2025-08-11 09:52:56.147 [Debug] Resolving placeholders in string: Check if the blocked cards appear in the card list.
2025-08-11 09:52:56.147 [Debug] Resolved string: Check if the blocked cards appear in the card list.
2025-08-11 09:52:56.147 [Debug] Resolving placeholders in string: css=#CardsSelectionErrorHandle > div:nth-child(1)
2025-08-11 09:52:56.147 [Debug] Resolved string: css=#CardsSelectionErrorHandle > div:nth-child(1)
2025-08-11 09:52:56.147 [Info] Successfully resolved parameters for test case: All users Cards are displayed successfully
2025-08-11 09:52:56.147 [Debug] Loading test case: WorkSet01/TestCases/012.CardServices/ActivateCard/57-fullCardActivation.json
2025-08-11 09:52:56.148 [Info] Loading test case from: WorkSet01/TestCases/012.CardServices/ActivateCard/57-fullCardActivation.json
2025-08-11 09:52:56.149 [Info] Attempting to load configuration file: WorkSet01/TestCases/012.CardServices/ActivateCard/57-fullCardActivation.json
2025-08-11 09:52:56.162 [Info] Successfully loaded configuration file: WorkSet01/TestCases/012.CardServices/ActivateCard/57-fullCardActivation.json
2025-08-11 09:52:56.163 [Info] No filtering configuration found. Including test case 'Verify that a user can successfully activate a card'.
2025-08-11 09:52:56.163 [Debug] Resolving parameters for test case: Verify that a user can successfully activate a card
2025-08-11 09:52:56.163 [Debug] Merging params
2025-08-11 09:52:56.163 [Debug] Merged basic params
2025-08-11 09:52:56.163 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-11 09:52:56.164 [Debug] Merged SpecialEnvParams params
2025-08-11 09:52:56.164 [Debug] Merged params
2025-08-11 09:52:56.165 [Debug] Loading parameters from reference file: WorkSet01/Params/ActivateCardParm.json
2025-08-11 09:52:56.173 [Info] Attempting to load configuration file: WorkSet01/Params/ActivateCardParm.json
2025-08-11 09:52:56.173 [Info] Successfully loaded configuration file: WorkSet01/Params/ActivateCardParm.json
2025-08-11 09:52:56.174 [Debug] Merging params
2025-08-11 09:52:56.174 [Debug] Merged basic params
2025-08-11 09:52:56.174 [Debug] Merged params
2025-08-11 09:52:56.175 [Debug] Merging params
2025-08-11 09:52:56.175 [Debug] Merging params
2025-08-11 09:52:56.176 [Debug] Resolving placeholders in test steps for test case: Verify that a user can successfully activate a card
2025-08-11 09:52:56.176 [Debug] Resolving TestCaseReference in step: 
2025-08-11 09:52:56.177 [Info] Loading test case from: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-11 09:52:56.177 [Info] Attempting to load configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-11 09:52:56.178 [Info] Successfully loaded configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-11 09:52:56.179 [Debug] Resolving parameters for test case: Login
2025-08-11 09:52:56.179 [Debug] Merging params
2025-08-11 09:52:56.180 [Debug] Merged basic params
2025-08-11 09:52:56.180 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-11 09:52:56.181 [Debug] Merged SpecialEnvParams params
2025-08-11 09:52:56.181 [Debug] Merged params
2025-08-11 09:52:56.182 [Debug] Loading parameters from reference file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-11 09:52:56.182 [Info] Attempting to load configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-11 09:52:56.183 [Info] Successfully loaded configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-11 09:52:56.183 [Debug] Merging params
2025-08-11 09:52:56.184 [Debug] Merged basic params
2025-08-11 09:52:56.184 [Debug] Merged params
2025-08-11 09:52:56.184 [Debug] Merging params
2025-08-11 09:52:56.185 [Debug] Merging params
2025-08-11 09:52:56.185 [Debug] Resolving placeholders in test steps for test case: Login
2025-08-11 09:52:56.186 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-11 09:52:56.187 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-11 09:52:56.187 [Debug] Resolving placeholders in string: {{Selectors.FinishButton}}
2025-08-11 09:52:56.188 [Debug] Resolved string: id=finish
2025-08-11 09:52:56.188 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-11 09:52:56.188 [Debug] Resolved string: Click on Login Button
2025-08-11 09:52:56.188 [Debug] Resolving placeholders in string: {{Selectors.LoginButton}}
2025-08-11 09:52:56.188 [Debug] Resolved string: id=LoginBtn
2025-08-11 09:52:56.188 [Debug] Resolving placeholders in string: Type the User Name
2025-08-11 09:52:56.188 [Debug] Resolved string: Type the User Name
2025-08-11 09:52:56.189 [Debug] Resolving placeholders in string: {{Selectors.UserNameField}}
2025-08-11 09:52:56.189 [Debug] Resolved string: id=UserName
2025-08-11 09:52:56.189 [Debug] Resolving placeholders in string: {{TestData.UserName}}
2025-08-11 09:52:56.189 [Debug] Resolved string: mahmoudsayed022
2025-08-11 09:52:56.189 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-11 09:52:56.189 [Debug] Resolved string: Click on Continue Button
2025-08-11 09:52:56.190 [Debug] Resolving placeholders in string: {{Selectors.ContinueButton}}
2025-08-11 09:52:56.190 [Debug] Resolved string: id=btn
2025-08-11 09:52:56.190 [Debug] Resolving placeholders in string: Type the Password
2025-08-11 09:52:56.190 [Debug] Resolved string: Type the Password
2025-08-11 09:52:56.190 [Debug] Resolving placeholders in string: {{Selectors.PasswordField}}
2025-08-11 09:52:56.190 [Debug] Resolved string: id=Password
2025-08-11 09:52:56.190 [Debug] Resolving placeholders in string: $('[{{Selectors.PasswordField}}]').val('{{TestData.Password}}')
2025-08-11 09:52:56.190 [Debug] Resolved string: $('[id=Password]').val('Password1')
2025-08-11 09:52:56.190 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-11 09:52:56.190 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-11 09:52:56.190 [Debug] Resolving placeholders in string: {{Selectors.ChallengeQuestionField}}
2025-08-11 09:52:56.191 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-11 09:52:56.191 [Debug] Resolving placeholders in string: {{TestData.ChallengeAnswer}}
2025-08-11 09:52:56.191 [Debug] Resolved string: bmw
2025-08-11 09:52:56.192 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-11 09:52:56.192 [Debug] Resolved string: Click on the Login Button
2025-08-11 09:52:56.193 [Debug] Resolving placeholders in string: {{Selectors.LoginAuthButton}}
2025-08-11 09:52:56.193 [Debug] Resolved string: id=LoginAuthBtn
2025-08-11 09:52:56.193 [Info] Successfully resolved parameters for test case: Login
2025-08-11 09:52:56.193 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-11 09:52:56.194 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-11 09:52:56.194 [Debug] Resolving placeholders in string: id=finish
2025-08-11 09:52:56.194 [Debug] Resolved string: id=finish
2025-08-11 09:52:56.194 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-11 09:52:56.195 [Debug] Resolved string: Click on Login Button
2025-08-11 09:52:56.195 [Debug] Resolving placeholders in string: id=LoginBtn
2025-08-11 09:52:56.195 [Debug] Resolved string: id=LoginBtn
2025-08-11 09:52:56.195 [Debug] Resolving placeholders in string: Type the User Name
2025-08-11 09:52:56.196 [Debug] Resolved string: Type the User Name
2025-08-11 09:52:56.196 [Debug] Resolving placeholders in string: id=UserName
2025-08-11 09:52:56.196 [Debug] Resolved string: id=UserName
2025-08-11 09:52:56.196 [Debug] Resolving placeholders in string: mahmoudsayed022
2025-08-11 09:52:56.196 [Debug] Resolved string: mahmoudsayed022
2025-08-11 09:52:56.196 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-11 09:52:56.196 [Debug] Resolved string: Click on Continue Button
2025-08-11 09:52:56.196 [Debug] Resolving placeholders in string: id=btn
2025-08-11 09:52:56.196 [Debug] Resolved string: id=btn
2025-08-11 09:52:56.196 [Debug] Resolving placeholders in string: Type the Password
2025-08-11 09:52:56.196 [Debug] Resolved string: Type the Password
2025-08-11 09:52:56.197 [Debug] Resolving placeholders in string: id=Password
2025-08-11 09:52:56.197 [Debug] Resolved string: id=Password
2025-08-11 09:52:56.197 [Debug] Resolving placeholders in string: $('[id=Password]').val('Password1')
2025-08-11 09:52:56.197 [Debug] Resolved string: $('[id=Password]').val('Password1')
2025-08-11 09:52:56.197 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-11 09:52:56.197 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-11 09:52:56.197 [Debug] Resolving placeholders in string: id=ChallengeQuestionAnswer
2025-08-11 09:52:56.198 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-11 09:52:56.198 [Debug] Resolving placeholders in string: bmw
2025-08-11 09:52:56.199 [Debug] Resolved string: bmw
2025-08-11 09:52:56.199 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-11 09:52:56.200 [Debug] Resolved string: Click on the Login Button
2025-08-11 09:52:56.200 [Debug] Resolving placeholders in string: id=LoginAuthBtn
2025-08-11 09:52:56.200 [Debug] Resolved string: id=LoginAuthBtn
2025-08-11 09:52:56.200 [Debug] Resolving placeholders in string: Click on change later button on the password expiration popup message
2025-08-11 09:52:56.200 [Debug] Resolved string: Click on change later button on the password expiration popup message
2025-08-11 09:52:56.200 [Debug] Resolving placeholders in string: {{Selectors.popupCancel}}
2025-08-11 09:52:56.200 [Debug] Resolved string: id=popup_cancel
2025-08-11 09:52:56.201 [Debug] Resolving placeholders in string: Click on biometric alert
2025-08-11 09:52:56.205 [Debug] Resolved string: Click on biometric alert
2025-08-11 09:52:56.206 [Debug] Resolving placeholders in string: {{Selectors.BioMetricAlert}}
2025-08-11 09:52:56.206 [Debug] Resolved string: css=#popbuttons > div > button.btn.Cancel.back_gradient2
2025-08-11 09:52:56.206 [Debug] Resolving placeholders in string: Click on Cards button from side menu
2025-08-11 09:52:56.206 [Debug] Resolved string: Click on Cards button from side menu
2025-08-11 09:52:56.206 [Debug] Resolving placeholders in string: {{Selectors.CardsButton}}
2025-08-11 09:52:56.206 [Debug] Resolved string: xpath=//div[@class='mainmenuitems']//label[contains(text(), 'Cards')]
2025-08-11 09:52:56.207 [Debug] Resolving placeholders in string: Click on Cards Services button from side menu
2025-08-11 09:52:56.207 [Debug] Resolved string: Click on Cards Services button from side menu
2025-08-11 09:52:56.207 [Debug] Resolving placeholders in string: {{Selectors.CardServices}}
2025-08-11 09:52:56.208 [Debug] Resolved string: xpath=//div[@class='submenuitem']//label[contains(text(), 'Card services')]
2025-08-11 09:52:56.208 [Debug] Resolving placeholders in string: Click on Activate Card
2025-08-11 09:52:56.209 [Debug] Resolved string: Click on Activate Card
2025-08-11 09:52:56.209 [Debug] Resolving placeholders in string: {{Selectors.ActivateCard}}
2025-08-11 09:52:56.210 [Debug] Resolved string: id=ActivateCard
2025-08-11 09:52:56.210 [Debug] Resolving placeholders in string: Click on card that need to be  Activated
2025-08-11 09:52:56.210 [Debug] Resolved string: Click on card that need to be  Activated
2025-08-11 09:52:56.210 [Debug] Resolving placeholders in string: {{Selectors.DeActivatedCard}}
2025-08-11 09:52:56.211 [Debug] Resolved string: css=#CardsSelectionErrorHandle > div:nth-child(2) > div > div.balance_cardList > h4
2025-08-11 09:52:56.211 [Debug] Resolving placeholders in string: Click on Continue
2025-08-11 09:52:56.211 [Debug] Resolved string: Click on Continue
2025-08-11 09:52:56.211 [Debug] Resolving placeholders in string: {{Selectors.ContinueBtn}}
2025-08-11 09:52:56.211 [Debug] Resolved string: id=btncontinue
2025-08-11 09:52:56.212 [Debug] Resolving placeholders in string: Enter new PIN
2025-08-11 09:52:56.212 [Debug] Resolved string: Enter new PIN
2025-08-11 09:52:56.212 [Debug] Resolving placeholders in string: {{Selectors.NewPINfield}}
2025-08-11 09:52:56.212 [Debug] Resolved string: id=ACpinNumber
2025-08-11 09:52:56.212 [Debug] Resolving placeholders in string: 1234
2025-08-11 09:52:56.213 [Debug] Resolved string: 1234
2025-08-11 09:52:56.213 [Debug] Resolving placeholders in string: Confirm new PIN
2025-08-11 09:52:56.213 [Debug] Resolved string: Confirm new PIN
2025-08-11 09:52:56.214 [Debug] Resolving placeholders in string: {{Selectors.ConfirmPINfield}}
2025-08-11 09:52:56.214 [Debug] Resolved string: id=ACconfirmPin
2025-08-11 09:52:56.215 [Debug] Resolving placeholders in string: 1234
2025-08-11 09:52:56.215 [Debug] Resolved string: 1234
2025-08-11 09:52:56.216 [Debug] Resolving placeholders in string: Enter a valid OTP
2025-08-11 09:52:56.216 [Debug] Resolved string: Enter a valid OTP
2025-08-11 09:52:56.216 [Debug] Resolving placeholders in string: {{Selectors.OTPfield}}
2025-08-11 09:52:56.216 [Debug] Resolved string: id=ACsmsCode
2025-08-11 09:52:56.216 [Debug] Resolving placeholders in string: 123456
2025-08-11 09:52:56.216 [Debug] Resolved string: 123456
2025-08-11 09:52:56.216 [Debug] Resolving placeholders in string: Click on Continue and showing activation is successfull
2025-08-11 09:52:56.216 [Debug] Resolved string: Click on Continue and showing activation is successfull
2025-08-11 09:52:56.217 [Debug] Resolving placeholders in string: {{Selectors.ContinueBtn2}}
2025-08-11 09:52:56.217 [Debug] Resolved string: id=ACcontinueBtn
2025-08-11 09:52:56.218 [Info] Successfully resolved parameters for test case: Verify that a user can successfully activate a card
2025-08-11 09:52:56.218 [Debug] Loading test case: WorkSet01/TestCases/012.CardServices/BlockCard/52-UnBlockCard.json
2025-08-11 09:52:56.218 [Info] Loading test case from: WorkSet01/TestCases/012.CardServices/BlockCard/52-UnBlockCard.json
2025-08-11 09:52:56.218 [Info] Attempting to load configuration file: WorkSet01/TestCases/012.CardServices/BlockCard/52-UnBlockCard.json
2025-08-11 09:52:56.234 [Info] Successfully loaded configuration file: WorkSet01/TestCases/012.CardServices/BlockCard/52-UnBlockCard.json
2025-08-11 09:52:56.234 [Info] No filtering configuration found. Including test case 'Verify that a user can successfully unblock a card'.
2025-08-11 09:52:56.235 [Debug] Resolving parameters for test case: Verify that a user can successfully unblock a card
2025-08-11 09:52:56.235 [Debug] Merging params
2025-08-11 09:52:56.235 [Debug] Merged basic params
2025-08-11 09:52:56.235 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-11 09:52:56.235 [Debug] Merged SpecialEnvParams params
2025-08-11 09:52:56.235 [Debug] Merged params
2025-08-11 09:52:56.236 [Debug] Loading parameters from reference file: WorkSet01/Params/BlockCardParams.json
2025-08-11 09:52:56.236 [Info] Attempting to load configuration file: WorkSet01/Params/BlockCardParams.json
2025-08-11 09:52:56.236 [Info] Successfully loaded configuration file: WorkSet01/Params/BlockCardParams.json
2025-08-11 09:52:56.237 [Debug] Merging params
2025-08-11 09:52:56.237 [Debug] Merged basic params
2025-08-11 09:52:56.237 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-11 09:52:56.237 [Debug] Merged SpecialEnvParams params
2025-08-11 09:52:56.238 [Debug] Merged params
2025-08-11 09:52:56.238 [Debug] Merging params
2025-08-11 09:52:56.239 [Debug] Merging params
2025-08-11 09:52:56.239 [Debug] Resolving placeholders in test steps for test case: Verify that a user can successfully unblock a card
2025-08-11 09:52:56.239 [Debug] Resolving TestCaseReference in step: 
2025-08-11 09:52:56.239 [Info] Loading test case from: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-11 09:52:56.240 [Info] Attempting to load configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-11 09:52:56.242 [Info] Successfully loaded configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-11 09:52:56.242 [Debug] Resolving parameters for test case: Login
2025-08-11 09:52:56.243 [Debug] Merging params
2025-08-11 09:52:56.243 [Debug] Merged basic params
2025-08-11 09:52:56.243 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-11 09:52:56.243 [Debug] Merged SpecialEnvParams params
2025-08-11 09:52:56.243 [Debug] Merged params
2025-08-11 09:52:56.243 [Debug] Loading parameters from reference file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-11 09:52:56.243 [Info] Attempting to load configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-11 09:52:56.244 [Info] Successfully loaded configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-11 09:52:56.244 [Debug] Merging params
2025-08-11 09:52:56.244 [Debug] Merged basic params
2025-08-11 09:52:56.245 [Debug] Merged params
2025-08-11 09:52:56.246 [Debug] Merging params
2025-08-11 09:52:56.246 [Debug] Merging params
2025-08-11 09:52:56.247 [Debug] Resolving placeholders in test steps for test case: Login
2025-08-11 09:52:56.247 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-11 09:52:56.247 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-11 09:52:56.247 [Debug] Resolving placeholders in string: {{Selectors.FinishButton}}
2025-08-11 09:52:56.247 [Debug] Resolved string: id=finish
2025-08-11 09:52:56.247 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-11 09:52:56.247 [Debug] Resolved string: Click on Login Button
2025-08-11 09:52:56.248 [Debug] Resolving placeholders in string: {{Selectors.LoginButton}}
2025-08-11 09:52:56.248 [Debug] Resolved string: id=LoginBtn
2025-08-11 09:52:56.248 [Debug] Resolving placeholders in string: Type the User Name
2025-08-11 09:52:56.248 [Debug] Resolved string: Type the User Name
2025-08-11 09:52:56.249 [Debug] Resolving placeholders in string: {{Selectors.UserNameField}}
2025-08-11 09:52:56.249 [Debug] Resolved string: id=UserName
2025-08-11 09:52:56.249 [Debug] Resolving placeholders in string: {{TestData.UserName}}
2025-08-11 09:52:56.249 [Debug] Resolved string: mahmoudsayed022
2025-08-11 09:52:56.249 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-11 09:52:56.249 [Debug] Resolved string: Click on Continue Button
2025-08-11 09:52:56.249 [Debug] Resolving placeholders in string: {{Selectors.ContinueButton}}
2025-08-11 09:52:56.249 [Debug] Resolved string: id=btn
2025-08-11 09:52:56.250 [Debug] Resolving placeholders in string: Type the Password
2025-08-11 09:52:56.250 [Debug] Resolved string: Type the Password
2025-08-11 09:52:56.251 [Debug] Resolving placeholders in string: {{Selectors.PasswordField}}
2025-08-11 09:52:56.251 [Debug] Resolved string: id=Password
2025-08-11 09:52:56.252 [Debug] Resolving placeholders in string: $('[{{Selectors.PasswordField}}]').val('{{TestData.Password}}')
2025-08-11 09:52:56.252 [Debug] Resolved string: $('[id=Password]').val('Password1')
2025-08-11 09:52:56.252 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-11 09:52:56.252 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-11 09:52:56.252 [Debug] Resolving placeholders in string: {{Selectors.ChallengeQuestionField}}
2025-08-11 09:52:56.252 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-11 09:52:56.252 [Debug] Resolving placeholders in string: {{TestData.ChallengeAnswer}}
2025-08-11 09:52:56.253 [Debug] Resolved string: bmw
2025-08-11 09:52:56.253 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-11 09:52:56.253 [Debug] Resolved string: Click on the Login Button
2025-08-11 09:52:56.253 [Debug] Resolving placeholders in string: {{Selectors.LoginAuthButton}}
2025-08-11 09:52:56.253 [Debug] Resolved string: id=LoginAuthBtn
2025-08-11 09:52:56.254 [Info] Successfully resolved parameters for test case: Login
2025-08-11 09:52:56.254 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-11 09:52:56.254 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-11 09:52:56.254 [Debug] Resolving placeholders in string: id=finish
2025-08-11 09:52:56.254 [Debug] Resolved string: id=finish
2025-08-11 09:52:56.254 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-11 09:52:56.254 [Debug] Resolved string: Click on Login Button
2025-08-11 09:52:56.254 [Debug] Resolving placeholders in string: id=LoginBtn
2025-08-11 09:52:56.254 [Debug] Resolved string: id=LoginBtn
2025-08-11 09:52:56.254 [Debug] Resolving placeholders in string: Type the User Name
2025-08-11 09:52:56.255 [Debug] Resolved string: Type the User Name
2025-08-11 09:52:56.255 [Debug] Resolving placeholders in string: id=UserName
2025-08-11 09:52:56.255 [Debug] Resolved string: id=UserName
2025-08-11 09:52:56.255 [Debug] Resolving placeholders in string: mahmoudsayed022
2025-08-11 09:52:56.255 [Debug] Resolved string: mahmoudsayed022
2025-08-11 09:52:56.256 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-11 09:52:56.256 [Debug] Resolved string: Click on Continue Button
2025-08-11 09:52:56.257 [Debug] Resolving placeholders in string: id=btn
2025-08-11 09:52:56.257 [Debug] Resolved string: id=btn
2025-08-11 09:52:56.257 [Debug] Resolving placeholders in string: Type the Password
2025-08-11 09:52:56.257 [Debug] Resolved string: Type the Password
2025-08-11 09:52:56.258 [Debug] Resolving placeholders in string: id=Password
2025-08-11 09:52:56.258 [Debug] Resolved string: id=Password
2025-08-11 09:52:56.258 [Debug] Resolving placeholders in string: $('[id=Password]').val('Password1')
2025-08-11 09:52:56.258 [Debug] Resolved string: $('[id=Password]').val('Password1')
2025-08-11 09:52:56.259 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-11 09:52:56.259 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-11 09:52:56.259 [Debug] Resolving placeholders in string: id=ChallengeQuestionAnswer
2025-08-11 09:52:56.259 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-11 09:52:56.259 [Debug] Resolving placeholders in string: bmw
2025-08-11 09:52:56.260 [Debug] Resolved string: bmw
2025-08-11 09:52:56.260 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-11 09:52:56.260 [Debug] Resolved string: Click on the Login Button
2025-08-11 09:52:56.260 [Debug] Resolving placeholders in string: id=LoginAuthBtn
2025-08-11 09:52:56.260 [Debug] Resolved string: id=LoginAuthBtn
2025-08-11 09:52:56.260 [Debug] Resolving placeholders in string: Click on change later button on the password expiration popup message
2025-08-11 09:52:56.260 [Debug] Resolved string: Click on change later button on the password expiration popup message
2025-08-11 09:52:56.260 [Debug] Resolving placeholders in string: {{Selectors.popupCancel}}
2025-08-11 09:52:56.260 [Debug] Resolved string: id=popup_cancel
2025-08-11 09:52:56.261 [Debug] Resolving placeholders in string: Click on biometric alert
2025-08-11 09:52:56.261 [Debug] Resolved string: Click on biometric alert
2025-08-11 09:52:56.261 [Debug] Resolving placeholders in string: {{Selectors.BioMetricAlert}}
2025-08-11 09:52:56.261 [Debug] Resolved string: css=#popbuttons > div > button.btn.Cancel.back_gradient2
2025-08-11 09:52:56.261 [Debug] Resolving placeholders in string: Click on Cards button from side menu
2025-08-11 09:52:56.261 [Debug] Resolved string: Click on Cards button from side menu
2025-08-11 09:52:56.261 [Debug] Resolving placeholders in string: {{Selectors.CardsButton}}
2025-08-11 09:52:56.261 [Debug] Resolved string: xpath=//div[@class='mainmenuitems']//label[contains(text(), 'Cards')]
2025-08-11 09:52:56.261 [Debug] Resolving placeholders in string: Execute JS code to activate token
2025-08-11 09:52:56.262 [Debug] Resolved string: Execute JS code to activate token
2025-08-11 09:52:56.262 [Debug] Resolving placeholders in string: Globals.ActivationState = 'ACTIVATED';
2025-08-11 09:52:56.263 [Debug] Resolved string: Globals.ActivationState = 'ACTIVATED';
2025-08-11 09:52:56.264 [Debug] Resolving placeholders in string: Click on Cards Services button from side menu
2025-08-11 09:52:56.264 [Debug] Resolved string: Click on Cards Services button from side menu
2025-08-11 09:52:56.264 [Debug] Resolving placeholders in string: {{Selectors.CardServices}}
2025-08-11 09:52:56.264 [Debug] Resolved string: xpath=//div[@class='submenuitem']//label[contains(text(), 'Card services')]
2025-08-11 09:52:56.264 [Debug] Resolving placeholders in string: Click on UnBlock Btn
2025-08-11 09:52:56.264 [Debug] Resolved string: Click on UnBlock Btn
2025-08-11 09:52:56.264 [Debug] Resolving placeholders in string: {{Selectors.UnblockCard}}
2025-08-11 09:52:56.265 [Debug] Resolved string: id=UnblockCard
2025-08-11 09:52:56.265 [Debug] Resolving placeholders in string: Click on Blocked Card
2025-08-11 09:52:56.266 [Debug] Resolved string: Click on Blocked Card
2025-08-11 09:52:56.266 [Debug] Resolving placeholders in string: {{Selectors.BlockedCard}}
2025-08-11 09:52:56.266 [Debug] Resolved string: css=#CardsSelectionErrorHandle > div
2025-08-11 09:52:56.266 [Debug] Resolving placeholders in string: Click on Agree Check Box
2025-08-11 09:52:56.266 [Debug] Resolved string: Click on Agree Check Box
2025-08-11 09:52:56.266 [Debug] Resolving placeholders in string: {{Selectors.AgreeCheckBox}}
2025-08-11 09:52:56.266 [Debug] Resolved string: css=#wginsUnblockCard_Default > div > div > div > div.details_items_container.back_white > div.terms.AgreeTermsAndConditions > input[type=checkbox]
2025-08-11 09:52:56.266 [Debug] Resolving placeholders in string: Click on Continue
2025-08-11 09:52:56.266 [Debug] Resolved string: Click on Continue
2025-08-11 09:52:56.267 [Debug] Resolving placeholders in string: {{Selectors.ContinueBtn}}
2025-08-11 09:52:56.267 [Debug] Resolved string: id=SubmitUnblockCardReq
2025-08-11 09:52:56.267 [Debug] Resolving placeholders in string: Type valid Token
2025-08-11 09:52:56.267 [Debug] Resolved string: Type valid Token
2025-08-11 09:52:56.267 [Debug] Resolving placeholders in string: {{Selectors.TokenInput}}
2025-08-11 09:52:56.267 [Debug] Resolved string: id=TokenNUMBER
2025-08-11 09:52:56.267 [Debug] Resolving placeholders in string: 123456
2025-08-11 09:52:56.267 [Debug] Resolved string: 123456
2025-08-11 09:52:56.267 [Debug] Resolving placeholders in string: Click On Confirm
2025-08-11 09:52:56.267 [Debug] Resolved string: Click On Confirm
2025-08-11 09:52:56.268 [Debug] Resolving placeholders in string: {{Selectors.ConfirmBtn}}
2025-08-11 09:52:56.268 [Debug] Resolved string: id=btnTokenConfirm
2025-08-11 09:52:56.268 [Info] Successfully resolved parameters for test case: Verify that a user can successfully unblock a card
2025-08-11 09:52:56.268 [Debug] Loading test case: WorkSet01/TestCases/012.CardServices/BlockCard/48-CheckCardsList.json
2025-08-11 09:52:56.268 [Info] Loading test case from: WorkSet01/TestCases/012.CardServices/BlockCard/48-CheckCardsList.json
2025-08-11 09:52:56.269 [Info] Attempting to load configuration file: WorkSet01/TestCases/012.CardServices/BlockCard/48-CheckCardsList.json
2025-08-11 09:52:56.279 [Info] Successfully loaded configuration file: WorkSet01/TestCases/012.CardServices/BlockCard/48-CheckCardsList.json
2025-08-11 09:52:56.279 [Info] No filtering configuration found. Including test case 'Check Cards List'.
2025-08-11 09:52:56.279 [Debug] Resolving parameters for test case: Check Cards List
2025-08-11 09:52:56.279 [Debug] Merging params
2025-08-11 09:52:56.279 [Debug] Merged basic params
2025-08-11 09:52:56.280 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-11 09:52:56.280 [Debug] Merged SpecialEnvParams params
2025-08-11 09:52:56.281 [Debug] Merged params
2025-08-11 09:52:56.295 [Debug] Loading parameters from reference file: WorkSet01/Params/BlockCardParams.json
2025-08-11 09:52:56.295 [Info] Attempting to load configuration file: WorkSet01/Params/BlockCardParams.json
2025-08-11 09:52:56.295 [Info] Successfully loaded configuration file: WorkSet01/Params/BlockCardParams.json
2025-08-11 09:52:56.296 [Debug] Merging params
2025-08-11 09:52:56.296 [Debug] Merged basic params
2025-08-11 09:52:56.296 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-11 09:52:56.297 [Debug] Merged SpecialEnvParams params
2025-08-11 09:52:56.298 [Debug] Merged params
2025-08-11 09:52:56.298 [Debug] Merging params
2025-08-11 09:52:56.299 [Debug] Merging params
2025-08-11 09:52:56.299 [Debug] Resolving placeholders in test steps for test case: Check Cards List
2025-08-11 09:52:56.299 [Debug] Resolving TestCaseReference in step: 
2025-08-11 09:52:56.300 [Info] Loading test case from: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-11 09:52:56.300 [Info] Attempting to load configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-11 09:52:56.301 [Info] Successfully loaded configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-11 09:52:56.302 [Debug] Resolving parameters for test case: Login
2025-08-11 09:52:56.303 [Debug] Merging params
2025-08-11 09:52:56.303 [Debug] Merged basic params
2025-08-11 09:52:56.303 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-11 09:52:56.303 [Debug] Merged SpecialEnvParams params
2025-08-11 09:52:56.303 [Debug] Merged params
2025-08-11 09:52:56.304 [Debug] Loading parameters from reference file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-11 09:52:56.304 [Info] Attempting to load configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-11 09:52:56.305 [Info] Successfully loaded configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-11 09:52:56.305 [Debug] Merging params
2025-08-11 09:52:56.305 [Debug] Merged basic params
2025-08-11 09:52:56.305 [Debug] Merged params
2025-08-11 09:52:56.305 [Debug] Merging params
2025-08-11 09:52:56.305 [Debug] Merging params
2025-08-11 09:52:56.306 [Debug] Resolving placeholders in test steps for test case: Login
2025-08-11 09:52:56.307 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-11 09:52:56.308 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-11 09:52:56.308 [Debug] Resolving placeholders in string: {{Selectors.FinishButton}}
2025-08-11 09:52:56.308 [Debug] Resolved string: id=finish
2025-08-11 09:52:56.308 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-11 09:52:56.308 [Debug] Resolved string: Click on Login Button
2025-08-11 09:52:56.308 [Debug] Resolving placeholders in string: {{Selectors.LoginButton}}
2025-08-11 09:52:56.308 [Debug] Resolved string: id=LoginBtn
2025-08-11 09:52:56.308 [Debug] Resolving placeholders in string: Type the User Name
2025-08-11 09:52:56.309 [Debug] Resolved string: Type the User Name
2025-08-11 09:52:56.309 [Debug] Resolving placeholders in string: {{Selectors.UserNameField}}
2025-08-11 09:52:56.309 [Debug] Resolved string: id=UserName
2025-08-11 09:52:56.309 [Debug] Resolving placeholders in string: {{TestData.UserName}}
2025-08-11 09:52:56.309 [Debug] Resolved string: mahmoudsayed022
2025-08-11 09:52:56.310 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-11 09:52:56.310 [Debug] Resolved string: Click on Continue Button
2025-08-11 09:52:56.310 [Debug] Resolving placeholders in string: {{Selectors.ContinueButton}}
2025-08-11 09:52:56.310 [Debug] Resolved string: id=btn
2025-08-11 09:52:56.310 [Debug] Resolving placeholders in string: Type the Password
2025-08-11 09:52:56.311 [Debug] Resolved string: Type the Password
2025-08-11 09:52:56.311 [Debug] Resolving placeholders in string: {{Selectors.PasswordField}}
2025-08-11 09:52:56.312 [Debug] Resolved string: id=Password
2025-08-11 09:52:56.313 [Debug] Resolving placeholders in string: $('[{{Selectors.PasswordField}}]').val('{{TestData.Password}}')
2025-08-11 09:52:56.313 [Debug] Resolved string: $('[id=Password]').val('Password1')
2025-08-11 09:52:56.313 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-11 09:52:56.313 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-11 09:52:56.313 [Debug] Resolving placeholders in string: {{Selectors.ChallengeQuestionField}}
2025-08-11 09:52:56.314 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-11 09:52:56.314 [Debug] Resolving placeholders in string: {{TestData.ChallengeAnswer}}
2025-08-11 09:52:56.314 [Debug] Resolved string: bmw
2025-08-11 09:52:56.314 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-11 09:52:56.314 [Debug] Resolved string: Click on the Login Button
2025-08-11 09:52:56.314 [Debug] Resolving placeholders in string: {{Selectors.LoginAuthButton}}
2025-08-11 09:52:56.315 [Debug] Resolved string: id=LoginAuthBtn
2025-08-11 09:52:56.315 [Info] Successfully resolved parameters for test case: Login
2025-08-11 09:52:56.315 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-11 09:52:56.315 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-11 09:52:56.315 [Debug] Resolving placeholders in string: id=finish
2025-08-11 09:52:56.315 [Debug] Resolved string: id=finish
2025-08-11 09:52:56.315 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-11 09:52:56.315 [Debug] Resolved string: Click on Login Button
2025-08-11 09:52:56.315 [Debug] Resolving placeholders in string: id=LoginBtn
2025-08-11 09:52:56.315 [Debug] Resolved string: id=LoginBtn
2025-08-11 09:52:56.316 [Debug] Resolving placeholders in string: Type the User Name
2025-08-11 09:52:56.316 [Debug] Resolved string: Type the User Name
2025-08-11 09:52:56.316 [Debug] Resolving placeholders in string: id=UserName
2025-08-11 09:52:56.316 [Debug] Resolved string: id=UserName
2025-08-11 09:52:56.317 [Debug] Resolving placeholders in string: mahmoudsayed022
2025-08-11 09:52:56.317 [Debug] Resolved string: mahmoudsayed022
2025-08-11 09:52:56.317 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-11 09:52:56.317 [Debug] Resolved string: Click on Continue Button
2025-08-11 09:52:56.318 [Debug] Resolving placeholders in string: id=btn
2025-08-11 09:52:56.318 [Debug] Resolved string: id=btn
2025-08-11 09:52:56.318 [Debug] Resolving placeholders in string: Type the Password
2025-08-11 09:52:56.318 [Debug] Resolved string: Type the Password
2025-08-11 09:52:56.318 [Debug] Resolving placeholders in string: id=Password
2025-08-11 09:52:56.318 [Debug] Resolved string: id=Password
2025-08-11 09:52:56.318 [Debug] Resolving placeholders in string: $('[id=Password]').val('Password1')
2025-08-11 09:52:56.319 [Debug] Resolved string: $('[id=Password]').val('Password1')
2025-08-11 09:52:56.319 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-11 09:52:56.319 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-11 09:52:56.319 [Debug] Resolving placeholders in string: id=ChallengeQuestionAnswer
2025-08-11 09:52:56.319 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-11 09:52:56.319 [Debug] Resolving placeholders in string: bmw
2025-08-11 09:52:56.319 [Debug] Resolved string: bmw
2025-08-11 09:52:56.319 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-11 09:52:56.319 [Debug] Resolved string: Click on the Login Button
2025-08-11 09:52:56.319 [Debug] Resolving placeholders in string: id=LoginAuthBtn
2025-08-11 09:52:56.320 [Debug] Resolved string: id=LoginAuthBtn
2025-08-11 09:52:56.320 [Debug] Resolving placeholders in string: Click on change later button on the password expiration popup message
2025-08-11 09:52:56.320 [Debug] Resolved string: Click on change later button on the password expiration popup message
2025-08-11 09:52:56.320 [Debug] Resolving placeholders in string: {{Selectors.popupCancel}}
2025-08-11 09:52:56.320 [Debug] Resolved string: id=popup_cancel
2025-08-11 09:52:56.321 [Debug] Resolving placeholders in string: Click on biometric alert
2025-08-11 09:52:56.321 [Debug] Resolved string: Click on biometric alert
2025-08-11 09:52:56.322 [Debug] Resolving placeholders in string: {{Selectors.BioMetricAlert}}
2025-08-11 09:52:56.322 [Debug] Resolved string: css=#popbuttons > div > button.btn.Cancel.back_gradient2
2025-08-11 09:52:56.322 [Debug] Resolving placeholders in string: Execute JS code to activate token
2025-08-11 09:52:56.322 [Debug] Resolved string: Execute JS code to activate token
2025-08-11 09:52:56.322 [Debug] Resolving placeholders in string: Globals.ActivationState = 'ACTIVATED';
2025-08-11 09:52:56.323 [Debug] Resolved string: Globals.ActivationState = 'ACTIVATED';
2025-08-11 09:52:56.323 [Debug] Resolving placeholders in string: Click on Cards option on the side menu
2025-08-11 09:52:56.323 [Debug] Resolved string: Click on Cards option on the side menu
2025-08-11 09:52:56.323 [Debug] Resolving placeholders in string: {{Selectors.CardsButton}}
2025-08-11 09:52:56.323 [Debug] Resolved string: xpath=//div[@class='mainmenuitems']//label[contains(text(), 'Cards')]
2025-08-11 09:52:56.323 [Debug] Resolving placeholders in string: Click on Card Services
2025-08-11 09:52:56.323 [Debug] Resolved string: Click on Card Services
2025-08-11 09:52:56.324 [Debug] Resolving placeholders in string: {{Selectors.CardServices}}
2025-08-11 09:52:56.324 [Debug] Resolved string: xpath=//div[@class='submenuitem']//label[contains(text(), 'Card services')]
2025-08-11 09:52:56.324 [Debug] Resolving placeholders in string: Click on Block Card option
2025-08-11 09:52:56.324 [Debug] Resolved string: Click on Block Card option
2025-08-11 09:52:56.324 [Debug] Resolving placeholders in string: {{Selectors.BlockCardOption}}
2025-08-11 09:52:56.324 [Debug] Resolved string: id=BlockCard
2025-08-11 09:52:56.324 [Debug] Resolving placeholders in string: Click on Select Card Button and show the cards list
2025-08-11 09:52:56.324 [Debug] Resolved string: Click on Select Card Button and show the cards list
2025-08-11 09:52:56.324 [Debug] Resolving placeholders in string: {{Selectors.SelectCardButton}}
2025-08-11 09:52:56.324 [Debug] Resolved string: id=SelectCardBtn
2025-08-11 09:52:56.324 [Debug] Resolving placeholders in string: Check if the blocked cards appear in the card list.
2025-08-11 09:52:56.325 [Debug] Resolved string: Check if the blocked cards appear in the card list.
2025-08-11 09:52:56.325 [Debug] Resolving placeholders in string: css=#CardsSelectionErrorHandle > div:nth-child(1)
2025-08-11 09:52:56.325 [Debug] Resolved string: css=#CardsSelectionErrorHandle > div:nth-child(1)
2025-08-11 09:52:56.325 [Info] Successfully resolved parameters for test case: Check Cards List
2025-08-11 09:52:56.325 [Debug] Loading test case: WorkSet01/TestCases/04.ATMdispute/43-CompleteRequest.json
2025-08-11 09:52:56.325 [Info] Loading test case from: WorkSet01/TestCases/04.ATMdispute/43-CompleteRequest.json
2025-08-11 09:52:56.325 [Info] Attempting to load configuration file: WorkSet01/TestCases/04.ATMdispute/43-CompleteRequest.json
2025-08-11 09:52:56.330 [Info] Successfully loaded configuration file: WorkSet01/TestCases/04.ATMdispute/43-CompleteRequest.json
2025-08-11 09:52:56.331 [Info] No filtering configuration found. Including test case 'Complete ATM dispute request'.
2025-08-11 09:52:56.331 [Debug] Resolving parameters for test case: Complete ATM dispute request
2025-08-11 09:52:56.331 [Debug] Merging params
2025-08-11 09:52:56.331 [Debug] Merged basic params
2025-08-11 09:52:56.331 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-11 09:52:56.331 [Debug] Merged SpecialEnvParams params
2025-08-11 09:52:56.332 [Debug] Merged params
2025-08-11 09:52:56.332 [Debug] Loading parameters from reference file: WorkSet01/Params/ATMdisputeParams.json
2025-08-11 09:52:56.332 [Info] Attempting to load configuration file: WorkSet01/Params/ATMdisputeParams.json
2025-08-11 09:52:56.332 [Info] Successfully loaded configuration file: WorkSet01/Params/ATMdisputeParams.json
2025-08-11 09:52:56.333 [Debug] Merging params
2025-08-11 09:52:56.333 [Debug] Merged basic params
2025-08-11 09:52:56.333 [Debug] Merged params
2025-08-11 09:52:56.333 [Debug] Merging params
2025-08-11 09:52:56.333 [Debug] Merging params
2025-08-11 09:52:56.333 [Debug] Resolving placeholders in test steps for test case: Complete ATM dispute request
2025-08-11 09:52:56.333 [Debug] Resolving TestCaseReference in step: 
2025-08-11 09:52:56.333 [Info] Loading test case from: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-11 09:52:56.333 [Info] Attempting to load configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-11 09:52:56.334 [Info] Successfully loaded configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-11 09:52:56.334 [Debug] Resolving parameters for test case: Login
2025-08-11 09:52:56.334 [Debug] Merging params
2025-08-11 09:52:56.334 [Debug] Merged basic params
2025-08-11 09:52:56.335 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-11 09:52:56.335 [Debug] Merged SpecialEnvParams params
2025-08-11 09:52:56.335 [Debug] Merged params
2025-08-11 09:52:56.335 [Debug] Loading parameters from reference file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-11 09:52:56.336 [Info] Attempting to load configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-11 09:52:56.336 [Info] Successfully loaded configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-11 09:52:56.336 [Debug] Merging params
2025-08-11 09:52:56.336 [Debug] Merged basic params
2025-08-11 09:52:56.336 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-11 09:52:56.336 [Debug] Merged SpecialEnvParams params
2025-08-11 09:52:56.336 [Debug] Merged params
2025-08-11 09:52:56.337 [Debug] Merging params
2025-08-11 09:52:56.337 [Debug] Merging params
2025-08-11 09:52:56.337 [Debug] Resolving placeholders in test steps for test case: Login
2025-08-11 09:52:56.337 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-11 09:52:56.337 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-11 09:52:56.337 [Debug] Resolving placeholders in string: {{Selectors.FinishButton}}
2025-08-11 09:52:56.338 [Debug] Resolved string: id=finish
2025-08-11 09:52:56.338 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-11 09:52:56.338 [Debug] Resolved string: Click on Login Button
2025-08-11 09:52:56.338 [Debug] Resolving placeholders in string: {{Selectors.LoginButton}}
2025-08-11 09:52:56.338 [Debug] Resolved string: id=LoginBtn
2025-08-11 09:52:56.338 [Debug] Resolving placeholders in string: Type the User Name
2025-08-11 09:52:56.338 [Debug] Resolved string: Type the User Name
2025-08-11 09:52:56.338 [Debug] Resolving placeholders in string: {{Selectors.UserNameField}}
2025-08-11 09:52:56.338 [Debug] Resolved string: id=UserName
2025-08-11 09:52:56.338 [Debug] Resolving placeholders in string: {{TestData.UserName}}
2025-08-11 09:52:56.339 [Debug] Resolved string: Mm_azmy
2025-08-11 09:52:56.340 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-11 09:52:56.340 [Debug] Resolved string: Click on Continue Button
2025-08-11 09:52:56.340 [Debug] Resolving placeholders in string: {{Selectors.ContinueButton}}
2025-08-11 09:52:56.340 [Debug] Resolved string: id=btn
2025-08-11 09:52:56.340 [Debug] Resolving placeholders in string: Type the Password
2025-08-11 09:52:56.340 [Debug] Resolved string: Type the Password
2025-08-11 09:52:56.340 [Debug] Resolving placeholders in string: {{Selectors.PasswordField}}
2025-08-11 09:52:56.340 [Debug] Resolved string: id=Password
2025-08-11 09:52:56.341 [Debug] Resolving placeholders in string: $('[{{Selectors.PasswordField}}]').val('{{TestData.Password}}')
2025-08-11 09:52:56.341 [Debug] Resolved string: $('[id=Password]').val('Asdf2025')
2025-08-11 09:52:56.341 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-11 09:52:56.341 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-11 09:52:56.341 [Debug] Resolving placeholders in string: {{Selectors.ChallengeQuestionField}}
2025-08-11 09:52:56.341 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-11 09:52:56.342 [Debug] Resolving placeholders in string: {{TestData.ChallengeAnswer}}
2025-08-11 09:52:56.342 [Debug] Resolved string: armada
2025-08-11 09:52:56.342 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-11 09:52:56.342 [Debug] Resolved string: Click on the Login Button
2025-08-11 09:52:56.342 [Debug] Resolving placeholders in string: {{Selectors.LoginAuthButton}}
2025-08-11 09:52:56.342 [Debug] Resolved string: id=LoginAuthBtn
2025-08-11 09:52:56.342 [Info] Successfully resolved parameters for test case: Login
2025-08-11 09:52:56.342 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-11 09:52:56.342 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-11 09:52:56.342 [Debug] Resolving placeholders in string: id=finish
2025-08-11 09:52:56.343 [Debug] Resolved string: id=finish
2025-08-11 09:52:56.343 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-11 09:52:56.343 [Debug] Resolved string: Click on Login Button
2025-08-11 09:52:56.343 [Debug] Resolving placeholders in string: id=LoginBtn
2025-08-11 09:52:56.343 [Debug] Resolved string: id=LoginBtn
2025-08-11 09:52:56.343 [Debug] Resolving placeholders in string: Type the User Name
2025-08-11 09:52:56.344 [Debug] Resolved string: Type the User Name
2025-08-11 09:52:56.344 [Debug] Resolving placeholders in string: id=UserName
2025-08-11 09:52:56.344 [Debug] Resolved string: id=UserName
2025-08-11 09:52:56.344 [Debug] Resolving placeholders in string: Mm_azmy
2025-08-11 09:52:56.345 [Debug] Resolved string: Mm_azmy
2025-08-11 09:52:56.345 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-11 09:52:56.345 [Debug] Resolved string: Click on Continue Button
2025-08-11 09:52:56.345 [Debug] Resolving placeholders in string: id=btn
2025-08-11 09:52:56.345 [Debug] Resolved string: id=btn
2025-08-11 09:52:56.346 [Debug] Resolving placeholders in string: Type the Password
2025-08-11 09:52:56.346 [Debug] Resolved string: Type the Password
2025-08-11 09:52:56.346 [Debug] Resolving placeholders in string: id=Password
2025-08-11 09:52:56.346 [Debug] Resolved string: id=Password
2025-08-11 09:52:56.346 [Debug] Resolving placeholders in string: $('[id=Password]').val('Asdf2025')
2025-08-11 09:52:56.346 [Debug] Resolved string: $('[id=Password]').val('Asdf2025')
2025-08-11 09:52:56.346 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-11 09:52:56.346 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-11 09:52:56.346 [Debug] Resolving placeholders in string: id=ChallengeQuestionAnswer
2025-08-11 09:52:56.346 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-11 09:52:56.347 [Debug] Resolving placeholders in string: armada
2025-08-11 09:52:56.347 [Debug] Resolved string: armada
2025-08-11 09:52:56.347 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-11 09:52:56.347 [Debug] Resolved string: Click on the Login Button
2025-08-11 09:52:56.347 [Debug] Resolving placeholders in string: id=LoginAuthBtn
2025-08-11 09:52:56.348 [Debug] Resolved string: id=LoginAuthBtn
2025-08-11 09:52:56.348 [Debug] Resolving placeholders in string: Click on change later button on the password expiration popup message
2025-08-11 09:52:56.348 [Debug] Resolved string: Click on change later button on the password expiration popup message
2025-08-11 09:52:56.348 [Debug] Resolving placeholders in string: {{Selectors.popupCancel}}
2025-08-11 09:52:56.348 [Debug] Resolved string: id=popup_cancel
2025-08-11 09:52:56.348 [Debug] Resolving placeholders in string: Click on biometric alert
2025-08-11 09:52:56.348 [Debug] Resolved string: Click on biometric alert
2025-08-11 09:52:56.349 [Debug] Resolving placeholders in string: {{Selectors.BioMetricAlert}}
2025-08-11 09:52:56.349 [Debug] Resolved string: css=#popbuttons > div > button.btn.Cancel.back_gradient2
2025-08-11 09:52:56.349 [Debug] Resolving placeholders in string: Execute JS code to activate token
2025-08-11 09:52:56.349 [Debug] Resolved string: Execute JS code to activate token
2025-08-11 09:52:56.349 [Debug] Resolving placeholders in string: Globals.ActivationState = 'ACTIVATED';
2025-08-11 09:52:56.351 [Debug] Resolved string: Globals.ActivationState = 'ACTIVATED';
2025-08-11 09:52:56.351 [Debug] Resolving placeholders in string: Click on Cards option on the side menu
2025-08-11 09:52:56.351 [Debug] Resolved string: Click on Cards option on the side menu
2025-08-11 09:52:56.351 [Debug] Resolving placeholders in string: {{Selectors.CardsButton}}
2025-08-11 09:52:56.351 [Debug] Resolved string: xpath=//div[@class='mainmenuitems']//label[contains(text(), 'Cards')]
2025-08-11 09:52:56.351 [Debug] Resolving placeholders in string: Click on Card Services
2025-08-11 09:52:56.351 [Debug] Resolved string: Click on Card Services
2025-08-11 09:52:56.351 [Debug] Resolving placeholders in string: {{Selectors.CardServices}}
2025-08-11 09:52:56.351 [Debug] Resolved string: xpath=//div[@class='submenuitem']//label[contains(text(), 'Card services')]
2025-08-11 09:52:56.351 [Debug] Resolving placeholders in string: Click on ATM dispute
2025-08-11 09:52:56.351 [Debug] Resolved string: Click on ATM dispute
2025-08-11 09:52:56.352 [Debug] Resolving placeholders in string: {{Selectors.ATMdisputeButton}}
2025-08-11 09:52:56.352 [Debug] Resolved string: id=ATMDispute
2025-08-11 09:52:56.352 [Debug] Resolving placeholders in string: Click on dispute type
2025-08-11 09:52:56.352 [Debug] Resolved string: Click on dispute type
2025-08-11 09:52:56.352 [Debug] Resolving placeholders in string: {{Selectors.SelectDisputeType}}
2025-08-11 09:52:56.352 [Debug] Resolved string: xpath=//div[@class='details_container']//span[normalize-space(text())='Select...']
2025-08-11 09:52:56.352 [Debug] Resolving placeholders in string: Choose dispute type
2025-08-11 09:52:56.352 [Debug] Resolved string: Choose dispute type
2025-08-11 09:52:56.352 [Debug] Resolving placeholders in string: {{Selectors.DisputeType}}
2025-08-11 09:52:56.353 [Debug] Resolved string: xpath=//div[@class='details_container']//span[normalize-space(text())='Withdrawal did not dispense']
2025-08-11 09:52:56.354 [Debug] Resolving placeholders in string: Click on Select Cards button
2025-08-11 09:52:56.354 [Debug] Resolved string: Click on Select Cards button
2025-08-11 09:52:56.354 [Debug] Resolving placeholders in string: {{Selectors.SelectButton}}
2025-08-11 09:52:56.354 [Debug] Resolved string: xpath=//div[@class='details_container']//button[normalize-space(text())='Select']
2025-08-11 09:52:56.354 [Debug] Resolving placeholders in string: Choose a card with ATM transactions
2025-08-11 09:52:56.354 [Debug] Resolved string: Choose a card with ATM transactions
2025-08-11 09:52:56.354 [Debug] Resolving placeholders in string: {{Selectors.CardWithTransactions}}
2025-08-11 09:52:56.355 [Debug] Resolved string: xpath=//div[@class='module_list_container cardData']//h4[normalize-space(text())='4204XXXXXXXX2562']
2025-08-11 09:52:56.359 [Debug] Resolving placeholders in string: Click on select a transaction
2025-08-11 09:52:56.359 [Debug] Resolved string: Click on select a transaction
2025-08-11 09:52:56.359 [Debug] Resolving placeholders in string: {{Selectors.SelectTransactionButton}}
2025-08-11 09:52:56.359 [Debug] Resolved string: xpath=//div[@class='details_container']//button[normalize-space(text())='Select Transaction']
2025-08-11 09:52:56.360 [Debug] Resolving placeholders in string: Choose a transaction
2025-08-11 09:52:56.360 [Debug] Resolved string: Choose a transaction
2025-08-11 09:52:56.361 [Debug] Resolving placeholders in string: {{Selectors.Transaction}}
2025-08-11 09:52:56.361 [Debug] Resolved string: css=#Transaction > div:nth-child(1) > div.list_header
2025-08-11 09:52:56.362 [Debug] Resolving placeholders in string: Click on continue
2025-08-11 09:52:56.362 [Debug] Resolved string: Click on continue
2025-08-11 09:52:56.363 [Debug] Resolving placeholders in string: {{Selectors.ContinueButtonPilot1}}
2025-08-11 09:52:56.363 [Debug] Resolved string: xpath=//div[@class='details_container']//button[normalize-space(text())='Continue']
2025-08-11 09:52:56.363 [Debug] Resolving placeholders in string: Enter the disputed amount
2025-08-11 09:52:56.364 [Debug] Resolved string: Enter the disputed amount
2025-08-11 09:52:56.364 [Debug] Resolving placeholders in string: {{Selectors.DisputedAmmountField}}
2025-08-11 09:52:56.364 [Debug] Resolved string: xpath=//div[@class='details_container']//input[normalize-space(@placeholder)='Enter Disputed amount']
2025-08-11 09:52:56.364 [Debug] Resolving placeholders in string: 0
2025-08-11 09:52:56.364 [Debug] Resolved string: 0
2025-08-11 09:52:56.364 [Debug] Resolving placeholders in string: Enter the dispute note
2025-08-11 09:52:56.365 [Debug] Resolved string: Enter the dispute note
2025-08-11 09:52:56.365 [Debug] Resolving placeholders in string: {{Selectors.DisputeNoteField}}
2025-08-11 09:52:56.365 [Debug] Resolved string: xpath=//div[@class='details_container']//textarea[normalize-space(@placeholder)='']
2025-08-11 09:52:56.366 [Debug] Resolving placeholders in string: This is a test for the feature by the dev team. Please ignore this request.
2025-08-11 09:52:56.366 [Debug] Resolved string: This is a test for the feature by the dev team. Please ignore this request.
2025-08-11 09:52:56.367 [Debug] Resolving placeholders in string: Click on the agree terms checkbox
2025-08-11 09:52:56.367 [Debug] Resolved string: Click on the agree terms checkbox
2025-08-11 09:52:56.367 [Debug] Resolving placeholders in string: {{Selectors.TermsCheckbox}}
2025-08-11 09:52:56.367 [Debug] Resolved string: xpath=//div[@class='details_container']//input[@type='checkbox']
2025-08-11 09:52:56.367 [Debug] Resolving placeholders in string: Click on continue button
2025-08-11 09:52:56.368 [Debug] Resolved string: Click on continue button
2025-08-11 09:52:56.368 [Debug] Resolving placeholders in string: {{Selectors.ContinueButtonPilot2}}
2025-08-11 09:52:56.368 [Debug] Resolved string: xpath=//div[@class='details_container']//button[normalize-space(text())='Confirm']
2025-08-11 09:52:56.368 [Debug] Resolving placeholders in string: Click on confirm button
2025-08-11 09:52:56.368 [Debug] Resolved string: Click on confirm button
2025-08-11 09:52:56.368 [Debug] Resolving placeholders in string: {{Selectors.ConfirmDispute}}
2025-08-11 09:52:56.369 [Debug] Resolved string: id=DisputePreConfirmationBtn
2025-08-11 09:52:56.369 [Info] Successfully resolved parameters for test case: Complete ATM dispute request
2025-08-11 09:52:56.369 [Debug] Loading test case: WorkSet01/TestCases/03.Beneficiaries/Add/TC-34 AddinglocalBen.json
2025-08-11 09:52:56.369 [Info] Loading test case from: WorkSet01/TestCases/03.Beneficiaries/Add/TC-34 AddinglocalBen.json
2025-08-11 09:52:56.370 [Info] Attempting to load configuration file: WorkSet01/TestCases/03.Beneficiaries/Add/TC-34 AddinglocalBen.json
2025-08-11 09:52:56.378 [Info] Successfully loaded configuration file: WorkSet01/TestCases/03.Beneficiaries/Add/TC-34 AddinglocalBen.json
2025-08-11 09:52:56.379 [Info] No filtering configuration found. Including test case 'Add local beneficiary with valid data '.
2025-08-11 09:52:56.379 [Debug] Resolving parameters for test case: Add local beneficiary with valid data 
2025-08-11 09:52:56.379 [Debug] Merging params
2025-08-11 09:52:56.379 [Debug] Merged basic params
2025-08-11 09:52:56.379 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-11 09:52:56.379 [Debug] Merged SpecialEnvParams params
2025-08-11 09:52:56.380 [Debug] Merged params
2025-08-11 09:52:56.380 [Debug] Loading parameters from reference file: WorkSet01/Params/VerifictionParams.json
2025-08-11 09:52:56.380 [Info] Attempting to load configuration file: WorkSet01/Params/VerifictionParams.json
2025-08-11 09:52:56.393 [Info] Successfully loaded configuration file: WorkSet01/Params/VerifictionParams.json
2025-08-11 09:52:56.393 [Debug] Merging params
2025-08-11 09:52:56.394 [Debug] Merged basic params
2025-08-11 09:52:56.394 [Debug] Merged params
2025-08-11 09:52:56.394 [Debug] Merging params
2025-08-11 09:52:56.394 [Debug] Merging params
2025-08-11 09:52:56.394 [Debug] Resolving placeholders in test steps for test case: Add local beneficiary with valid data 
2025-08-11 09:52:56.395 [Debug] Resolving TestCaseReference in step: 
2025-08-11 09:52:56.395 [Info] Loading test case from: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-11 09:52:56.396 [Info] Attempting to load configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-11 09:52:56.396 [Info] Successfully loaded configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-11 09:52:56.398 [Debug] Resolving parameters for test case: Login
2025-08-11 09:52:56.399 [Debug] Merging params
2025-08-11 09:52:56.399 [Debug] Merged basic params
2025-08-11 09:52:56.399 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-11 09:52:56.399 [Debug] Merged SpecialEnvParams params
2025-08-11 09:52:56.399 [Debug] Merged params
2025-08-11 09:52:56.399 [Debug] Loading parameters from reference file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-11 09:52:56.400 [Info] Attempting to load configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-11 09:52:56.401 [Info] Successfully loaded configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-11 09:52:56.401 [Debug] Merging params
2025-08-11 09:52:56.401 [Debug] Merged basic params
2025-08-11 09:52:56.401 [Debug] Merged params
2025-08-11 09:52:56.401 [Debug] Merging params
2025-08-11 09:52:56.401 [Debug] Merging params
2025-08-11 09:52:56.402 [Debug] Resolving placeholders in test steps for test case: Login
2025-08-11 09:52:56.402 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-11 09:52:56.404 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-11 09:52:56.404 [Debug] Resolving placeholders in string: {{Selectors.FinishButton}}
2025-08-11 09:52:56.404 [Debug] Resolved string: id=finish
2025-08-11 09:52:56.405 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-11 09:52:56.405 [Debug] Resolved string: Click on Login Button
2025-08-11 09:52:56.405 [Debug] Resolving placeholders in string: {{Selectors.LoginButton}}
2025-08-11 09:52:56.405 [Debug] Resolved string: id=LoginBtn
2025-08-11 09:52:56.405 [Debug] Resolving placeholders in string: Type the User Name
2025-08-11 09:52:56.406 [Debug] Resolved string: Type the User Name
2025-08-11 09:52:56.406 [Debug] Resolving placeholders in string: {{Selectors.UserNameField}}
2025-08-11 09:52:56.406 [Debug] Resolved string: id=UserName
2025-08-11 09:52:56.406 [Debug] Resolving placeholders in string: {{TestData.UserName}}
2025-08-11 09:52:56.406 [Debug] Resolved string: mahmoudsayed022
2025-08-11 09:52:56.406 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-11 09:52:56.407 [Debug] Resolved string: Click on Continue Button
2025-08-11 09:52:56.407 [Debug] Resolving placeholders in string: {{Selectors.ContinueButton}}
2025-08-11 09:52:56.407 [Debug] Resolved string: id=btn
2025-08-11 09:52:56.408 [Debug] Resolving placeholders in string: Type the Password
2025-08-11 09:52:56.408 [Debug] Resolved string: Type the Password
2025-08-11 09:52:56.409 [Debug] Resolving placeholders in string: {{Selectors.PasswordField}}
2025-08-11 09:52:56.410 [Debug] Resolved string: id=Password
2025-08-11 09:52:56.411 [Debug] Resolving placeholders in string: $('[{{Selectors.PasswordField}}]').val('{{TestData.Password}}')
2025-08-11 09:52:56.411 [Debug] Resolved string: $('[id=Password]').val('Password1')
2025-08-11 09:52:56.411 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-11 09:52:56.411 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-11 09:52:56.411 [Debug] Resolving placeholders in string: {{Selectors.ChallengeQuestionField}}
2025-08-11 09:52:56.412 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-11 09:52:56.412 [Debug] Resolving placeholders in string: {{TestData.ChallengeAnswer}}
2025-08-11 09:52:56.412 [Debug] Resolved string: bmw
2025-08-11 09:52:56.413 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-11 09:52:56.413 [Debug] Resolved string: Click on the Login Button
2025-08-11 09:52:56.413 [Debug] Resolving placeholders in string: {{Selectors.LoginAuthButton}}
2025-08-11 09:52:56.413 [Debug] Resolved string: id=LoginAuthBtn
2025-08-11 09:52:56.413 [Info] Successfully resolved parameters for test case: Login
2025-08-11 09:52:56.413 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-11 09:52:56.413 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-11 09:52:56.414 [Debug] Resolving placeholders in string: id=finish
2025-08-11 09:52:56.414 [Debug] Resolved string: id=finish
2025-08-11 09:52:56.414 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-11 09:52:56.414 [Debug] Resolved string: Click on Login Button
2025-08-11 09:52:56.414 [Debug] Resolving placeholders in string: id=LoginBtn
2025-08-11 09:52:56.414 [Debug] Resolved string: id=LoginBtn
2025-08-11 09:52:56.414 [Debug] Resolving placeholders in string: Type the User Name
2025-08-11 09:52:56.415 [Debug] Resolved string: Type the User Name
2025-08-11 09:52:56.415 [Debug] Resolving placeholders in string: id=UserName
2025-08-11 09:52:56.416 [Debug] Resolved string: id=UserName
2025-08-11 09:52:56.416 [Debug] Resolving placeholders in string: mahmoudsayed022
2025-08-11 09:52:56.417 [Debug] Resolved string: mahmoudsayed022
2025-08-11 09:52:56.418 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-11 09:52:56.419 [Debug] Resolved string: Click on Continue Button
2025-08-11 09:52:56.419 [Debug] Resolving placeholders in string: id=btn
2025-08-11 09:52:56.419 [Debug] Resolved string: id=btn
2025-08-11 09:52:56.419 [Debug] Resolving placeholders in string: Type the Password
2025-08-11 09:52:56.419 [Debug] Resolved string: Type the Password
2025-08-11 09:52:56.419 [Debug] Resolving placeholders in string: id=Password
2025-08-11 09:52:56.420 [Debug] Resolved string: id=Password
2025-08-11 09:52:56.426 [Debug] Resolving placeholders in string: $('[id=Password]').val('Password1')
2025-08-11 09:52:56.426 [Debug] Resolved string: $('[id=Password]').val('Password1')
2025-08-11 09:52:56.426 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-11 09:52:56.427 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-11 09:52:56.427 [Debug] Resolving placeholders in string: id=ChallengeQuestionAnswer
2025-08-11 09:52:56.427 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-11 09:52:56.428 [Debug] Resolving placeholders in string: bmw
2025-08-11 09:52:56.429 [Debug] Resolved string: bmw
2025-08-11 09:52:56.429 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-11 09:52:56.430 [Debug] Resolved string: Click on the Login Button
2025-08-11 09:52:56.431 [Debug] Resolving placeholders in string: id=LoginAuthBtn
2025-08-11 09:52:56.431 [Debug] Resolved string: id=LoginAuthBtn
2025-08-11 09:52:56.432 [Debug] Resolving placeholders in string: Click on change later button on the password expiration popup message
2025-08-11 09:52:56.432 [Debug] Resolved string: Click on change later button on the password expiration popup message
2025-08-11 09:52:56.432 [Debug] Resolving placeholders in string: {{Selectors.popupCancel}}
2025-08-11 09:52:56.432 [Debug] Resolved string: id=popup_cancel
2025-08-11 09:52:56.432 [Debug] Resolving placeholders in string: Click on biometric alert
2025-08-11 09:52:56.432 [Debug] Resolved string: Click on biometric alert
2025-08-11 09:52:56.432 [Debug] Resolving placeholders in string: {{Selectors.BioMetricAlert}}
2025-08-11 09:52:56.433 [Debug] Resolved string: css=#popbuttons > div > button.btn.Cancel.back_gradient2
2025-08-11 09:52:56.433 [Debug] Resolving placeholders in string: Execute JS code to activate token
2025-08-11 09:52:56.434 [Debug] Resolved string: Execute JS code to activate token
2025-08-11 09:52:56.434 [Debug] Resolving placeholders in string: Globals.ActivationState = 'ACTIVATED';
2025-08-11 09:52:56.434 [Debug] Resolved string: Globals.ActivationState = 'ACTIVATED';
2025-08-11 09:52:56.435 [Debug] Resolving placeholders in string: Click on Beneficiaries
2025-08-11 09:52:56.435 [Debug] Resolved string: Click on Beneficiaries
2025-08-11 09:52:56.436 [Debug] Resolving placeholders in string: {{Selectors.BeneficiariesToButton}}
2025-08-11 09:52:56.437 [Debug] Resolved string: xpath=//div[@class='mainmenuitems']//label[contains(text(), 'Beneficiaries')]
2025-08-11 09:52:56.437 [Debug] Resolving placeholders in string: Click on Local Transfer option
2025-08-11 09:52:56.437 [Debug] Resolved string: Click on Local Transfer option
2025-08-11 09:52:56.437 [Debug] Resolving placeholders in string: {{Selectors.LocalTransfer}}
2025-08-11 09:52:56.437 [Debug] Resolved string: xpath=//div[@class='submenuitem']//label[contains(text(), 'Local Transfer')]
2025-08-11 09:52:56.438 [Debug] Resolving placeholders in string: Click on Add Beneficiary
2025-08-11 09:52:56.438 [Debug] Resolved string: Click on Add Beneficiary
2025-08-11 09:52:56.438 [Debug] Resolving placeholders in string: {{Selectors.AddLocalBenBTn}}
2025-08-11 09:52:56.439 [Debug] Resolved string: id=AddLocalBtn
2025-08-11 09:52:56.439 [Debug] Resolving placeholders in string: Type Account number
2025-08-11 09:52:56.439 [Debug] Resolved string: Type Account number
2025-08-11 09:52:56.439 [Debug] Resolving placeholders in string: {{Selectors.AccountNumberInput}}
2025-08-11 09:52:56.439 [Debug] Resolved string: id=BenFAccountNum
2025-08-11 09:52:56.439 [Debug] Resolving placeholders in string: **********
2025-08-11 09:52:56.439 [Debug] Resolved string: **********
2025-08-11 09:52:56.439 [Debug] Resolving placeholders in string: Type the Nick Name
2025-08-11 09:52:56.440 [Debug] Resolved string: Type the Nick Name
2025-08-11 09:52:56.440 [Debug] Resolving placeholders in string: {{Selectors.NickNameInput}}
2025-08-11 09:52:56.441 [Debug] Resolved string: id=BenfNickName
2025-08-11 09:52:56.442 [Debug] Resolving placeholders in string: eedhefdet
2025-08-11 09:52:56.442 [Debug] Resolved string: eedhefdet
2025-08-11 09:52:56.443 [Debug] Resolving placeholders in string: Type the  Beneficiary Full Name
2025-08-11 09:52:56.443 [Debug] Resolved string: Type the  Beneficiary Full Name
2025-08-11 09:52:56.443 [Debug] Resolving placeholders in string: {{Selectors.BeneficiaryFullNameInput}}
2025-08-11 09:52:56.443 [Debug] Resolved string: id=BenFName
2025-08-11 09:52:56.443 [Debug] Resolving placeholders in string: rmrldcrt
2025-08-11 09:52:56.443 [Debug] Resolved string: rmrldcrt
2025-08-11 09:52:56.444 [Debug] Resolving placeholders in string: select bank name
2025-08-11 09:52:56.444 [Debug] Resolved string: select bank name
2025-08-11 09:52:56.444 [Debug] Resolving placeholders in string: {{Selectors.BankNameSelect}}
2025-08-11 09:52:56.444 [Debug] Resolved string: id=SelectBankName
2025-08-11 09:52:56.445 [Debug] Resolving placeholders in string: select bank option
2025-08-11 09:52:56.445 [Debug] Resolved string: select bank option
2025-08-11 09:52:56.445 [Debug] Resolving placeholders in string: {{Selectors.BankOption}}
2025-08-11 09:52:56.445 [Debug] Resolved string: css=#Banks > div > div:nth-child(1) > span
2025-08-11 09:52:56.445 [Debug] Resolving placeholders in string: click on currency dropdown list
2025-08-11 09:52:56.445 [Debug] Resolved string: click on currency dropdown list
2025-08-11 09:52:56.445 [Debug] Resolving placeholders in string: {{Selectors.currencySelect}}
2025-08-11 09:52:56.445 [Debug] Resolved string: id=SelectCurrency_Name
2025-08-11 09:52:56.446 [Debug] Resolving placeholders in string: select currency option 
2025-08-11 09:52:56.446 [Debug] Resolved string: select currency option 
2025-08-11 09:52:56.447 [Debug] Resolving placeholders in string: {{Selectors.currencyoptionEGP}}
2025-08-11 09:52:56.448 [Debug] Resolved string: css=#wginsAddLocalBeneficiary_Default > div > div > div.section_body > div > div.input_group.retail > div.select_warpper > div.options.back_white_solid.dropdown-active.d-block > div > div:nth-child(1)
2025-08-11 09:52:56.448 [Debug] Resolving placeholders in string: Click on continue
2025-08-11 09:52:56.448 [Debug] Resolved string: Click on continue
2025-08-11 09:52:56.449 [Debug] Resolving placeholders in string: {{Selectors.SaveBtn}}
2025-08-11 09:52:56.449 [Debug] Resolved string: id=SavaBtn
2025-08-11 09:52:56.449 [Debug] Resolving placeholders in string: Click on continue
2025-08-11 09:52:56.449 [Debug] Resolved string: Click on continue
2025-08-11 09:52:56.450 [Debug] Resolving placeholders in string: {{Selectors.SecondContinueBen}}
2025-08-11 09:52:56.450 [Debug] Resolved string: id=DelLocBenSumContinue
2025-08-11 09:52:56.450 [Debug] Resolving placeholders in string: Enter Token number
2025-08-11 09:52:56.450 [Debug] Resolved string: Enter Token number
2025-08-11 09:52:56.450 [Debug] Resolving placeholders in string: {{Selectors.TokenInput}}
2025-08-11 09:52:56.450 [Debug] Resolved string: id=TokenNUMBER
2025-08-11 09:52:56.451 [Debug] Resolving placeholders in string: 123456
2025-08-11 09:52:56.451 [Debug] Resolved string: 123456
2025-08-11 09:52:56.451 [Debug] Resolving placeholders in string: Click on confirm and show the confirmation page
2025-08-11 09:52:56.451 [Debug] Resolved string: Click on confirm and show the confirmation page
2025-08-11 09:52:56.451 [Debug] Resolving placeholders in string: {{Selectors.btnTokenConfirm}}
2025-08-11 09:52:56.451 [Debug] Resolved string: id=btnTokenConfirm
2025-08-11 09:52:56.451 [Info] Successfully resolved parameters for test case: Add local beneficiary with valid data 
2025-08-11 09:52:56.452 [Info] Attempting to load configuration file: ExcludedTestData.json
2025-08-11 09:52:56.452 [Info] Successfully loaded configuration file: ExcludedTestData.json
2025-08-11 09:52:56.454 [Info] Retrieved 25 test cases.
2025-08-11 09:52:56.454 [Info] Total test cases found: 25
2025-08-11 09:53:06.438 [Info] Starting test case execution: Verify That User Can't Redeem Happy Points Using Wrong SMS
2025-08-11 09:53:28.427 [Info] Executing test case: Verify That User Can't Redeem Happy Points Using Wrong SMS
2025-08-11 09:53:28.431 [Info] Using browser: System.String[] and BaseUrl: https://ebs.ca-egypt.com/OmniChannel/CEEPDigitalBankR6/eBankApplication/ePortal5Core/ePortal5.htm?Menu=New#/EN/Landing
2025-08-11 09:53:28.787 [Info] Initializing WebDriver for browser: Chrome
2025-08-11 09:53:28.787 [Debug] Setting up Chrome WebDriver.
2025-08-11 09:53:51.085 [Info] WebDriver initialized and window maximized.
2025-08-11 09:53:55.904 [Info] Navigated to BaseUrl: https://ebs.ca-egypt.com/OmniChannel/CEEPDigitalBankR6/eBankApplication/ePortal5Core/ePortal5.htm?Menu=New#/EN/Landing
2025-08-11 09:53:56.513 [Debug] Executing step: click on target: id=finish
2025-08-11 09:53:56.538 [Debug] Waiting for loading spinner to disappear.
2025-08-11 09:53:56.557 [Debug] Parsing selector: id=LoadingSpinner
2025-08-11 09:54:01.225 [Debug] Delaying step execution by 2000 ms.
2025-08-11 09:54:03.237 [Debug] Locating element with target: id=finish
2025-08-11 09:54:03.238 [Debug] Parsing selector: id=finish
2025-08-11 09:54:03.390 [Debug] Clicking on target: id=finish
2025-08-11 09:54:06.549 [Debug] Waiting for loading spinner to disappear.
2025-08-11 09:54:06.550 [Debug] Parsing selector: id=LoadingSpinner
2025-08-11 09:54:08.283 [Info] The ScreenShot was successfully taken for Step: Click on Finish Button to skip the demo, in TestCase:Verify That User Can't Redeem Happy Points Using Wrong SMS
2025-08-11 09:54:08.303 [Debug] Executing step: click on target: id=LoginBtn
2025-08-11 09:54:08.312 [Debug] Waiting for loading spinner to disappear.
2025-08-11 09:54:08.317 [Debug] Parsing selector: id=LoadingSpinner
2025-08-11 09:54:08.887 [Debug] Delaying step execution by 2000 ms.
2025-08-11 09:54:10.901 [Debug] Locating element with target: id=LoginBtn
2025-08-11 09:54:10.902 [Debug] Parsing selector: id=LoginBtn
2025-08-11 09:54:11.959 [Debug] Clicking on target: id=LoginBtn
2025-08-11 09:54:12.144 [Debug] Waiting for loading spinner to disappear.
2025-08-11 09:54:12.144 [Debug] Parsing selector: id=LoadingSpinner
2025-08-11 09:54:13.173 [Info] The ScreenShot was successfully taken for Step: Click on Login Button, in TestCase:Verify That User Can't Redeem Happy Points Using Wrong SMS
2025-08-11 09:54:13.175 [Debug] Executing step: type on target: id=UserName
2025-08-11 09:54:13.176 [Debug] Waiting for loading spinner to disappear.
2025-08-11 09:54:13.178 [Debug] Parsing selector: id=LoadingSpinner
2025-08-11 09:54:13.423 [Debug] Delaying step execution by 500 ms.
2025-08-11 09:54:13.936 [Debug] Locating element with target: id=UserName
2025-08-11 09:54:13.943 [Debug] Parsing selector: id=UserName
2025-08-11 09:54:16.622 [Error] Error executing step: type on target: id=UserName , CustomStatusToReportUponFailure: . Details: Can`t Find Element with target: id=UserName 
2025-08-11 09:54:16.657 [Error] Error executing test case: Verify That User Can't Redeem Happy Points Using Wrong SMS. Details: Can`t Find Element with target: id=UserName 
2025-08-11 09:54:16.954 [Info] WebDriver instance quit.
2025-08-11 09:54:32.589 [Error] Error during test case execution: Verify That User Can't Redeem Happy Points Using Wrong SMS. Details: Can`t Find Element with target: id=UserName 
2025-08-11 09:55:49.427 [Info] Starting test case execution: Verify That User Can't Redeem Happy Points Using Wrong SMS
2025-08-11 09:55:55.513 [Info] Executing test case: Verify That User Can't Redeem Happy Points Using Wrong SMS
2025-08-11 09:55:55.514 [Info] Using browser: System.String[] and BaseUrl: https://ebs.ca-egypt.com/OmniChannel/CEEPDigitalBankR6/eBankApplication/ePortal5Core/ePortal5.htm?Menu=New#/EN/Landing
2025-08-11 09:55:55.515 [Info] Initializing WebDriver for browser: Chrome
2025-08-11 09:55:55.518 [Debug] Setting up Chrome WebDriver.
2025-08-11 09:56:07.092 [Info] WebDriver initialized and window maximized.
2025-08-11 09:56:10.158 [Info] Navigated to BaseUrl: https://ebs.ca-egypt.com/OmniChannel/CEEPDigitalBankR6/eBankApplication/ePortal5Core/ePortal5.htm?Menu=New#/EN/Landing
2025-08-11 09:56:10.158 [Debug] Executing step: click on target: id=finish
2025-08-11 09:56:10.158 [Debug] Waiting for loading spinner to disappear.
2025-08-11 09:56:10.158 [Debug] Parsing selector: id=LoadingSpinner
2025-08-11 09:56:12.359 [Debug] Delaying step execution by 2000 ms.
2025-08-11 09:56:14.363 [Debug] Locating element with target: id=finish
2025-08-11 09:56:14.363 [Debug] Parsing selector: id=finish
2025-08-11 09:56:15.938 [Debug] Clicking on target: id=finish
2025-08-11 09:56:16.152 [Debug] Waiting for loading spinner to disappear.
2025-08-11 09:56:16.154 [Debug] Parsing selector: id=LoadingSpinner
2025-08-11 09:56:18.320 [Info] The ScreenShot was successfully taken for Step: Click on Finish Button to skip the demo, in TestCase:Verify That User Can't Redeem Happy Points Using Wrong SMS
2025-08-11 09:56:18.348 [Debug] Executing step: click on target: id=LoginBtn
2025-08-11 09:56:18.352 [Debug] Waiting for loading spinner to disappear.
2025-08-11 09:56:18.356 [Debug] Parsing selector: id=LoadingSpinner
2025-08-11 09:56:18.932 [Debug] Delaying step execution by 2000 ms.
2025-08-11 09:56:20.946 [Debug] Locating element with target: id=LoginBtn
2025-08-11 09:56:20.948 [Debug] Parsing selector: id=LoginBtn
2025-08-11 09:56:21.354 [Debug] Clicking on target: id=LoginBtn
2025-08-11 09:56:21.856 [Debug] Waiting for loading spinner to disappear.
2025-08-11 09:56:21.865 [Debug] Parsing selector: id=LoadingSpinner
2025-08-11 09:56:23.849 [Info] The ScreenShot was successfully taken for Step: Click on Login Button, in TestCase:Verify That User Can't Redeem Happy Points Using Wrong SMS
2025-08-11 09:56:23.849 [Debug] Executing step: type on target: id=UserName
2025-08-11 09:56:23.849 [Debug] Waiting for loading spinner to disappear.
2025-08-11 09:56:23.850 [Debug] Parsing selector: id=LoadingSpinner
2025-08-11 09:56:24.008 [Error] Error executing step: type on target: id=UserName , CustomStatusToReportUponFailure: . Details: invalid session id: session deleted as the browser has closed the connection
from disconnected: not connected to DevTools
  (Session info: chrome=139.0.7258.66)
2025-08-11 09:56:24.068 [Error] Error executing test case: Verify That User Can't Redeem Happy Points Using Wrong SMS. Details: invalid session id: session deleted as the browser has closed the connection
from disconnected: not connected to DevTools
  (Session info: chrome=139.0.7258.66)
2025-08-11 09:56:24.313 [Info] WebDriver instance quit.
2025-08-11 09:56:28.696 [Error] Error during test case execution: Verify That User Can't Redeem Happy Points Using Wrong SMS. Details: invalid session id: session deleted as the browser has closed the connection
from disconnected: not connected to DevTools
  (Session info: chrome=139.0.7258.66)
2025-08-11 10:01:51.404 [Info] Report generated as html
2025-08-11 10:02:04.410 [Info] Report generated as html
2025-08-11 10:02:09.349 [Info] Combined summary report generated successfully