{"TestSuiteName": "Accounts Suite", "TestSuiteItems": [{"Type": "TestCase", "Reference": "WorkSet01/TestCases/accounts/286-Display 50 transactions.json", "ParamsReference": "WorkSet01/Params/AccountsParams.json"}, {"Type": "TestCase", "Reference": "WorkSet01/TestCases/accounts/306-Pay a Bill from a Blocked Account.json", "ParamsReference": "WorkSet01/Params/AccountsParams.json"}, {"Type": "TestCase", "Reference": "WorkSet01/TestCases/accounts/305-Transfer from a Blocked Account to a Card.json", "ParamsReference": "WorkSet01/Params/AccountsParams.json"}, {"Type": "TestCase", "Reference": "WorkSet01/TestCases/accounts/447-inward Checks data are displayed.json", "ParamsReference": "WorkSet01/Params/AccountsParams.json"}]}