{"TestCaseName": "Credit cards and prepaid cards displayed on 2 widgets", "TestCaseCode": "TC-114", "Steps": [{"TestCaseReference": {"Name": "<PERSON><PERSON>", "TestCasePath": "WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json", "ParamsReference": "WorkSet01/Params/LoginTestCaseParams.json", "Params": {"TestData": {"UserName": "mahmoudsayed022", "Password": "Password1", "ChallengeAnswer": "bmw"}}}}, {"Name": "Click on change later button on the password expiration popup message", "Command": "Click", "Target": "{{Selectors.popupCancel}}", "ContinueOnError": true}, {"Name": "Execute JS code to activate token", "Command": "executescript", "Value": "Globals.ActivationState = 'ACTIVATED';", "CustomDelayBeforeStepExecustionInMilliseconds": 2000}, {"Name": "Click on Cards option on the side menu", "Command": "Click", "Target": "{{Selectors.CardsButton}}", "CustomDelayBeforeStepExecustionInMilliseconds": 2000}, {"Name": "Click on Card Inquiry", "Command": "Click", "Target": "{{Selectors.CardInquiry}}"}, {"Name": "check Credit cards and prepaid cards displayed on 2 widgets", "Command": "appear", "Target": "id=CreditCard", "CustomDelayBeforeStepExecustionInMilliseconds": 20000}]}