<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Test Execution Report</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <div class="header">
            <img src="D:\TFS\DevWork\R and D Work\TestAutomationFramework\TestAutomationFramework\Projects\CA\Templates/CA_Logo.png" alt="Company Logo">
            <h1  style="display:none;" >Card Services Suite Execution Report</h1>
            <p>Generated on: <strong>2025-08-14 12:09 PM</strong> | Environment: <strong>UAT61</strong> | Browser: <strong>Chrome</strong> | Duration:<strong>00:00:10</strong></p>
        </div>
        <h2  style="display:none;" >Overall Summary</h2>
<table  class="summary-table overall-summary"  style="display:none;" >
    <tr>
        <th>Total Suites</th>
        <th>Total Modules</th>
        <th>Total Test Cases</th>
        <!-- <th>Total Steps</th>-->
        <th>Passed</th>
        <th>Failed</th>
        <th>Total Duration</th>

        <!-- <th>Skipped</th>-->
    </tr>
    <tr>
        <td>1</td>
        <td>0</td>
        <td>1</td>
        <!--<td>0</td>-->
        <td>0</td>
        <td>1</td>
        <!-- <td>{{TotalSkipped}}</td>-->
         <td>00:00:10</td>

    </tr>
</table>

		
        <h2  style="display:none;" >2. Card Services</h2>
<table  class="summary-table suite-summary"  style="display:none;" >
    <tr>
        <th>Total Modules</th>
        <th>Total Test Cases</th>
        <th>Passed</th>
        <th>Failed</th>
      <!--  <th>Skipped</th>-->
    </tr>
    <tr>
        <td>0</td>
        <td>1</td>
        <td>0</td>
        <td>1</td>
       <!-- <td>{{SuiteSkipped}}</td>-->
    </tr>
</table>
<div class="suite-content">
    <div class="metadata">
    <div class="test-case">
        <strong>2.1.1. All users Cards are displayed successfully</strong><span style="font-weight: normal;"><i> (TC-113 | Card Services)</i></span>
    </div>
    <div class="status">
        <span>Status:</span>
        <br>
        <span class="status-failed">Failed</span>
    </div>
    <div class="status">
        <span>Exec Time (mm:ss)</span>
        <br>
        <span>00:10</span>
    </div>
</div>
<div class="metadata error" style="display: block">
    <span>Error Message:</span>
    <span class="status-failed">Error executing step: Click on Continue Button.</span>
</div>
<table class="test-steps-table">
    <tr>
        <th>Step</th>
        <th>Description</th>
        <th style="display: none">Result</th>
        <th style="display: none">Execution Time</th>
        <th>Screenshot</th>
    </tr>
    <tr>
    <td>1</td>
    <td>Click on Finish Button to skip the demo</td>
    <td style="display: none;">Passed</td>
    <td style="display: none;">0s</td>
    <td><img class='test-step-image' src='D:\TFS\DevWork\R and D Work\TestAutomationFramework\TestAutomationFramework\Projects\CA\Results\ScreenShots\Card Services\2025-08-14 12.09.13.261_Click on Finish Button to skip the demo.png' alt='Step Screenshot'></td>
</tr>
<tr>
    <td>2</td>
    <td>Click on Login Button</td>
    <td style="display: none;">Passed</td>
    <td style="display: none;">0s</td>
    <td><img class='test-step-image' src='D:\TFS\DevWork\R and D Work\TestAutomationFramework\TestAutomationFramework\Projects\CA\Results\ScreenShots\Card Services\2025-08-14 12.09.18.374_Click on Login Button.png' alt='Step Screenshot'></td>
</tr>
<tr>
    <td>3</td>
    <td>Type the User Name</td>
    <td style="display: none;">Passed</td>
    <td style="display: none;">0s</td>
    <td><img class='test-step-image' src='D:\TFS\DevWork\R and D Work\TestAutomationFramework\TestAutomationFramework\Projects\CA\Results\ScreenShots\Card Services\2025-08-14 12.09.19.282_Type the User Name.png' alt='Step Screenshot'></td>
</tr>

    <tr>
        <td class="status-failed" style="display:none" ; colspan="3">Error Details: Object reference not set to an instance of an object.</td>
    </tr>
</table>

</div>

    </div>
    <div class="footer">
        <p>End of Report</p>
    </div>
</body>
</html>
