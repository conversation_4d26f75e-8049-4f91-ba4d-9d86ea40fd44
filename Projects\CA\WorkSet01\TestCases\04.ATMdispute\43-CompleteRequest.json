{"TestCaseName": "Complete ATM dispute request", "TestCaseCode": "TC-43", "Environment": "Pilot", "Steps": [{"TestCaseReference": {"Name": "<PERSON><PERSON>", "TestCasePath": "WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json", "ParamsReference": "WorkSet01/Params/LoginTestCaseParams.json"}}, {"Name": "Click on change later button on the password expiration popup message", "Command": "Click", "Target": "{{Selectors.popupCancel}}", "ContinueOnError": true}, {"Name": "Click on biometric alert", "Command": "Click", "Target": "{{Selectors.BioMetricAlert}}", "ContinueOnError": true}, {"Name": "Execute JS code to activate token", "Command": "executescript", "Value": "Globals.ActivationState = 'ACTIVATED';"}, {"Name": "Click on Cards option on the side menu", "Command": "Click", "Target": "{{Selectors.CardsButton}}"}, {"Name": "Click on Card Services", "Command": "Click", "Target": "{{Selectors.CardServices}}"}, {"Name": "Click on ATM dispute", "Command": "Click", "Target": "{{Selectors.ATMdisputeButton}}"}, {"Name": "Click on dispute type", "Command": "Click", "Target": "{{Selectors.SelectDisputeType}}"}, {"Name": "Choose dispute type", "Command": "click", "Target": "{{Selectors.DisputeType}}"}, {"Name": "Click on Select Cards button", "Command": "Click", "Target": "{{Selectors.SelectButton}}"}, {"Name": "Choose a card with ATM transactions", "Command": "click", "Target": "{{Selectors.CardWithTransactions}}"}, {"Name": "Click on select a transaction", "Command": "click", "Target": "{{Selectors.SelectTransactionButton}}"}, {"Name": "Choose a transaction", "Command": "click", "Target": "{{Selectors.Transaction}}"}, {"Name": "Click on continue", "Command": "click", "Target": "{{Selectors.ContinueButtonPilot1}}"}, {"Name": "Enter the disputed amount", "Command": "type", "Target": "{{Selectors.DisputedAmmountField}}", "Value": "0"}, {"Name": "Enter the dispute note", "Command": "type", "Target": "{{Selectors.DisputeNoteField}}", "Value": "This is a test for the feature by the dev team. Please ignore this request."}, {"Name": "Click on the agree terms checkbox", "Command": "click", "Target": "{{Selectors.TermsCheckbox}}"}, {"Name": "Click on continue button", "Command": "click", "Target": "{{Selectors.ContinueButtonPilot2}}"}, {"Name": "Click on confirm button", "Command": "click", "Target": "{{Selectors.ConfirmDispute}}"}]}