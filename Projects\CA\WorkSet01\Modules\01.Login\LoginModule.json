{"ModuleName": "<PERSON><PERSON>", "TestCases": [{"TestCasePath": "WorkSet01/TestCases/01.Login/5-LoginInvalidUsername.json", "ParamsReference": "WorkSet01/Params/LoginTestCaseParams.json"}, {"TestCasePath": "WorkSet01/TestCases/01.Login/6-ValidUsername,ValidPassword,InvalidChallengeAnswer.json", "ParamsReference": "WorkSet01/Params/LoginTestCaseParams.json"}, {"TestCasePath": "WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json", "ParamsReference": "WorkSet01/Params/LoginTestCaseParams.json"}, {"TestCasePath": "WorkSet01/TestCases/01.Login/7-VaildUserName,InvalidPassword.json", "ParamsReference": "WorkSet01/Params/LoginTestCaseParams.json"}, {"TestCasePath": "WorkSet01/TestCases/01.Login/8-EnterLoginCredentials--PasswordIsAboutToExpire.json", "ParamsReference": "WorkSet01/Params/LoginTestCaseParams.json"}, {"TestCasePath": "WorkSet01/TestCases/01.Login/88-EnterLoginCredentials--PasswordIsExpired.json", "ParamsReference": "WorkSet01/Params/LoginTestCaseParams.json"}]}