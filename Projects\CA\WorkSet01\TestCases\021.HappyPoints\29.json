{"TestCaseName": "Happy Points valid redemption", "TestCaseCode": "TC-29", "Environment": "Pilot", "Steps": [{"TestCaseReference": {"Name": "<PERSON><PERSON>", "TestCasePath": "WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json", "ParamsReference": "WorkSet01/Params/LoginTestCaseParams.json"}}, {"Name": "Click on change later button on the password expiration popup message", "Command": "Click", "Target": "{{Selectors.popupCancel}}", "ContinueOnError": true}, {"Name": "Click on biometric alert", "Command": "Click", "Target": "{{Selectors.BioMetricAlert}}", "ContinueOnError": true}, {"Name": "Click on Happy points option from side menu", "Command": "Click", "Target": "{{Selectors.HappyPointsOption}}"}, {"Name": "Click on Redeem Online button", "Command": "Click", "Target": "{{Selectors.RedeemOnline}}"}, {"Name": "Choose a merchant", "Command": "Click", "Target": "{{Selectors.SelectMerchant}}"}, {"Name": "Click Redeem button", "Command": "Click", "Target": "{{Select<PERSON>.<PERSON>eem<PERSON>}}"}, {"Name": "Enter the number of points to redeem", "Command": "Type", "Target": "{{Selectors.PointsTextbox}}", "Value": "1000"}, {"Name": "Click on create cupon button", "Command": "Click", "Target": "{{Selectors.CreateCupon}}"}, {"Name": "Click on Continue button", "Command": "Click", "Target": "{{Selectors.CuntinueConfirmCupon}}"}, {"Name": "Ensure that it redirected to the token page", "Command": "appear", "Target": "{{Selectors.CreateCupon}}", "CustomStatusToReportUponFailure": "Blocked-SMS"}], "unusedSteps": [{"Name": "Enter the OTP", "Command": "Type", "Target": "{{Selectors.OTPbox}}"}, {"Name": "Click on Confirm OTP button", "Command": "Click", "Target": "{{Selectors.OTPconfirmButton}}"}]}