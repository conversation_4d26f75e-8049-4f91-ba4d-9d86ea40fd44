{"Selectors": {"FinishButton": "id=finish", "LoginButton": "id=LoginBtn", "UserNameField": "id=UserName", "ContinueButton": "id=btn", "PasswordField": "id=Password", "ChallengeQuestionField": "id=ChallengeQuestionAnswer", "LoginAuthButton": "id=LoginAuthBtn", "popupMessage": "id=popup_message", "popupOk": "id=popup_ok"}, "TestData": {"UserName": "sherif1234", "Password": "Password1", "ChallengeAnswer": "bmw", "ValidCIF": "*********"}, "SpecialEnvParams": {"Pilot": {"TestData": {"UserName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Password": "Password2", "ChallengeAnswer": "bmw", "ValidCIF": "*********"}, "Selectors": {"EGP2AccountID": "id=Account15088180071394", "TransferOnceCC": "id=TransferOnce", "NNNdscdxAccount": "xpath=//h4[text()='New']", "ChallengeAnswer": "bmw", "ValidCIF": "*********"}}}}