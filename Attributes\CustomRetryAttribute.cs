﻿using Microsoft.Extensions.Configuration;
using NUnit.Framework;
using NUnit.Framework.Interfaces;
using NUnit.Framework.Internal;
using NUnit.Framework.Internal.Commands;

using TestAutomationFramework.Helpers;

namespace TestAutomationFramework.Attributes
{
    [AttributeUsage(AttributeTargets.Method | AttributeTargets.Class, AllowMultiple = false, Inherited = false)]
    public class CustomRetryAttribute : Attribute, IRepeatTest
    {
        private readonly int _tryCount;
        private const int _defaultRetryCount = 1; // Default retry count if not specified

        public CustomRetryAttribute(int tryCount = 0)
        {
            _tryCount = tryCount;
        }

        public TestCommand Wrap(TestCommand command)
        {
            return new RetryCommand(command, _tryCount);
        }

        private static int GetRetryCountFromConfig(int tryCount)
        {
            try
            {
                // Option 1: From NUnit Run Console parameters
                var paramValue = TestContext.Parameters.Get("Retry");
                if (!string.IsNullOrEmpty(paramValue) && int.TryParse(paramValue, out int paramRetryCount))
                {
                    Helpers.Logger.Log($"Retry count from parameters: {paramRetryCount}", LogLevel.Info);
                    return paramRetryCount;
                }

                // Option 2: From RunningInstructions
                var configValue = ConfigHelper._RunningInstructions.RetryCount;
                if (configValue != null && configValue != 0)
                {
                    Helpers.Logger.Log($"Retry count from RunningInstructions: {configValue}", LogLevel.Info);
                    return configValue;
                }

                // Option 3: From the attribute constructor
                if (tryCount != null && tryCount != 0)
                {
                    Helpers.Logger.Log($"Retry count from attribute constructor: {tryCount}", LogLevel.Info);
                    return tryCount;
                }

                Helpers.Logger.Log($"Using default retry count: {_defaultRetryCount}", LogLevel.Info);
                return _defaultRetryCount;
            }
            catch
            {
                return _defaultRetryCount;
            }
        }

        private class RetryCommand : TestCommand
        {
            private readonly TestCommand _innerCommand;
            private readonly int _tryCount;

            public RetryCommand(TestCommand innerCommand, int tryCount)
                : base(innerCommand.Test)
            {
                _innerCommand = innerCommand;
                _tryCount = GetRetryCountFromConfig(tryCount);
            }

            public override TestResult Execute(TestExecutionContext context)
            {
                TestResult result = null;

                for (int i = 1; i <= _tryCount; i++)
                {
                    context.CurrentRepeatCount = i - 1;
                    result = _innerCommand.Execute(context);

                    if (result.ResultState != ResultState.Error &&
                        result.ResultState != ResultState.Failure)
                    {
                        break;
                    }

                    if (i < _tryCount)
                    {
                        TestContext.WriteLine($"Test failed on attempt {i}, retrying...");
                    }
                }

                return result;
            }
        }
    }
}
