{"TestCaseName": "Verify that a user can successfully unblock a card", "TestCaseCode": "TC-52", "Steps": [{"TestCaseReference": {"Name": "<PERSON><PERSON>", "TestCasePath": "WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json", "ParamsReference": "WorkSet01/Params/LoginTestCaseParams.json", "Params": {"TestData": {"UserName": "mahmoudsayed022", "Password": "Password1", "ChallengeAnswer": "bmw"}}}}, {"Name": "Click on change later button on the password expiration popup message", "Command": "Click", "Target": "{{Selectors.popupCancel}}", "ContinueOnError": true}, {"Name": "Click on biometric alert", "Command": "Click", "Target": "{{Selectors.BioMetricAlert}}", "ContinueOnError": true, "CustomDelayBeforeStepExecustionInMilliseconds": 1500}, {"Name": "Click on Cards button from side menu", "Command": "Click", "Target": "{{Selectors.CardsButton}}"}, {"Name": "Execute JS code to activate token", "Command": "executescript", "Value": "Globals.ActivationState = 'ACTIVATED';"}, {"Name": "Click on Cards Services button from side menu", "Command": "Click", "Target": "{{Selectors.CardServices}}"}, {"Name": "Click on UnBlock Btn", "Command": "Click", "Target": "{{Selectors.UnblockCard}}"}, {"Name": "Click on Blocked Card", "Command": "Click", "Target": "{{Selectors.BlockedCard}}"}, {"Name": "Click on Agree Check Box", "Command": "Click", "Target": "{{Selectors.AgreeCheckBox}}"}, {"Name": "Click on Continue", "Command": "Click", "Target": "{{Selectors.ContinueBtn}}"}, {"Name": "Type valid Token", "Command": "type", "Target": "{{Selectors.TokenInput}}", "value": "{{TestData.Token_Number}}"}, {"Name": "Click On Confirm", "Command": "click", "Target": "{{Selectors.ConfirmBtn}}"}], "unesedStep": {"Name": "Assert error MSG: Invalid authentication code", "Command": "assert", "Target": "{{Selectors.popupMessage}}", "value": "Invalid authentication code"}}