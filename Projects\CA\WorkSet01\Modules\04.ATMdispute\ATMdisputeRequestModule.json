{"ModuleName": "ATM disputes", "TestCases": [{"TestCasePath": "WorkSet01/TestCases/04.ATMdispute/Dispute request/TC-409 Ensure that Disputed amount applicable enter English numbers only.json", "ParamsReference": "WorkSet01/Params/ATMdisputeParams.json"}, {"TestCasePath": "WorkSet01/TestCases/04.ATMdispute/Dispute request/TC-408 User enters letters or special characters instead of numbers in the disputed amount field.json", "ParamsReference": "WorkSet01/Params/ATMdisputeParams.json"}, {"TestCasePath": "WorkSet01/TestCases/04.ATMdispute/Dispute request/TC-410 Ensure that note applicable enter number and character.json", "ParamsReference": "WorkSet01/Params/ATMdisputeParams.json"}, {"TestCasePath": "WorkSet01/TestCases/04.ATMdispute/Dispute request/TC-411 Ensure that Terms and Conditions displayed.json", "ParamsReference": "WorkSet01/Params/ATMdisputeParams.json"}, {"TestCasePath": "WorkSet01/TestCases/04.ATMdispute/Dispute request/TC-412 User can submit a dispute without seeing or agreeing to the terms and conditions.json", "ParamsReference": "WorkSet01/Params/ATMdisputeParams.json"}, {"TestCasePath": "WorkSet01/TestCases/04.ATMdispute/Dispute request/TC-401 Ensure that Dispute Type displayed.json", "ParamsReference": "WorkSet01/Params/ATMdisputeParams.json"}, {"TestCasePath": "WorkSet01/TestCases/04.ATMdispute/Dispute request/TC-402 Ensure that all cards displayed credit, debit , prepaid.json", "ParamsReference": "WorkSet01/Params/ATMdisputeParams.json"}, {"TestCasePath": "WorkSet01/TestCases/04.ATMdispute/Dispute request/TC-403 Ensure that Select Disputed transaction the transactions displayed.json", "ParamsReference": "WorkSet01/Params/ATMdisputeParams.json"}, {"TestCasePath": "WorkSet01/TestCases/04.ATMdispute/Dispute request/TC-404 Select transaction Ensure that autofill ATM Location , BankATM.json", "ParamsReference": "WorkSet01/Params/ATMdisputeParams.json"}]}