{"TestCaseName": "Choose card from a list in ATM dispute", "TestCaseCode": "TC-45", "Steps": [{"TestCaseReference": {"TestCasePath": "WorkSet01/TestCases/04.ATMdispute/ATMdispute.json", "ParamsReference": "WorkSet01/Params/ATMdisputeParams.json"}}, {"Name": "Click on Select Cards button", "Command": "Click", "Target": "{{Selectors.SelectButton}}", "CustomDelayBeforeStepExecustionInMilliseconds": 3000}, {"Name": "Check the Cards List showing the cards available for the account", "Command": "countclass", "Target": "{{Selectors.CardListClass}}", "CustomDelayBeforeStepExecustionInMilliseconds": 3000}]}