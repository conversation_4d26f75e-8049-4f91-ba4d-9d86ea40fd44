{"AllOf": [{"Predicate": {"Context": "RunFlags", "Field": "StopAtTokenStep", "Operator": "Equals", "Value": "TokenStep"}}, {"AnyOf": [{"Predicate": {"Context": "Environment", "Field": "UAT", "Operator": "Equals", "Value": "{{environment}}"}}, {"Predicate": {"Context": "Environment", "Field": "UAT61", "Operator": "Equals", "Value": "{{environment}}"}}]}, {"not": {"Predicate": {"Context": "TestCase", "Field": "Status", "Operator": "Equals", "Value": "Failed"}}}, {"Reference": {"Ref": "StepEqualsParam.json", "Params": {"expectedStep": "LoginStep"}}}]}