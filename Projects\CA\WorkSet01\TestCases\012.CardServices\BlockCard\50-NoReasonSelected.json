{"TestCaseName": "No reason Selected", "TestCaseCode": "TC-50", "Steps": [{"TestCaseReference": {"Name": "Block Card", "TestCasePath": "WorkSet01/TestCases/012.CardServices/BlockCard/BlockCard.json", "ParamsReference": "WorkSet01/Params/BlockCardParams.json"}}, {"Name": "Click on Select Card Button", "Command": "Click", "Target": "{{Selectors.SelectCardButton}}"}, {"Name": "<PERSON><PERSON>ose a card from the cards list", "Command": "Click", "Target": "{{Selectors.CardFromList}}"}, {"Name": "Click on Block Card Button", "Command": "Click", "Target": "{{Selectors.BlockCardButton}}"}, {"Name": "Assert popup error message with value: {{Selectors.SelectReasonToBlockMessage}}", "Command": "Verify", "Target": "{{Selectors.popupMessage}}", "Value": "{{Selectors.SelectReasonToBlockMessage}}"}]}