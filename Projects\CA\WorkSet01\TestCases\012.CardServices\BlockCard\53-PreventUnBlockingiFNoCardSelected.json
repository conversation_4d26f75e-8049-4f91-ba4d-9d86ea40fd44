{"TestCaseName": "Unblock card with no card selected", "TestCaseCode": "TC-53", "Steps": [{"TestCaseReference": {"Name": "<PERSON><PERSON>", "TestCasePath": "WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json", "ParamsReference": "WorkSet01/Params/LoginTestCaseParams.json"}}, {"Name": "Click on change later button on the password expiration popup message", "Command": "Click", "Target": "{{Selectors.popupCancel}}", "ContinueOnError": true}, {"Name": "Click on biometric alert", "Command": "Click", "Target": "{{Selectors.BioMetricAlert}}", "ContinueOnError": true, "CustomDelayBeforeStepExecustionInMilliseconds": 1500}, {"Name": "Click on ok popup", "Command": "Click", "Target": "{{Selectors.SorryPOPup}}", "ContinueOnError": true}, {"Name": "Click on Cards button from side menu", "Command": "Click", "Target": "{{Selectors.CardsButton}}"}, {"Name": "Click on Cards Services button from side menu", "Command": "Click", "Target": "{{Selectors.CardServices}}"}, {"Name": "Click on UnBlock Btn", "Command": "Click", "Target": "{{Selectors.UnBlockBtn}}"}, {"Name": "Click on Agree Check Box", "Command": "Click", "Target": "{{Selectors.AgreeCheckBox}}"}, {"Name": "Click on Continue", "Command": "Click", "Target": "{{Selectors.ContinueBtn}}"}, {"Name": "Assert Error Message: Please fill all required fields.", "Command": "assert", "Target": "{{Selectors.FillFieldMSG}}", "value": "Please fill all required fields."}]}