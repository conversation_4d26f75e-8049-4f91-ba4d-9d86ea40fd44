{"TestSuiteName": "CDs & TDs Suite", "TestSuiteItems": [{"Type": "TestCase", "Reference": "WorkSet01/TestCases/cdtd/TC-479 Request Submission.json", "ParamsReference": "WorkSet01/Params/CdTdParams.json"}, {"Type": "TestCase", "Reference": "WorkSet01/TestCases/cdtd/TC-480 Confirmation screen.json", "ParamsReference": "WorkSet01/Params/CdTdParams.json"}, {"Type": "TestCase", "Reference": "WorkSet01/TestCases/cdtd/TC-474 Maturity Instructions for TDs.json", "ParamsReference": "WorkSet01/Params/CdTdParams.json"}, {"Type": "TestCase", "Reference": "WorkSet01/TestCases/cdtd/TC-475 Maturity Instructions for TDs.json", "ParamsReference": "WorkSet01/Params/CdTdParams.json"}, {"Type": "TestCase", "Reference": "WorkSet01/TestCases/cdtd/TC-476 Maturity Instructions for TDs.json", "ParamsReference": "WorkSet01/Params/CdTdParams.json"}, {"Type": "TestCase", "Reference": "WorkSet01/TestCases/cdtd/TC-467 Display Product Details (Apply for TD).json", "ParamsReference": "WorkSet01/Params/CdTdParams.json"}, {"Type": "TestCase", "Reference": "WorkSet01/TestCases/cdtd/TC-470 Maturity Amount & Interest Amount Calculation for TDs.json", "ParamsReference": "WorkSet01/Params/CdTdParams.json"}, {"Type": "TestCase", "Reference": "WorkSet01/TestCases/cdtd/TC-468 Min Amount & Multiples Display.json", "ParamsReference": "WorkSet01/Params/CdTdParams.json"}, {"Type": "TestCase", "Reference": "WorkSet01/TestCases/cdtd/TC-499 Display Product Details View TDs-Type Floating.json", "ParamsReference": "WorkSet01/Params/CdTdParams.json"}, {"Type": "TestCase", "Reference": "WorkSet01/TestCases/cdtd/TC-498 Display Product Details View TDs-Type Fixed.json", "ParamsReference": "WorkSet01/Params/CdTdParams.json"}, {"Type": "TestCase", "Reference": "WorkSet01/TestCases/cdtd/TC-466 Product Selection.json", "ParamsReference": "WorkSet01/Params/CdTdParams.json"}, {"Type": "TestCase", "Reference": "WorkSet01/TestCases/cdtd/TC-461 Terms & Conditions Agreement.json", "ParamsReference": "WorkSet01/Params/CdTdParams.json"}, {"Type": "TestCase", "Reference": "WorkSet01/TestCases/cdtd/TC-493 Apply for deposit using invalid multiples amount.json", "ParamsReference": "WorkSet01/Params/CdTdParams.json"}]}