{"Selectors": {"SavedAccount": "id=Benf0", "EditBTN": "id=EditBtn", "savebtn": "id=SaveBtn", "ContinueBtn": "id=LocalBeneficiaryContinueDeleteBtn", "TokenInput": "id=TokenNUMBER", "btnTokenConfirm": "id=btnTokenConfirm", "BioMetricAlert": "css=#popbuttons > div > button.btn.Cancel.back_gradient2", "SaveBtn": "id=SavaBtn", "NickNameMSG": "xpath=/html/body/div[2]/div[2]/div/div/div[4]/div[1]/div/div[2]/div/div/div/div[2]/div[2]/div[1]/div[1]/div/span", "FullNameMSG": "css=#wginsIPNAddLocalBeneficiary_Default > div > div > div.section_body > div.add_beneficiary.back_white > div.Add_Account.Account > div:nth-child(2) > div > span", "BankAccountMSG": "css=#wginsIPNAddLocalBeneficiary_Default > div > div > div.section_body > div.add_beneficiary.back_white > div.Add_Account.Account > div.input_group.Account_input > div > span", "NickNameInput": "id=BenfNickName", "BeneficiaryFullNameInput": "id=BenFName", "BankNameSelect": "id=SelectBankName", "BankOption": "css=#wginsIPNAddLocalBeneficiary_Default > div > div > div.section_body > div.add_beneficiary.back_white > div.Add_Account.Account > div.input_group.Bank_name > div > div.options.back_white_solid.dropdown-active.d-block > div > div:nth-child(9) > span", "AccountNumberInput": "id=BenFAccountNum", "currencySelect": "id=SelectCurrency_Name", "editcurrencySelect": "id=EditSelectCurrency_Name", "currencyoptionEG": "css=#wginsIPNAddLocalBeneficiary_Default > div > div > div.section_body > div.add_beneficiary.back_white > div.Add_Account.Account > div.input_group.retail > div.select_warpper > div.options.back_white_solid.dropdown-active.d-block > div > div:nth-child(2) > div > span", "AddressInput": "id=BenefAddress", "CityInput": "id=BenfCity", "CountryInput": "id=SelectBeneficiaryCity", "countrtyoption": "css=#AddLocalBenefCountryForignCurrency > div > div.options.back_white_solid.dropdown-active.d-block > div > div:nth-child(7) > span", "AddressEdit": "id=BenfAddress", "CityEditEdit": "id=EditBenfCity", "CountryEdit": "id=ErrorSelectedCountry", "BeneficiaryHeader": "id=BeneficiaryHeader", "BeneficiariesToButton": "xpath=//div[@class='mainmenuitems']//label[contains(text(), 'Beneficiaries')]", "OtherCAEAccounts": "xpath=//div[@class='submenuitem']//label[contains(text(), 'Other CAE Accounts')]", "OtherCAECards": "xpath=//div[@class='submenuitem']//label[contains(text(), 'Other CAE Cards')]", "LocalTransfer": "xpath=//div[@class='submenuitem']//label[contains(text(), 'Local Transfer')]", "BanksInsideEgypt": "xpath=//div[@class='submenuitem']//label[contains(text(), 'Banks Inside Egypt')]", "BankAccountIBANOption": "css=#IPNSelectType > div > div:nth-child(1)", "MobileNumberOption": "css=#IPNSelectType > div > div:nth-child(2)", "PaymentAddressOption": "css=#IPNSelectType > div > div:nth-child(3)", "CardOption": "css=#IPNSelectType > div > div:nth-child(4)", "WalletOption": "css=#IPNSelectType > div > div:nth-child(5)", "IBANben": "id=Benf1", "Accountben": "id=Benf3", "EditBeneficiary": "id=EditBeneficiaryButton", "EditBeneficiaryOtherCAEcards": "id=EditBeneficiary", "BeneficiaryNameTextbox": "id=BeneficiaryName", "SaveButton": "id=Save", "SaveLocalBenButton": "id=SaveBtn", "ContinueToSaveBeneficiary": "id=DigitalBankBenfContinueDeleteBtn", "EditBeneficiaryBanksInsideEgypt": "id=EditBtn", "EditBenBankselect": "id=ErrorBankName", "BenNickNameTextbox": "id=EditBenfNickName", "BenFullNameTextbox": "id=EditBenFName", "BenAccNumberTextbox": "id=EditBenFAccountNum", "EditLocalNickName": "id=BenfNickName", "EditLocalFullName": "id=BenFName", "SelectLocalBankName": "id=SelectBankName", "SelectLocalBankNameOption": "css=#BanklistSelectedElement > div > div:nth-child(2)", "MobileNickname": "id=MobBenfNickName", "IBANnumberField": "id=BenFIBANNum", "CardNickname": "id=CardBenfNickName", "CardNumber": "id=BenfCardNum", "PaymentAddressNickname": "id=AddressBenfNickName", "PaymentAddress": "id=paymentAddress", "PaymentAddressTypeList": "css=#SelectBlockCardReason > div.select", "PaymentAddressType": "css=#SelectBlockCardReason > div.options.back_white_solid.dropdown-active.d-block > span:nth-child(3)", "CurrencyList": "css=#SelectCurrency_Name", "CurrencyChoice": "css=#wginsIPNEditLocalBeneficiary_Default > div > div > div.section_body > div > div.Add_Account.Account > div.input_group.retail > div.select_warpper > div.options.back_white_solid.dropdown-active.d-block > div > div:nth-child(3)", "SelectToAccount": "id=SelectToBTN", "AddNewBeneficiaryButton": "id=IPNAddLocalBtn", "AddNewBeneficiaryButtonOtheCAEaccounts": "id=AddDigitalBankBeneficiary", "BeneficiaryAccountNumberField": "id=BenFAccountNum", "BeneficiaryAccountNumber": "id=BeneficiaryAccountNumber", "popupCancel": "id=popup_cancel", "popupMessage": "id=popup_message", "popupOk": "id=popup_ok", "tokenerrormessage": "You need to activate a token to perform this request. Please go to the main menu and click banki Token.", "AddBTn": "id=IPNAddLocalBtn"}, "TestData": {"UserName": "mahmoudsayed022", "Password": "Password1", "ChallengeAnswer": "bmw", "NickName": "tmp user", "BeneficiaryFullName": "tmp full", "BankNameSelect": "Suez Canal Bank", "AccountNumber": "11111111111111111111111111111111111", "currencySelect": "US DOLLAR", "Account_number": "**********", "Nick_Name_For_Add": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Token_Number": "123456", "Beneficiary_FullName": "rmrldcrt", "Beneficiary_Nickname": "OtherCAE_account_2", "Beneficiary_Account_Number": "**************", "Beneficiary_Card_Nickname": "OtherCAE_card_2", "Cardnumber": "****************"}}