{"ModuleName": "Adding beneficiariary <PERSON>", "TestCases": [{"TestCasePath": "WorkSet01/TestCases/03.Beneficiaries/Add/TC-34 AddinglocalBen.json", "ParamsReference": "WorkSet01/Params/VerifictionParams.json"}, {"TestCasePath": "WorkSet01/TestCases/03.Beneficiaries/Add/TC-35 AddinglocalBenforeign.json", "ParamsReference": "WorkSet01/Params/VerifictionParams.json"}, {"TestCasePath": "WorkSet01/TestCases/03.Beneficiaries/Add/TC-255 AddingOtherCAEaccount.json", "ParamsReference": "WorkSet01/Params/BeneficiariesParams.json"}, {"TestCasePath": "WorkSet01/TestCases/03.Beneficiaries/Add/TC-256 AddingOtherCAEcard.json", "ParamsReference": "WorkSet01/Params/BeneficiariesParams.json"}]}