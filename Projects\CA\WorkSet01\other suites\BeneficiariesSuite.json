{"TestSuiteName": "Beneficiaries Suite", "TestSuiteItems": [{"Type": "<PERSON><PERSON><PERSON>", "Reference": "WorkSet01/Modules/03.Beneficiaries/AddingModule.json"}, {"Type": "<PERSON><PERSON><PERSON>", "Reference": "WorkSet01/Modules/03.Beneficiaries/VerifictionModule.json", "Params": {"SpecialEnvParams": {"UAT": {"TestData": {"UserName": "suite", "Password": "Password2", "ChallengeAnswer": "bmw"}}}}}, {"Type": "<PERSON><PERSON><PERSON>", "Reference": "WorkSet01/Modules/03.Beneficiaries/EditBeneficiariesModule.json"}, {"Type": "<PERSON><PERSON><PERSON>", "Reference": "WorkSet01/Modules/03.Beneficiaries/DeleteModule.json"}]}