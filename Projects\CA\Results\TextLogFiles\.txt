================================ Start of LogFile ================================
2025-08-19 13:22:19.544 [Info] Checking for work directory: ..\..\..\Projects\CA
2025-08-19 13:22:19.581 [Info] Using work directory: D:\TFS\DevWork\R and D Work\TestAutomationFramework\TestAutomationFramework\Projects\CA
2025-08-19 13:22:19.581 [Info] No Args for SuitePaths, scanning: WorkSet01\TestSuites, WorkSet02\TestSuites, WorkSet03\TestSuites, WorkSet04\TestSuites, MainWorkSet\TestSuites
2025-08-19 13:22:19.586 [Info] Retrieving all test cases.
2025-08-19 13:22:19.587 [Info] Retrieving test suite paths.
2025-08-19 13:22:19.591 [Info] Retrieved 0 test suite paths.
2025-08-19 13:22:19.592 [Info] Retrieved 5 test suite paths.
2025-08-19 13:22:19.594 [Info] Retrieved 89 test suite paths.
2025-08-19 13:22:19.595 [Info] Retrieved 144 test suite paths.
2025-08-19 13:22:19.596 [Info] Retrieved 157 test suite paths.
2025-08-19 13:22:19.599 [Info] Loading RunningInstructions configuration...
2025-08-19 13:22:19.612 [Info] Attempting to load configuration file: RunningInstructions.json
2025-08-19 13:22:19.613 [Info] Successfully loaded configuration file: RunningInstructions.json
2025-08-19 13:22:20.195 [Info] Attempting to load configuration file: RunningInstructions.local.json
2025-08-19 13:22:20.198 [Info] Successfully loaded configuration file: RunningInstructions.local.json
2025-08-19 13:22:20.198 [Info] Merging local RunningInstructions with main configuration.
2025-08-19 13:22:20.199 [Info] Successfully merged local RunningInstructions with main configuration.
2025-08-19 13:22:20.250 [Debug] Applying suite filtering with regexes: .*WorkSet01.*
2025-08-19 13:22:20.261 [Info] Loading test suite: WorkSet01\TestSuites\Test Condition Suite.json
2025-08-19 13:22:20.262 [Info] Attempting to load test suite: WorkSet01\TestSuites\Test Condition Suite.json
2025-08-19 13:22:20.277 [Info] Successfully loaded test suite: WorkSet01\TestSuites\Test Condition Suite.json
2025-08-19 13:22:20.277 [Debug] Loading test case: WorkSet01/TestCases/TC-461 Terms & Conditions Agreement.json
2025-08-19 13:22:20.278 [Info] Loading test case from: WorkSet01/TestCases/TC-461 Terms & Conditions Agreement.json
2025-08-19 13:22:20.278 [Info] Attempting to load configuration file: WorkSet01/TestCases/TC-461 Terms & Conditions Agreement.json
2025-08-19 13:22:20.278 [Info] Successfully loaded configuration file: WorkSet01/TestCases/TC-461 Terms & Conditions Agreement.json
2025-08-19 13:22:20.312 [Debug] Applying label filtering with regexes: ForTest
2025-08-19 13:22:20.321 [Debug] Test case 'Terms & Conditions Agreement TDs' Labels [ForTest] match at least one filtering pattern. Including in execution.
2025-08-19 13:22:20.321 [Info] Test case 'Terms & Conditions Agreement TDs' passed label filtering.
2025-08-19 13:22:20.321 [Info] Test case 'Terms & Conditions Agreement TDs' passed all configured filters.
2025-08-19 13:22:20.332 [Info] Attempting to load configuration file: GlobalParams.json
2025-08-19 13:22:20.334 [Info] Successfully loaded configuration file: GlobalParams.json
2025-08-19 13:22:20.354 [Debug] Resolving parameters for test case: Terms & Conditions Agreement TDs
2025-08-19 13:22:20.356 [Debug] Merging params
2025-08-19 13:22:20.356 [Debug] Merged basic params
2025-08-19 13:22:20.356 [Debug] Merged params
2025-08-19 13:22:20.356 [Debug] Loading parameters from reference file: WorkSet01/Params/CdTdParams.json
2025-08-19 13:22:20.356 [Info] Attempting to load configuration file: WorkSet01/Params/CdTdParams.json
2025-08-19 13:22:20.357 [Info] Successfully loaded configuration file: WorkSet01/Params/CdTdParams.json
2025-08-19 13:22:20.357 [Debug] Merging params
2025-08-19 13:22:20.358 [Debug] Merged basic params
2025-08-19 13:22:20.358 [Debug] Merged params
2025-08-19 13:22:20.358 [Debug] Merging params
2025-08-19 13:22:20.358 [Debug] Merging params
2025-08-19 13:22:20.358 [Debug] Resolving placeholders in test steps for test case: Terms & Conditions Agreement TDs
2025-08-19 13:22:20.360 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-19 13:22:20.361 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-19 13:22:20.361 [Debug] Resolving placeholders in string: {{Selectors.FinishButton}}
2025-08-19 13:22:20.361 [Debug] Resolved string: {{Selectors.FinishButton}}
2025-08-19 13:22:20.363 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-19 13:22:20.363 [Debug] Resolved string: Click on Login Button
2025-08-19 13:22:20.363 [Debug] Resolving placeholders in string: {{Selectors.LoginButton}}
2025-08-19 13:22:20.363 [Debug] Resolved string: {{Selectors.LoginButton}}
2025-08-19 13:22:20.363 [Info] Successfully resolved parameters for test case: Terms & Conditions Agreement TDs
2025-08-19 13:22:20.365 [Info] Attempting to load configuration file: ExcludedTestData.json
2025-08-19 13:22:20.366 [Info] Successfully loaded configuration file: ExcludedTestData.json
2025-08-19 13:22:20.368 [Info] Loading test suite: WorkSet01\TestSuites\WS01 Sanity Pilot Suite\WS01 Sanity ATMDispute Suite.json
2025-08-19 13:22:20.368 [Info] Attempting to load test suite: WorkSet01\TestSuites\WS01 Sanity Pilot Suite\WS01 Sanity ATMDispute Suite.json
2025-08-19 13:22:20.369 [Info] Successfully loaded test suite: WorkSet01\TestSuites\WS01 Sanity Pilot Suite\WS01 Sanity ATMDispute Suite.json
2025-08-19 13:22:20.370 [Debug] Loading test case: WorkSet01/TestCases/04.ATMdispute/Request Status/TC-430 Ensure that all request displayed Dispute Request.json
2025-08-19 13:22:20.370 [Info] Loading test case from: WorkSet01/TestCases/04.ATMdispute/Request Status/TC-430 Ensure that all request displayed Dispute Request.json
2025-08-19 13:22:20.370 [Info] Attempting to load configuration file: WorkSet01/TestCases/04.ATMdispute/Request Status/TC-430 Ensure that all request displayed Dispute Request.json
2025-08-19 13:22:20.370 [Info] Successfully loaded configuration file: WorkSet01/TestCases/04.ATMdispute/Request Status/TC-430 Ensure that all request displayed Dispute Request.json
2025-08-19 13:22:20.379 [Debug] Test case ' Ensure that all request displayed -Dispute Request-' has no labels.
2025-08-19 13:22:20.379 [Info] Test case ' Ensure that all request displayed -Dispute Request-' excluded by label filtering.
2025-08-19 13:22:20.379 [Info] Test case ' Ensure that all request displayed -Dispute Request-' is excluded based on filtering criteria.
2025-08-19 13:22:20.379 [Debug] Loading test case: WorkSet01/TestCases/04.ATMdispute/128-ATM Dispute, click View Details.json
2025-08-19 13:22:20.379 [Info] Loading test case from: WorkSet01/TestCases/04.ATMdispute/128-ATM Dispute, click View Details.json
2025-08-19 13:22:20.379 [Info] Attempting to load configuration file: WorkSet01/TestCases/04.ATMdispute/128-ATM Dispute, click View Details.json
2025-08-19 13:22:20.380 [Info] Successfully loaded configuration file: WorkSet01/TestCases/04.ATMdispute/128-ATM Dispute, click View Details.json
2025-08-19 13:22:20.381 [Debug] Test case 'ATM Dispute, click View Details' has no labels.
2025-08-19 13:22:20.381 [Info] Test case 'ATM Dispute, click View Details' excluded by label filtering.
2025-08-19 13:22:20.381 [Info] Test case 'ATM Dispute, click View Details' is excluded based on filtering criteria.
2025-08-19 13:22:20.381 [Debug] Loading test case: WorkSet01/TestCases/04.ATMdispute/43-CompleteRequest.json
2025-08-19 13:22:20.381 [Info] Loading test case from: WorkSet01/TestCases/04.ATMdispute/43-CompleteRequest.json
2025-08-19 13:22:20.381 [Info] Attempting to load configuration file: WorkSet01/TestCases/04.ATMdispute/43-CompleteRequest.json
2025-08-19 13:22:20.382 [Info] Successfully loaded configuration file: WorkSet01/TestCases/04.ATMdispute/43-CompleteRequest.json
2025-08-19 13:22:20.382 [Debug] Test case 'Complete ATM dispute request' has no labels.
2025-08-19 13:22:20.382 [Info] Test case 'Complete ATM dispute request' excluded by label filtering.
2025-08-19 13:22:20.383 [Info] Test case 'Complete ATM dispute request' is excluded based on filtering criteria.
2025-08-19 13:22:20.383 [Info] Attempting to load configuration file: ExcludedTestData.json
2025-08-19 13:22:20.383 [Info] Successfully loaded configuration file: ExcludedTestData.json
2025-08-19 13:22:20.383 [Info] Loading test suite: WorkSet01\TestSuites\WS01 Sanity Pilot Suite\WS01 Sanity Happy Points Suite.json
2025-08-19 13:22:20.383 [Info] Attempting to load test suite: WorkSet01\TestSuites\WS01 Sanity Pilot Suite\WS01 Sanity Happy Points Suite.json
2025-08-19 13:22:20.384 [Info] Successfully loaded test suite: WorkSet01\TestSuites\WS01 Sanity Pilot Suite\WS01 Sanity Happy Points Suite.json
2025-08-19 13:22:20.384 [Debug] Loading test case: WorkSet01/TestCases/021.HappyPoints/29.json
2025-08-19 13:22:20.384 [Info] Loading test case from: WorkSet01/TestCases/021.HappyPoints/29.json
2025-08-19 13:22:20.384 [Info] Attempting to load configuration file: WorkSet01/TestCases/021.HappyPoints/29.json
2025-08-19 13:22:20.385 [Info] Successfully loaded configuration file: WorkSet01/TestCases/021.HappyPoints/29.json
2025-08-19 13:22:20.386 [Debug] Test case 'Happy Points valid redemption' has no labels.
2025-08-19 13:22:20.386 [Info] Test case 'Happy Points valid redemption' excluded by label filtering.
2025-08-19 13:22:20.386 [Info] Test case 'Happy Points valid redemption' is excluded based on filtering criteria.
2025-08-19 13:22:20.386 [Debug] Loading test case: WorkSet01/TestCases/021.HappyPoints/31.json
2025-08-19 13:22:20.386 [Info] Loading test case from: WorkSet01/TestCases/021.HappyPoints/31.json
2025-08-19 13:22:20.387 [Info] Attempting to load configuration file: WorkSet01/TestCases/021.HappyPoints/31.json
2025-08-19 13:22:20.387 [Info] Successfully loaded configuration file: WorkSet01/TestCases/021.HappyPoints/31.json
2025-08-19 13:22:20.387 [Debug] Test case 'Redeem less than 10000 points' has no labels.
2025-08-19 13:22:20.387 [Info] Test case 'Redeem less than 10000 points' excluded by label filtering.
2025-08-19 13:22:20.387 [Info] Test case 'Redeem less than 10000 points' is excluded based on filtering criteria.
2025-08-19 13:22:20.388 [Debug] Loading test case: WorkSet01/TestCases/021.HappyPoints/32.json
2025-08-19 13:22:20.388 [Info] Loading test case from: WorkSet01/TestCases/021.HappyPoints/32.json
2025-08-19 13:22:20.388 [Info] Attempting to load configuration file: WorkSet01/TestCases/021.HappyPoints/32.json
2025-08-19 13:22:20.388 [Info] Successfully loaded configuration file: WorkSet01/TestCases/021.HappyPoints/32.json
2025-08-19 13:22:20.389 [Debug] Test case 'Verify That User Can't Redeem Happy Points Using Wrong SMS' has no labels.
2025-08-19 13:22:20.389 [Info] Test case 'Verify That User Can't Redeem Happy Points Using Wrong SMS' excluded by label filtering.
2025-08-19 13:22:20.389 [Info] Test case 'Verify That User Can't Redeem Happy Points Using Wrong SMS' is excluded based on filtering criteria.
2025-08-19 13:22:20.389 [Debug] Loading test case: WorkSet01/TestCases/021.HappyPoints/157-MorePointsThanAvailable.json
2025-08-19 13:22:20.390 [Info] Loading test case from: WorkSet01/TestCases/021.HappyPoints/157-MorePointsThanAvailable.json
2025-08-19 13:22:20.390 [Info] Attempting to load configuration file: WorkSet01/TestCases/021.HappyPoints/157-MorePointsThanAvailable.json
2025-08-19 13:22:20.391 [Info] Successfully loaded configuration file: WorkSet01/TestCases/021.HappyPoints/157-MorePointsThanAvailable.json
2025-08-19 13:22:20.391 [Debug] Test case 'Redeem Online - input points more than the available' has no labels.
2025-08-19 13:22:20.392 [Info] Test case 'Redeem Online - input points more than the available' excluded by label filtering.
2025-08-19 13:22:20.392 [Info] Test case 'Redeem Online - input points more than the available' is excluded based on filtering criteria.
2025-08-19 13:22:20.392 [Info] Attempting to load configuration file: ExcludedTestData.json
2025-08-19 13:22:20.393 [Info] Successfully loaded configuration file: ExcludedTestData.json
2025-08-19 13:22:20.393 [Info] Loading test suite: WorkSet01\TestSuites\WS01 Sanity UAT Suite\WS01 Sanity Beneficiaries Suite.json
2025-08-19 13:22:20.393 [Info] Attempting to load test suite: WorkSet01\TestSuites\WS01 Sanity UAT Suite\WS01 Sanity Beneficiaries Suite.json
2025-08-19 13:22:20.394 [Info] Successfully loaded test suite: WorkSet01\TestSuites\WS01 Sanity UAT Suite\WS01 Sanity Beneficiaries Suite.json
2025-08-19 13:22:20.394 [Debug] Loading test case: WorkSet01/TestCases/03.Beneficiaries/Add/TC-255 AddingOtherCAEaccount.json
2025-08-19 13:22:20.394 [Info] Loading test case from: WorkSet01/TestCases/03.Beneficiaries/Add/TC-255 AddingOtherCAEaccount.json
2025-08-19 13:22:20.394 [Info] Attempting to load configuration file: WorkSet01/TestCases/03.Beneficiaries/Add/TC-255 AddingOtherCAEaccount.json
2025-08-19 13:22:20.395 [Info] Successfully loaded configuration file: WorkSet01/TestCases/03.Beneficiaries/Add/TC-255 AddingOtherCAEaccount.json
2025-08-19 13:22:20.395 [Debug] Test case 'Add new beneficiary account' has no labels.
2025-08-19 13:22:20.395 [Info] Test case 'Add new beneficiary account' excluded by label filtering.
2025-08-19 13:22:20.395 [Info] Test case 'Add new beneficiary account' is excluded based on filtering criteria.
2025-08-19 13:22:20.395 [Debug] Loading test case: WorkSet01/TestCases/03.Beneficiaries/Add/TC-256 AddingOtherCAEcard.json
2025-08-19 13:22:20.396 [Info] Loading test case from: WorkSet01/TestCases/03.Beneficiaries/Add/TC-256 AddingOtherCAEcard.json
2025-08-19 13:22:20.397 [Info] Attempting to load configuration file: WorkSet01/TestCases/03.Beneficiaries/Add/TC-256 AddingOtherCAEcard.json
2025-08-19 13:22:20.397 [Info] Successfully loaded configuration file: WorkSet01/TestCases/03.Beneficiaries/Add/TC-256 AddingOtherCAEcard.json
2025-08-19 13:22:20.398 [Debug] Test case 'Add new beneficiary card' has no labels.
2025-08-19 13:22:20.398 [Info] Test case 'Add new beneficiary card' excluded by label filtering.
2025-08-19 13:22:20.398 [Info] Test case 'Add new beneficiary card' is excluded based on filtering criteria.
2025-08-19 13:22:20.398 [Debug] Loading test case: WorkSet01/TestCases/03.Beneficiaries/Add/TC-34 AddinglocalBen.json
2025-08-19 13:22:20.398 [Info] Loading test case from: WorkSet01/TestCases/03.Beneficiaries/Add/TC-34 AddinglocalBen.json
2025-08-19 13:22:20.398 [Info] Attempting to load configuration file: WorkSet01/TestCases/03.Beneficiaries/Add/TC-34 AddinglocalBen.json
2025-08-19 13:22:20.399 [Info] Successfully loaded configuration file: WorkSet01/TestCases/03.Beneficiaries/Add/TC-34 AddinglocalBen.json
2025-08-19 13:22:20.400 [Debug] Test case 'Add local beneficiary with valid data ' has no labels.
2025-08-19 13:22:20.400 [Info] Test case 'Add local beneficiary with valid data ' excluded by label filtering.
2025-08-19 13:22:20.400 [Info] Test case 'Add local beneficiary with valid data ' is excluded based on filtering criteria.
2025-08-19 13:22:20.401 [Debug] Loading test case: WorkSet01/TestCases/03.Beneficiaries/Verify/251-Existing Account beneficiaries displayed.json
2025-08-19 13:22:20.401 [Info] Loading test case from: WorkSet01/TestCases/03.Beneficiaries/Verify/251-Existing Account beneficiaries displayed.json
2025-08-19 13:22:20.401 [Info] Attempting to load configuration file: WorkSet01/TestCases/03.Beneficiaries/Verify/251-Existing Account beneficiaries displayed.json
2025-08-19 13:22:20.402 [Info] Successfully loaded configuration file: WorkSet01/TestCases/03.Beneficiaries/Verify/251-Existing Account beneficiaries displayed.json
2025-08-19 13:22:20.402 [Debug] Test case 'Existing Account beneficiaries displayed' has no labels.
2025-08-19 13:22:20.402 [Info] Test case 'Existing Account beneficiaries displayed' excluded by label filtering.
2025-08-19 13:22:20.402 [Info] Test case 'Existing Account beneficiaries displayed' is excluded based on filtering criteria.
2025-08-19 13:22:20.403 [Debug] Loading test case: WorkSet01/TestCases/03.Beneficiaries/Verify/252-Existing Card beneficiaries displayed.json
2025-08-19 13:22:20.403 [Info] Loading test case from: WorkSet01/TestCases/03.Beneficiaries/Verify/252-Existing Card beneficiaries displayed.json
2025-08-19 13:22:20.403 [Info] Attempting to load configuration file: WorkSet01/TestCases/03.Beneficiaries/Verify/252-Existing Card beneficiaries displayed.json
2025-08-19 13:22:20.404 [Info] Successfully loaded configuration file: WorkSet01/TestCases/03.Beneficiaries/Verify/252-Existing Card beneficiaries displayed.json
2025-08-19 13:22:20.404 [Debug] Test case 'Existing Card beneficiaries displayed' has no labels.
2025-08-19 13:22:20.404 [Info] Test case 'Existing Card beneficiaries displayed' excluded by label filtering.
2025-08-19 13:22:20.404 [Info] Test case 'Existing Card beneficiaries displayed' is excluded based on filtering criteria.
2025-08-19 13:22:20.405 [Debug] Loading test case: WorkSet01/TestCases/03.Beneficiaries/Verify/TC-145 Existing bankinsideegypt beneficiaries displayed.json
2025-08-19 13:22:20.405 [Info] Loading test case from: WorkSet01/TestCases/03.Beneficiaries/Verify/TC-145 Existing bankinsideegypt beneficiaries displayed.json
2025-08-19 13:22:20.405 [Info] Attempting to load configuration file: WorkSet01/TestCases/03.Beneficiaries/Verify/TC-145 Existing bankinsideegypt beneficiaries displayed.json
2025-08-19 13:22:20.406 [Info] Successfully loaded configuration file: WorkSet01/TestCases/03.Beneficiaries/Verify/TC-145 Existing bankinsideegypt beneficiaries displayed.json
2025-08-19 13:22:20.406 [Debug] Test case 'Existing Banks Inside Egypt beneficiaries displayed' has no labels.
2025-08-19 13:22:20.407 [Info] Test case 'Existing Banks Inside Egypt beneficiaries displayed' excluded by label filtering.
2025-08-19 13:22:20.407 [Info] Test case 'Existing Banks Inside Egypt beneficiaries displayed' is excluded based on filtering criteria.
2025-08-19 13:22:20.407 [Debug] Loading test case: WorkSet02/TestCases/028.Beneficiary/TC-249 Edit an Existing beneficiary Account.json
2025-08-19 13:22:20.407 [Info] Loading test case from: WorkSet02/TestCases/028.Beneficiary/TC-249 Edit an Existing beneficiary Account.json
2025-08-19 13:22:20.407 [Info] Attempting to load configuration file: WorkSet02/TestCases/028.Beneficiary/TC-249 Edit an Existing beneficiary Account.json
2025-08-19 13:22:20.409 [Info] Successfully loaded configuration file: WorkSet02/TestCases/028.Beneficiary/TC-249 Edit an Existing beneficiary Account.json
2025-08-19 13:22:20.411 [Debug] Test case 'Edit an Existing beneficiary Account' has no labels.
2025-08-19 13:22:20.411 [Info] Test case 'Edit an Existing beneficiary Account' excluded by label filtering.
2025-08-19 13:22:20.412 [Info] Test case 'Edit an Existing beneficiary Account' is excluded based on filtering criteria.
2025-08-19 13:22:20.412 [Debug] Loading test case: WorkSet02/TestCases/028.Beneficiary/TC-146  Edit Existing beneficiary.json
2025-08-19 13:22:20.412 [Info] Loading test case from: WorkSet02/TestCases/028.Beneficiary/TC-146  Edit Existing beneficiary.json
2025-08-19 13:22:20.412 [Info] Attempting to load configuration file: WorkSet02/TestCases/028.Beneficiary/TC-146  Edit Existing beneficiary.json
2025-08-19 13:22:20.413 [Info] Successfully loaded configuration file: WorkSet02/TestCases/028.Beneficiary/TC-146  Edit Existing beneficiary.json
2025-08-19 13:22:20.413 [Debug] Test case 'Edit Existing beneficiary' has no labels.
2025-08-19 13:22:20.414 [Info] Test case 'Edit Existing beneficiary' excluded by label filtering.
2025-08-19 13:22:20.414 [Info] Test case 'Edit Existing beneficiary' is excluded based on filtering criteria.
2025-08-19 13:22:20.414 [Debug] Loading test case: WorkSet01/TestCases/03.Beneficiaries/Delete/147-Delete Existing beneficiary.json
2025-08-19 13:22:20.414 [Info] Loading test case from: WorkSet01/TestCases/03.Beneficiaries/Delete/147-Delete Existing beneficiary.json
2025-08-19 13:22:20.414 [Info] Attempting to load configuration file: WorkSet01/TestCases/03.Beneficiaries/Delete/147-Delete Existing beneficiary.json
2025-08-19 13:22:20.415 [Info] Successfully loaded configuration file: WorkSet01/TestCases/03.Beneficiaries/Delete/147-Delete Existing beneficiary.json
2025-08-19 13:22:20.416 [Debug] Test case 'Delete Existing beneficiary' has no labels.
2025-08-19 13:22:20.416 [Info] Test case 'Delete Existing beneficiary' excluded by label filtering.
2025-08-19 13:22:20.416 [Info] Test case 'Delete Existing beneficiary' is excluded based on filtering criteria.
2025-08-19 13:22:20.416 [Debug] Loading test case: WorkSet01/TestCases/03.Beneficiaries/Delete/TC-254 Delete an Existing Card beneficiary.json
2025-08-19 13:22:20.416 [Info] Loading test case from: WorkSet01/TestCases/03.Beneficiaries/Delete/TC-254 Delete an Existing Card beneficiary.json
2025-08-19 13:22:20.416 [Info] Attempting to load configuration file: WorkSet01/TestCases/03.Beneficiaries/Delete/TC-254 Delete an Existing Card beneficiary.json
2025-08-19 13:22:20.417 [Info] Successfully loaded configuration file: WorkSet01/TestCases/03.Beneficiaries/Delete/TC-254 Delete an Existing Card beneficiary.json
2025-08-19 13:22:20.428 [Debug] Test case 'Delete an Existing Card beneficiary' has no labels.
2025-08-19 13:22:20.429 [Info] Test case 'Delete an Existing Card beneficiary' excluded by label filtering.
2025-08-19 13:22:20.429 [Info] Test case 'Delete an Existing Card beneficiary' is excluded based on filtering criteria.
2025-08-19 13:22:20.429 [Debug] Loading test case: WorkSet01/TestCases/03.Beneficiaries/Delete/TC-253 Delete an Existing Account beneficiary.json
2025-08-19 13:22:20.429 [Info] Loading test case from: WorkSet01/TestCases/03.Beneficiaries/Delete/TC-253 Delete an Existing Account beneficiary.json
2025-08-19 13:22:20.429 [Info] Attempting to load configuration file: WorkSet01/TestCases/03.Beneficiaries/Delete/TC-253 Delete an Existing Account beneficiary.json
2025-08-19 13:22:20.429 [Info] Successfully loaded configuration file: WorkSet01/TestCases/03.Beneficiaries/Delete/TC-253 Delete an Existing Account beneficiary.json
2025-08-19 13:22:20.430 [Debug] Test case 'Delete an Existing Account beneficiary' has no labels.
2025-08-19 13:22:20.430 [Info] Test case 'Delete an Existing Account beneficiary' excluded by label filtering.
2025-08-19 13:22:20.430 [Info] Test case 'Delete an Existing Account beneficiary' is excluded based on filtering criteria.
2025-08-19 13:22:20.430 [Info] Attempting to load configuration file: ExcludedTestData.json
2025-08-19 13:22:20.431 [Info] Successfully loaded configuration file: ExcludedTestData.json
2025-08-19 13:22:20.431 [Info] Loading test suite: WorkSet01\TestSuites\WS01 Sanity UAT Suite\WS01 Sanity Card Services Suite.json
2025-08-19 13:22:20.431 [Info] Attempting to load test suite: WorkSet01\TestSuites\WS01 Sanity UAT Suite\WS01 Sanity Card Services Suite.json
2025-08-19 13:22:20.432 [Info] Successfully loaded test suite: WorkSet01\TestSuites\WS01 Sanity UAT Suite\WS01 Sanity Card Services Suite.json
2025-08-19 13:22:20.432 [Debug] Loading test case: WorkSet01/TestCases/012.CardServices/DirectDebit/130-DirectDebitRequest.json
2025-08-19 13:22:20.432 [Info] Loading test case from: WorkSet01/TestCases/012.CardServices/DirectDebit/130-DirectDebitRequest.json
2025-08-19 13:22:20.432 [Info] Attempting to load configuration file: WorkSet01/TestCases/012.CardServices/DirectDebit/130-DirectDebitRequest.json
2025-08-19 13:22:20.433 [Info] Successfully loaded configuration file: WorkSet01/TestCases/012.CardServices/DirectDebit/130-DirectDebitRequest.json
2025-08-19 13:22:20.438 [Debug] Test case 'Direct Debit, standard users' has no labels.
2025-08-19 13:22:20.438 [Info] Test case 'Direct Debit, standard users' excluded by label filtering.
2025-08-19 13:22:20.438 [Info] Test case 'Direct Debit, standard users' is excluded based on filtering criteria.
2025-08-19 13:22:20.438 [Debug] Loading test case: WorkSet01/TestCases/012.CardServices/ActivateCard/121-Activate Card, check that inactive card in backend are displayed.json
2025-08-19 13:22:20.438 [Info] Loading test case from: WorkSet01/TestCases/012.CardServices/ActivateCard/121-Activate Card, check that inactive card in backend are displayed.json
2025-08-19 13:22:20.438 [Info] Attempting to load configuration file: WorkSet01/TestCases/012.CardServices/ActivateCard/121-Activate Card, check that inactive card in backend are displayed.json
2025-08-19 13:22:20.439 [Info] Successfully loaded configuration file: WorkSet01/TestCases/012.CardServices/ActivateCard/121-Activate Card, check that inactive card in backend are displayed.json
2025-08-19 13:22:20.439 [Debug] Test case 'Activate Card, check that inactive card in backend are displayed' has no labels.
2025-08-19 13:22:20.439 [Info] Test case 'Activate Card, check that inactive card in backend are displayed' excluded by label filtering.
2025-08-19 13:22:20.439 [Info] Test case 'Activate Card, check that inactive card in backend are displayed' is excluded based on filtering criteria.
2025-08-19 13:22:20.439 [Debug] Loading test case: WorkSet01/TestCases/012.CardServices/BlockCard/120-ClickBlockCardInquiry.json
2025-08-19 13:22:20.439 [Info] Loading test case from: WorkSet01/TestCases/012.CardServices/BlockCard/120-ClickBlockCardInquiry.json
2025-08-19 13:22:20.439 [Info] Attempting to load configuration file: WorkSet01/TestCases/012.CardServices/BlockCard/120-ClickBlockCardInquiry.json
2025-08-19 13:22:20.440 [Info] Successfully loaded configuration file: WorkSet01/TestCases/012.CardServices/BlockCard/120-ClickBlockCardInquiry.json
2025-08-19 13:22:20.440 [Debug] Test case 'Click Block card' has no labels.
2025-08-19 13:22:20.440 [Info] Test case 'Click Block card' excluded by label filtering.
2025-08-19 13:22:20.440 [Info] Test case 'Click Block card' is excluded based on filtering criteria.
2025-08-19 13:22:20.440 [Debug] Loading test case: WorkSet01/TestCases/012.CardServices/BlockCard/125-CheckBlockedCardsList.json
2025-08-19 13:22:20.440 [Info] Loading test case from: WorkSet01/TestCases/012.CardServices/BlockCard/125-CheckBlockedCardsList.json
2025-08-19 13:22:20.440 [Info] Attempting to load configuration file: WorkSet01/TestCases/012.CardServices/BlockCard/125-CheckBlockedCardsList.json
2025-08-19 13:22:20.441 [Info] Successfully loaded configuration file: WorkSet01/TestCases/012.CardServices/BlockCard/125-CheckBlockedCardsList.json
2025-08-19 13:22:20.442 [Debug] Test case 'Check Blocked Cards List' has no labels.
2025-08-19 13:22:20.442 [Info] Test case 'Check Blocked Cards List' excluded by label filtering.
2025-08-19 13:22:20.442 [Info] Test case 'Check Blocked Cards List' is excluded based on filtering criteria.
2025-08-19 13:22:20.442 [Debug] Loading test case: WorkSet01/TestCases/012.CardServices/BlockCard/114-CardInquiry2Widgets.json
2025-08-19 13:22:20.442 [Info] Loading test case from: WorkSet01/TestCases/012.CardServices/BlockCard/114-CardInquiry2Widgets.json
2025-08-19 13:22:20.442 [Info] Attempting to load configuration file: WorkSet01/TestCases/012.CardServices/BlockCard/114-CardInquiry2Widgets.json
2025-08-19 13:22:20.443 [Info] Successfully loaded configuration file: WorkSet01/TestCases/012.CardServices/BlockCard/114-CardInquiry2Widgets.json
2025-08-19 13:22:20.443 [Debug] Test case 'Credit cards and prepaid cards displayed on 2 widgets' has no labels.
2025-08-19 13:22:20.443 [Info] Test case 'Credit cards and prepaid cards displayed on 2 widgets' excluded by label filtering.
2025-08-19 13:22:20.443 [Info] Test case 'Credit cards and prepaid cards displayed on 2 widgets' is excluded based on filtering criteria.
2025-08-19 13:22:20.443 [Debug] Loading test case: WorkSet01/TestCases/012.CardServices/BlockCard/113-CardInquiry.json
2025-08-19 13:22:20.443 [Info] Loading test case from: WorkSet01/TestCases/012.CardServices/BlockCard/113-CardInquiry.json
2025-08-19 13:22:20.443 [Info] Attempting to load configuration file: WorkSet01/TestCases/012.CardServices/BlockCard/113-CardInquiry.json
2025-08-19 13:22:20.444 [Info] Successfully loaded configuration file: WorkSet01/TestCases/012.CardServices/BlockCard/113-CardInquiry.json
2025-08-19 13:22:20.444 [Debug] Test case 'All users Cards are displayed successfully' has no labels.
2025-08-19 13:22:20.444 [Info] Test case 'All users Cards are displayed successfully' excluded by label filtering.
2025-08-19 13:22:20.445 [Info] Test case 'All users Cards are displayed successfully' is excluded based on filtering criteria.
2025-08-19 13:22:20.445 [Debug] Loading test case: WorkSet01/TestCases/012.CardServices/ActivateCard/57-fullCardActivation.json
2025-08-19 13:22:20.445 [Info] Loading test case from: WorkSet01/TestCases/012.CardServices/ActivateCard/57-fullCardActivation.json
2025-08-19 13:22:20.445 [Info] Attempting to load configuration file: WorkSet01/TestCases/012.CardServices/ActivateCard/57-fullCardActivation.json
2025-08-19 13:22:20.446 [Info] Successfully loaded configuration file: WorkSet01/TestCases/012.CardServices/ActivateCard/57-fullCardActivation.json
2025-08-19 13:22:20.446 [Debug] Test case 'Verify that a user can successfully activate a card' has no labels.
2025-08-19 13:22:20.446 [Info] Test case 'Verify that a user can successfully activate a card' excluded by label filtering.
2025-08-19 13:22:20.446 [Info] Test case 'Verify that a user can successfully activate a card' is excluded based on filtering criteria.
2025-08-19 13:22:20.446 [Debug] Loading test case: WorkSet01/TestCases/012.CardServices/BlockCard/52-UnBlockCard.json
2025-08-19 13:22:20.446 [Info] Loading test case from: WorkSet01/TestCases/012.CardServices/BlockCard/52-UnBlockCard.json
2025-08-19 13:22:20.446 [Info] Attempting to load configuration file: WorkSet01/TestCases/012.CardServices/BlockCard/52-UnBlockCard.json
2025-08-19 13:22:20.447 [Info] Successfully loaded configuration file: WorkSet01/TestCases/012.CardServices/BlockCard/52-UnBlockCard.json
2025-08-19 13:22:20.447 [Debug] Test case 'Verify that a user can successfully unblock a card' has no labels.
2025-08-19 13:22:20.447 [Info] Test case 'Verify that a user can successfully unblock a card' excluded by label filtering.
2025-08-19 13:22:20.447 [Info] Test case 'Verify that a user can successfully unblock a card' is excluded based on filtering criteria.
2025-08-19 13:22:20.448 [Debug] Loading test case: WorkSet01/TestCases/012.CardServices/BlockCard/48-CheckCardsList.json
2025-08-19 13:22:20.448 [Info] Loading test case from: WorkSet01/TestCases/012.CardServices/BlockCard/48-CheckCardsList.json
2025-08-19 13:22:20.448 [Info] Attempting to load configuration file: WorkSet01/TestCases/012.CardServices/BlockCard/48-CheckCardsList.json
2025-08-19 13:22:20.448 [Info] Successfully loaded configuration file: WorkSet01/TestCases/012.CardServices/BlockCard/48-CheckCardsList.json
2025-08-19 13:22:20.449 [Debug] Test case 'Check Cards List In Block Card Service' has no labels.
2025-08-19 13:22:20.449 [Info] Test case 'Check Cards List In Block Card Service' excluded by label filtering.
2025-08-19 13:22:20.449 [Info] Test case 'Check Cards List In Block Card Service' is excluded based on filtering criteria.
2025-08-19 13:22:20.449 [Debug] Loading test case: WorkSet01/TestCases/012.CardServices/BlockCard/47-BlockCard.json
2025-08-19 13:22:20.449 [Info] Loading test case from: WorkSet01/TestCases/012.CardServices/BlockCard/47-BlockCard.json
2025-08-19 13:22:20.449 [Info] Attempting to load configuration file: WorkSet01/TestCases/012.CardServices/BlockCard/47-BlockCard.json
2025-08-19 13:22:20.449 [Info] Successfully loaded configuration file: WorkSet01/TestCases/012.CardServices/BlockCard/47-BlockCard.json
2025-08-19 13:22:20.450 [Debug] Test case 'Block card request' has no labels.
2025-08-19 13:22:20.450 [Info] Test case 'Block card request' excluded by label filtering.
2025-08-19 13:22:20.450 [Info] Test case 'Block card request' is excluded based on filtering criteria.
2025-08-19 13:22:20.450 [Info] Attempting to load configuration file: ExcludedTestData.json
2025-08-19 13:22:20.450 [Info] Successfully loaded configuration file: ExcludedTestData.json
2025-08-19 13:22:20.450 [Info] Retrieved 1 test cases.
2025-08-19 13:22:20.451 [Info] Total test cases found: 1
2025-08-19 13:22:20.963 [Info] Retry count from RunningInstructions: 1
2025-08-19 13:22:21.417 [Info] Starting test case execution: Terms & Conditions Agreement TDs
2025-08-19 13:22:21.777 [Info] Executing test case: Terms & Conditions Agreement TDs
2025-08-19 13:22:21.779 [Info] Using browser: System.String[] and BaseUrl: https://banki-uat61.ebseg.com/OmniChannel/CEEPDigitalBankR6/eBankApplication/ePortal5Core/ePortal5.htm?Menu=New#/EN/Landing
2025-08-19 13:22:21.819 [Info] Initializing WebDriver for browser: Chrome
2025-08-19 13:22:21.822 [Debug] Setting up Chrome WebDriver.
2025-08-19 13:22:46.092 [Error] Error executing test case: Terms & Conditions Agreement TDs. Details: One or more errors occurred. (The SSL connection could not be established, see inner exception.)
2025-08-19 13:22:46.131 [Info] WebDriver instance quit.
2025-08-19 13:22:46.131 [Error] Error during test case execution: Terms & Conditions Agreement TDs. Details: One or more errors occurred. (The SSL connection could not be established, see inner exception.)
2025-08-19 13:22:58.208 [Info] OneTimeTearDown started