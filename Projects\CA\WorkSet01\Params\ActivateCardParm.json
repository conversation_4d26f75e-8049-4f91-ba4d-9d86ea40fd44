{"Selectors": {"BioMetricAlert": "css=#popbuttons > div > button.btn.Cancel.back_gradient2", "CardsButton": "xpath=//div[@class='mainmenuitems']//label[contains(text(), 'Cards')]", "CardServices": "xpath=//div[@class='submenuitem']//label[contains(text(), 'Card services')]", "ActivateCard": "id=ActivateCard", "DeActivatedCard": "css=#CardsSelectionErrorHandle > div:nth-child(2) > div > div.balance_cardList > h4", "CardList": "css=#CardsSelectionErrorHandle", "ContinueBtn": "id=btncontinue", "OTPfield": "id=ACsmsCode", "NewPINfield": "id=ACpinNumber", "ConfirmPINfield": "id=ACconfirmPin", "ContinueBtn2": "id=ACcontinueBtn", "popupCancel": "id=popup_cancel", "popupMessage": "id=popup_message", "popupOk": "id=popup_ok"}, "TestData": {"UserName": "mahmoudsayed022", "Password": "Password1", "ChallengeAnswer": "bmw", "NickName": "tmp user", "BeneficiaryFullName": "tmp full", "BankNameSelect": "Suez Canal Bank", "AccountNumber": "11111111111111111111111111111111111", "currencySelect": "US DOLLAR", "New_PIN": "1234", "OTP": "123456"}}