{"Selectors": {"FinishButton": "id=finish", "LoginButton": "id=LoginBtn", "UserNameField": "id=UserName", "ContinueButton": "id=btn", "PasswordField": "id=Password", "ChallengeQuestionField": "id=ChallengeQuestionAnswer", "LoginAuthButton": "id=LoginAuthBtn", "popupMessage": "id=popup_message", "popupOk": "id=popup_ok"}, "TestData": {"UserName": "mahmoudsayed022", "Password": "Password1", "ChallengeAnswer": "bmw"}, "SpecialEnvParams": {"Pilot": {"TestData": {"UserName": "Mm_azmy", "Password": "Asdf2025", "ChallengeAnswer": "armada"}}}}