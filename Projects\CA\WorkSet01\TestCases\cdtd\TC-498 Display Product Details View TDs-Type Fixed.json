{"TestCaseName": "Display Product Details View TDs-Type Fixed", "TestCaseCode": "TC-498", "Environment": "UAT", "Steps": [{"TestCaseReference": {"Name": "<PERSON><PERSON>", "TestCasePath": "WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json", "ParamsReference": "WorkSet01/Params/LoginTestCaseParams.json", "Params": {"TestData": {"UserName": "sherif1234", "Password": "Password1", "ChallengeAnswer": "bmw"}}}}, {"Name": "Click on change later button on the password expiration popup message", "Command": "Click", "Target": "id=popup_cancel", "ContinueOnError": true}, {"Name": "Handle E-Statement Popup", "Command": "Click", "Target": "class=Cancel", "ContinueOnError": true, "CustomDelayBeforeStepExecustionInMilliseconds": 3000}, {"Name": "Click On Accounts", "Command": "click", "Target": "{{Selectors.accounts}}"}, {"Name": "<PERSON><PERSON>", "Command": "Click", "Target": "{{Selectors.InsideDepoists}}"}, {"Name": "Select Product With Fixed Type", "Command": "click", "Target": "{{Selectors.FixedDeposit}}"}, {"Name": "Verify That the  Product Name Displayed", "Command": "appear", "Target": "{{Selectors.productDescription}}", "Value": "Time Deposit General", "CustomDelayBeforeStepExecustionInMilliseconds": 2000}, {"Name": "Verify That the  Product Reference Number Displayed", "Command": "appear", "Target": "{{Selectors.bankiRefNo}}", "Value": "TDGN8181938327", "CustomDelayBeforeStepExecustionInMilliseconds": 2000}, {"Name": "Verify That the Opening Date Displayed", "Command": "appear", "Target": "{{Selectors.accountOpenDate}}", "Value": "24-06-2025", "CustomDelayBeforeStepExecustionInMilliseconds": 2000}, {"Name": "Verify That the Maturity Date Displayed", "Command": "appear", "Target": "{{Selectors.maturityDate}}", "Value": "25-06-2025", "CustomDelayBeforeStepExecustionInMilliseconds": 2000}, {"Name": "Verify That the Maturity Instructions Displayed", "Command": "appear", "Target": "{{Selectors.Instructions}}", "Value": "Close On Maturity", "CustomDelayBeforeStepExecustionInMilliseconds": 2000}, {"Name": "Verify That the Type Displayed", "Command": "appear", "Target": "{{Selectors.Type}}", "Value": "Fixed", "CustomDelayBeforeStepExecustionInMilliseconds": 2000}, {"Name": "Verify That the Tenor Displayed", "Command": "appear", "Target": "{{<PERSON><PERSON>.Tenor}}", "Value": "0 Year - 0 Month - 1 Day", "CustomDelayBeforeStepExecustionInMilliseconds": 2000}, {"Name": "Verify That the Interest Rate Displayed", "Command": "appear", "Target": "{{Selectors.InterestRate}}", "CustomDelayBeforeStepExecustionInMilliseconds": 2000}, {"Name": "Verify That the Maturity Amount Displayed", "Command": "ScrollToElement", "Target": "{{Selectors.maturity_Amount}}", "CustomDelayBeforeStepExecustionInMilliseconds": 2000}, {"Name": "Verify That the Blocked Amount Displayed", "Command": "ScrollToElement", "Target": "{{Selectors.blockamount}}", "Value": "EGP 0.00", "CustomDelayBeforeStepExecustionInMilliseconds": 2000}, {"Name": "Verify That the Next Interest Date Displayed", "Command": "ScrollToElement", "Target": "{{Selectors.nextInterestDate}}", "Value": "24-06-2025", "CustomDelayBeforeStepExecustionInMilliseconds": 2000}, {"Name": "Verify That the Interest Frequency Displayed", "Command": "ScrollToElement", "Target": "{{Selectors.interestFrequency}}", "Value": "Monthly", "CustomDelayBeforeStepExecustionInMilliseconds": 2000}]}