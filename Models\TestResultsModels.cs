﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace TestAutomationFramework.Models
{

    public class FullTestResults
    {
        public List<TestSuite_Results> Suites { get; set; } = new();
        public List<string> excludedTestData{ get; set; } = new();  
    }

    public class TestSuite_Results
    {
        public string Name { get; set; } = "Untitled Suite";
        public List<TestModule_Results> Modules { get; set; } = new();
        public List<TestCase_Results> TestCases { get; set; } = new();
    }

    public class TestModule_Results
    {
        public string Name { get; set; } = "Untitled Module";
        public List<TestCase_Results> TestCases { get; set; } = new();
    }

    public class TestCase_Results
    {
        public string Name { get; set; } = "Untitled Test Case";
        public string TestResultCode { get; set; } = "Untitled Test Case";
        public string Status { get; set; } = "Skipped";  // Passed, Failed, Skipped
        public List<TestStep_Results> Steps { get; set; } = new();
        public string? ErrorMsg { get; set; } = "-";
        public string? ErrorMsgDetails { get; set; } = "no error";

        public Stopwatch ExecutionTimeFromFirstStepInMS { get; set; }
        public string SuiteName { get; set; } = "UnknownSuite";   // Used to group in OneTimeTearDown
        public string? ModuleName { get; set; }                   // Null for direct test cases
        public TestCase? OriginalTestCase { get; set; }           // Optional: If needed later
        public string? Environment { get; set; }
        public TestData? TestData { get; set; } = new TestData();
    }

    public class TestStep_Results
    {
        public int StepNumber { get; set; }
        public string Name { get; set; } = "No Step Name";
        public string Description { get; set; } = "No description";
        public string Result { get; set; } = "Skipped";  // Passed, Failed, Skipped
        public string ExecutionTime { get; set; } = "0s";
        public string ScreenshotUrlorPath { get; set; } = "";  // URL if available
        public TestData TestData { get; set; }=new TestData();
    }

}
