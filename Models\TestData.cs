﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace TestAutomationFramework.Models
{
   public class TestData
    {
      public Dictionary<string, object> Data { get; set; }= new Dictionary<string, object>();

        // if i want specific ExcludedTestData per test later i will handle it 
        public List<string> ExcludedTestData= new List<string>();
    }
}
