﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Mail;
using System.Text;
using System.Threading.Tasks;
using TestAutomationFramework.Models;
using System.IO;

namespace TestAutomationFramework.Helpers
{
    class EmailSender
    {

        public static void SendEmail(EmailSenderResult MailSenderObj, string pdfPath = null)
        {
            try
            {
                string smtpServer = Config.Settings.MailSettings.smtpServer;
                int smtpPort = Config.Settings.MailSettings.smtpPort;
                string smtpUser = Config.Settings.MailSettings.smtpUsername;
                string smtpPass = Config.Settings.MailSettings.smtpPassword; 
               

                MailMessage mail = new MailMessage
                {
                    From = new MailAddress(smtpUser),
                    Subject = $"Test Suite Summary - {DateTime.Now:yyyy-MM-dd HH:mm:ss}",
                    IsBodyHtml = true,
                    Body = $@"
                    <!DOCTYPE html>
                    <html lang='en'>
                    <head><meta charset='UTF-8'></head>
                    <body style='font-family: Arial, sans-serif; background-color: #f9f9f9; padding: 20px; color: #333;'>
                    <h2 style='text-align: left; color: #004085;'>Overall Summary</h2>
                    <table style='width: 100%; border-collapse: collapse; margin-bottom: 20px; border: 1px solid #ddd;'>
                      <tr style='background-color: #3f8277; color: white;'>
                        <th style='border: 1px solid #ddd; padding: 10px; text-align: center;'>Total Suites</th>
                        <th style='border: 1px solid #ddd; padding: 10px; text-align: center;'>Total Modules</th>
                        <th style='border: 1px solid #ddd; padding: 10px; text-align: center;'>Total Test Cases</th>
                        <th style='border: 1px solid #ddd; padding: 10px; text-align: center;'>Passed</th>
                        <th style='border: 1px solid #ddd; padding: 10px; text-align: center;'>Failed</th>
                      </tr>
                      <tr>
                        <td style='border: 1px solid #ddd; padding: 10px; text-align: center;'>{MailSenderObj.totalSuites}</td>
                        <td style='border: 1px solid #ddd; padding: 10px; text-align: center;'>{MailSenderObj.totalModules}</td>
                        <td style='border: 1px solid #ddd; padding: 10px; text-align: center;'>{MailSenderObj.totalTestCases}</td>
                        <td style='border: 1px solid #ddd; padding: 10px; text-align: center; color: green;'>{MailSenderObj.totalPassed}</td>
                        <td style='border: 1px solid #ddd; padding: 10px; text-align: center; color: red;'>{MailSenderObj.totalFailed}</td>
                      </tr>
                    </table>
                    </body>
                    </html>"
                };

                foreach (var email in Config.Settings.MailSettings.recipients)
                {
                    mail.To.Add(email);
                }


                if (!string.IsNullOrEmpty(pdfPath) && File.Exists(pdfPath))
                {
                    Logger.Log("PDF File Path Is Exists ");
                    using (Attachment pdfAttachment = new Attachment(pdfPath))
                    {
                        mail.Attachments.Add(pdfAttachment);
                        Logger.Log("Pdf Attachment Added to Mail Attachments");

                        using (SmtpClient smtp = new SmtpClient(smtpServer, smtpPort))
                        {
                            Logger.Log($"Establish new object smtp to send mail to server{smtpServer} on {smtp}");
                            smtp.EnableSsl = true;
                            smtp.Credentials = new System.Net.NetworkCredential(smtpUser, smtpPass);
                            smtp.Send(mail);
                        }
                    }
                }
                else
                {
                    using (SmtpClient smtp = new SmtpClient(smtpServer, smtpPort))
                    {
                        smtp.EnableSsl = true;
                        smtp.Credentials = new System.Net.NetworkCredential(smtpUser, smtpPass);
                        smtp.Send(mail);
                    }
                }
                Logger.Log(message: "Email sent successfully.", LogLevel.Debug);
            }
            catch (Exception ex)
            {
                Logger.Log(message: $"Failed to send email: {ex.Message}", LogLevel.Error);
            }
        }
    }
}
