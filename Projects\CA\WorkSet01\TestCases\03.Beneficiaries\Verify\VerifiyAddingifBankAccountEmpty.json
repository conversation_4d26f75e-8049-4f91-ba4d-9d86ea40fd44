{"TestCaseName": "Add beneficiary without Bank Account", "TestCaseCode": "TC-38", "Steps": [{"TestCaseReference": {"Name": "<PERSON><PERSON>", "TestCasePath": "WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json", "ParamsReference": "WorkSet01/Params/LoginTestCaseParams.json"}}, {"Name": "Click on change later button on the password expiration popup message", "Command": "Click", "Target": "{{Selectors.popupCancel}}", "ContinueOnError": true}, {"Name": "Click on biometric alert", "Command": "Click", "Target": "{{Selectors.BioMetricAlert}}", "ContinueOnError": true}, {"Name": "Execute JS code to activate token", "Command": "executescript", "Value": "Globals.ActivationState = 'ACTIVATED';"}, {"Name": "Click on Beneficiaries", "Command": "click", "Target": "{{Selectors.BeneficiariesToButt<PERSON>}}"}, {"Name": "Click on Local Transfer option", "Command": "Click", "Target": "{{Selectors.LocalTransfer}}"}, {"Name": "Click on Add Beneficiary", "Command": "Click", "Target": "{{Selectors.AddLocalBenBTn}}"}, {"Name": "Type the Nick Name", "Command": "Type", "Target": "{{Selectors.NickNameInput}}", "Value": "teest"}, {"Name": "Type the  Beneficiary Full Name", "Command": "Type", "Target": "{{Selectors.BeneficiaryFullNameInput}}", "Value": "{{TestData.BeneficiaryFullName}}"}, {"Name": "select bank name", "Command": "click", "Target": "{{Selectors.BankNameSelect}}"}, {"Name": "select bank option", "Command": "click", "Target": "{{Selectors.BankOption}}"}, {"Name": "click on currency dropdown list", "Command": "click", "Target": "{{Selectors.currencySelect}}"}, {"Name": "select currency option ", "Command": "click", "Target": "{{Selectors.currencyoption}}"}, {"Name": "fill address input", "Command": "type", "Target": "{{Selectors.AddressInput}}", "Value": "30 st usa"}, {"Name": "fill city input", "Command": "type", "Target": "{{Selectors.CityInput}}", "Value": "California"}, {"Name": "select Country input", "Command": "click", "Target": "{{Selectors.CountryInput}}"}, {"Name": "select Country option", "Command": "click", "Target": "{{Selectors.countrtyoption}}"}, {"Name": "Click on continue", "Command": "click", "Target": "{{Selectors.SaveBtn}}"}, {"Name": "Check that it did not save the details with empty account number", "Command": "appear", "Target": "css=#wginsAddLocalBeneficiary_Default > div > div > div.section_header > h2"}], "unesedTempStep": {"Name": "Assert hint error message with value: This field is required.", "Command": "assert", "Target": "{{Selectors.BankAccountMSG}}", "value": "This field is required."}}