﻿using NUnit.Framework;
using System;
using System.IO;

namespace TestAutomationFramework.Helpers
{
    public static class Logger
    {
        private static readonly object _lock = new object();
        private static string LogFileString= "================================ Start of LogFile ================================";

        /// <summary>
        /// Initializes the logger with optional file logging.
        /// </summary>
        /// <param name="logFilePath">The file path where logs will be written (if file logging is enabled).</param>
        /// <param name="enableFileLogging">Determines if logs should be written to a file. Default is false.</param>
        public static void GenerateLogFile()
        {
            var outPath = Path.Combine(Config.Settings.OutputDir, "TextLogFiles");
            Directory.CreateDirectory(outPath);
            string logFilePath = Path.Combine(outPath, $"{ReportGenerator.GlobalNameWithTimestampForReportFiles}.txt");
            File.WriteAllText(logFilePath, LogFileString);

        }

        /// <summary>
        /// Logs a message with a specified log level.
        /// </summary>
        /// <param name="message">The log message.</param>
        /// <param name="level">The log level (e.g., Info, Debug, Error).</param>
        public static void Log(string message, LogLevel level = LogLevel.Info, ConsoleColor? consoleColor = null)
        {
            lock (_lock)
            {
                var logEntry = $"{DateTime.Now:yyyy-MM-dd HH:mm:ss.fff} [{level}] {message}";

                // Change console color if a color is provided
                if (consoleColor.HasValue)
                {
                    Console.ForegroundColor = consoleColor.Value;
                }


                // Always write to console
                Console.WriteLine(logEntry);
                TestContext.Progress.WriteLine(logEntry);
                LogFileString += $"\n{logEntry}";
            }
        }
    }

    public enum LogLevel
    {
        Info,
        Debug,
        Warn,
        Error
    }
}
