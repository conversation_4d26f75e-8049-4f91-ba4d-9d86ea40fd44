{"TestCaseName": "Ensure that Request Type displayed", "TestCaseCode": "TC-432", "Steps": [{"TestCaseReference": {"Name": "<PERSON><PERSON>", "TestCasePath": "WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json", "ParamsReference": "WorkSet01/Params/LoginTestCaseParams.json", "Params": {"TestData": {"UserName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Password": "Pass1234", "ChallengeAnswer": "bmw"}}}}, {"Name": "Click on Cards option on the side menu", "Command": "Click", "Target": "{{Selectors.CardsButton}}"}, {"Name": "Click on Card Services", "Command": "Click", "Target": "{{Selectors.CardServices}}"}, {"Name": "Click on request status", "Command": "click", "Target": "{{Selectors.RequestStatues}}"}, {"Name": "Choose a Dispute request", "Command": "click", "Target": "css=#RequestItem1"}, {"Name": "Ensure that Request Type displayed", "Command": "click", "Target": "css=#RequestDataItems > div:nth-child(1) > p", "ElementToValidateThatScreenLoaded": "css=#RequestDataItems > div:nth-child(1) > p"}]}