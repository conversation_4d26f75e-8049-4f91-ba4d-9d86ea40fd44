{"TestSuiteName": "Beneficiaries", "TestSuiteItems": [{"Type": "TestCase", "Reference": "WorkSet01/TestCases/03.Beneficiaries/Add/TC-255 AddingOtherCAEaccount.json", "ParamsReference": "WorkSet01/Params/BeneficiariesParams.json"}, {"Type": "TestCase", "Reference": "WorkSet01/TestCases/03.Beneficiaries/Add/TC-256 AddingOtherCAEcard.json", "ParamsReference": "WorkSet01/Params/BeneficiariesParams.json"}, {"Type": "TestCase", "Reference": "WorkSet01/TestCases/03.Beneficiaries/Add/TC-34 AddinglocalBen.json", "ParamsReference": "WorkSet01/Params/VerifictionParams.json"}, {"Type": "TestCase", "Reference": "WorkSet01/TestCases/03.Beneficiaries/Verify/251-Existing Account beneficiaries displayed.json", "ParamsReference": "WorkSet01/Params/BeneficiariesParams.json"}, {"Type": "TestCase", "Reference": "WorkSet01/TestCases/03.Beneficiaries/Verify/252-Existing Card beneficiaries displayed.json", "ParamsReference": "WorkSet01/Params/BeneficiariesParams.json"}, {"Type": "TestCase", "Reference": "WorkSet01/TestCases/03.Beneficiaries/Verify/TC-145 Existing bankinsideegypt beneficiaries displayed.json", "ParamsReference": "WorkSet01/Params/BeneficiariesParams.json"}, {"Type": "TestCase", "Reference": "WorkSet02/TestCases/028.Beneficiary/TC-249 Edit an Existing beneficiary Account.json", "ParamsReference": "WorkSet02/Params/Amr <PERSON><PERSON>f Beneficiaries Params.json"}, {"Type": "TestCase", "Reference": "WorkSet02/TestCases/028.Beneficiary/TC-146  Edit Existing beneficiary.json", "ParamsReference": "WorkSet02/Params/Amr <PERSON><PERSON>f Beneficiaries Params.json"}, {"Type": "TestCase", "Reference": "WorkSet01/TestCases/03.Beneficiaries/Delete/147-Delete Existing beneficiary.json", "ParamsReference": "WorkSet01/Params/BeneficiariesParams.json"}, {"Type": "TestCase", "Reference": "WorkSet01/TestCases/03.Beneficiaries/Delete/TC-254 Delete an Existing Card beneficiary.json", "ParamsReference": "WorkSet01/Params/BeneficiariesParams.json"}, {"Type": "TestCase", "Reference": "WorkSet01/TestCases/03.Beneficiaries/Delete/TC-253 Delete an Existing Account beneficiary.json", "ParamsReference": "WorkSet01/Params/BeneficiariesParams.json"}]}