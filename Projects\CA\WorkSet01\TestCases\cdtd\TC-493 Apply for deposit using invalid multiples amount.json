{"TestCaseName": "Apply for deposit using invalid multiples amount", "TestCaseCode": "TC-493", "Steps": [{"TestCaseReference": {"Name": "<PERSON><PERSON>", "TestCasePath": "WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json", "ParamsReference": "WorkSet01/Params/LoginTestCaseParams.json", "Params": {"TestData": {"UserName": "sherif1234", "Password": "Password1", "ChallengeAnswer": "bmw"}}}}, {"Name": "Click on Cancel Button", "Command": "click", "Target": "id=popup_cancel", "ElementToValidateThatScreenLoaded": "css=#popbuttons > div > button.btn.Cancel.back_gradient2", "ContinueOnError": true}, {"Name": "Click on <PERSON><PERSON>", "Command": "click", "Target": "css=#popbuttons > div > button.btn.Cancel.back_gradient2", "ContinueOnError": true, "CustomDelayBeforeStepExecustionInMilliseconds": 2000}, {"Name": "Click on biometric alert", "Command": "Click", "Target": "{{Selectors.BioMetricAlert}}", "ContinueOnError": true}, {"Name": "Click on Deposits button from side menu", "Command": "Click", "Target": "{{Select<PERSON>.Depo<PERSON>}}"}, {"Name": "Click on Time Deposits button from side menu", "Command": "Click", "Target": "{{Selectors.TimeDeposits}}"}, {"Name": "Click on Select Deposit button", "Command": "Click", "Target": "{{Selectors.SelectCertificateBtn}}"}, {"Name": "Click on Deposit product", "Command": "Click", "Target": "id=ProductBlock0"}, {"Name": "Type an invalid multiples amount", "Command": "type", "Target": "{{Selectors.Amount}}", "value": "100005"}, {"Name": "<PERSON>lick on Select Account <PERSON><PERSON>", "Command": "click", "Target": "{{Selectors.SelectAccountBtn}}"}, {"Name": "Click on Account", "Command": "click", "Target": "{{Selectors.Account}}"}, {"Name": "Assert on the input amount hint indicating an invalid amount was entered", "Command": "verify", "Target": "{{Selectors.input_hint}}", "Value": "The value must be Multiples of 1000"}, {"Name": "Click on Yes", "Command": "click", "Target": "{{Selectors.SameAccOption}}"}, {"Name": "Click on Continue <PERSON><PERSON> and ensure it does not continue the flow", "Command": "click", "Target": "{{Selectors.ContinueBookProductBtn}}"}, {"Name": "Type a valid multiples amount", "Command": "type", "Target": "{{Selectors.Amount}}", "value": "100000"}, {"Name": "<PERSON>lick on Select Account <PERSON><PERSON>", "Command": "click", "Target": "{{Selectors.SelectAccountBtn}}"}, {"Name": "Click on Account", "Command": "click", "Target": "{{Selectors.Account}}"}, {"Name": "Click on Yes", "Command": "click", "Target": "{{Selectors.SameAccOption}}"}, {"Name": "Click on Continue <PERSON><PERSON> and ensure it continues the flow with the correct amount", "Command": "click", "Target": "{{Selectors.ContinueBookProductBtn}}"}]}