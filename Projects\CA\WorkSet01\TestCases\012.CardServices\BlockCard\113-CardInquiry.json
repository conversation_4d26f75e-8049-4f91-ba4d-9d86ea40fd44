{"TestCaseName": "All users Cards are displayed successfully", "TestCaseCode": "TC-113", "Steps": [{"TestCaseReference": {"Name": "<PERSON><PERSON>", "TestCasePath": "WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json", "ParamsReference": "WorkSet01/Params/LoginTestCaseParams.json", "Params": {"TestData": {"UserName": "mahmoudsayed022", "Password": "Password1", "ChallengeAnswer": "bmw"}}}}, {"Name": "Click on change later button on the password expiration popup message", "Command": "Click", "Target": "{{Selectors.popupCancel}}", "ContinueOnError": true}, {"Name": "Execute JS code to activate token", "Command": "executescript", "Value": "Globals.ActivationState = 'ACTIVATED';", "CustomDelayBeforeStepExecustionInMilliseconds": 2000}, {"Name": "Click on Cards option on the side menu", "Command": "Click", "Target": "{{Selectors.CardsButton}}"}, {"Name": "Click on Card Inquiry and check cards details", "Command": "Click", "Target": "{{Selectors.CardInquiry}}"}, {"Name": "Check if the blocked cards appear in the card list.", "Command": "appear", "Target": "css=#CardsSelectionErrorHandle > div:nth-child(1)", "CustomDelayBeforeStepExecustionInMilliseconds": 5000, "ContinueOnError": true}]}