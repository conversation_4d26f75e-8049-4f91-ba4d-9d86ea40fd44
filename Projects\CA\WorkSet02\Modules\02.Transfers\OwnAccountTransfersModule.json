{"ModuleName": "Own Account Transfers", "TestCases": [{"TestCasePath": "WorkSet02/TestCases/02.Transfers/OwnAccountTransfers/90-SufficientAmount.json", "ParamsReference": "WorkSet02/Params/PayToParams.json"}, {"TestCasePath": "WorkSet02/TestCases/02.Transfers/OwnAccountTransfers/91-InsufficientAmount.json", "ParamsReference": "WorkSet02/Params/PayToParams.json"}, {"TestCasePath": "WorkSet02/TestCases/02.Transfers/OwnAccountTransfers/92-EnabledToBlockedAccount.json", "ParamsReference": "WorkSet02/Params/PayToParams.json"}, {"TestCasePath": "WorkSet02/TestCases/02.Transfers/OwnAccountTransfers/93-EURToEURSuffecirntAmount.json", "ParamsReference": "WorkSet02/Params/PayToParams.json"}, {"TestCasePath": "WorkSet02/TestCases/02.Transfers/OwnAccountTransfers/94-AEDTOUSDSuffecirntAmount.json", "ParamsReference": "WorkSet02/Params/PayToParams.json"}]}