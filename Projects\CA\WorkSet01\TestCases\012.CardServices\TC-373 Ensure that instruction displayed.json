{"TestCaseName": "Ensure that instruction displayed", "TestCaseCode": "TC-373", "Steps": [{"TestCaseReference": {"Name": "<PERSON><PERSON>", "TestCasePath": "WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json", "ParamsReference": "WorkSet01/Params/LoginTestCaseParams.json"}}, {"Name": "Click on change later button on the password expiration popup message", "Command": "Click", "Target": "{{Selectors.popupCancel}}", "ContinueOnError": true}, {"Name": "Click on biometric alert", "Command": "Click", "Target": "{{Selectors.BioMetricAlert}}", "ContinueOnError": true}, {"Name": "Click on biometric alert", "Command": "Click", "Target": "{{Selectors.KYCskip}}", "ContinueOnError": true}, {"Name": "Execute JS code to activate token", "Command": "executescript", "Value": "Globals.ActivationState = 'ACTIVATED';"}, {"Name": "Click on Cards button from side menu", "Command": "Click", "Target": "{{Selectors.CardsButton}}"}, {"Name": "Click on Card Inquiry and check cards details", "Command": "Click", "Target": "{{Selectors.CardInquiry}}", "ElementToValidateThatScreenLoaded": "xpath=/html/body/div[2]/div[2]/div/div/div[4]/div/div[2]/div[2]/div/div[2]/div/div[2]/div[3]/div[1]/div[1]/div/div[3]/h3"}, {"Name": "Click on a card", "Command": "Click", "Target": "{{Selectors.CardToBlockFromInquiry}}"}, {"Name": "Click on Block Button", "Command": "Click", "Target": "{{Selectors.BlockCardOptionInquiry}}"}, {"Name": "Select block reason Stolen", "Command": "select", "Target": "{{Selectors.BlockReasonDroplist}}", "Value": "<PERSON><PERSON><PERSON>"}, {"Name": "Select block reason Temporary", "Command": "select", "Target": "{{Selectors.BlockReasonDroplist}}", "Value": "Temporary"}, {"Name": "Select block reason Lost", "Command": "select", "Target": "{{Selectors.BlockReasonDroplist}}", "Value": "Lost"}]}