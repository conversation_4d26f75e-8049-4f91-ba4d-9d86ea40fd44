{"Selectors": {"HappyPointsOption": "xpath=//div[@class='mainmenuitems']//label[contains(text(), 'Happy Points')]", "RedeemOnline": "css=#BuyOnlineBTN > div", "SelectMerchant": "css=#RequestItem0", "RedeemButton": "id=RedeemID", "PointsTextbox": "id=redeempointsID", "CreateCupon": "id=CCoponID", "CuntinueConfirmCupon": "id=RequestRedemptionConfrimationSbmtBtn", "OTPbox": "id=OTPInpt", "OTPconfirmButton": "id=ConfirmSbmtBtn", "popupCancel": "id=popup_cancel", "popupMessage": "id=popup_message", "popupOk": "id=popup_ok", "pyaccf": "css=#PayCreditCardFeesContainer > div", "happy_points": "xpath=/html/body/div[2]/div[2]/div/div/div[2]/div/div[3]/div/div/div/div[2]/div/div[8]", "verification_text": "xpath=/html/body/div[2]/div[2]/div/div/div[4]/div/div/div[2]/div/div/div/div/div[1]/h2", "verification_text1": "xpath=/html/body/div[8]/section/div/div[2]/div[2]/div/div[2]/h2"}}