{"ModuleName": "Beneficiaries", "TestCases": [{"TestCasePath": "WorkSet02/TestCases/03.Beneficiaries/33-BeneficiariesAccountNumberLength.json", "ParamsReference": "WorkSet02/Params/BeneficiariesParams.json"}, {"TestCasePath": "WorkSet02/TestCases/03.Beneficiaries/34-BeneficiariesNeedAuthenticationToken.json", "ParamsReference": "WorkSet02/Params/BeneficiariesParams.json"}, {"TestCasePath": "WorkSet02/TestCases/03.Beneficiaries/35-EditBeneficiaries/35-EditBeneficiaries-BanksInsideEgypt/35-EditBeneficiaries-BanksInsideEgypt-IBAN.json", "ParamsReference": "WorkSet02/Params/BeneficiariesParams.json"}, {"TestCasePath": "WorkSet02/TestCases/03.Beneficiaries/35-EditBeneficiaries/35-EditBeneficiaries-BanksInsideEgypt/35-EditBeneficiaries-BanksInsideEgypt-MobileNumber.json", "ParamsReference": "WorkSet02/Params/BeneficiariesParams.json"}, {"TestCasePath": "WorkSet02/TestCases/03.Beneficiaries/35-EditBeneficiaries/35-EditBeneficiaries-BanksInsideEgypt/35-EditBeneficiaries-BanksInsideEgypt-PaymentAddress.json", "ParamsReference": "WorkSet02/Params/BeneficiariesParams.json"}, {"TestCasePath": "WorkSet02/TestCases/03.Beneficiaries/35-EditBeneficiaries/35-EditBeneficiaries-BanksInsideEgypt/35-EditBeneficiaries-BanksInsideEgypt-Wallet.json", "ParamsReference": "WorkSet02/Params/BeneficiariesParams.json"}, {"TestCasePath": "WorkSet02/TestCases/03.Beneficiaries/35-EditBeneficiaries/35-EditBeneficiaries-BanksInsideEgypt/35-EditBeneficiaries-BanksInsideEgypt-BankAccount.json", "ParamsReference": "WorkSet02/Params/BeneficiariesParams.json"}, {"TestCasePath": "WorkSet02/TestCases/03.Beneficiaries/35-EditBeneficiaries/35-EditBeneficiaries-BanksInsideEgypt/35-EditBeneficiaries-BanksInsideEgypt-Card.json", "ParamsReference": "WorkSet02/Params/BeneficiariesParams.json"}]}