using Microsoft.VisualStudio.TestPlatform.ObjectModel;
using PuppeteerSharp;
using PuppeteerSharp.Media;
using System;
using System.Diagnostics;
using System.IO;
using System.Runtime.InteropServices;
using System.Text;
using TestAutomationFramework.Helpers;
using TestAutomationFramework.Models;


namespace TestAutomationFramework.Helpers
{
    public class ReportGenerator
    {
        //
        public static int testCaseCounter = 1;
        public static int suitCounter = 1;
        public static int moduleCounter = 1;
        private static string OutputPath => Path.Combine(Config.Settings.OutputDir, "GeneratedReports");
        public static string GlobalNameWithTimestampForReportFiles;
        public static bool SingleTestExecutionFlag = false;
        private static string TestCaseNAME { get; set; }
        private static string TestCaseCODE { get; set; }
        private static string TestCaseSTATUS { get; set; }

        // TestCaseDetails Properties
        private static StringBuilder TestCaseDetailsContent = new();
        private static StringBuilder TestCaseRowDetailsContent = new();
        private static int CounterTestCaseDetailsRow = 1;

        private static int RowSpanSuiteIndex = 0;
        private static int RowSpanModuleIndex = 0;

        static ReportGenerator()
        {
            Directory.CreateDirectory(OutputPath);
            Directory.CreateDirectory(Path.Combine(OutputPath, "HTML"));
            Directory.CreateDirectory(Path.Combine(OutputPath, "PDF"));
        }


        private static string LoadTemplate(string filename)
        {

            //return File.ReadAllText(Path.Combine(TemplatesDir, filename));
            return File.ReadAllText("Templates\\" + filename);
        }

        public static void GenerateHtmlReport(
        FullTestResults testResults,
        string outputPath,
        out EmailSenderResult MailSenderObj,
        out string pdfAttachmentMailPath,
        string? customReportName = null,
        bool isSingleTestExecution = false,
        bool generateSuammaryWithoutDetails = false)
        {
            string reportTemplate = LoadTemplate("report_template.html");
            string summaryTemplate = LoadTemplate("summary_template.html");
            string suiteTemplate = LoadTemplate("suite_template.html");
            string moduleTemplate = LoadTemplate("module_template.html");
            string testcaseTemplate = LoadTemplate("testcase_template.html");
            string teststepTemplate = LoadTemplate("teststep_template.html");
            // testcase details load
            string TestCaseDetailsTemplate = LoadTemplate("TestCaseDetails.html");
            string TestCaseDetailsRowTemplate = LoadTemplate("TestCaseDetailsRow.html");


            var CurrentEnv = "UAT";
            // Generate overall summary
            int totalSuites = 0;
            int totalModules = 0;
            int totalTestCases = 0;
            int totalSteps = 0;
            int totalPassed = 0;
            int totalFailed = 0;
            TimeSpan totalExecutionTime = TimeSpan.Zero;

            /*   int totalSkipped = 0;*/
            List<string> ResultSuiteName = new List<string>();







            StringBuilder suitesContent = new();



            foreach (var suite in testResults.Suites)
            {
                RowSpanSuiteIndex = 0;
                // Filter valid test cases in the suite (non-skipped and with steps)
                var suiteTestCases = suite.TestCases.Where(tc => !tc.Status.Equals("Skipped", StringComparison.OrdinalIgnoreCase) && tc.Steps.Any()).ToList();

                // Checking if a single test is being executed or not to change the report format accordingly
                if (ConfigHelper.TestCaseRefrenceToTestResult?.Count >= ConfigHelper._RunningInstructions.RunningBrowsers.Length &&
                    ConfigHelper.TestCaseRefrenceToTestResult?.Where(KVP => KVP.Value.Status != "Skipped")?.ToList()?.Count == ConfigHelper._RunningInstructions.RunningBrowsers.Length)
                {
                    SingleTestExecutionFlag = true;
                }

                // Filter valid modules (only those with non-skipped test cases and with steps)
                var suiteModules = suite.Modules.Where(m => m.TestCases.Any(tc => !tc.Status.Equals("Skipped", StringComparison.OrdinalIgnoreCase)/* && tc.Steps.Any()*/)).ToList();

                // Skip suite if it has no valid test cases or modules
                if (!suiteTestCases.Any() && !suiteModules.Any())
                    continue;

                totalSuites++;
                ResultSuiteName.Add(suite.Name);
                // Suite summary calculations
                int suiteModulesCount = suiteModules.Count;
                int suiteTestCasesCount = suiteTestCases.Count + suiteModules.Sum(m => m.TestCases.Count);
                int suitePassed = suiteTestCases.Count(tc => tc.Status.Equals("Passed", StringComparison.OrdinalIgnoreCase)) +
                                  suiteModules.Sum(m => m.TestCases.Count(tc => tc.Status.Equals("Passed", StringComparison.OrdinalIgnoreCase)));
                int suiteFailed = suiteTestCases.Count(tc => tc.Status.Equals("Failed", StringComparison.OrdinalIgnoreCase)) +
                                  suiteModules.Sum(m => m.TestCases.Count(tc => tc.Status.Equals("Failed", StringComparison.OrdinalIgnoreCase)));


                StringBuilder suiteModulesContent = new();



                foreach (var module in suiteModules)
                {
                    RowSpanModuleIndex = 0;
                    // Filter valid test cases in the module (non-skipped and with steps)
                    int moduleTestCases = module.TestCases.Count();
                    if (moduleTestCases == 0) continue; // Skip module if it doesn't have valid test cases

                    int modulePassed = module.TestCases.Count(tc => tc.Status.Equals("Passed", StringComparison.OrdinalIgnoreCase));
                    int moduleFailed = module.TestCases.Count(tc => tc.Status.Equals("Failed", StringComparison.OrdinalIgnoreCase));


                    StringBuilder moduleContent = new();
                    foreach (var testCase in module.TestCases.Where(tc => !tc.Status.Equals("Skipped", StringComparison.OrdinalIgnoreCase)))
                    {
                        // skip Failed Test Cases IF HideFailedTestCases Flag ==True
                        if (ConfigHelper._RunningInstructions.ReportSettings.HideFailedTestCases == true && testCase.Status == "Failed")
                            continue;
                        // skip Failed Test Cases IF HideFailedTestCases Flag ==True
                        if (ConfigHelper._RunningInstructions.ReportSettings.HidePassedTestCases == true && testCase.Status == "Passed")
                            continue;


                        var TestCaseGenerationResult = GenerateTestCaseHtml(testCase, $"{suite.Name}",
                            $"{module.Name}", testcaseTemplate, teststepTemplate);

                        moduleContent.Append(TestCaseGenerationResult);
                        testCaseCounter++;
                        CurrentEnv = testCase.Environment;
                        totalExecutionTime += testCase.ExecutionTimeFromFirstStepInMS.Elapsed;

                        if (isSingleTestExecution == true)
                        {
                            TestCaseRowDetailsContent = new StringBuilder();
                            continue;

                        }


                        // build test Case Details 
                        buidTestCaseDetailsTable(testCase, TestCaseDetailsRowTemplate, module.Name, suite.Name, testResults);


                    }

                    TestCaseRowDetailsContent
                           .Replace("numberofmodule", RowSpanModuleIndex.ToString())
                           .Replace("numberofsuite", (RowSpanSuiteIndex).ToString());


                    suiteModulesContent.Append(moduleTemplate
                        .Replace("{{ModuleName}}", $"{suitCounter}.{moduleCounter}. {module.Name}")
                        .Replace("{{ModuleTotalTestCases}}", moduleTestCases.ToString())
                        .Replace("{{ModulePassed}}", modulePassed.ToString())
                        .Replace("{{ModuleFailed}}", moduleFailed.ToString())
                        //.Replace("{{ModuleSkipped}}", moduleSkipped.ToString())
                        .Replace("{{ModuleContent}}", moduleContent.ToString()));
                    moduleCounter++;
                }

                // Build direct test cases for suite
                StringBuilder suiteDirectContent = new();
                foreach (var testCase in suiteTestCases)
                {
                    // skip Failed Test Cases IF HideFailedTestCases Flag ==True
                    if (ConfigHelper._RunningInstructions.ReportSettings.HideFailedTestCases == true && testCase.Status == "Failed")
                        continue;
                    // skip Failed Test Cases IF HideFailedTestCases Flag ==True
                    if (ConfigHelper._RunningInstructions.ReportSettings.HidePassedTestCases == true && testCase.Status == "Passed")
                        continue;


                    suiteDirectContent.Append(GenerateTestCaseHtml(testCase, $"{suite.Name}", "Direct", testcaseTemplate, teststepTemplate));
                    testCaseCounter++;
                    CurrentEnv = testCase.Environment;
                    totalExecutionTime += testCase.ExecutionTimeFromFirstStepInMS.Elapsed;
                    if (isSingleTestExecution == true)
                    {
                        TestCaseRowDetailsContent = new StringBuilder();
                        continue;

                    }

                    buidTestCaseDetailsTable(testCase, TestCaseDetailsRowTemplate, "Direct", suite.Name, testResults);




                }
                TestCaseRowDetailsContent
                          .Replace("numberofmodule", RowSpanModuleIndex.ToString())
                          .Replace("numberofsuite", (RowSpanSuiteIndex).ToString());
                RowSpanModuleIndex = 0;

                string suiteContent = suiteModulesContent.ToString() + suiteDirectContent.ToString();





                suitesContent.Append(suiteTemplate
                    .Replace("{{SuiteName}}", $"{suitCounter}. {suite.Name}")
                    .Replace("{{SuiteTotalModules}}", suiteModulesCount.ToString())
                    .Replace("{{SuiteTotalTestCases}}", suiteTestCasesCount.ToString())
                    .Replace("{{SuitePassed}}", suitePassed.ToString())
                    .Replace("{{SuiteFailed}}", suiteFailed.ToString())
                    //.Replace("{{SuiteSkipped}}", suiteSkipped.ToString())
                    .Replace("{{SuiteContent}}", suiteContent));

                // reset the TestCaseDetailsContent
                // TestCaseDetailsContent = new StringBuilder();



                // Update overall summary
                totalModules += suiteModulesCount;
                totalTestCases += suiteTestCasesCount;
                totalPassed += suitePassed;
                totalFailed += suiteFailed;
                // totalSkipped += suiteSkipped;
                suitCounter++;
                testCaseCounter = 1;
                moduleCounter = 1;
                RowSpanSuiteIndex++;
            }

            // Now build the final HTML
            string overallSummary = summaryTemplate
                .Replace("{{TotalSuites}}", totalSuites.ToString())
               // .Replace("{{TotalModules}}", totalModules.ToString())
                .Replace("{{TotalTestCases}}", totalTestCases.ToString())
                .Replace("{{TotalSteps}}", totalSteps.ToString())
                .Replace("{{TotalExecutionTime}}", totalExecutionTime.ToString(@"hh\:mm\:ss"))
                .Replace("{{TotalPassed}}", totalPassed.ToString())
                .Replace("{{TotalFailed}}", totalFailed.ToString());


            // Combine TestCaseRowDetailsContent into TestCaseDetailsContent 
            TestCaseDetailsContent.Append(TestCaseDetailsTemplate).Replace("{{testCaseDetails}}", TestCaseRowDetailsContent.ToString());

            string finalHtml = reportTemplate
                .Replace("{{LogoUrl}}", Path.Combine(Environment.CurrentDirectory, "Templates/CA_Logo.png"))
                .Replace("{{ReportTitle}}", ResultSuiteName.Count > 1 ? $"{string.Join(", ", ResultSuiteName)} Suites Execution Report" : $"{ResultSuiteName[0]} Suite Execution Report")
                .Replace("{{ReportDate}}", DateTime.Now.ToString("yyyy-MM-dd hh:mm tt"))
                .Replace("{{Environment}}", $"{string.Join(", ", CurrentEnv ?? ConfigHelper._RunningInstructions.RunningEnvironment)}")
                .Replace("{{Browser}}", $"{string.Join(", ", ConfigHelper._RunningInstructions.RunningBrowsers[0])}")
                //.Replace("{{Device}}", $"{(ConfigHelper._RunningInstructions.ReportSettings.MobileView ? "iPhone X" : "Desktop")}")
                .Replace("{{SummarySection}}", overallSummary)
                .Replace("{{SuitesContent}}", ConfigHelper._RunningInstructions.ReportSettings.HideEvidence is false ? suitesContent.ToString() : "")
                .Replace("{{TestCaseDetailContent}}", isSingleTestExecution ? "" : TestCaseDetailsContent.ToString() ?? "")
                .Replace("{{TotalExecutionTime}}", totalExecutionTime.ToString(@"hh\:mm\:ss")); ;


            finalHtml = finalHtml.Replace("{{HideForSingleTestExecutionFlag}}", isSingleTestExecution ? " style=\"display:none;\" " : " ");

            GlobalNameWithTimestampForReportFiles = customReportName ??
            $"{TestCaseCODE} {TestCaseNAME} - {(ConfigHelper._RunningInstructions.RunningEnvironment)} - {TestCaseSTATUS} - {ConfigHelper._RunningInstructions.RunningBrowsers[0]}";

            string htmlFile = Path.Combine(outputPath, "HTML", $"{GlobalNameWithTimestampForReportFiles}.html");

            File.WriteAllText(htmlFile, finalHtml);

            if (generateSuammaryWithoutDetails)
            {
                string testCaseDetailsHtml = TestCaseDetailsTemplate.Replace("{{testCaseDetails}}", TestCaseRowDetailsContent.ToString());

                string sammaryReportWithoutTestCaseDetails = reportTemplate
                .Replace("{{LogoUrl}}", Path.Combine(Environment.CurrentDirectory, "Templates/CA_Logo.png"))
                .Replace("{{ReportTitle}}", ResultSuiteName.Count > 1 ? $"{string.Join(", ", ResultSuiteName)} Suites Execution Report" : $"{ResultSuiteName[0]} Suite Execution Report")
                .Replace("{{ReportDate}}", DateTime.Now.ToString("yyyy-MM-dd hh:mm tt"))
                .Replace("{{Environment}}", $"{string.Join(", ", CurrentEnv ?? ConfigHelper._RunningInstructions.RunningEnvironment)}")
                .Replace("{{Browser}}", $"{string.Join(", ", ConfigHelper._RunningInstructions.RunningBrowsers[0])}")
                .Replace("{{SummarySection}}", overallSummary)
                .Replace("{{TestCaseDetailContent}}", testCaseDetailsHtml)
                .Replace("{{TotalExecutionTime}}", totalExecutionTime.ToString(@"hh\:mm\:ss"))
                .Replace("{{SuitesContent}}", "");


                string additionalHtmlPath = Path.Combine(outputPath, "HTML", $"{GlobalNameWithTimestampForReportFiles}_TestCaseDetailsOnly.html");
                File.WriteAllText(additionalHtmlPath, sammaryReportWithoutTestCaseDetails);

                var outputSummaryPdfPath = Path.Combine(outputPath, "PDF", $"{GlobalNameWithTimestampForReportFiles}_TestCaseDetailsOnly.pdf");
                GeneratePdfAsync(Path.Combine(Environment.CurrentDirectory, additionalHtmlPath), outputSummaryPdfPath).GetAwaiter().GetResult();
            }

            ForceWriteOnFileReadOnlyHelper.ForceWriteToFolder(outputPath);
            File.Copy(Path.Combine(Directory.GetCurrentDirectory(), "Templates", "styles.css"), Path.Combine(outputPath, "HTML", "styles.css"), true);

            Logger.Log("Report generated as html", LogLevel.Info);

            var pdfFileName = $"{(customReportName ?? GlobalNameWithTimestampForReportFiles)}.pdf";
            var outputPdfPath = Path.Combine(outputPath, "PDF", pdfFileName);
            GeneratePdfAsync(Path.Combine(Environment.CurrentDirectory, htmlFile), outputPdfPath).GetAwaiter().GetResult();

            pdfAttachmentMailPath = outputPdfPath;

            MailSenderObj = new EmailSenderResult
            {
                totalSuites = totalSuites,
                totalModules = totalModules,
                totalTestCases = totalTestCases,
                totalSteps = totalSteps,
                totalFailed = totalFailed,
                totalPassed = totalPassed,
            };
        }


        private static string GenerateTestCaseHtml(TestCase_Results testCase, string suiteName, string moduleName, string testcaseTemplate, string teststepTemplate)
        {
            TestCaseNAME = testCase.Name;
            TestCaseCODE = testCase.TestResultCode;
            TestCaseSTATUS = testCase.Status;
            StringBuilder stepsContent = new();
            int i = 0;
            foreach (var step in testCase.Steps)
            {
                /*if (!string.IsNullOrEmpty(step.ScreenshotUrlorPath) && !step.ScreenshotUrlorPath.StartsWith("http", StringComparison.OrdinalIgnoreCase))
                {
                    step.ScreenshotUrlorPath = $"data:image/jpeg;base64,{Convert.ToBase64String(File.ReadAllBytes(step.ScreenshotUrlorPath))}";
                }*/
                i++;
                string screenshot = string.IsNullOrEmpty(step.ScreenshotUrlorPath) ? "" : $"<img class='test-step-image' src='{step.ScreenshotUrlorPath}' alt='Step Screenshot'>";
                stepsContent.Append(teststepTemplate
                    //.Replace("{{StepNumber}}", step.StepNumber.ToString())
                    .Replace("{{StepNumber}}", i.ToString())
                    .Replace("{{StepDescription}}", step.Description)
                    .Replace("{{StepResult}}", step.Result)
                    .Replace("{{StepExecutionTime}}", step.ExecutionTime)
                    .Replace("{{StepScreenshot}}", screenshot));
            }
            return testcaseTemplate
                .Replace("{{SuiteName}}", suiteName)
                .Replace("{{ModuleName}}", moduleName)
                .Replace("{{TestCaseName}}", SingleTestExecutionFlag ? $" {testCase.Name}" : $"{suitCounter}.{moduleCounter}.{testCaseCounter}. {testCase.Name}")
                .Replace("{{TestCaseStatus}}", testCase.Status)
                .Replace("{{TestCaseExecutionTime}}", $"{testCase.ExecutionTimeFromFirstStepInMS.Elapsed.Minutes:D2}:{testCase.ExecutionTimeFromFirstStepInMS.Elapsed.Seconds:D2}")
                .Replace("{{TestCaseStatusLower}}",
                testCase.Status.ToLower() == "passed" ?
                testCase.Status.ToLower() :
                testCase.Status.ToLower() == "failed" ?
                testCase.Status.ToLower() : "blocked")
                .Replace("{{TestSteps}}", stepsContent.ToString())
                .Replace("{{TestCaseCode}}", testCase.TestResultCode)
                .Replace("{{ErrorMsg}}", testCase.ErrorMsg ?? "")
                .Replace("{{display}}", (testCase.ErrorMsg != null && testCase.Status.ToLower() == "failed") ? "block" : "none")
                .Replace("{{ErrorMsgDetails}}", testCase.ErrorMsgDetails.Replace("<", "(").Replace(">", ")") ?? "");
        }

        public static async Task GeneratePdfAsync(string htmlFilePath, string outputPdfPath)
        {
            // string pdfFile = $"TestReport{DateTime.Now.ToString("yyyy-MM-dd HH.mm.ss.fffffff")}.pdf";

            if (string.IsNullOrWhiteSpace(htmlFilePath))
                throw new ArgumentException("HTML file path cannot be null or whitespace.", nameof(htmlFilePath));

            if (!File.Exists(htmlFilePath))
                throw new FileNotFoundException("The specified HTML file does not exist.", htmlFilePath);

            try
            {
                string chromeBinaryPath = GetChromeBinaryPath();
                if (string.IsNullOrEmpty(chromeBinaryPath) || !File.Exists(chromeBinaryPath))
                    throw new FileNotFoundException("Could not locate the Chrome binary.", chromeBinaryPath);

                var launchOptions = new LaunchOptions
                {
                    Headless = true,
                    ExecutablePath = chromeBinaryPath,
                    Args = new[]
                    {
                "--disable-web-security",
                "--allow-file-access-from-files",
                "--enable-local-file-accesses"
            }
                };

                await using var browser = await Puppeteer.LaunchAsync(launchOptions);
                await using var page = await browser.NewPageAsync();


                string fileUrl = new Uri(htmlFilePath).AbsoluteUri;
                await page.GoToAsync(fileUrl);


                await page.GoToAsync(fileUrl, new NavigationOptions
                {
                    WaitUntil = new[] { WaitUntilNavigation.Load, WaitUntilNavigation.Networkidle0 },
                    Timeout = 0,

                });


                await page.EmulateMediaTypeAsync(MediaType.Screen);


                await page.SetViewportAsync(new ViewPortOptions
                {
                    Width = 1280,
                    Height = 1024,
                    DeviceScaleFactor = 2
                });


                var pdfOptions = new PdfOptions
                {
                    Format = PaperFormat.A3,
                    PrintBackground = true,
                    MarginOptions = new MarginOptions
                    {
                        Top = "5mm",
                        Bottom = "5mm",
                        Left = "5mm",
                        Right = "5mm"
                    }
                };

                string timestamp = DateTime.Now.ToString("yy-MM-dd HH.mm.ss.FFFFFFF");

                await page.PdfAsync(outputPdfPath, pdfOptions);

                Console.WriteLine($"PDF successfully generated at: {outputPdfPath}");
            }
            catch (Exception ex)
            {
                Console.Error.WriteLine($"Error generating PDF: {ex}");
                throw;
            }
        }

        private static string GetChromeBinaryPath()
        {
            if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
            {
                // Try both "Program Files" and "Program Files (x86)"
                var windowsPaths = new[]
                {
                    @"C:\Program Files\Google\Chrome\Application\chrome.exe",
                    @"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe"
                };

                foreach (var path in windowsPaths)
                {
                    if (File.Exists(path))
                        return path;
                }
            }
            else if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
            {
                var linuxPaths = new[]
                {
                    "/usr/bin/google-chrome",
                    "/usr/bin/chromium-browser",
                    "/usr/bin/chromium"
                };

                foreach (var path in linuxPaths)
                {
                    if (File.Exists(path))
                        return path;
                }
            }
            else if (RuntimeInformation.IsOSPlatform(OSPlatform.OSX))
            {
                string macPath = "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome";
                if (File.Exists(macPath))
                    return macPath;
            }

            return string.Empty;
        }

        private static void buidTestCaseDetailsTable(
            TestCase_Results testCase,
            string TestCaseDetailsRowTemplate,
            string ModuleName,
            string SuiteName,
             FullTestResults testResults

            )
        {
            StringBuilder TestDataCellBuilder = new StringBuilder();

            TestCaseDetailsContent = new();
            var testData = testCase.TestData.Data;

            TestDataCellBuilder.Append("<td>\r\n");

            foreach (var item in testData)
            {

                // remove excludedTestData
                if (testResults.excludedTestData.Any(k => k.Equals(item.Key, StringComparison.OrdinalIgnoreCase)))
                    continue;

                TestDataCellBuilder.Append($"<div><strong>{item.Key}</strong>: {item.Value}</div>\r\n");
            }

            TestDataCellBuilder.Append("</td>\r\n");

            // build test Case Details 
            string statusStyle = testCase.Status switch
            {
                "Passed" => "color: green; font-weight: bold;",
                "Failed" => "color: red; font-weight: bold;",
                "Warning" => "color: orange; font-weight: bold;"
            };

            var lastStep = testCase.Steps.LastOrDefault();

            string screenshot = string.IsNullOrEmpty(lastStep?.ScreenshotUrlorPath) ? "" : $"<img class='test-step-image' style='height: 120px;' src='{lastStep.ScreenshotUrlorPath}' alt='Step Screenshot'>";

            TestCaseRowDetailsContent.Append(TestCaseDetailsRowTemplate)
                .Replace("{{counter}}", CounterTestCaseDetailsRow.ToString())
                .Replace("{{code}}", testCase.TestResultCode ?? "")
                .Replace("{{name}}", testCase.Name ?? "")
                .Replace("{{status}}", testCase.Status ?? "")
                .Replace("{{statusStyle}}", statusStyle)
                .Replace("{{duration}}", testCase.ExecutionTimeFromFirstStepInMS.Elapsed
                    .ToString(@"mm\:ss"))
                .Replace("{{notes}}", testCase.Status == "Passed" ? "_" : testCase.ErrorMsg ?? "-")
                .Replace("{{TestData}}", TestDataCellBuilder.ToString())
                .Replace("{{screenshot}}", testCase.Status == "Passed" ? "_" : screenshot);

            if (RowSpanModuleIndex == 0 && RowSpanSuiteIndex == 0)
            {
                //TestCaseRowDetailsContent.AppendLine($"<td rowspan=numberofmodule>{ModuleName}</td>");
                TestCaseRowDetailsContent.AppendLine($"<td rowspan=numberofsuite>{SuiteName}</td>");
            }
            TestCaseRowDetailsContent.AppendLine("</tr>");

            RowSpanModuleIndex++;
            RowSpanSuiteIndex++;


            CounterTestCaseDetailsRow++;

        }


    }
}
