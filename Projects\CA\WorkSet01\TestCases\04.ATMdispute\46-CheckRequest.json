{"TestCaseName": "Check ATM dispute request status", "TestCaseCode": "TC-46", "Environment": "Pilot", "Steps": [{"TestCaseReference": {"TestCasePath": "WorkSet01/TestCases/04.ATMdispute/ATMdispute.json", "ParamsReference": "WorkSet01/Params/ATMdisputeParams.json"}}, {"Name": "Click on ATM dispute", "Command": "Click", "Target": "{{Selectors.ATMdisputeButton}}"}, {"Name": "Click on dispute type", "Command": "Click", "Target": "{{Selectors.SelectDisputeType}}"}, {"Name": "Choose dispute type", "Command": "click", "Target": "{{Selectors.DisputeType}}"}, {"Name": "Click on Select Cards button", "Command": "Click", "Target": "{{Selectors.SelectButton}}"}, {"Name": "Choose a card with ATM transactions", "Command": "click", "Target": "{{Selectors.CardWithTransactions}}"}, {"Name": "Click on select a transaction", "Command": "click", "Target": "{{Selectors.SelectTransactionButton}}"}, {"Name": "Choose a transaction", "Command": "click", "Target": "{{Selectors.Transaction}}"}, {"Name": "Click on continue", "Command": "click", "Target": "{{Selectors.ContinueButtonPilot1}}"}, {"Name": "Enter the disputed ammount", "Command": "type", "Target": "{{Selectors.DisputedAmmountField}}", "Value": "0"}, {"Name": "Enter the dispute note", "Command": "type", "Target": "{{Selectors.DisputeNoteField}}", "Value": "This is a test for the feature by the dev team. Please ignore this request."}, {"Name": "Click on the agree terms checkbox", "Command": "click", "Target": "{{Selectors.TermsCheckbox}}"}, {"Name": "Click on continue button", "Command": "click", "Target": "{{Selectors.ContinueButtonPilot2}}"}, {"Name": "Click on confirm button", "Command": "click", "Target": "{{Selectors.ConfirmDispute}}"}, {"Name": "Click on Card Services", "Command": "Click", "Target": "{{Selectors.CardServices}}", "TargetsToCaptureDataFrom": [{"UniqueKey": "RequestID", "Selector": "{{Selectors.DisputeRequestID}}", "Regex": "Request Reference Number : (.*)"}]}, {"Name": "Click on request status", "Command": "assert", "Target": "{{Selectors.RequestStatues}}", "Value": "{{CapturedData.RequestID}}"}, {"Name": "Choose the submited request and match the request ID: {{CapturedData.RequestID}}", "Command": "Click", "Target": "xpath=//div[contains(@class, 'requestList')]//h4[contains(text(), '{{CapturedData.RequestID}}')]"}]}