{"TestCaseName": "Add foreign currency local beneficiary with valid data", "TestCaseCode": "TC-35", "Steps": [{"TestCaseReference": {"Name": "<PERSON><PERSON>", "TestCasePath": "WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json", "ParamsReference": "WorkSet01/Params/LoginTestCaseParams.json"}}, {"Name": "Click on change later button on the password expiration popup message", "Command": "Click", "Target": "{{Selectors.popupCancel}}", "ContinueOnError": true}, {"Name": "Click on biometric alert", "Command": "Click", "Target": "{{Selectors.BioMetricAlert}}", "ContinueOnError": true}, {"Name": "Execute JS code to activate token", "Command": "executescript", "Value": "Globals.ActivationState = 'ACTIVATED';"}, {"Name": "Click on Beneficiaries", "Command": "click", "Target": "{{Selectors.BeneficiariesToButt<PERSON>}}"}, {"Name": "Click on Local Transfer option", "Command": "Click", "Target": "{{Selectors.LocalTransfer}}"}, {"Name": "Click on Add Beneficiary", "Command": "Click", "Target": "{{Selectors.AddLocalBenBTn}}"}, {"Name": "Type the Nick Name", "Command": "Type", "Target": "{{Selectors.NickNameInput}}", "Value": "pelfest"}, {"Name": "Type the  Beneficiary Full Name", "Command": "Type", "Target": "{{Selectors.BeneficiaryFullNameInput}}", "Value": "testkktssd"}, {"Name": "select bank name", "Command": "click", "Target": "{{Selectors.BankNameSelect}}"}, {"Name": "Type account number", "Command": "Type", "Target": "{{Selectors.AccountNumberInput}}", "Value": "**********"}, {"Name": "select bank option", "Command": "click", "Target": "{{Selectors.BankOption}}"}, {"Name": "click on currency dropdown list", "Command": "click", "Target": "{{Selectors.currencySelect}}"}, {"Name": "select currency option ", "Command": "click", "Target": "{{Selectors.currencyoption}}"}, {"Name": "make sure that address input appear ", "Command": "appear", "Target": "{{Selectors.AddressInput}}"}, {"Name": "make sure that city input appear ", "Command": "appear", "Target": "{{Selectors.CityInput}}"}, {"Name": "make sure if Country input appear ", "Command": "appear", "Target": "{{Selectors.CountryInput}}"}, {"Name": "fill address input", "Command": "type", "Target": "{{Selectors.AddressInput}}", "Value": "30 st usa114"}, {"Name": "fill city input", "Command": "type", "Target": "{{Selectors.CityInput}}", "Value": "California44"}, {"Name": "select Country input", "Command": "click", "Target": "{{Selectors.CountryInput}}"}, {"Name": "select Country option", "Command": "click", "Target": "{{Selectors.countrtyoption}}"}, {"Name": "Click on continue", "Command": "click", "Target": "{{Selectors.SaveBtn}}"}, {"Name": "Click on continue", "Command": "click", "Target": "{{Selectors.SecondContinueBen}}"}, {"Name": "Enter Token number", "Command": "type", "Target": "{{Selectors.TokenInput}}", "Value": "123456"}, {"Name": "Click on confirm and show the confirmation page", "Command": "click", "Target": "{{Selectors.btnTokenConfirm}}"}, {"Name": "Appear : Success Screen", "Command": "appear", "Target": "css=#InnerHtmlwidget > div > div > div > div > div.success_text_content.back_white"}]}