
namespace TestAutomationFramework.Models
{
   
    public class AppSettings
    {
        public string[] WorkDirectories { get; set; } = [];
        public string[] TestSuiteFolderPaths { get; set; }
        public Dictionary<string, string> BaseUrls { get; set; }
        //public string ResultsPath { get; set; }
        public string LoadingSpinnerTargetSelector { get; set; } = "id=LoadingSpinner";
        public int GeneralWaitTimeForSpinnerInMilliseconds { get; set; } = 60000;
        public int DelayBeforeStepExecustionInMilliseconds { get; set; }
        public int ImplicitWaitInSeconds { get; set; } = 30;
        public int PageLoadTimeoutInSeconds { get; set; } = 60;
        public int ScriptTimeoutInSeconds { get; set; } = 30;
        public string GlobalParamsPath { get; set; }
        public string ExcludedTestDataPath { get; set; }
        public string OutputDir { get; set; }
        public MailSettings MailSettings { get; set; }
    }

    public class ReportSettings
    {
        public bool IncludeScreenshots { get; set; }
        public bool GenerateSummaryReport { get; set; } = true;
        public bool GenerateSummaryReportWithoutDetails { get; set; } = true;
        public bool GenerateIndividualReports { get; set; } = true;
        public bool HideEvidence { get; set; } = false;
        public bool HidePassedTestCases { get; set; } = false;
        public bool HideFailedTestCases { get; set; } = false;
    }

    public class RunningInstructions
    {
        // Running environment and browser settings
        public string RunningEnvironment { get; set; }
        public bool MobileView { get; set; }
        public string[] RunningBrowsers { get; set; }

        public int RetryCount { get; set; }
        // Report generation options
        public ReportSettings ReportSettings { get; set; }

        public FilteringOptions FilteringOptions { get; set; }
    }

    public class FilteringOptions
    {
        public string[] TestCasesLabelFilteringRegex { get; set; }
        public string[] TestCasesOwnerFilteringRegex { get; set; }
        public string[] SuitesFilteringRegex { get; set; }
    }

    public class MailSettings
    {
        public string smtpServer { get; set; } = "mail.ebseg.com";
        public int smtpPort { get; set; } = 587;
        public string smtpUsername { get; set; } 
        public string smtpPassword { get; set; }
        public string[] recipients { get; set; }
    }
}
