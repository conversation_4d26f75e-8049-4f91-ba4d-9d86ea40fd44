{"TestCaseName": "Add new beneficiary account", "TestCaseCode": "TC-255", "Steps": [{"TestCaseReference": {"Name": "<PERSON><PERSON>", "TestCasePath": "WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json", "ParamsReference": "WorkSet01/Params/LoginTestCaseParams.json"}}, {"Name": "Click on change later button on the password expiration popup message", "Command": "Click", "Target": "{{Selectors.popupCancel}}", "ContinueOnError": true}, {"Name": "Click on biometric alert", "Command": "Click", "Target": "{{Selectors.BioMetricAlert}}", "ContinueOnError": true}, {"Name": "Execute JS code to activate token", "Command": "executescript", "Value": "Globals.ActivationState = 'ACTIVATED';"}, {"Name": "Click on Beneficiaries", "Command": "click", "Target": "{{Selectors.BeneficiariesToButt<PERSON>}}"}, {"Name": "Click on Other CAE Accounts", "Command": "Click", "Target": "{{Selectors.OtherCAEAccounts}}"}, {"Name": "Click on Add Beneficiary", "Command": "Click", "Target": "{{Selectors.AddNewBeneficiaryButtonOtheCAEaccounts}}"}, {"Name": "Type the Nickname", "Command": "Type", "Target": "{{Selectors.BeneficiaryNameTextbox}}", "Value": "{{TestData.Beneficiary_Nickname}}"}, {"Name": "Type Account number", "Command": "type", "Target": "{{Selectors.BeneficiaryAccountNumber}}", "Value": "{{TestData.Beneficiary_Account_Number}} "}, {"Name": "Click on Save button", "Command": "click", "Target": "{{Select<PERSON>.<PERSON>}}"}, {"Name": "Click on continue", "Command": "click", "Target": "{{Selectors.ContinueToSaveBeneficiary}}"}, {"Name": "Enter Token number", "Command": "type", "Target": "{{Selectors.TokenInput}}", "Value": "{{TestData.Token_Number}}"}, {"Name": "Click on confirm and show the confirmation page", "Command": "click", "Target": "{{Selectors.btnTokenConfirm}}"}, {"Name": "Appear : Success Screen", "Command": "appear", "Target": "css=#InnerHtmlwidget > div > div > div > div > div.success_text_content.back_white"}]}