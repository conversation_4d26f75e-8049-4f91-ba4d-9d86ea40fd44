{"TestCaseName": "Unblock Card with invalid token", "TestCaseCode": "TC-54", "Steps": [{"TestCaseReference": {"Name": "<PERSON><PERSON>", "TestCasePath": "WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json", "ParamsReference": "WorkSet01/Params/LoginTestCaseParams.json"}}, {"Name": "Click on change later button on the password expiration popup message", "Command": "Click", "Target": "{{Selectors.popupCancel}}"}, {"Name": "Click on biometric alert", "Command": "Click", "Target": "{{Selectors.BioMetricAlert}}", "ContinueOnError": true, "CustomDelayBeforeStepExecustionInMilliseconds": 1500}, {"Name": "Click on Cards button from side menu", "Command": "Click", "Target": "{{Selectors.CardsButton}}"}, {"Name": "Click on Cards Services button from side menu", "Command": "Click", "Target": "{{Selectors.CardServices}}"}, {"Name": "Click on UnBlock Btn", "Command": "Click", "Target": "{{Selectors.UnBlockBtn}}"}, {"Name": "Click on Blocked Card", "Command": "Click", "Target": "{{Selectors.BlockedCard}}"}, {"Name": "Click on Agree Check Box", "Command": "Click", "Target": "{{Selectors.AgreeCheckBox}}"}, {"Name": "Click on Continue", "Command": "Click", "Target": "{{Selectors.ContinueBtn}}"}, {"Name": "Type Wrong Token", "Command": "type", "Target": "{{Selectors.TokenInput}}", "value": "000000"}, {"Name": "Click On Confirm", "Command": "click", "Target": "{{Selectors.ConfirmBtn}}"}, {"Name": "Assert error MSG: Invalid authentication code", "Command": "assert", "Target": "{{Selectors.popupMessage}}", "value": "Invalid authentication code"}]}