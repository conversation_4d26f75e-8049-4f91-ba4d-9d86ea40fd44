{"TestCaseName": "Activate Card, check that inactive card in backend are displayed", "TestCaseCode": "TC-121", "Steps": [{"TestCaseReference": {"Name": "<PERSON><PERSON>", "TestCasePath": "WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json", "ParamsReference": "WorkSet01/Params/LoginTestCaseParams.json", "Params": {"TestData": {"UserName": "mahmoudsayed022", "Password": "Password1", "ChallengeAnswer": "bmw"}}}}, {"Name": "Click on change later button on the password expiration popup message", "Command": "Click", "Target": "{{Selectors.popupCancel}}", "ContinueOnError": true}, {"Name": "Click on biometric alert", "Command": "Click", "Target": "{{Selectors.BioMetricAlert}}", "ContinueOnError": true}, {"Name": "Click on Cards button from side menu", "Command": "Click", "Target": "{{Selectors.CardsButton}}"}, {"Name": "Click on Cards Services button from side menu", "Command": "Click", "Target": "{{Selectors.CardServices}}"}, {"Name": "Click on Activate Card and show the cards list", "Command": "Click", "Target": "{{Selectors.ActivateCard}}"}, {"Name": "Check if the cards list appear", "Command": "appear", "Target": "{{Selectors.CardList}}", "ContinueOnError": true}]}