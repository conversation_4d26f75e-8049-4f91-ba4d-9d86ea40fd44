{"format": 1, "restore": {"D:\\TFS\\DevWork\\R and D Work\\TestAutomationFramework\\TestAutomationFramework\\TestAutomationFramework.csproj": {}}, "projects": {"D:\\TFS\\DevWork\\R and D Work\\TestAutomationFramework\\TestAutomationFramework\\TestAutomationFramework.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\TFS\\DevWork\\R and D Work\\TestAutomationFramework\\TestAutomationFramework\\TestAutomationFramework.csproj", "projectName": "TestAutomationFramework", "projectPath": "D:\\TFS\\DevWork\\R and D Work\\TestAutomationFramework\\TestAutomationFramework\\TestAutomationFramework.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\TFS\\DevWork\\R and D Work\\TestAutomationFramework\\TestAutomationFramework\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.Extensions.Configuration": {"target": "Package", "version": "[9.0.8, )"}, "Microsoft.Extensions.Configuration.Binder": {"target": "Package", "version": "[9.0.8, )"}, "Microsoft.Extensions.Configuration.FileExtensions": {"target": "Package", "version": "[9.0.8, )"}, "Microsoft.Extensions.Configuration.Json": {"target": "Package", "version": "[9.0.8, )"}, "Microsoft.NET.Test.Sdk": {"target": "Package", "version": "[17.12.0, )"}, "NUnit": {"target": "Package", "version": "[4.3.2, )"}, "NUnit3TestAdapter": {"target": "Package", "version": "[4.6.0, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "PuppeteerSharp": {"target": "Package", "version": "[20.2.2, )"}, "Selenium.Support": {"target": "Package", "version": "[4.28.0, )"}, "Selenium.WebDriver": {"target": "Package", "version": "[4.28.0, )"}, "Selenium.WebDriver.ChromeDriver": {"target": "Package", "version": "[132.0.6834.8300, )"}, "SeleniumExtras.WaitHelpers": {"target": "Package", "version": "[1.0.2, )"}, "WebDriverManager": {"target": "Package", "version": "[2.17.5, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}}}}