﻿using System.Text.Json;
using TestAutomationFramework.Models;

namespace TestAutomationFramework.Helpers
{
    public static class ConditionEvaluator
    {


        public static bool Evaluate(Condition condition, IDictionary<string, object> context)
        {
            try
            {
                if (condition.AnyOf != null)
                    return condition.AnyOf.Any(c => Evaluate(c, context));

                if (condition.AllOf != null)
                    return condition.AllOf.All(c => Evaluate(c, context));

                if (condition.Not != null)
                    return !Evaluate(condition.Not, context);

                if (condition.Predicate != null)
                    return EvaluatePredicate(condition.Predicate, context);

                if (condition.Reference != null)
                    return EvaluateReference(condition.Reference, context);

                Logger.Log("Condition was empty or unsupported.", LogLevel.Warn);
                return false;
            }
            catch (Exception ex)
            {
                Logger.Log($"Error evaluating condition: {ex}", LogLevel.Error);
                return false;
            }
        }

        private static bool EvaluatePredicate(PredicateCondition predicate, IDictionary<string, object> context)
        {
            object? valueFromContext = null;
            // Check if the context contains the specified key
            if (context.TryGetValue(predicate.Context, out var ctxObj))
            {
                // If a field is specified, try to get the value from the context object

                if (predicate.Field != null && ctxObj is IDictionary<string, object> dict)
                {
                    dict.TryGetValue(predicate.Field, out valueFromContext);
                }
                else
                {
                    valueFromContext = ctxObj;
                }
            }

            var expected = predicate.Value;
            var actual = valueFromContext?.ToString();

            return predicate.Operator switch
            {
                "Equals" => actual == expected,
                "NotEquals" => actual != expected,
                "Contains" => actual != null && actual.Contains(expected ?? "", StringComparison.OrdinalIgnoreCase),
                "NotContains" => actual != null && !actual.Contains(expected ?? "", StringComparison.OrdinalIgnoreCase),
                _ => throw new InvalidOperationException($"Unsupported operator: {predicate.Operator}")
            };
        }

        private static bool EvaluateReference(ConditionReference reference, IDictionary<string, object> context)
        {
            var filePath = Path.Combine("Conditions", reference.Ref);

            if (!File.Exists(filePath))
                throw new FileNotFoundException($"Condition file not found: {filePath}");

            var json = File.ReadAllText(filePath);
            var condition = JsonSerializer.Deserialize<Condition>(json);

            if (condition == null)
                throw new InvalidOperationException($"Failed to deserialize condition file: {filePath}");

            // replace {{param}} placeholders
            if (reference.Params != null)
            {
                ReplaceParams(condition, reference.Params);
            }

            return Evaluate(condition, context);
        }

        private static void ReplaceParams(Condition condition, Dictionary<string, string> parameters)
        {
            if (condition.Predicate != null && condition.Predicate.Value != null)
            {
                foreach (var kv in parameters)
                {
                    condition.Predicate.Value = condition.Predicate.Value
                        .Replace($"{{{{{kv.Key}}}}}", kv.Value);
                }
            }

            if (condition.AnyOf != null)
                foreach (var c in condition.AnyOf) ReplaceParams(c, parameters);

            if (condition.AllOf != null)
                foreach (var c in condition.AllOf) ReplaceParams(c, parameters);

            if (condition.Not != null)
                ReplaceParams(condition.Not, parameters);
        }
    }

}
