{"ModuleName": "ATM dispute", "TestCases": [{"TestCasePath": "WorkSet01/TestCases/04.ATMdispute/43-CompleteRequest.json", "ParamsReference": "WorkSet01/Params/ATMdisputeParams.json"}, {"TestCasePath": "WorkSet01/TestCases/04.ATMdispute/128-ATM Dispute, click View Details.json", "ParamsReference": "WorkSet01/Params/ATMdisputeParams.json"}, {"TestCasePath": "WorkSet01/TestCases/04.ATMdispute/46-CheckRequest.json", "ParamsReference": "WorkSet01/Params/ATMdisputeParams.json"}, {"TestCasePath": "WorkSet01/TestCases/04.ATMdispute/Request Status/TC-435 Ensure that Dispute Type displayed.json", "ParamsReference": "WorkSet01/Params/CardServicesParams.json"}, {"TestCasePath": "WorkSet01/TestCases/04.ATMdispute/Request Status/TC-436 Ensure that card number displayed .json", "ParamsReference": "WorkSet01/Params/CardServicesParams.json"}, {"TestCasePath": "WorkSet01/TestCases/04.ATMdispute/Request Status/TC-437 Ensure that ATM Location displayed.json", "ParamsReference": "WorkSet01/Params/CardServicesParams.json"}, {"TestCasePath": "WorkSet01/TestCases/04.ATMdispute/Request Status/TC-438 Ensure that BankATM displayed.json", "ParamsReference": "WorkSet01/Params/CardServicesParams.json"}, {"TestCasePath": "WorkSet01/TestCases/04.ATMdispute/Request Status/TC-439 Ensure that Bank Name displayed.json", "ParamsReference": "WorkSet01/Params/CardServicesParams.json"}, {"TestCasePath": "WorkSet01/TestCases/04.ATMdispute/Request Status/TC-440 Ensure that Reference Number displayed.json", "ParamsReference": "WorkSet01/Params/CardServicesParams.json"}, {"TestCasePath": "WorkSet01/TestCases/04.ATMdispute/Request Status/TC-441 Ensure that Disputed amount displayed.json", "ParamsReference": "WorkSet01/Params/CardServicesParams.json"}, {"TestCasePath": "WorkSet01/TestCases/04.ATMdispute/Request Status/TC-442 Ensure that Transaction Date displayed.json", "ParamsReference": "WorkSet01/Params/CardServicesParams.json"}, {"TestCasePath": "WorkSet01/TestCases/04.ATMdispute/Request Status/TC-443 Ensure that Posting Date displayed.json", "ParamsReference": "WorkSet01/Params/CardServicesParams.json"}, {"TestCasePath": "WorkSet01/TestCases/04.ATMdispute/Request Status/TC-444 Ensure that Notes displayed .json", "ParamsReference": "WorkSet01/Params/CardServicesParams.json"}, {"TestCasePath": "WorkSet01/TestCases/04.ATMdispute/Request Status/TC-430 Ensure that all request displayed Dispute Request.json", "ParamsReference": "WorkSet01/Params/CardServicesParams.json"}, {"TestCasePath": "WorkSet01/TestCases/04.ATMdispute/Request Status/TC-431 Ensure that Request ID displayed.json", "ParamsReference": "WorkSet01/Params/CardServicesParams.json"}, {"TestCasePath": "WorkSet01/TestCases/04.ATMdispute/Request Status/TC-432 Ensure that Request Type displayed.json", "ParamsReference": "WorkSet01/Params/CardServicesParams.json"}, {"TestCasePath": "WorkSet01/TestCases/04.ATMdispute/Request Status/TC-433 Ensure that Status displayed.json", "ParamsReference": "WorkSet01/Params/CardServicesParams.json"}, {"TestCasePath": "WorkSet01/TestCases/04.ATMdispute/Request Status/TC-434 Ensure that  Service Request Number displayed .json", "ParamsReference": "WorkSet01/Params/CardServicesParams.json"}]}