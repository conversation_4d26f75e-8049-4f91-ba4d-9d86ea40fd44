{"TestCaseName": "Add beneficiary without full name", "TestCaseCode": "TC-37", "Steps": [{"TestCaseReference": {"Name": "<PERSON><PERSON>", "TestCasePath": "WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json", "ParamsReference": "WorkSet01/Params/LoginTestCaseParams.json"}}, {"Name": "Click on change later button on the password expiration popup message", "Command": "Click", "Target": "{{Selectors.popupCancel}}", "ContinueOnError": true}, {"Name": "Click on biometric alert", "Command": "Click", "Target": "{{Selectors.BioMetricAlert}}", "ContinueOnError": true}, {"Name": "Execute JS code to activate token", "Command": "executescript", "Value": "Globals.ActivationState = 'ACTIVATED';"}, {"Name": "Click on Beneficiaries", "Command": "click", "Target": "{{Selectors.BeneficiariesToButt<PERSON>}}"}, {"Name": "Click on Local Transfer option", "Command": "Click", "Target": "{{Selectors.LocalTransfer}}"}, {"Name": "Click on Add Beneficiary", "Command": "Click", "Target": "{{Selectors.AddLocalBenBTn}}"}, {"Name": "Click on Add save", "Command": "click", "Target": "{{Selectors.SaveBtn}}"}, {"Name": "Assert hint error message with value: Please enter Beneficiary Name.", "Command": "assert", "Target": "{{Selectors.FullNameMSG}}", "value": "Please enter Beneficiary Name."}]}