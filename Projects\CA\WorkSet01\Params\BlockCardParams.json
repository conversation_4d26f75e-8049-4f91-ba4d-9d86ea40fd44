{"Selectors": {"CardsButton": "xpath=//div[@class='mainmenuitems']//label[contains(text(), 'Cards')]", "CardServices": "xpath=//div[@class='submenuitem']//label[contains(text(), 'Card services')]", "SorryPOPup": "id=popup_ok", "DirectDebitBtn": "id=DirectDebit", "CardSelectedinDirectDebit": "xpath=//div[@class='module_list_container cardData']/*[1]", "SelectAccountBtn": "id=SelectAccountBtn", "DebitAccount": "xpath=//div[@class='module_list_container']//h4[contains(text(), '**************')]", "DebitedPercentageSelect": "css=#CardsSelectionErrorHandle > div.select", "DebitedPercentageSelectOption": "css=#CardsSelectionErrorHandle > div.options.back_white_solid.dropdown-active.d-block > span:nth-child(1)", "AgreeCheckBoxinDebit": "css=#wginsDirectDebit_Default > div > div.section_body > div > div > div.terms.AgreeTermsAndConditions > input[type=checkbox]", "UnblockCard": "id=UnblockCard", "CardInquiry": "xpath=//div[@class='submenuitem']//label[contains(text(), 'Card Inquiry')]", "SelectCardButton": "id=SelectCardBtn", "SubmitDirectDebitReq": "id=SubmitDirectDebitReq", "DirectDebitConfirm": "id=DirectDebitConfirm", "TokenWidgetTitle": "id=TokenWidgetTitle", "BlockCardBtn": "id=BlockCard", "NormalCard": "xpath=//div[@class='module_list_container cardData']/*[1]", "SelectBlockCardReason": "id=SelectBlockCardReason", "BlockCardBtnSubmit": "id=SubmitBlockCardReq", "BlockCardConfirm": "id=BlockCardConfirm", "BlockedCard": "css=#CardsSelectionErrorHandle > div", "AgreeCheckBox": "css=#wginsUnblockCard_Default > div > div > div > div.details_items_container.back_white > div.terms.AgreeTermsAndConditions > input[type=checkbox]", "FillFieldMSG": "css=#popup_message", "ContinueBtn": "id=SubmitUnblockCardReq", "TokenInput": "id=TokenNUMBER", "ConfirmBtn": "id=btnTokenConfirm", "btnTokenConfirm": "id=btnTokenConfirm", "WrongMSG": "css=#ENTRUST > div > div.section_body > div > div.input_group.input-validation > span", "BioMetricAlert": "css=#popbuttons > div > button.btn.Cancel.back_gradient2", "KYCskip": "css=#popbuttons > div > button.btn.Cancel.back_gradient2", "CardToBlockFromInquiry": "xpath=//div[@id='CardsListWorking']//h3[contains(text(), '4023XXXXXXXX0007')]", "CardListClass": "css=#CardsSelectionErrorHandle > div", "ConfirmCardBlockButton": "id=BlockCardConfirm", "CardFromList": "css=#CardsSelectionErrorHandle > div > div", "LatestRequest": "id=RequestItem0", "BlockCardOption": "id=BlockCard", "BlockCardOptionInquiry": "id=Stopbtn", "BlockCardButton": "id=SubmitBlockCardReq", "BlockReasonDroplist": "id=SelectBlockCardReason", "NoTransactionMesseage": "No ATM transactions were found for the selected card", "SelectCardToBlockMessage": "Please, Select card!", "SelectReasonToBlockMessage": "Please, Select reason for Block card!", "popupCancel": "id=popup_cancel", "popupMessage": "id=popup_message", "popupOk": "id=popup_ok", "UnBlockBtn": "id=UnblockCard", "BlocCard": "xpath=/html/body/div[2]/div[2]/div/div/div[4]/div[1]/div/div[2]/div/div/div/div[2]/div/div[2]/div", "cards": "xpath=/html/body/div[2]/div[2]/div/div/div[2]/div/div[3]/div/div/div/div[2]/div/div[3]/div[2]"}, "TestData": {"Token_Number": "123456"}, "SpecialEnvParams": {"Pilot": {"Selectors": {"CardToBlockFromInquiry": "xpath=//div[@id='CardsListWorking']//h3[contains(text(), '4603XXXXXXXX9621')]"}}, "UAT": {"Selectors": {}}}}