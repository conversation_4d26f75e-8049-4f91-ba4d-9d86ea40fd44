{"TestCaseName": "Redeem Online - input points more than the available", "TestCaseCode": "TC-157", "Environment": "Pilot", "Steps": [{"TestCaseReference": {"Name": "<PERSON><PERSON>", "TestCasePath": "WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json", "ParamsReference": "WorkSet01/Params/LoginTestCaseParams.json"}}, {"Name": "Click on Happy points option from side menu", "Command": "Click", "Target": "{{Selectors.HappyPointsOption}}"}, {"Name": "Click on Redeem Online button", "Command": "Click", "Target": "{{Selectors.RedeemOnline}}"}, {"Name": "Choose a merchant", "Command": "Click", "Target": "{{Selectors.SelectMerchant}}"}, {"Name": "Click Redeem button", "Command": "Click", "Target": "{{Select<PERSON>.<PERSON>eem<PERSON>}}"}, {"Name": "Enter the number of points to redeem", "Command": "Type", "Target": "{{Selectors.PointsTextbox}}", "Value": "50000"}, {"Name": "Click on create cupon button and show the error message", "Command": "Click", "Target": "{{Selectors.CreateCupon}}"}]}