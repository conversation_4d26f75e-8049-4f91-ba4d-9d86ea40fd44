<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Test Execution Report</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <div class="header">
            <img src="D:\TFS\DevWork\R and D Work\TestAutomationFramework\TestAutomationFramework\Projects\CA\Templates/CA_Logo.png" alt="Company Logo">
            <h1 {{HideForSingleTestExecutionFlag}}>Beneficiaries Suite Execution Report</h1>
            <p>Generated on: <strong>2025-08-14 11:02 AM</strong> | Environment: <strong>UAT</strong> | Browser: <strong>Chrome</strong> | Duration:<strong>00:00:48</strong></p>
        </div>
        <h2 {{HideForSingleTestExecutionFlag}}>Overall Summary</h2>
<table  class="summary-table overall-summary" {{HideForSingleTestExecutionFlag}}>
    <tr>
        <th>Total Suites</th>
        <th>Total Modules</th>
        <th>Total Test Cases</th>
        <!-- <th>Total Steps</th>-->
        <th>Passed</th>
        <th>Failed</th>
        <th>Total Duration</th>

        <!-- <th>Skipped</th>-->
    </tr>
    <tr>
        <td>1</td>
        <td>0</td>
        <td>2</td>
        <!--<td>0</td>-->
        <td>0</td>
        <td>2</td>
        <!-- <td>{{TotalSkipped}}</td>-->
         <td>00:00:48</td>

    </tr>
</table>

		<table border="1" cellpadding="5" cellspacing="0" style="border-collapse: collapse; width: 100%;">
    <thead style="background-color: #00796B; color: white;">
        <tr>
            <th>#</th>
            <th>Test Case ID</th>
            <th>Test Case Name</th>
            <th>Status</th>
            <th>Duration</th>
            <th>TestData</th>
            <th>Error/Remarks</th>
            <th>Screenshot</th>
            <th>Suite</th>

        </tr>
    </thead>
    <tbody>
        <!-- TestCaseDetailRow -->
<tr>
    <td>1</td>
    <td>TC-145</td>
    <td>Existing Banks Inside Egypt beneficiaries displayed</td>
    <td style="color: red; font-weight: bold;">Failed</td>
    <td>00:23</td>
    <td>
<div><strong>UserName</strong>: mahmoudsayed022</div>
<div><strong>ChallengeAnswer</strong>: bmw</div>
</td>

    <td>Error executing step: Click on Continue Button.</td>
    <td><img class='test-step-image' src='D:\TFS\DevWork\R and D Work\TestAutomationFramework\TestAutomationFramework\Projects\CA\Results\ScreenShots\Beneficiaries\2025-08-14 11.02.00.732_Type the User Name.png' alt='Step Screenshot'></td>
<td rowspan=2>Beneficiaries</td>
</tr>
<!-- TestCaseDetailRow -->
<tr>
    <td>2</td>
    <td>TC-146</td>
    <td>Edit Existing beneficiary</td>
    <td style="color: red; font-weight: bold;">Failed</td>
    <td>00:25</td>
    <td>
<div><strong>UserName</strong>: sherif1234</div>
<div><strong>ChallengeAnswer</strong>: bmw</div>
</td>

    <td>Error executing step: Type the Password.</td>
    <td><img class='test-step-image' src='D:\TFS\DevWork\R and D Work\TestAutomationFramework\TestAutomationFramework\Projects\CA\Results\ScreenShots\Beneficiaries\2025-08-14 11.02.02.860_Click on Continue Button.png' alt='Step Screenshot'></td>
</tr>

    </tbody>
</table>
        
    </div>
    <div class="footer">
        <p>End of Report</p>
    </div>
</body>
</html>
