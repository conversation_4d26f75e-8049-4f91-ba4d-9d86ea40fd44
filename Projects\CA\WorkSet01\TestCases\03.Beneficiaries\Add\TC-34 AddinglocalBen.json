{"TestCaseName": "Add local beneficiary with valid data ", "TestCaseCode": "TC-34", "Steps": [{"TestCaseReference": {"Name": "<PERSON><PERSON>", "TestCasePath": "WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json", "ParamsReference": "WorkSet01/Params/LoginTestCaseParams.json"}}, {"Name": "Click on change later button on the password expiration popup message", "Command": "Click", "Target": "{{Selectors.popupCancel}}", "ContinueOnError": true}, {"Name": "Click on biometric alert", "Command": "Click", "Target": "{{Selectors.BioMetricAlert}}", "ContinueOnError": true}, {"Name": "Execute JS code to activate token", "Command": "executescript", "Value": "Globals.ActivationState = 'ACTIVATED';"}, {"Name": "Click on Beneficiaries", "Command": "click", "Target": "{{Selectors.BeneficiariesToButt<PERSON>}}"}, {"Name": "Click on Local Transfer option", "Command": "Click", "Target": "{{Selectors.LocalTransfer}}"}, {"Name": "Click on Add Beneficiary", "Command": "Click", "Target": "{{Selectors.AddLocalBenBTn}}"}, {"Name": "Type Account number", "Command": "type", "Target": "{{Selectors.AccountNumberInput}}", "Value": "{{TestData.Account_number}}"}, {"Name": "Type the Nick Name", "Command": "Type", "Target": "{{Selectors.NickNameInput}}", "Value": "{{TestData.<PERSON>_Name_For_Add}}"}, {"Name": "Type the  Beneficiary Full Name", "Command": "Type", "Target": "{{Selectors.BeneficiaryFullNameInput}}", "Value": "{{TestData.Beneficiary_FullName}}"}, {"Name": "select bank name", "Command": "click", "Target": "{{Selectors.BankNameSelect}}"}, {"Name": "select bank option", "Command": "click", "Target": "{{Selectors.BankOption}}"}, {"Name": "click on currency dropdown list", "Command": "click", "Target": "{{Selectors.currencySelect}}"}, {"Name": "select currency option ", "Command": "click", "Target": "{{Selectors.currencyoptionEGP}}"}, {"Name": "Click on continue", "Command": "click", "Target": "{{Selectors.SaveBtn}}"}, {"Name": "Click on continue", "Command": "click", "Target": "{{Selectors.SecondContinueBen}}"}, {"Name": "Enter Token number", "Command": "type", "Target": "{{Selectors.TokenInput}}", "Value": "{{TestData.Token_Number}}"}, {"Name": "Click on confirm and show the confirmation page", "Command": "click", "Target": "{{Selectors.btnTokenConfirm}}"}, {"Name": "Appear : Success Screen", "Command": "appear", "Target": "css=#InnerHtmlwidget > div > div > div > div > div.success_text_content.back_white"}]}