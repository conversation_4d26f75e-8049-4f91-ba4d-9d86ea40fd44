{"TestCaseName": "Incorrect otp prevents card activation", "TestCaseCode": "TC-58", "Steps": [{"TestCaseReference": {"Name": "<PERSON><PERSON>", "TestCasePath": "WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json", "ParamsReference": "WorkSet01/Params/LoginTestCaseParams.json"}}, {"Name": "Click on change later button on the password expiration popup message", "Command": "Click", "Target": "{{Selectors.popupCancel}}", "ContinueOnError": true}, {"Name": "Click on biometric alert", "Command": "Click", "Target": "{{Selectors.BioMetricAlert}}", "ContinueOnError": true}, {"Name": "Click on Cards button from side menu", "Command": "Click", "Target": "{{Selectors.CardsButton}}"}, {"Name": "Click on Cards Services button from side menu", "Command": "Click", "Target": "{{Selectors.CardServices}}"}, {"Name": "Click on Activate Card", "Command": "Click", "Target": "{{Selectors.ActivateCard}}"}, {"Name": "Click on card that need to be  Activated", "Command": "Click", "Target": "xpath=//*[normalize-space(text())='4219xxxxxxxx3453']"}, {"Name": "Click on Continue", "Command": "Click", "Target": "{{Selectors.ContinueBtn}}"}, {"Name": "Enter a wrong OTP", "Command": "type", "Target": "{{Selectors.O<PERSON>field}}", "Value": "000000"}, {"Name": "Enter new PIN", "Command": "type", "Target": "{{Selectors.NewPI<PERSON>field}}", "Value": "1234"}, {"Name": "Confirm new PIN", "Command": "type", "Target": "{{Selectors.ConfirmPINfield}}", "Value": "1234"}, {"Name": "Click on Continue", "Command": "Click", "Target": "{{Selectors.ContinueBtn2}}"}, {"Name": "Assert popup error message with value: Wrong activation code", "Command": "Verify", "Target": "{{Selectors.popupMessage}}", "Value": "Wrong activation code"}]}