{"TestCaseName": " Ensure that all request displayed -Dispute Request-", "TestCaseCode": "TC-430", "Environment": "Pilot", "Steps": [{"TestCaseReference": {"Name": "<PERSON><PERSON>", "TestCasePath": "WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json", "ParamsReference": "WorkSet01/Params/LoginTestCaseParams.json"}}, {"Name": "Click on Cards option on the side menu", "Command": "Click", "Target": "{{Selectors.CardsButton}}"}, {"Name": "Click on Card Services", "Command": "Click", "Target": "{{Selectors.CardServices}}"}, {"Name": "Click on request status and Ensure that all request displayed", "Command": "click", "Target": "{{Selectors.RequestStatues}}", "ElementToValidateThatScreenLoaded": "css=#RequestItem1"}]}