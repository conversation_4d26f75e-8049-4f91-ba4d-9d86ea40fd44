{"TestCaseName": "Eligible Customer confirmation page Verify  Data Display", "TestCaseCode": "TC-544", "ModuleName": "eKYC", "Labels": ["eKYC"], "Steps": [{"TestCaseReference": {"Name": "<PERSON><PERSON>", "TestCasePath": "MainWorkSet/TestCases/000.CommonTestCasesSteps/LoginTestCase.json", "ParamsReference": "MainWorkSet/Params/LoginTestCaseParams.json", "Params": {"TestData": {"UserName": "Sherif1234", "Password": "Password1", "ChallengeAnswer": "bmw"}, "SpecialEnvParams": {"Pilot": {"TestData": {"UserName": "Mm_Azmy", "Password": "Asdf2025", "ChallengeAnswer": "armada"}}}}}}, {"Name": "Click on KYC Update", "Command": "Click", "Target": "{{Selectors.KYCupdate}}"}, {"Name": "Choose Option No in Do you have any changes", "Command": "Click", "Target": "{{Selectors.ChangesCheckBoxNOoption}}"}, {"Name": "Choose Option No in FATCA", "Command": "Click", "Target": "{{Selectors.FATCANOoption}}"}, {"Name": "Click on I Accept The Terms And Conditions", "Command": "Click", "Target": "{{Selectors.TermsAndConditionsCheckBox}}"}, {"Name": "Click On Continue", "Command": "Click", "Target": "{{Selectors.ContinueBTN}}"}, {"Name": "Verify that Data Display Correct", "Command": "Verify", "Target": "{{Selectors.PersonalDataText}}", "Value": "{{TestData.PersonalDataText}}"}]}