{"TestCaseName": "Refute Cards List", "TestCaseCode": "TC-51", "Steps": [{"TestCaseReference": {"Name": "Block Card", "TestCasePath": "WorkSet01/TestCases/012.CardServices/BlockCard/BlockCard.json", "ParamsReference": "WorkSet01/Params/BlockCardParams.json"}}, {"Name": "Click on Select Card Button", "Command": "Click", "Target": "{{Selectors.SelectCardButton}}"}, {"Name": "<PERSON><PERSON>ose a card from the cards list", "Command": "Click", "Target": "{{Selectors.CardFromList}}"}, {"Name": "Check cards list has all the cards", "Command": "refute", "Target": "{{Selectors.CardFromList}}", "Value": "4603XXXXXXXX8482"}]}