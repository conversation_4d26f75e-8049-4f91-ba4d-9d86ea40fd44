{"TestSuiteFolderPath": "FoldersAndFilesStructureSample/TestSuites", "Browser": ["Chrome"], "Environment": "UAT", "GenerateSummaryReport": true, "GenerateIndividualReports": true, "HideEvidence": false, "HideFailedTestCases": false, "HidePassedTestCases": false, "BaseUrls": {"UAT": "https://digitalbanking.ebseg.com/DigitalBankingCustom2/eBankApplication/ePortal5Core/ePortal5.htm?Menu=New#/EN/Landing", "Pilot": "https://ebs.ca-egypt.com/OmniChannel/CEEPDigitalBankR6/eBankApplication/ePortal5Core/ePortal5.htm?Menu=New#/EN/Landing"}, "ReportSettings": {"IncludeScreenshots": true, "OutputFolder": "Results/Reports"}, "LoadingSpinnerTargetSelector": "id=LoadingSpinner", "GeneralWaitTimeForSpinnerInMilliseconds": 90000, "DelayBeforeStepExecustionInMilliseconds": 500, "PageLoadTimeoutInSeconds": 90, "GlobalParams": "FoldersAndFilesStructureSample/Params/GlobalParams.json", "ExcludedTestDataPath": "FoldersAndFilesStructureSample/TestData/ExcludedTestData.json", "MobileView": false, "MailSettings": {"smtpServer": "mail.ebseg.com", "smtpPort": 587, "smtpUsername": "<EMAIL>", "smtpPassword": "eBSEG@12345", "recipients": ["<EMAIL>", "<EMAIL>"]}}