{"TestSuiteName": "Happy Points", "TestSuiteItems": [{"Type": "TestCase", "Reference": "WorkSet01/TestCases/021.HappyPoints/29.json", "ParamsReference": "WorkSet01/Params/HappyPointsParams.json"}, {"Type": "TestCase", "Reference": "WorkSet01/TestCases/021.HappyPoints/31.json", "ParamsReference": "WorkSet01/Params/HappyPointsParams.json"}, {"Type": "TestCase", "Reference": "WorkSet01/TestCases/021.HappyPoints/32.json", "ParamsReference": "WorkSet01/Params/HappyPointsParams.json"}, {"Type": "TestCase", "Reference": "WorkSet01/TestCases/021.HappyPoints/157-MorePointsThanAvailable.json", "ParamsReference": "WorkSet01/Params/HappyPointsParams.json"}]}