{"TestCaseName": "<PERSON><PERSON>", "TestCaseCode": "TC-000", "Steps": [{"Name": "<PERSON>lick on <PERSON><PERSON> <PERSON><PERSON> to skip the demo", "Command": "click", "Target": "{{Select<PERSON>.Fin<PERSON>}}", "ContinueOnError": true, "CustomDelayBeforeStepExecustionInMilliseconds": 2000}, {"Name": "Click on Lo<PERSON>", "Command": "click", "Target": "{{<PERSON><PERSON>.<PERSON>}}", "CustomDelayBeforeStepExecustionInMilliseconds": 2000}, {"Name": "Type the User Name", "Command": "type", "Target": "{{Selectors.UserNameField}}", "Value": "{{TestData.UserName}}"}, {"Name": "Click on Continue <PERSON><PERSON>", "Command": "click", "Target": "{{Selectors.Con<PERSON>ue<PERSON><PERSON>}}"}, {"Name": "Type the Password", "Command": "executescript", "Target": "{{Selectors.PasswordField}}", "Value": "$('[{{Selectors.PasswordField}}]').val('{{TestData.Password}}')"}, {"Name": "Type the answer for the Challenge Question", "Command": "type", "Target": "{{Selectors.ChallengeQuestionField}}", "Value": "{{TestData.ChallengeAnswer}}"}, {"Name": "Click on the Login Button", "Command": "click", "Target": "{{Select<PERSON>.<PERSON>}}"}]}