using OpenQA.Selenium;
using OpenQA.Selenium.Support.UI;
using System;



namespace TestAutomationFramework.Helpers
{
    public static class WebDriverExtensions
    {
        /// <summary>
        /// Waits until a specified condition is met or a timeout occurs.
        /// </summary>
        /// <param name="driver">The WebDriver instance.</param>
        /// <param name="condition">A lambda function representing the condition to wait for.</param>
        /// <param name="timeoutInMilliseconds">The maximum time to wait in seconds.</param>
        /// <param name="pollingIntervalInMilliseconds">The polling interval in milliseconds.</param>
        public static void WaitUntil(this IWebDriver driver, Func<IWebDriver, bool> condition, int timeoutInMilliseconds = 10, int pollingIntervalInMilliseconds = 500)
        {
            var wait = new DefaultWait<IWebDriver>(driver)
            {
                Timeout = TimeSpan.FromMilliseconds(timeoutInMilliseconds),
                PollingInterval = TimeSpan.FromMilliseconds(pollingIntervalInMilliseconds)
            };

            wait.IgnoreExceptionTypes(typeof(NoSuchElementException), typeof(StaleElementReferenceException));
            wait.Until(condition);
        }
        public static void WaitUntil(this IWebDriver driver, Func<IWebDriver, IWebElement> condition, int timeoutInMilliseconds = 500, int pollingIntervalInMilliseconds = 100)
        {
            var wait = new DefaultWait<IWebDriver>(driver)
            {
                Timeout = TimeSpan.FromMilliseconds(timeoutInMilliseconds),
                PollingInterval = TimeSpan.FromMilliseconds(pollingIntervalInMilliseconds)
            };

            wait.IgnoreExceptionTypes(typeof(NoSuchElementException), typeof(StaleElementReferenceException));
            wait.Until(condition);
        }

        /// <summary>
        /// Captures a screenshot during test execution and saves it to a specified directory with a timestamp.
        /// </summary>
        /// <param name="driver">The WebDriver instance used for capturing the screenshot.</param>
        /// <param name="suiteName">The name of the test suite the test belongs to.</param>
        /// <param name="ModuleName">The name of the module under test (optional).</param>
        /// <param name="testCaseName">The name of the test case for which the screenshot is being taken.</param>
        /// <param name="StepName">The name of the specific step in the test being captured.</param>

        public static string CaptureScreenshot(this IWebDriver driver, string suiteName, string ModuleName, string testCaseName, string StepName)
        {
            string screenshotDir = null;
            string screenshotPath = null;
            try
            {
                string timestamp = DateTime.Now.ToString("yyyy-MM-dd HH.mm.ss.fff");
                //string baseDir = Path.Combine(ConfigHelper._Config.ScreenShotsFolderPath,"TestRunResults", suiteName, string.IsNullOrEmpty(ModuleName)?"":ModuleName, testCaseName);
                screenshotDir = Path.Combine(Environment.CurrentDirectory,Config.Settings.OutputDir,"ScreenShots", suiteName);

                try
                {
                    if (!Directory.Exists(screenshotDir))
                        Directory.CreateDirectory(screenshotDir);
                }
                catch(Exception ex)
                {
                    Logger.Log($"Cant Find the Path of screenshot folder,", LogLevel.Error, ConsoleColor.Red);
                    Environment.Exit(1);
                }
                

                screenshotPath = Path.Combine(screenshotDir, $"{timestamp}_{StepName}.png");
                Screenshot screenshot = ((ITakesScreenshot)driver).GetScreenshot();
                //ForceWriteOnFileReadOnlyHelper.ForceWriteToFolder(screenshotDir);
                screenshot.SaveAsFile(screenshotPath);
                Logger.Log($"The ScreenShot was successfully taken for Step: {StepName}, in TestCase:{testCaseName}", LogLevel.Info, ConsoleColor.Green);

                return screenshotPath;  

            }
            catch (Exception ex)
            {
                Logger.Log($"Error capturing screenshot: {ex}; Extra Details: screenshotDir: {screenshotDir}, screenshotPath: {screenshotPath}", LogLevel.Error, ConsoleColor.Red);
                return string.Empty;
            }
        }

    }
}
