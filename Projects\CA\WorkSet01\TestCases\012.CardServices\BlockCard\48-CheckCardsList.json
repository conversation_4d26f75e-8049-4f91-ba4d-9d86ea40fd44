{"TestCaseName": "Check Cards List In Block Card Service", "TestCaseCode": "TC-48", "Steps": [{"TestCaseReference": {"Name": "<PERSON><PERSON>", "TestCasePath": "WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json", "ParamsReference": "WorkSet01/Params/LoginTestCaseParams.json", "Params": {"TestData": {"UserName": "mahmoudsayed022", "Password": "Password1", "ChallengeAnswer": "bmw"}}}}, {"Name": "Click on change later button on the password expiration popup message", "Command": "Click", "Target": "{{Selectors.popupCancel}}", "ContinueOnError": true}, {"Name": "Click on biometric alert", "Command": "Click", "Target": "{{Selectors.BioMetricAlert}}", "ContinueOnError": true}, {"Name": "Execute JS code to activate token", "Command": "executescript", "Value": "Globals.ActivationState = 'ACTIVATED';"}, {"Name": "Click on Cards option on the side menu", "Command": "Click", "Target": "{{Selectors.CardsButton}}"}, {"Name": "Click on Card Services", "Command": "Click", "Target": "{{Selectors.CardServices}}"}, {"Name": "Click on Block Card option", "Command": "Click", "Target": "{{Selectors.BlockCardOption}}"}, {"Name": "Click on Select Card Button and show the cards list", "Command": "Click", "Target": "{{Selectors.SelectCardButton}}"}, {"Name": "Check if the blocked cards appear in the card list.", "Command": "appear", "Target": "css=#CardsSelectionErrorHandle > div:nth-child(1)", "ContinueOnError": true}]}