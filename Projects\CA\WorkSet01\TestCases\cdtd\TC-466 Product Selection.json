{"TestCaseName": "Product Selection Customer will check all available TDs types and choose from them", "TestCaseCode": "TC-466", "Steps": [{"TestCaseReference": {"Name": "<PERSON><PERSON>", "TestCasePath": "WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json", "ParamsReference": "WorkSet01/Params/LoginTestCaseParams.json", "Params": {"TestData": {"UserName": "sherif1234", "Password": "Password1", "ChallengeAnswer": "bmw"}}}}, {"Name": "Click on Cancel Button", "Command": "click", "Target": "id=popup_cancel", "ElementToValidateThatScreenLoaded": "css=#popbuttons > div > button.btn.Cancel.back_gradient2", "ContinueOnError": true}, {"Name": "Click on <PERSON><PERSON>", "Command": "click", "Target": "css=#popbuttons > div > button.btn.Cancel.back_gradient2", "ContinueOnError": true, "CustomDelayBeforeStepExecustionInMilliseconds": 5000}, {"Name": "Click on biometric alert", "Command": "Click", "Target": "{{Selectors.BioMetricAlert}}", "ContinueOnError": true}, {"Name": "Click on Deposits button from side menu", "Command": "Click", "Target": "{{Select<PERSON>.Depo<PERSON>}}"}, {"Name": "Click on Time Deposits button from side menu", "Command": "Click", "Target": "{{Selectors.TimeDeposits}}"}, {"Name": "Click on Select Deposit button", "Command": "Click", "Target": "{{Selectors.SelectCertificateBtn}}"}, {"Name": "Click on Deposit", "Command": "Click", "Target": "{{Select<PERSON>.Deposit}}"}, {"Name": "scroll down to show more products", "Command": "scrolltoelement", "Target": "id=ProductBlock7", "ContinueOnError": true}, {"Name": "scroll down to show more products", "Command": "scrolltoelement", "Target": "id=ProductBlock11", "ContinueOnError": true}, {"Name": "scroll down to show more products", "Command": "scrolltoelement", "Target": "id=ProductBlock15", "ContinueOnError": true}, {"Name": "scroll down to show more products", "Command": "scrolltoelement", "Target": "id=ProductBlock19", "ContinueOnError": true}]}