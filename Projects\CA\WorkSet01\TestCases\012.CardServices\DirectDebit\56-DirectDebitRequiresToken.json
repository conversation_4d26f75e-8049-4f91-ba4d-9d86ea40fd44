{"TestCaseName": "Direct Debit token authentication", "TestCaseCode": "TC-56", "Steps": [{"TestCaseReference": {"Name": "<PERSON><PERSON>", "TestCasePath": "WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json", "ParamsReference": "WorkSet01/Params/LoginTestCaseParams.json"}}, {"Name": "Click on change later button on the password expiration popup message", "Command": "Click", "Target": "{{Selectors.popupCancel}}", "ContinueOnError": true}, {"Name": "Click on ok popup", "Command": "Click", "Target": "{{Selectors.SorryPOPup}}", "ContinueOnError": true}, {"Name": "Click on biometric alert", "Command": "Click", "Target": "{{Selectors.BioMetricAlert}}", "ContinueOnError": true}, {"Name": "Execute JS code to activate token", "Command": "executescript", "Value": "Globals.ActivationState = 'ACTIVATED';"}, {"Name": "Click on Cards button from side menu", "Command": "Click", "Target": "{{Selectors.CardsButton}}"}, {"Name": "Click on Cards Services button from side menu", "Command": "Click", "Target": "{{Selectors.CardServices}}"}, {"Name": "Click on Direct Debit", "Command": "Click", "Target": "{{Selectors.DirectDebitBtn}}"}, {"Name": "select card ", "Command": "Click", "Target": "{{Selectors.CardSelectedinDirectDebit}}"}, {"Name": "click accout button ", "Command": "Click", "Target": "{{Selectors.SelectAccountBtn}}"}, {"Name": "select accout", "Command": "Click", "Target": "{{Selectors.DebitAccount}}"}, {"Name": "click Debited Percentage select", "Command": "Click", "Target": "{{Selectors.DebitedPercentageSelect}}"}, {"Name": "click Debited Percentage select option", "Command": "Click", "Target": "{{Selectors.DebitedPercentageSelectOption}}"}, {"Name": "click ageree checkbox", "Command": "Click", "Target": "{{Selectors.AgreeCheckBoxinDebit}}"}, {"Name": "click on continue button", "Command": "Click", "Target": "{{Selectors.SubmitDirectDebitReq}}"}, {"Name": "click on confirm button", "Command": "Click", "Target": "{{Selectors.DirectDebitConfirm}}"}, {"Name": "Assert that token page appear: Authorization", "Command": "assert", "Target": "{{Selectors.TokenWidgetTitle}}", "value": " OFF987517517"}]}