﻿
namespace TestAutomationFramework.Models
{
    // Root condition wrapper (can hold different types)
    public class Condition
    {
        public List<Condition>? AnyOf { get; set; }

        public List<Condition>? AllOf { get; set; }

        public Condition? Not { get; set; }

        public PredicateCondition? Predicate { get; set; }

        public ConditionReference? Reference { get; set; }

        public string? TestCaseStatus { get; set; }
    }

    // Predicate condition (context/field/operator/value)
    public class PredicateCondition
    {
        public string Context { get; set; } = string.Empty;

        public string? Field { get; set; }

        public string Operator { get; set; } = string.Empty;

        public string? Value { get; set; }
    }

    // Reference to external condition file
    public class ConditionReference
    {
        /// <summary>
        /// Path to condition JSON file (relative or absolute)
        /// </summary>
        public string Ref { get; set; } = string.Empty;

        /// <summary>
        /// Parameters passed from step (key -> value)
        /// </summary>
        public Dictionary<string, string>? Params { get; set; }

    }
}
