{"Selectors": {"BeneficiariesToButton": "xpath=//div[@class='mainmenuitems']//label[contains(text(), 'Beneficiaries')]", "OtherCAEAccounts": "xpath=//div[@class='submenuitem']//label[contains(text(), 'Other CAE Accounts')]", "OtherCAECards": "xpath=//div[@class='submenuitem']//label[contains(text(), 'Other CAE Cards')]", "LocalTransfer": "xpath=//div[@class='submenuitem']//label[contains(text(), 'Local Transfer')]", "BanksInsideEgypt": "xpath=//div[@class='submenuitem']//label[contains(text(), 'Banks Inside Egypt')]", "BioMetricAlert": "css=#popbuttons > div > button.btn.Cancel.back_gradient2", "AddBTn": "id=IPNAddLocalBtn", "AddLocalBenBTn": "id=AddLocalBtn", "SaveBtn": "id=SavaBtn", "NickNameMSG": "css=#wginsAddLocalBeneficiary_Default > div > div > div.section_body > div > div:nth-child(1) > div > span", "popupCancel": "id=popup_cancel", "FullNameMSG": "css=#wginsAddLocalBeneficiary_Default > div > div > div.section_body > div > div:nth-child(2) > div > span", "BankAccountMSG": "css=#wginsIPNAddLocalBeneficiary_Default > div > div > div.section_body > div.add_beneficiary.back_white > div.Add_Account.Account > div.input_group.Account_input > div > span", "NickNameInput": "id=BenfNickName", "BeneficiaryFullNameInput": "id=BenFName", "BankNameSelect": "id=SelectBankName", "BankOption": "css=#Banks > div > div:nth-child(1) > span", "AccountNumberInput": "id=BenFAccountNum", "currencySelect": "id=SelectCurrency_Name", "currencyoption": "css=#wginsAddLocalBeneficiary_Default > div > div > div.section_body > div > div.input_group.retail > div.select_warpper > div.options.back_white_solid.dropdown-active.d-block > div > div:nth-child(2) > div > span", "currencyoptionEGP": "css=#wginsAddLocalBeneficiary_Default > div > div > div.section_body > div > div.input_group.retail > div.select_warpper > div.options.back_white_solid.dropdown-active.d-block > div > div:nth-child(1)", "AddressInput": "id=BenefAddress", "CityInput": "id=BenfCity", "CountryInput": "id=SelectBeneficiaryCity", "countrtyoption": "css=#AddLocalBenefCountryForignCurrency > div > div.options.back_white_solid.dropdown-active.d-block > div > div:nth-child(7) > span", "SecondContinueBen": "id=DelLocBenSumContinue", "TokenNumField": "id=TokenNUMBER", "ConfirmToken": "id=btnTokenConfirm", "EnterTokenLabel": "css=#ENTRUST > div > div.section_body > div > div.input_group > label", "DoneLabel": "css=#InnerHtmlwidget > div > div > div > div > div.success_text_content.back_white > div.success_text > h1", "TokenInput": "id=TokenNUMBER", "btnTokenConfirm": "id=btnTokenConfirm"}, "TestData": {"UserName": "mahmoudsayed022", "Password": "Password1", "ChallengeAnswer": "bmw", "NickName": "tmp user", "BeneficiaryFullName": "tmp full", "BankNameSelect": "Suez Canal Bank", "AccountNumber": "11111111111111111111111111111111111", "currencySelect": "US DOLLAR"}}