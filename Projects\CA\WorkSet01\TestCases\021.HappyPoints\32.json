{"TestCaseName": "Verify That User Can't Redeem Happy Points Using Wrong SMS", "TestCaseCode": "TC-32", "Environment": "Pilot", "Steps": [{"TestCaseReference": {"Name": "<PERSON><PERSON>", "TestCasePath": "WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json", "ParamsReference": "WorkSet01/Params/LoginTestCaseParams.json"}}, {"Name": "Click on Happy points option from side menu", "Command": "Click", "Target": "{{Selectors.HappyPointsOption}}"}, {"Name": "Click on Redeem Online button", "Command": "Click", "Target": "{{Selectors.RedeemOnline}}"}, {"Name": "Choose a merchant", "Command": "Click", "Target": "{{Selectors.SelectMerchant}}"}, {"Name": "Click Redeem button", "Command": "Click", "Target": "{{Select<PERSON>.<PERSON>eem<PERSON>}}"}, {"Name": "Enter the number of points to redeem", "Command": "Type", "Target": "{{Selectors.PointsTextbox}}", "Value": "1000"}, {"Name": "Click on create cupon button", "Command": "Click", "Target": "{{Selectors.CreateCupon}}"}, {"Name": "Click on Continue button", "Command": "Click", "Target": "{{Selectors.CuntinueConfirmCupon}}"}, {"Name": "Enter wrong OTP", "Command": "Type", "Target": "{{Selectors.OTPbox}}", "Value": "0000000"}, {"Name": "Click on Confirm OTP button", "Command": "Click", "Target": "{{Selectors.OTPconfirmButton}}"}, {"Name": "Assert the error message: ", "Command": "Assert", "Target": "{{Selectors.popupMessage}}", "Value": "Wrong activation code"}]}