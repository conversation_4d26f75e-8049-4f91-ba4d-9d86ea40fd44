﻿using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TestAutomationFramework.Models;

namespace TestAutomationFramework.Helpers
{
    public static class Config
    {

        private static readonly Lazy<AppSettings> _settings = new(() =>
        {
            var config = new ConfigurationBuilder()
                .SetBasePath(AppContext.BaseDirectory)
                .AddJsonFile("appsettings.json", optional: false)
                .AddJsonFile("appsettings.local.json", optional: true)
                .Build();

            return config.Get<AppSettings>();
        }, isThreadSafe: true);

        public static AppSettings Settings => _settings.Value;
    }
}
