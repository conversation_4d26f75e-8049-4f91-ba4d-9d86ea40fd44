{"TestCaseName": "Existing Banks Inside Egypt beneficiaries displayed", "TestCaseCode": "TC-145", "Steps": [{"TestCaseReference": {"Name": "<PERSON><PERSON>", "TestCasePath": "WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json", "ParamsReference": "WorkSet01/Params/LoginTestCaseParams.json"}}, {"Name": "Click on change later button on the password expiration popup message", "Command": "Click", "Target": "{{Selectors.popupCancel}}", "ContinueOnError": true}, {"Name": "Click on biometric alert", "Command": "Click", "Target": "{{Selectors.BioMetricAlert}}", "ContinueOnError": true}, {"Name": "Execute JS code to activate token", "Command": "executescript", "Value": "Globals.ActivationState = 'ACTIVATED';"}, {"Name": "Click on Beneficiaries", "Command": "click", "Target": "{{Selectors.BeneficiariesToButt<PERSON>}}"}, {"Name": "Click on Banks Inside Egypt", "Command": "Click", "Target": "{{Selectors.BanksInsideEgypt}}"}, {"Name": "Check if the Account List appears", "Command": "appear", "Target": "{{Selectors.Beneficiary<PERSON><PERSON>er}}"}]}