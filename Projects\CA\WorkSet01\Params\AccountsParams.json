{"Selectors": {"AccountBtn": "xpath=//div[@class='mainmenuitems']//label[contains(text(), 'Accounts')]", "SupAccountsBtn": "xpath=//div[@class='submenuitem']//label[contains(text(), 'Accounts')]", "Cheques": "xpath=//div[@class='submenuitem']//label[contains(text(), 'Cheques')]", "ChequeTypeList": "xpath=//div[@class='select_warpper IsNotStaff']//span[contains(@id, 'ChequeType')]", "ChequeTypeOutwardChoice": "xpath=//div[@class='select_warpper IsNotStaff']//span[@class='ChequeType_selection' and text()='Outward Cheques - Other Banks']", "ChequeTypeInwardChoice": "xpath=//div[@class='select_warpper IsNotStaff']//span[@class='ChequeType_selection' and text()='Inward Cheques - CAE']", "AccountsList": "xpath=//div[@id='AccountList']//div[contains(@class, 'select')]", "AccountChoiceWithInwardCheques": "xpath=//div[@id='AccountList']//span[@class='AccountNumbers_selection' and text()='**************']", "SearchBtn": "id=SearchBtn", "AccountTransactionsButton": "id=AccountTransBtn", "AccountToCheck50Transactions": "xpath=//div[@id='AccountListContainer']//h4[contains(text(), '**************')]", "AccountDisabled": "xpath=//div[@id='AccountListContainer']//h4[contains(text(), '**************')]", "TransactionsListClass": "xpath=//div[@class='module_list_container']//div[contains(@class, 'module_list_item click_effect back_white curved_box TransactionsHandleOnClick')]", "LoansBtn": "xpath=/html/body/div[2]/div[2]/div/div/div[2]/div/div[3]/div/div/div/div[2]/div/div[2]/div[3]/div/ul[2]", "DepositsBtn": "xpath=/html/body/div[2]/div[2]/div/div/div[2]/div/div[3]/div/div/div/div[2]/div/div[2]/div[3]/div/ul[4]", "CertificatesBtn": "xpath=/html/body/div[2]/div[2]/div/div/div[2]/div/div[3]/div/div/div/div[2]/div/div[2]/div[3]/div/ul[5]", "FullNameMSG": "css=#wginsIPNAddLocalBeneficiary_Default > div > div > div.section_body > div.add_beneficiary.back_white > div.Add_Account.Account > div:nth-child(2) > div > span", "BankAccountMSG": "css=#wginsIPNAddLocalBeneficiary_Default > div > div > div.section_body > div.add_beneficiary.back_white > div.Add_Account.Account > div.input_group.Account_input > div > span", "NickNameInput": "id=BenfNickName", "BeneficiaryFullNameInput": "id=BenFName", "AccountRelationshipText": "css=#accdetDetailsItems1 > div:nth-child(1) > h4", "AccountRelationshipValue": "css=#accdetDetailsItems1 > div:nth-child(1) > label", "AccountRestrictionText": "css=#accdetDetailsItems1 > div:nth-child(3) > h4", "AccountRestrictionValue": "css=#accdetDetailsItems1 > div:nth-child(3) > label", "AccountBranchText": "css=#accdetDetailsItems1 > div:nth-child(4) > h4", "AccountBranchValue": "css=#accdetDetailsItems1 > div:nth-child(4) > label", "AccountStatusText": "css=#accdetDetailsItems1 > div:nth-child(5) > h4", "AccountStatusValue": "css=#accdetDetailsItems1 > div:nth-child(5) > label", "UnclearedfundsText": "css=#accdetDetailsItems1 > div:nth-child(6) > h4", "UnclearedfundsValue": "css=#accdetDetailsItems1 > div:nth-child(6) > label", "AmountonholdText": "css=#accdetDetailsItems1 > div:nth-child(7) > h4", "AmountonholdValue": "css=#accdetDetailsItems1 > div:nth-child(7) > label", "OverdraftLimitText": "css=#accdetDetailsItems1 > div:nth-child(8) > h4", "OverdraftLimitValue": "css=#accdetDetailsItems1 > div:nth-child(8) > label", "IBANText": "css=#accdetDetailsItems1 > div:nth-child(9) > h4", "IBANValue": "css=#accdetDetailsItems1 > div:nth-child(9) > label", "PayFromAccountBtn": "id=PayFromAccountBtn", "TransferFromAccountBtn": "id=TransferFromAccountBtn", "PaymentsMyBills": "id=PaymentsMyBills", "PaymentsCreditCard": "id=PaymentsCreditCard", "PaymentsPrePaidCard": "id=PaymentsPrePaidCard", "PaymentsOtherDigitalBankAccounts": "id=PaymentsOtherDigitalBankAccounts", "PaymentsOtherDigitalBankCard": "id=PaymentsOtherDigitalBankCard", "PaymentsInsideEgyptAccount": "id=PaymentsInsideEgyptAccount", "BioMetricAlert": "css=#popbuttons > div > button.btn.Cancel.back_gradient2", "KYCskip": "css=#popbuttons > div > button.btn.Cancel.back_gradient2", "popupCancel": "id=popup_cancel", "popupMessage": "id=popup_message", "popupOk": "id=popup_ok", "kyc": "class=Cancel", "accountinside": "xpath=//div[@class='submenuitem']//label[   contains(text(), 'Accounts')]", "Account11068180004845": "id=Account11068180004845", "Account00022090000052": "id=Account00022090000052", "credit_cards": "xpath=/html/body/div[2]/div[2]/div/div/div[4]/div[1]/div/div[2]/div/div/div/div[2]/div/div[2]/div", "targetaccount": "xpath=/html/body/div[2]/div[2]/div/div/div[4]/div[2]/div/div[2]/div/div/div/div[2]/div[2]/div[1]/div", "amount": "id=AmountCardToAccount", "tranfer": "id=CardToAccountTransferBtn", "tranfer2": "id=TransferToPreCreditCardsBtn", "done": "class=text_primary", "ownaccounts": "xpath=/html/body/div[2]/div[2]/div/div/div[4]/div[1]/div/div[2]/div/div/div/div[2]/div/div[1]/div", "toAccount": "id=Account11188180020580", "AmountTransfersInsideMyAccounts": "id=AmountTransfersInsideMyAccounts", "continue": "xpath=/html/body/div[2]/div[2]/div/div/div[4]/div[1]/div/div[2]/div/div/div/div[2]/div/div/div[5]/button/a", "TransferTransferBtn": "id=TransferTransferBtn", "mybills": "xpath=/html/body/div[2]/div[2]/div/div/div[4]/div[1]/div/div[2]/div/div/div/div[2]/div/div[2]/div", "bill1": "xpath=/html/body/div[2]/div[2]/div/div/div[4]/div[1]/div/div[2]/div/div/div/div[2]/div[2]/div[1]/div", "Pay": "id=Pay", "select": "xpath=/html/body/div[2]/div[2]/div/div/div[4]/div[1]/div/div[2]/div/div/div/div[2]/div/div/div[3]/div/div[1]", "option1": "xpath=/html/body/div[2]/div[2]/div/div/div[4]/div[1]/div/div[2]/div/div/div/div[2]/div/div/div[3]/div/div[2]/span[1]", "PayBtn": "id=PayBtn", "OtherCAECards": "xpath=/html/body/div[2]/div[2]/div/div/div[4]/div[1]/div/div[2]/div/div/div/div[2]/div/div[6]/div", "Amount11": "id=Amount1", "Reason": "id=Reason", "TransferToOtherBankCardContinueBtn": "id=TransferToOtherBankCardContinueBtn", "transfer_other": "id=TransferToOtherDigitalBankCardTransferBtn", "Account00021810031968": "id=Account00021810031968", "ContinueBtn": "id=ContinueBtn", "card": "xpath=//span[contains(text(), '4023XXXXXXXX0000')]"}, "SpecialEnvParams": {"Pilot": {"Selectors": {"AccountToCheck50Transactions": "xpath=//div[@id='AccountListContainer']//h4[contains(text(), '**************')]"}}}}