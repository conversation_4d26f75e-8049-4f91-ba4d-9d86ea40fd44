{"TestCaseName": "Block card request", "TestCaseCode": "TC-47", "Steps": [{"TestCaseReference": {"Name": "<PERSON><PERSON>", "TestCasePath": "WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json", "ParamsReference": "WorkSet01/Params/LoginTestCaseParams.json", "Params": {"TestData": {"UserName": "mahmoudsayed022", "Password": "Password1", "ChallengeAnswer": "bmw"}}}}, {"Name": "Click on change later button on the password expiration popup message", "Command": "Click", "Target": "{{Selectors.popupCancel}}", "ContinueOnError": true}, {"Name": "Click on biometric alert", "Command": "Click", "Target": "{{Selectors.BioMetricAlert}}", "ContinueOnError": true}, {"Name": "Click on biometric alert", "Command": "Click", "Target": "{{Selectors.KYCskip}}", "ContinueOnError": true}, {"Name": "Execute JS code to activate token", "Command": "executescript", "Value": "Globals.ActivationState = 'ACTIVATED';"}, {"Name": "Click on Cards button from side menu", "Command": "Click", "Target": "{{Selectors.CardsButton}}"}, {"Name": "Click on Card Inquiry and check cards details", "Command": "Click", "Target": "{{Selectors.CardInquiry}}"}, {"Name": "Click on a card to block it", "Command": "Click", "Target": "{{Selectors.CardToBlockFromInquiry}}"}, {"Name": "Click on Block Button", "Command": "Click", "Target": "{{Selectors.BlockCardOptionInquiry}}"}, {"Name": "Click on block card button", "Command": "Click", "Target": "{{Selectors.BlockCardBtnSubmit}}"}, {"Name": "Click on block card confirm", "Command": "Click", "Target": "{{Selectors.BlockCardConfirm}}"}]}