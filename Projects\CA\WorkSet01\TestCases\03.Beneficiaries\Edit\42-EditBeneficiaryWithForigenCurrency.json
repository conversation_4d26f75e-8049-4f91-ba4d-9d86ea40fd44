{"TestCaseName": "Edit Account Screen: All Fields Editable for Foreign Currency", "TestCaseCode": "TC-42", "Steps": [{"TestCaseReference": {"Name": "<PERSON><PERSON>", "TestCasePath": "WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json", "ParamsReference": "WorkSet01/Params/LoginTestCaseParams.json"}}, {"Name": "Click on change later button on the password expiration popup message", "Command": "Click", "Target": "{{Selectors.popupCancel}}", "ContinueOnError": true}, {"Name": "Click on biometric alert", "Command": "Click", "Target": "{{Selectors.BioMetricAlert}}", "ContinueOnError": true}, {"Name": "Click on biometric alert", "Command": "Click", "Target": "{{Selectors.KYCskip}}", "ContinueOnError": true}, {"Name": "Execute JS code to activate token", "Command": "executescript", "Value": "Globals.ActivationState = 'ACTIVATED';"}, {"Name": "Click on Beneficiaries", "Command": "click", "Target": "{{Selectors.BeneficiariesToButt<PERSON>}}"}, {"Name": "Click on Local Transfer option", "Command": "Click", "Target": "{{Selectors.LocalTransfer}}"}, {"Name": "Choose Foreign currency Beneficiary to edit", "Command": "Click", "Target": "css=#Benf0"}, {"Name": "Click on Edit Beneficiary button", "Command": "Click", "Target": "{{Selectors.EditBTN}}"}, {"Name": "Edit Beneficiary nickname", "Command": "type", "Target": "{{Selectors.BenNickNameTextbox}}", "Value": "TestNicknameF2"}, {"Name": "Edit Beneficiary full name", "Command": "type", "Target": "{{Selectors.BenFullNameTextbox}}", "Value": "TestFullNameFF"}, {"Name": "Edit Beneficiary account number", "Command": "type", "Target": "{{Selectors.BenAccNumberTextbox}}", "Value": "*************"}, {"Name": "Edit Beneficiary bank", "Command": "click", "Target": "{{Selectors.EditBenBankselect}}"}, {"Name": "select Beneficiary bank", "Command": "click", "Target": "css=#Banks > div > div:nth-child(4) > span"}, {"Name": "select currency", "Command": "click", "Target": "{{Selectors.editcurrencySelect}}"}, {"Name": "select other foreign currency", "Command": "click", "Target": "css=#wginsEditLocalBeneficiary_Default > div > div > div.section_body > div > div:nth-child(5) > div > div.options.back_white_solid.dropdown-active.d-block > div > div:nth-child(6) > div > span"}, {"Name": "fill address input", "Command": "type", "Target": "{{Selectors.AddressEdit}}", "Value": "99 st 9999usa"}, {"Name": "fill city input", "Command": "type", "Target": "{{Selectors.CityEditEdit}}", "Value": "Ohio"}, {"Name": "select Country input", "Command": "click", "Target": "{{Selectors.CountryEdit}}"}, {"Name": "select Country option", "Command": "click", "Target": "css=#EditLocalBenefCountryForignCurrency > div > div.options.back_white_solid.dropdown-active.d-block > div > div:nth-child(8) > span"}, {"Name": "Click on save button", "Command": "Click", "Target": "{{Selectors.SaveLocalBenButton}}"}, {"Name": "Click on continue button", "Command": "Click", "Target": "{{Selectors.ContinueBtn}}"}]}