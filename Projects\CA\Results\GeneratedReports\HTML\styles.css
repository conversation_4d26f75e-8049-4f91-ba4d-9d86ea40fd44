body {
    font-family: Arial, sans-serif;
    margin: 20px;
    padding: 20px;
    background-color: #f9f9f9;
    color: #333;
}

.container {
    max-width: 1400px;
    margin: auto;
    background: #fff;
    padding: 20px;
    box-shadow: 0 0 10px rgba(0,0,0,0.1);
}

.header {
    text-align: center;
    border-bottom: 3px solid #009598;
    padding-bottom: 10px;
    margin-bottom: 20px;
}

    .header img {
        height: 60px;
    }

    .header h1 {
        margin: 10px 0 0;
        font-size: 2em;
        color: #333;
    }

    .header p {
        color: #666;
    }

.summary-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
}

    .summary-table th, .summary-table td {
        border: 1px solid #ddd;
        padding: 10px;
        text-align: center;
    }

.overall-summary th {
    background-color: #3f8277;
    color: white;
}

.suite-summary {
    background-color: #009598;
    color: white;
}

.module-summary {
    background-color: #88b131;
    color: black;
}

.suite, .module, .testcase {
    border: 1px solid #ddd;
    margin-bottom: 20px;
    padding: 15px;
    border-radius: 5px;
    background: #fafafa;
    page-break-inside: avoid;
}

.suite {
    border-left: 6px solid #007BFF;
}

.module {
    border-left: 6px solid #28a745;
    margin-left: 20px;
}

.testcase {
    border-left: 6px solid #dc3545;
    margin-left: 40px;
}

.metadata {
    display: flex;
    flex-wrap: nowrap;
    background: #89cecf;
    padding: 10px;
    margin-bottom: 10px;
    border: 1px solid #ddd;
    position: relative;
    justify-content: space-between;
}


    .metadata .test-case {
        max-width: 80%;
    }

    .metadata .status {
        position: relative;
        right: 0;
        left: 0;
        min-width: fit-content;
    }

    .metadata div {
        margin-right: 20px;
        font-size: 16px;
    }

    .metadata span {
        font-weight: bold;
    }


.test-case {
    flex-grow: 1;
}


.error {
    background-color: #f1c8c7;
}

.test-steps-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 10px;
}

    .test-steps-table th, .test-steps-table td {
        border: 1px solid #ddd;
        padding: 10px;
        text-align: left;
    }

    .test-steps-table th {
        background-color: #a7b49e;
        color: white;
        font-size: 16px;
        padding: 12px;
    }

.test-step-image {
    max-width: 450px;
    height: auto;
    border: 1px solid #ccc;
    border-radius: 5px;
    padding: 5px;
}

.status-passed {
    color: #28a745;
    font-weight: bold;
}
.status-blocked {
    color: #FFA500;
    font-weight: bold;
}

.status-failed {
    color: #dc3545;
    font-weight: bold;
}

.status-skipped {
    color: #fd7e14;
    font-weight: bold;
}

.footer {
    margin-top: 30px;
    text-align: center;
    font-size: 14px;
    color: #777;
}

@media print {
    .screenshot img, .test-step-image img {
        max-width: 400px;
    }
}
