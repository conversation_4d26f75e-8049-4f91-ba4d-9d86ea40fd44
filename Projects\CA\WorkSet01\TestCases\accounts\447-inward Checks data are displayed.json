{"TestCaseName": "inward Checks data are displayed", "TestCaseCode": "TC-447", "Steps": [{"TestCaseReference": {"Name": "<PERSON><PERSON>", "TestCasePath": "WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json", "ParamsReference": "WorkSet01/Params/LoginTestCaseParams.json", "Params": {"TestData": {"UserName": "Mm_azmy", "Password": "Zxcv2025", "ChallengeAnswer": "armada"}}}}, {"Name": "Click on change later button on the password expiration popup message", "Command": "Click", "Target": "{{Selectors.popupCancel}}", "ContinueOnError": true}, {"Name": "Click on biometric alert", "Command": "Click", "Target": "{{Selectors.BioMetricAlert}}", "ContinueOnError": true}, {"Name": "Execute JS code to activate token", "Command": "executescript", "Value": "Globals.ActivationState = 'ACTIVATED';"}, {"Name": "Click on Accounts", "Command": "Click", "Target": "{{Selectors.AccountBtn}}"}, {"Name": "Click on Cheques", "Command": "Click", "Target": "{{Selectors.Cheques}}"}, {"Name": "Click on cheque type list", "Command": "Click", "Target": "{{Selectors.ChequeTypeList}}"}, {"Name": "Click on Inward cheques option", "Command": "Click", "Target": "{{Selectors.ChequeTypeInwardChoice}}"}, {"Name": "Click on Accounts list", "Command": "Click", "Target": "{{Selectors.AccountsList}}"}, {"Name": "Choose an Account with inward cheques", "Command": "Click", "Target": "{{Selectors.AccountChoiceWithInwardCheques}}"}, {"Name": "Click on Search and check results appear", "Command": "Click", "Target": "{{Selectors.SearchBtn}}"}]}