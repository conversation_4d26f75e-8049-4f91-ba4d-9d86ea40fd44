{"ModuleName": "Verify beneficiariary <PERSON>", "TestCases": [{"TestCasePath": "WorkSet01/TestCases/03.Beneficiaries/Verify/VerifiyAddingifNickNameEmpty.json", "ParamsReference": "WorkSet01/Params/VerifictionParams.json"}, {"TestCasePath": "WorkSet01/TestCases/03.Beneficiaries/Verify/VerifiyAddingifFullNameEmpty.json", "ParamsReference": "WorkSet01/Params/VerifictionParams.json"}, {"TestCasePath": "WorkSet01/TestCases/03.Beneficiaries/Verify/VerifiyAddingifBankAccountEmpty.json", "ParamsReference": "WorkSet01/Params/VerifictionParams.json"}, {"TestCasePath": "WorkSet01/TestCases/03.Beneficiaries/Verify/BeneficiariesAccountNumberLength.json", "ParamsReference": "WorkSet01/Params/VerifictionParams.json"}, {"TestCasePath": "WorkSet01/TestCases/03.Beneficiaries/Verify/VerifyAddRedirectsToTokenPage.json", "ParamsReference": "WorkSet01/Params/VerifictionParams.json"}, {"TestCasePath": "WorkSet01/TestCases/03.Beneficiaries/Verify/251-Existing Account beneficiaries displayed.json", "ParamsReference": "WorkSet01/Params/BeneficiariesParams.json"}, {"TestCasePath": "WorkSet01/TestCases/03.Beneficiaries/Verify/252-Existing Card beneficiaries displayed.json", "ParamsReference": "WorkSet01/Params/BeneficiariesParams.json"}, {"TestCasePath": "WorkSet01/TestCases/03.Beneficiaries/Verify/TC-145 Existing bankinsideegypt beneficiaries displayed.json", "ParamsReference": "WorkSet01/Params/BeneficiariesParams.json"}]}