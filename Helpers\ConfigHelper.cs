using AngleSharp.Css.Dom;
using AngleSharp.Dom;

//using AngleSharp.Text;
//using Microsoft.VisualStudio.TestPlatform.ObjectModel;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text.RegularExpressions;
using TestAutomationFramework.Models;

namespace TestAutomationFramework.Helpers
{
    public static class ConfigHelper
    {
        public static Dictionary<string, string> suitePathToSprint = new Dictionary<string, string>();
        private static readonly string MainRunningInstructionPath = "RunningInstructions.json";
        private static readonly string LocalRunningInstructionPath = "RunningInstructions.local.json";
        private static readonly object _lock = new object();
        private static readonly Lazy<RunningInstructions> _runningInstructionsLazy = new Lazy<RunningInstructions>(() => LoadRunningInstructions());
        private static readonly Lazy<Parameters> _globalParamsLazy
            = new Lazy<Parameters>(() => LoadConfig<Parameters>(Config.Settings.GlobalParamsPath));

        public static Parameters _GlobaPrams => _globalParamsLazy.Value;
        public static RunningInstructions _RunningInstructions => _runningInstructionsLazy.Value;

        public static FullTestResults FinalTestResultReport { get; set; } = new FullTestResults();

        public static Dictionary<Guid, TestCase_Results> TestCaseRefrenceToTestResult = new();

        /// <summary>
        /// Loads a generic configuration object from the specified path.
        /// </summary>
        public static T LoadConfig<T>(string path, bool throwExceptionIfNotExist = true)
        {
            Logger.Log($"Attempting to load configuration file: {path}", LogLevel.Info);
            if (!File.Exists(path))
            {
                if (!throwExceptionIfNotExist)
                {
                    Logger.Log($"Config file not found: {path}. Returning default value.", LogLevel.Warn);
                    return default;
                }

                Logger.Log($"Config file not found: {path}", LogLevel.Error);
                throw new FileNotFoundException($"Config file not found at path: {path}");
            }

            try
            {
                var json = File.ReadAllText(path);
                Logger.Log($"Successfully loaded configuration file: {path}", LogLevel.Info);
                return JsonConvert.DeserializeObject<T>(json);
            }
            catch (Exception ex)
            {
                Logger.Log($"Failed to load configuration file: {path}. Details: {ex.Message}", LogLevel.Error);
                throw;
            }
        }

        /// <summary>
        /// Loads a test suite from the specified file path.
        /// </summary>
        public static TestSuite LoadTestSuite(string suitePath)
        {
            Logger.Log($"Attempting to load test suite: {suitePath}", LogLevel.Info);
            if (!File.Exists(suitePath))
            {
                Logger.Log($"Test suite file not found: {suitePath}", LogLevel.Error);
                throw new FileNotFoundException($"Test suite file not found at path: {suitePath}");
            }

            try
            {
                var suite = JsonConvert.DeserializeObject<TestSuite>(File.ReadAllText(suitePath));
                Logger.Log($"Successfully loaded test suite: {suitePath}", LogLevel.Info);
                return suite;
            }
            catch (Exception ex)
            {
                Logger.Log($"Failed to load test suite: {suitePath}. Details: {ex.Message}", LogLevel.Error);
                throw;
            }
        }

        /// <summary>
        /// Loads and merges RunningInstructions from both main and local files.
        /// Local file settings override main file settings.
        /// </summary>
        public static RunningInstructions LoadRunningInstructions()
        {
            //Ensure Config is loaded
            if (Config.Settings == null)
            {
                Logger.Log("Configuration settings are not loaded. Please load the configuration first.", LogLevel.Error);
                throw new InvalidOperationException("Configuration settings are not loaded.");
            }

            Logger.Log("Loading RunningInstructions configuration...", LogLevel.Info);

            // Load main configuration
            RunningInstructions mainConfig = LoadConfig<RunningInstructions>(MainRunningInstructionPath);
            if (mainConfig == null)
            {
                Logger.Log("Main RunningInstructions configuration not found or invalid. Using default settings.", LogLevel.Warn);
                return new RunningInstructions();
            }
            RunningInstructions localConfig = LoadConfig<RunningInstructions>(LocalRunningInstructionPath, false);

            if (localConfig != null)
            {
                Logger.Log("Merging local RunningInstructions with main configuration.", LogLevel.Info);
                // Override main config with local config

                if (!string.IsNullOrEmpty(localConfig.RunningEnvironment))
                    mainConfig.RunningEnvironment = localConfig.RunningEnvironment;
                if (localConfig.RunningBrowsers != null && localConfig.RunningBrowsers.Length > 0)
                    mainConfig.RunningBrowsers = localConfig.RunningBrowsers;
                if (localConfig.MobileView != mainConfig.MobileView)
                    mainConfig.MobileView = localConfig.MobileView;
                 
                if (localConfig.ReportSettings != null)
                    mainConfig.ReportSettings = localConfig.ReportSettings;

                if (localConfig.FilteringOptions != null)
                {
                    if (localConfig.FilteringOptions.TestCasesLabelFilteringRegex != null && localConfig.FilteringOptions.TestCasesLabelFilteringRegex.Length > 0)
                        mainConfig.FilteringOptions.TestCasesLabelFilteringRegex = localConfig.FilteringOptions.TestCasesLabelFilteringRegex;
                    if (localConfig.FilteringOptions.TestCasesOwnerFilteringRegex != null && localConfig.FilteringOptions.TestCasesOwnerFilteringRegex.Length > 0)
                        mainConfig.FilteringOptions.TestCasesOwnerFilteringRegex = localConfig.FilteringOptions.TestCasesOwnerFilteringRegex;
                    if (localConfig.FilteringOptions.SuitesFilteringRegex != null && localConfig.FilteringOptions.SuitesFilteringRegex.Length > 0)
                        mainConfig.FilteringOptions.SuitesFilteringRegex = localConfig.FilteringOptions.SuitesFilteringRegex;
                }


                Logger.Log("Successfully merged local RunningInstructions with main configuration.", LogLevel.Info);
            }
            else
            {
                Logger.Log("No local RunningInstructions found, using main configuration only.", LogLevel.Info);
            }

            return mainConfig;
        }

        /// <summary>
        /// Gets all test suite paths from the configured folder.
        /// </summary>
        public static List<string> GetTestSuitePaths()
        {
            Logger.Log("Retrieving test suite paths.", LogLevel.Info);

            var folderPaths = Config.Settings.TestSuiteFolderPaths;
             
            if (folderPaths == null || folderPaths.Length == 0)
            {
                Logger.Log("No test suite folder paths configured.", LogLevel.Error);
                throw new InvalidOperationException("No test suite folder paths configured in the configuration file.");
            }

            var paths = new List<string>();
            foreach (var folderPath in folderPaths)
            {
                if (string.IsNullOrEmpty(folderPath) || !Directory.Exists(folderPath))
                {
                    Logger.Log($"Test suite folder not found at path: {folderPath}", LogLevel.Error);
                    throw new DirectoryNotFoundException($"Test suite folder not found at path: {folderPath}");
                }

                try
                {
                    var suiteFiles = Directory.GetFiles(folderPath, "*.json", SearchOption.AllDirectories).ToList();

                    Logger.Log($"Retrieved {paths.Count} test suite paths.", LogLevel.Info);

                    paths.AddRange(suiteFiles);
                }
                catch (Exception ex)
                {
                    Logger.Log($"Error retrieving test suite paths. Details: {ex.Message}", LogLevel.Error);
                    throw;
                }
            }

            return paths;

        }

        /// <summary>
        /// Gets all test cases from the specified or default test suite paths.
        /// </summary>

        public static IEnumerable<TestCase> GetAllTestCases(List<string> testSuitesPaths = null)
        {
            Logger.Log("Retrieving all test cases.", LogLevel.Info);
            string sprintName;
            var testCases = new List<TestCase>();
            var suitePaths = testSuitesPaths ?? GetTestSuitePaths();

            //filter test suites based on configured regex patterns
            if (_RunningInstructions.FilteringOptions.SuitesFilteringRegex?.Length > 0)
            {
                suitePaths = ApplySuiteFilteringRegex(suitePaths);
            }


            foreach (var suitePath in suitePaths)
            {
                try
                {

                    Logger.Log($"Loading test suite: {suitePath}", LogLevel.Info);
                    var suite = LoadTestSuite(suitePath);

                    var suiteResult = new TestSuite_Results { Name = suite.TestSuiteName };
                    foreach (var _SuiteItemParams in suite.TestSuiteItems)
                    {
                        if (_SuiteItemParams.Type.Equals("TestCase", StringComparison.OrdinalIgnoreCase))
                        {
                            Logger.Log($"Loading test case: {_SuiteItemParams.Reference}", LogLevel.Debug);

                            var testCase = LoadTestCase(_SuiteItemParams.Reference);

                            if (!ShouldIncludeTestCase(testCase))
                            {
                                Logger.Log($"Test case '{testCase.TestCaseName}' is excluded based on filtering criteria.", LogLevel.Info);
                                continue; // Skiped test case
                            }

                            var testCaseWithResolvedParams = ProcessTestCaseWithResolvedParams(
                                testCase: testCase,
                                paramsReferencePath: _SuiteItemParams.ParamsReference,
                                inlineParams: _SuiteItemParams.Params,
                                TestDataReferencePath: _SuiteItemParams.TestDataReference
                            );


                            testCaseWithResolvedParams.TestCaseName = !string.IsNullOrEmpty(_SuiteItemParams.Name) ? _SuiteItemParams.Name : testCaseWithResolvedParams.TestCaseName;
                            testCaseWithResolvedParams.SuiteName = suite.TestSuiteName;

                            var testCaseResult = new TestCase_Results
                            {
                                Name = testCaseWithResolvedParams.TestCaseName,
                                TestData = testCaseWithResolvedParams.TestData
                                //Status = "Skipped"
                            };

                            var testCaseGuid = Guid.NewGuid();
                            TestCaseRefrenceToTestResult[testCaseGuid] = testCaseResult;  // Store in dictionary
                            testCaseWithResolvedParams.TestCaseId = testCaseGuid;          // Assign GUID to TestCase object

                            suiteResult.TestCases.Add(testCaseResult);
                            testCases.Add(testCase);

                            sprintName = Path.GetFileName(Path.GetDirectoryName(suitePath));
                            if (testCaseWithResolvedParams.TestCaseCode != null)
                                suitePathToSprint[testCaseWithResolvedParams.TestCaseCode] = sprintName;
                        }
                        else if (_SuiteItemParams.Type.Equals("Module", StringComparison.OrdinalIgnoreCase))
                        {
                            Logger.Log($"Loading module: {_SuiteItemParams.Reference}", LogLevel.Debug);
                            var module = LoadConfig<Module>(_SuiteItemParams.Reference);
                            var moduleResult = new TestModule_Results { Name = module.ModuleName };

                            foreach (var moduleTestCaseRef in module.TestCases)
                            {
                                Logger.Log($"Loading module test case: {moduleTestCaseRef.TestCasePath}", LogLevel.Debug);
                                var moduleTestCase = LoadTestCase(moduleTestCaseRef.TestCasePath);

                                if (!ShouldIncludeTestCase(moduleTestCase))
                                {
                                    Logger.Log($"Test case '{moduleTestCase.TestCaseName}' is excluded based on filtering criteria.", LogLevel.Info);
                                    continue; // Skiped test case
                                }

                                var moduleTestCaseWithResolvedParams = ProcessTestCaseWithResolvedParams(
                                    moduleTestCase,
                                    moduleTestCaseRef.ParamsReference,
                                    moduleTestCaseRef.Params,
                                    _SuiteItemParams.Params,
                                    TestDataReferencePath: moduleTestCaseRef.TestDataReference
                                );


                                moduleTestCaseWithResolvedParams.TestCaseName = !string.IsNullOrEmpty(moduleTestCaseRef.Name) ? moduleTestCaseRef.Name : moduleTestCaseWithResolvedParams.TestCaseName;
                                moduleTestCaseWithResolvedParams.TestCaseCode = !string.IsNullOrEmpty(moduleTestCaseRef.TestCode) ? moduleTestCaseRef.TestCode : moduleTestCaseWithResolvedParams.TestCaseCode;
                                moduleTestCaseWithResolvedParams.SuiteName = suite.TestSuiteName;
                                moduleTestCaseWithResolvedParams.ModuleName = module.ModuleName;

                                var moduleTestCaseResult = new TestCase_Results
                                {
                                    Name = moduleTestCaseWithResolvedParams.TestCaseName,
                                    TestData = moduleTestCaseWithResolvedParams.TestData
                                    //Status = "Skipped"
                                };

                                var moduleTestCaseGuid = Guid.NewGuid();
                                TestCaseRefrenceToTestResult[moduleTestCaseGuid] = moduleTestCaseResult;  // Store in dictionary
                                moduleTestCaseWithResolvedParams.TestCaseId = moduleTestCaseGuid;          // Assign GUID to TestCase object

                                moduleResult.TestCases.Add(moduleTestCaseResult);

                                testCases.Add(moduleTestCase);
                                sprintName = Path.GetFileName(Path.GetDirectoryName(suitePath));

                                if (moduleTestCaseWithResolvedParams.TestCaseCode != null)
                                    suitePathToSprint[moduleTestCaseWithResolvedParams.TestCaseCode] = sprintName;
                            }
                            suiteResult.Modules.Add(moduleResult);
                        }
                    }

                    // load 
                    var loadExcludedTestData = LoadConfig<List<string>>(Config.Settings.ExcludedTestDataPath);
                    if (loadExcludedTestData != null)
                        FinalTestResultReport.excludedTestData.AddRange(loadExcludedTestData);


                    FinalTestResultReport.Suites.Add(suiteResult);

                }
                catch (Exception ex)
                {
                    Logger.Log($"Error loading test suite or module: {suitePath}. Details: {ex.Message}", LogLevel.Error);
                }
            }

            Logger.Log($"Retrieved {testCases.Count} test cases.", LogLevel.Info);

            return testCases;
        }


        private static TestCase ProcessTestCaseWithResolvedParams(
            TestCase testCase, string paramsReferencePath,
            Parameters inlineParams,
            [Optional] Parameters SuiteParams,
            [Optional] string Environment,
            [Optional] string TestDataReferencePath

            )
        {

            var globalPrams = _GlobaPrams;

            // load test Data and deserialize it 
            var testData = new TestData();
            if (!string.IsNullOrEmpty(TestDataReferencePath))
                testData = LoadConfig<TestData>(TestDataReferencePath);



            Logger.Log($"Resolving parameters for test case: {testCase.TestCaseName}", LogLevel.Debug);

            // Final resolved environment (priority: param > testCase > config > "UAT")
            string currentEnv = !string.IsNullOrEmpty(Environment) ? Environment : !string.IsNullOrEmpty(testCase.Environment) ? testCase.Environment : _RunningInstructions.RunningEnvironment ?? "UAT";



            var resolvedSelectors = new Dictionary<string, object>();
            var resolvedTestData = new Dictionary<string, object>();

            void MergeParams(Parameters sourceParams)
            {
                Logger.Log($"Merging params", LogLevel.Debug);
                if (sourceParams == null) return;

                // Always merge base Selectors and TestData first
                foreach (var kvp in sourceParams.Selectors ?? new Dictionary<string, object>())
                    resolvedSelectors[kvp.Key] = kvp.Value;

                foreach (var kvp in sourceParams.TestData ?? new Dictionary<string, object>())
                    resolvedTestData[kvp.Key] = kvp.Value;

                Logger.Log($"Merged basic params", LogLevel.Debug);

                // Then override with SpecialEnvParams (if applicable)
                   if (sourceParams.SpecialEnvParams != null &&
                    sourceParams.SpecialEnvParams.TryGetValue(currentEnv, out var envParams))
                {
                    Logger.Log($"SpecialEnvParams were found and will start to merge them", LogLevel.Debug);
                    foreach (var kvp in envParams.Selectors ?? new Dictionary<string, object>())
                        resolvedSelectors[kvp.Key] = kvp.Value;

                    foreach (var kvp in envParams.TestData ?? new Dictionary<string, object>())
                        resolvedTestData[kvp.Key] = kvp.Value;

                    Logger.Log($"Merged SpecialEnvParams params", LogLevel.Debug);
                }
                Logger.Log($"Merged params", LogLevel.Debug);
            }


            MergeParams(globalPrams);

            if (!string.IsNullOrEmpty(paramsReferencePath) && File.Exists(paramsReferencePath))
            {
                Logger.Log($"Loading parameters from reference file: {paramsReferencePath}", LogLevel.Debug);
                var referenceParams = LoadConfig<Parameters>(paramsReferencePath);
                MergeParams(referenceParams);
            }

            MergeParams(SuiteParams);
            MergeParams(inlineParams);

            Logger.Log($"Resolving placeholders in test steps for test case: {testCase.TestCaseName}", LogLevel.Debug);
            var expandedSteps = new List<TestStep>();


            // combine separated test data file with resolvedTestData
            if (testData.Data != null)
            {
                foreach (var kvp in testData.Data)
                {
                    resolvedTestData[kvp.Key] = kvp.Value; // Adds or overwrites
                }
            }




            foreach (var step in testCase.Steps)
            {
                if (step.TestCaseReference != null)
                {
                    Logger.Log($"Resolving TestCaseReference in step: {step.Name}", LogLevel.Debug);
                    var subTestCase = LoadTestCase(step.TestCaseReference.TestCasePath);

                    var referencedTestCase = ProcessTestCaseWithResolvedParams(
                         subTestCase,
                         step.TestCaseReference.ParamsReference,
                         step.TestCaseReference.Params,
                         null,
                         currentEnv,
                         TestDataReferencePath: step.TestCaseReference.TestDataReference
                     );

                    // combine referencedTestCase testdata to main testCase TestData obj

                    if (referencedTestCase?.TestData?.Data != null)
                    {
                        if (testCase?.TestData?.Data == null)
                        {
                            testCase.TestData.Data = new Dictionary<string, object>();
                        }

                        foreach (var item in referencedTestCase.TestData.Data)
                        {
                            if (!string.IsNullOrEmpty(item.Key))
                            {
                                testCase.TestData.Data[item.Key] = item.Value ?? string.Empty;
                            }
                        }
                    }
                    else
                    {
                        Logger.Log("Referenced test case data is null. Skipping merge.", LogLevel.Error);
                    }




                    foreach (var item in referencedTestCase.TestData.Data)
                    {
                        testCase.TestData.Data[item.Key] = item.Value;
                    }


                    foreach (var referencedStep in referencedTestCase.Steps)
                    {
                        referencedStep.Name = ResolvePlaceholders(
                            referencedStep.Name,
                            resolvedSelectors,
                            resolvedTestData,
                            testCase
                            );
                        referencedStep.Target = ResolvePlaceholders(
                            referencedStep.Target,
                            resolvedSelectors,
                            resolvedTestData,
                            testCase
                            );
                        referencedStep.Value = ResolvePlaceholders(
                            referencedStep.Value,
                            resolvedSelectors,
                            resolvedTestData,
                            testCase
                            );

                        foreach (var target in referencedStep.TargetsToCaptureDataFrom ?? Enumerable.Empty<CaptureTarget>())
                            target.Selector = ResolvePlaceholders(
                                target.Selector,
                                resolvedSelectors,
                                resolvedTestData,
                                testCase
                                );


                        expandedSteps.Add(referencedStep);
                    }
                }
                else
                {
                    step.Name = ResolvePlaceholders(step.Name, resolvedSelectors, resolvedTestData, testCase);
                    step.Target = ResolvePlaceholders(step.Target, resolvedSelectors, resolvedTestData, testCase);
                    step.Value = ResolvePlaceholders(step.Value, resolvedSelectors, resolvedTestData, testCase);

                    foreach (var target in step.TargetsToCaptureDataFrom ?? Enumerable.Empty<CaptureTarget>())
                        target.Selector = ResolvePlaceholders(target.Selector, resolvedSelectors, resolvedTestData, testCase);


                    var finalStep = ApplyEnvironmentStepOverride(step, currentEnv);
                    expandedSteps.Add(finalStep);
                }

            }

            testCase.Steps = expandedSteps;
            Logger.Log($"Successfully resolved parameters for test case: {testCase.TestCaseName}", LogLevel.Info);
            return testCase;
        }

        // Helper method to apply environment-specific step overrides
        private static TestStep ApplyEnvironmentStepOverride(TestStep step, string currentEnv)
        {
            //ToDo: To Check What More To override
            if (step.SpecialEnvTestStepParams != null && step.SpecialEnvTestStepParams.TryGetValue(currentEnv, out var overrideStep))
            {
                step.CustomStatusToReportUponFailure = overrideStep.CustomStatusToReportUponFailure ?? step.CustomStatusToReportUponFailure;

                return step;
            }

            return step;
        }

        private static TestCase LoadTestCase(string referencePath)
        {
            Logger.Log($"Loading test case from: {referencePath}", LogLevel.Info);
            if (!File.Exists(referencePath))
            {
                Logger.Log($"Test case file not found: {referencePath}", LogLevel.Error);
                throw new FileNotFoundException($"Test case file not found at path: {referencePath}");
            }

            return LoadConfig<TestCase>(referencePath);
        }

        /// <summary>
        /// Checks if a test case should be included based on label filtering configuration.
        /// </summary>
        public static bool ShouldIncludeTestCaseByLabels(TestCase testCase)
        {
            var filteringRegexes = _RunningInstructions.FilteringOptions.TestCasesLabelFilteringRegex;
            if (filteringRegexes == null || filteringRegexes.Length == 0)
            {
                Logger.Log("No label filtering configuration found.", LogLevel.Debug);
                return true; // No filtering regexes provided, pass this filter
            }

            if (testCase.Labels == null || testCase.Labels.Length == 0)
            {
                Logger.Log($"Test case '{testCase.TestCaseName}' has no labels.", LogLevel.Debug);
                return false; // No labels provided, fail this filter
            }

            Logger.Log($"Applying label filtering with regexes: {string.Join(", ", filteringRegexes)}", LogLevel.Debug);
            return ShouldIncludeTestCaseByField(
                testCase.TestCaseName,
                testCase.Labels,
                filteringRegexes,
                "Label");
        }

        /// <summary>
        /// Checks if a test case should be included based on owner filtering configuration.
        /// </summary>
        public static bool ShouldIncludeTestCaseByOwner(TestCase testCase)
        {
            var filteringRegexes = _RunningInstructions.FilteringOptions.TestCasesOwnerFilteringRegex;
            if (filteringRegexes == null || filteringRegexes.Length == 0)
            {
                Logger.Log("No owner filtering configuration found.", LogLevel.Debug);
                return true; // No filtering regexes provided, pass this filter
            }

            if (testCase.Owners == null || testCase.Owners.Length == 0)
            {
                Logger.Log($"Test case '{testCase.TestCaseName}' has no owners.", LogLevel.Debug);
                return false; // No owners provided, fail this filter
            }

            Logger.Log($"Applying owner filtering with regexes: {string.Join(", ", filteringRegexes)}", LogLevel.Debug);
            return ShouldIncludeTestCaseByField(
                testCase.TestCaseName,
                testCase.Owners,
                filteringRegexes,
                "Owner");
        }

        /// <summary>
        /// Generic method to check if a test case should be included based on a field (label or owner).
        /// </summary>
        private static bool ShouldIncludeTestCaseByField(string testCaseName, string[] values, string[] filteringRegexes, string fieldName)
        {
            if (values == null || values.Length == 0)
            {
                Logger.Log($"Test case '{testCaseName}' has no {fieldName}s. Excluding from execution.", LogLevel.Debug);
                return false;
            }

            var compiledRegexes = filteringRegexes.Select(pattern =>
            {
                try
                {
                    return new Regex(pattern, RegexOptions.IgnoreCase | RegexOptions.Compiled);
                }
                catch (Exception ex)
                {
                    Logger.Log($"Invalid regex pattern '{pattern}': {ex.Message}. Skipping pattern.", LogLevel.Error);
                    return null;
                }
            }).Where(r => r != null).ToList();

            if (compiledRegexes.Count == 0)
            {
                Logger.Log($"No valid filtering patterns provided for {fieldName}s. Excluding all test cases.", LogLevel.Debug);
                return false;
            }

            if (values.Any(val => compiledRegexes.Any(regex => regex!.IsMatch(val))))
            {
                Logger.Log($"Test case '{testCaseName}' {fieldName}s [{string.Join(", ", values)}] match at least one filtering pattern. Including in execution.", LogLevel.Debug);
                return true;
            }

            Logger.Log($"Test case '{testCaseName}' {fieldName}s [{string.Join(", ", values)}] do not match any filtering patterns. Excluding from execution.", LogLevel.Debug);
            return false;
        }

        /// <summary>
        /// Main method to determine if a test case should be included in the scan process.
        /// </summary>
        public static bool ShouldIncludeTestCase(TestCase testCase)
        {
            // Hard exclusions - these are never included
            string[] excludedTestCases = new string[] { };  // { "TC-105", "TC-111", "TC-135", "TC-136", "TC-137", "TC-139", "TC-140", "TC-142", "TC-143", "TC-144", "TC-153", "TC-159", "TC-160", "TC-161", "TC-162", "TC-164", "TC-166", "TC-168", "TC-207", "TC-208", "TC-212", "TC-216", "TC-220", "TC-230", "TC-233", "TC-234", "TC-240", "TC-247", "TC-248", "TC-257", "TC-258", "TC-267", "TC-55", "TC-66" };

            if (excludedTestCases.Contains(testCase.TestCaseCode))
            {
                Logger.Log($"TestCase: {testCase.TestCaseCode} is in excludedTestCases, WILL NOT LIST IT In Scan Process", LogLevel.Info);
                return false;
            }

            // Check if any filtering is configured
            var hasLabelFiltering = _RunningInstructions.FilteringOptions.TestCasesLabelFilteringRegex != null &&
                                   _RunningInstructions.FilteringOptions.TestCasesLabelFilteringRegex.Length > 0;
            var hasOwnerFiltering = _RunningInstructions.FilteringOptions.TestCasesOwnerFilteringRegex != null &&
                                   _RunningInstructions.FilteringOptions.TestCasesOwnerFilteringRegex.Length > 0;

            // If no filtering is configured at all, include the test case
            if (!hasLabelFiltering && !hasOwnerFiltering)
            {
                Logger.Log($"No filtering configuration found. Including test case '{testCase.TestCaseName}'.", LogLevel.Info);
                return true;
            }

            // Apply only the configured filters - test case passes if it matches ALL configured filters
            if (hasLabelFiltering)
            {
                bool includeByLabels = ShouldIncludeTestCaseByLabels(testCase);
                if (!includeByLabels)
                {
                    Logger.Log($"Test case '{testCase.TestCaseName}' excluded by label filtering.", LogLevel.Info);
                    return false;
                }
                Logger.Log($"Test case '{testCase.TestCaseName}' passed label filtering.", LogLevel.Info);
            }

            if (hasOwnerFiltering)
            {
                bool includeByOwners = ShouldIncludeTestCaseByOwner(testCase);
                if (!includeByOwners)
                {
                    Logger.Log($"Test case '{testCase.TestCaseName}' excluded by owner filtering.", LogLevel.Info);
                    return false;
                }
                Logger.Log($"Test case '{testCase.TestCaseName}' passed owner filtering.", LogLevel.Info);
            }

            // If we reach here, the test case passed all configured filters
            Logger.Log($"Test case '{testCase.TestCaseName}' passed all configured filters.", LogLevel.Info);
            return true;
        }

        private static List<string> ApplySuiteFilteringRegex(List<string> suitePaths)
        {
            var regexList = _RunningInstructions.FilteringOptions.SuitesFilteringRegex
                                .Select(pattern => new Regex(pattern, RegexOptions.IgnoreCase | RegexOptions.Compiled))
                                .ToList();

            Logger.Log(
                $"Applying suite filtering with regexes: {string.Join(", ", _RunningInstructions.FilteringOptions.SuitesFilteringRegex)}",
                LogLevel.Debug
            );

            suitePaths = suitePaths
                .Where(path =>
                {
                    return regexList.Any(regex => regex.IsMatch(path));
                })
                .ToList();
            return suitePaths;
        }


        private static string ResolvePlaceholders(
            string input,
            Dictionary<string, object> selectors,
            Dictionary<string, object> testData,
            TestCase testCase
            )
        {
            if (string.IsNullOrEmpty(input)) return input;

            Logger.Log($"Resolving placeholders in string: {input}", LogLevel.Debug);

            foreach (var pair in selectors)
            {
                string key = pair.Key;
                string value = pair.Value?.ToString() ?? string.Empty;
                input = input.Replace($"{{{{Selectors.{key}}}}}", value);
            }

            foreach (var pair in testData)
            {
                string key = pair.Key;
                string value = pair.Value?.ToString() ?? string.Empty;

                if (input.Contains($"{{{{TestData.{key}}}}}") || input.Contains($"{{{{{key}}}}}"))
                {
                    // Add only if not already added
                    if (!testCase.TestData.Data.ContainsKey(key))
                    {
                        testCase.TestData.Data.Add(key, value);
                    }
                }

                input = input.Replace($"{{{{TestData.{key}}}}}", value);
                input = input.Replace($"{{{{{key}}}}}", value);
            }

            Logger.Log($"Resolved string: {input}", LogLevel.Debug);
            return input;
        }


    }
}
