{"Selectors": {"CardInquiry": "xpath=//div[@class='submenuitem']//label[contains(text(), 'Card Inquiry')]", "Card": "css=#PrePaidCard > div.card_number", "BioMetricAlert": "css=#popbuttons > div > button.btn.Cancel.back_gradient2", "CardsButton": "xpath=//div[@class='mainmenuitems']//label[contains(text(), 'Cards')]", "CardServices": "xpath=//div[@class='submenuitem']//label[contains(text(), 'Card services')]", "ATMdisputeButton": "id=ATMDispute", "SelectButton": "xpath=//div[@class='details_container']//button[normalize-space(text())='Select']", "CardListClass": "css=#CardsSelectionErrorHandle", "SelectDisputeType": "xpath=//div[@class='details_container']//span[normalize-space(text())='Select...']", "DisputeType": "xpath=//div[@class='details_container']//span[normalize-space(text())='<PERSON><PERSON><PERSON> did not dispense']", "ContinueButton": "xpath=//div[@class='details_container']//button[normalize-space(text())='Continue']", "SubmitProdRequestDetailsBtn": "id=SubmitProdRequestDetailsBtn", "RequestStatues": "id=RequestStatus", "LatestRequest": "id=RequestItem0", "CardWithNoTransactions": "xpath=//div[@class='module_list_container cardData']//h4[normalize-space(text())='4603XXXXXXXX9621']", "CardWithTransactions": "xpath=//div[@class='module_list_container cardData']//h4[normalize-space(text())='4204XXXXXXXX2562']", "SelectTransactionButton": "xpath=//div[@class='details_container']//button[normalize-space(text())='Select Transaction']", "Transaction": "css=#Transaction > div:nth-child(1) > div.list_header", "ContinueButtonPilot1": "xpath=//div[@class='details_container']//button[normalize-space(text())='Continue']", "DisputedAmmountField": "xpath=//div[@class='details_container']//input[normalize-space(@placeholder)='Enter Disputed amount']", "DisputeNoteField": "xpath=//div[@class='details_container']//textarea[normalize-space(@placeholder)='']", "TermsCheckbox": "xpath=//div[@class='details_container']//input[@type='checkbox']", "ContinueButtonPilot2": "xpath=//div[@class='details_container']//button[normalize-space(text())='Confirm']", "ConfirmDispute": "id=DisputePreConfirmationBtn", "NoTransactionMesseage": "No ATM transactions were found for the selected card", "DisputeRequestID": "css=//div[@class='details_items_container']//h3[contains(text(), 'Request Reference Number')]", "RequestDetailsButton": "id=SubmitProdRequestDetailsBtn", "popupCancel": "id=popup_cancel", "popupMessage": "id=popup_message", "popupOk": "id=popup_ok"}}