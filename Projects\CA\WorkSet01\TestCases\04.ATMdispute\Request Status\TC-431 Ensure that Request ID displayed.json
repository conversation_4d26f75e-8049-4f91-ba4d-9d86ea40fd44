{"TestCaseName": "Ensure that Request ID displayed", "TestCaseCode": "TC-431", "Steps": [{"TestCaseReference": {"Name": "<PERSON><PERSON>", "TestCasePath": "WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json", "ParamsReference": "WorkSet01/Params/LoginTestCaseParams.json"}}, {"Name": "Click on Cards option on the side menu", "Command": "Click", "Target": "{{Selectors.CardsButton}}"}, {"Name": "Click on Card Services", "Command": "Click", "Target": "{{Selectors.CardServices}}"}, {"Name": "Click on request status", "Command": "click", "Target": "{{Selectors.RequestStatues}}"}, {"Name": "Choose a Dispute request", "Command": "click", "Target": "css=#RequestItem1"}, {"Name": "Ensure that Request ID displayed", "Command": "click", "Target": "css=#offCertificateS1 > div.details_container > div > div.details_header.pb-2.back_white.curved_box > div > h4", "ElementToValidateThatScreenLoaded": "css=#offCertificateS1 > div.details_container > div > div.details_header.pb-2.back_white.curved_box > div > h4"}]}