{"TestCaseName": "Ensure that note applicable enter number and character", "TestCaseCode": "TC-410", "Steps": [{"TestCaseReference": {"Name": "<PERSON><PERSON>", "TestCasePath": "WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json", "ParamsReference": "WorkSet01/Params/LoginTestCaseParams.json", "Params": {"TestData": {"UserName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Password": "Password2", "ChallengeAnswer": "bmw"}}}}, {"Name": "Click on Cards option on the side menu", "Command": "Click", "Target": "{{Selectors.CardsButton}}"}, {"Name": "Click on Card Services", "Command": "Click", "Target": "{{Selectors.CardServices}}"}, {"Name": "Click on ATM dispute", "Command": "Click", "Target": "{{Selectors.ATMdisputeButton}}"}, {"Name": "Click on dispute type", "Command": "Click", "Target": "{{Selectors.SelectDisputeType}}"}, {"Name": "Choose dispute type", "Command": "click", "Target": "{{Selectors.DisputeType}}"}, {"Name": "Click on Select Cards button", "Command": "Click", "Target": "{{Selectors.SelectButton}}"}, {"Name": "Choose a card with ATM transactions", "Command": "click", "Target": "{{Selectors.CardWithTransactions}}"}, {"Name": "Click on select a transaction", "Command": "click", "Target": "{{Selectors.SelectTransactionButton}}"}, {"Name": "Choose a transaction", "Command": "click", "Target": "{{Selectors.Transaction}}"}, {"Name": "Click on continue", "Command": "click", "Target": "{{Selectors.ContinueButtonPilot1}}"}, {"Name": "Enter the disputed amount", "Command": "type", "Target": "{{Selectors.DisputedAmmountField}}", "Value": "0"}, {"Name": "Enter the dispute note in free-text format", "Command": "type", "Target": "{{Selectors.DisputeNoteField}}", "Value": "Testing text 350 *-+!.,/ نص تجريبي ٣٥٠"}]}