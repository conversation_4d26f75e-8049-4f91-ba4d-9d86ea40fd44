<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Test Execution Report</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <div class="header">
            <img src="D:\TFS\DevWork\R and D Work\TestAutomationFramework\TestAutomationFramework\Projects\CA\Templates/CA_Logo.png" alt="Company Logo">
            <h1  >Card Services Suite Execution Report</h1>
            <p>Generated on: <strong>2025-08-14 12:10 PM</strong> | Environment: <strong>UAT61</strong> | Browser: <strong>Chrome</strong> | Duration:<strong>00:00:53</strong></p>
        </div>
        <h2  >Overall Summary</h2>
<table  class="summary-table overall-summary"  >
    <tr>
        <th>Total Suites</th>
        <th>Total Modules</th>
        <th>Total Test Cases</th>
        <!-- <th>Total Steps</th>-->
        <th>Passed</th>
        <th>Failed</th>
        <th>Total Duration</th>

        <!-- <th>Skipped</th>-->
    </tr>
    <tr>
        <td>1</td>
        <td>0</td>
        <td>3</td>
        <!--<td>0</td>-->
        <td>0</td>
        <td>3</td>
        <!-- <td>{{TotalSkipped}}</td>-->
         <td>00:00:53</td>

    </tr>
</table>

		<table border="1" cellpadding="5" cellspacing="0" style="border-collapse: collapse; width: 100%;">
    <thead style="background-color: #00796B; color: white;">
        <tr>
            <th>#</th>
            <th>Test Case ID</th>
            <th>Test Case Name</th>
            <th>Status</th>
            <th>Duration</th>
            <th>TestData</th>
            <th>Error/Remarks</th>
            <th>Screenshot</th>
            <th>Suite</th>

        </tr>
    </thead>
    <tbody>
        <!-- TestCaseDetailRow -->
<tr>
    <td>1</td>
    <td>TC-125</td>
    <td>Check Blocked Cards List</td>
    <td style="color: red; font-weight: bold;">Failed</td>
    <td>00:19</td>
    <td>
<div><strong>UserName</strong>: mahmoudsayed022</div>
<div><strong>ChallengeAnswer</strong>: bmw</div>
</td>

    <td>Error executing step: Click on Login Button.</td>
    <td><img class='test-step-image' style='height: 120px;' src='D:\TFS\DevWork\R and D Work\TestAutomationFramework\TestAutomationFramework\Projects\CA\Results\ScreenShots\Card Services\2025-08-14 12.08.58.295_Click on Finish Button to skip the demo.png' alt='Step Screenshot'></td>
<td rowspan=3>Card Services</td>
</tr>
<!-- TestCaseDetailRow -->
<tr>
    <td>2</td>
    <td>TC-113</td>
    <td>All users Cards are displayed successfully</td>
    <td style="color: red; font-weight: bold;">Failed</td>
    <td>00:10</td>
    <td>
<div><strong>UserName</strong>: mahmoudsayed022</div>
<div><strong>ChallengeAnswer</strong>: bmw</div>
</td>

    <td>Error executing step: Click on Continue Button.</td>
    <td><img class='test-step-image' style='height: 120px;' src='D:\TFS\DevWork\R and D Work\TestAutomationFramework\TestAutomationFramework\Projects\CA\Results\ScreenShots\Card Services\2025-08-14 12.09.19.282_Type the User Name.png' alt='Step Screenshot'></td>
</tr>
<!-- TestCaseDetailRow -->
<tr>
    <td>3</td>
    <td>TC-48</td>
    <td>Check Cards List</td>
    <td style="color: red; font-weight: bold;">Failed</td>
    <td>00:24</td>
    <td>
<div><strong>UserName</strong>: mahmoudsayed022</div>
<div><strong>ChallengeAnswer</strong>: bmw</div>
</td>

    <td>Error executing step: Type the answer for the Challenge Question.</td>
    <td></td>
</tr>

    </tbody>
</table>
        <h2  >4. Card Services</h2>
<table  class="summary-table suite-summary"  >
    <tr>
        <th>Total Modules</th>
        <th>Total Test Cases</th>
        <th>Passed</th>
        <th>Failed</th>
      <!--  <th>Skipped</th>-->
    </tr>
    <tr>
        <td>0</td>
        <td>3</td>
        <td>0</td>
        <td>3</td>
       <!-- <td>{{SuiteSkipped}}</td>-->
    </tr>
</table>
<div class="suite-content">
    <div class="metadata">
    <div class="test-case">
        <strong>4.1.1. Check Blocked Cards List</strong><span style="font-weight: normal;"><i> (TC-125 | Card Services)</i></span>
    </div>
    <div class="status">
        <span>Status:</span>
        <br>
        <span class="status-failed">Failed</span>
    </div>
    <div class="status">
        <span>Exec Time (mm:ss)</span>
        <br>
        <span>00:19</span>
    </div>
</div>
<div class="metadata error" style="display: block">
    <span>Error Message:</span>
    <span class="status-failed">Error executing step: Click on Login Button.</span>
</div>
<table class="test-steps-table">
    <tr>
        <th>Step</th>
        <th>Description</th>
        <th style="display: none">Result</th>
        <th style="display: none">Execution Time</th>
        <th>Screenshot</th>
    </tr>
    <tr>
    <td>1</td>
    <td>Click on Finish Button to skip the demo</td>
    <td style="display: none;">Passed</td>
    <td style="display: none;">0s</td>
    <td><img class='test-step-image' src='D:\TFS\DevWork\R and D Work\TestAutomationFramework\TestAutomationFramework\Projects\CA\Results\ScreenShots\Card Services\2025-08-14 12.08.58.295_Click on Finish Button to skip the demo.png' alt='Step Screenshot'></td>
</tr>

    <tr>
        <td class="status-failed" style="display:none" ; colspan="3">Error Details: Can`t Find Element with target: id=LoginBtn </td>
    </tr>
</table>
<div class="metadata">
    <div class="test-case">
        <strong>4.1.2. All users Cards are displayed successfully</strong><span style="font-weight: normal;"><i> (TC-113 | Card Services)</i></span>
    </div>
    <div class="status">
        <span>Status:</span>
        <br>
        <span class="status-failed">Failed</span>
    </div>
    <div class="status">
        <span>Exec Time (mm:ss)</span>
        <br>
        <span>00:10</span>
    </div>
</div>
<div class="metadata error" style="display: block">
    <span>Error Message:</span>
    <span class="status-failed">Error executing step: Click on Continue Button.</span>
</div>
<table class="test-steps-table">
    <tr>
        <th>Step</th>
        <th>Description</th>
        <th style="display: none">Result</th>
        <th style="display: none">Execution Time</th>
        <th>Screenshot</th>
    </tr>
    <tr>
    <td>1</td>
    <td>Click on Finish Button to skip the demo</td>
    <td style="display: none;">Passed</td>
    <td style="display: none;">0s</td>
    <td><img class='test-step-image' src='D:\TFS\DevWork\R and D Work\TestAutomationFramework\TestAutomationFramework\Projects\CA\Results\ScreenShots\Card Services\2025-08-14 12.09.13.261_Click on Finish Button to skip the demo.png' alt='Step Screenshot'></td>
</tr>
<tr>
    <td>2</td>
    <td>Click on Login Button</td>
    <td style="display: none;">Passed</td>
    <td style="display: none;">0s</td>
    <td><img class='test-step-image' src='D:\TFS\DevWork\R and D Work\TestAutomationFramework\TestAutomationFramework\Projects\CA\Results\ScreenShots\Card Services\2025-08-14 12.09.18.374_Click on Login Button.png' alt='Step Screenshot'></td>
</tr>
<tr>
    <td>3</td>
    <td>Type the User Name</td>
    <td style="display: none;">Passed</td>
    <td style="display: none;">0s</td>
    <td><img class='test-step-image' src='D:\TFS\DevWork\R and D Work\TestAutomationFramework\TestAutomationFramework\Projects\CA\Results\ScreenShots\Card Services\2025-08-14 12.09.19.282_Type the User Name.png' alt='Step Screenshot'></td>
</tr>

    <tr>
        <td class="status-failed" style="display:none" ; colspan="3">Error Details: Object reference not set to an instance of an object.</td>
    </tr>
</table>
<div class="metadata">
    <div class="test-case">
        <strong>4.1.3. Check Cards List</strong><span style="font-weight: normal;"><i> (TC-48 | Card Services)</i></span>
    </div>
    <div class="status">
        <span>Status:</span>
        <br>
        <span class="status-failed">Failed</span>
    </div>
    <div class="status">
        <span>Exec Time (mm:ss)</span>
        <br>
        <span>00:24</span>
    </div>
</div>
<div class="metadata error" style="display: block">
    <span>Error Message:</span>
    <span class="status-failed">Error executing step: Type the answer for the Challenge Question.</span>
</div>
<table class="test-steps-table">
    <tr>
        <th>Step</th>
        <th>Description</th>
        <th style="display: none">Result</th>
        <th style="display: none">Execution Time</th>
        <th>Screenshot</th>
    </tr>
    <tr>
    <td>1</td>
    <td>Click on Finish Button to skip the demo</td>
    <td style="display: none;">Passed</td>
    <td style="display: none;">0s</td>
    <td><img class='test-step-image' src='D:\TFS\DevWork\R and D Work\TestAutomationFramework\TestAutomationFramework\Projects\CA\Results\ScreenShots\Card Services\2025-08-14 12.09.36.264_Click on Finish Button to skip the demo.png' alt='Step Screenshot'></td>
</tr>
<tr>
    <td>2</td>
    <td>Click on Login Button</td>
    <td style="display: none;">Passed</td>
    <td style="display: none;">0s</td>
    <td><img class='test-step-image' src='D:\TFS\DevWork\R and D Work\TestAutomationFramework\TestAutomationFramework\Projects\CA\Results\ScreenShots\Card Services\2025-08-14 12.09.43.125_Click on Login Button.png' alt='Step Screenshot'></td>
</tr>
<tr>
    <td>3</td>
    <td>Type the User Name</td>
    <td style="display: none;">Passed</td>
    <td style="display: none;">0s</td>
    <td><img class='test-step-image' src='D:\TFS\DevWork\R and D Work\TestAutomationFramework\TestAutomationFramework\Projects\CA\Results\ScreenShots\Card Services\2025-08-14 12.09.43.996_Type the User Name.png' alt='Step Screenshot'></td>
</tr>
<tr>
    <td>4</td>
    <td>Click on Continue Button</td>
    <td style="display: none;">Passed</td>
    <td style="display: none;">0s</td>
    <td><img class='test-step-image' src='D:\TFS\DevWork\R and D Work\TestAutomationFramework\TestAutomationFramework\Projects\CA\Results\ScreenShots\Card Services\2025-08-14 12.09.45.835_Click on Continue Button.png' alt='Step Screenshot'></td>
</tr>
<tr>
    <td>5</td>
    <td>Type the Password</td>
    <td style="display: none;">Passed</td>
    <td style="display: none;">0s</td>
    <td></td>
</tr>

    <tr>
        <td class="status-failed" style="display:none" ; colspan="3">Error Details: An unknown exception was encountered sending an HTTP request to the remote WebDriver server for URL http://localhost:60918/session/f8a71fe3d7eff41fc60e4d1b6ed3a48f/element. The exception message was: No connection could be made because the target machine actively refused it. (localhost:60918)</td>
    </tr>
</table>

</div>

    </div>
    <div class="footer">
        <p>End of Report</p>
    </div>
</body>
</html>
