namespace TestAutomationFramework.Models
{
    public class TestSuite
    {
        public string TestSuiteName { get; set; }
        public string Description { get; set; }
        public List<TestSuiteItem> TestSuiteItems { get; set; }
        public ReportingSettings ReportingSettings { get; set; }
        public RunSettings RunSettings { get; set; }
    }

    public class TestSuiteItem
    {
        public string Type { get; set; } // "Module" or "TestCase"
        public string Name { get; set; } // "Module" or "TestCase"
        public string Reference { get; set; }
        public string ParamsReference { get; set; }
        public string TestDataReference { get; set; }
        public Parameters Params { get;  set; }
    }

    public class ReportingSettings
    {
        public List<string> ReportTypes { get; set; }
        public List<string> MailRecipients { get; set; }
        public ExcelSettings ExcelSettings { get; set; }
    }

    public class ExcelSettings
    {
        public string FileName { get; set; }
        public bool IncludeScreenshots { get; set; }
    }

    public class RunSettings
    {
        public bool TakeScreenshotsOnFailure { get; set; }
        public bool ContinueOnFailure { get; set; }
        public bool ParallelExecution { get; set; }
    }
}


