{"TestCaseName": "No Card Selected", "TestCaseCode": "TC-49", "Steps": [{"TestCaseReference": {"Name": "Block Card", "TestCasePath": "WorkSet01/TestCases/012.CardServices/BlockCard/BlockCard.json", "ParamsReference": "WorkSet01/Params/BlockCardParams.json"}}, {"Name": "Select block reasaon from the drop list", "Command": "select", "Target": "{{Selectors.BlockReasonDroplist}}", "Value": "Lost"}, {"Name": "Click on Block Card Button", "Command": "Click", "Target": "{{Selectors.BlockCardButton}}"}, {"Name": "Assert popup error message with value: {{Selectors.SelectCardToBlockMessage}}", "Command": "Verify", "Target": "{{Selectors.popupMessage}}", "Value": "{{Selectors.SelectCardToBlockMessage}}"}]}