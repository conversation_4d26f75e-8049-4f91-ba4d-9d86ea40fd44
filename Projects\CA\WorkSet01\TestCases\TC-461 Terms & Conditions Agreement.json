{"TestCaseName": "Terms & Conditions Agreement TDs", "TestCaseCode": "TC-461", "Labels": ["ForTest"], "Steps": [{"Name": "<PERSON>lick on <PERSON><PERSON> <PERSON><PERSON> to skip the demo", "Command": "click", "Target": "{{Select<PERSON>.Fin<PERSON>}}", "ContinueOnError": true, "CustomDelayBeforeStepExecustionInMilliseconds": 2000}, {"Name": "Click on Lo<PERSON>", "Command": "click", "Target": "{{<PERSON><PERSON>.<PERSON>}}", "CustomDelayBeforeStepExecustionInMilliseconds": 2000, "Condition": {"TestCaseStatus": "TokenStopped", "Reference": {"Ref": "TokenStepCondition.json", "Params": {"environment": "UAT", "expectedStep": "LoginStep"}}}}]}