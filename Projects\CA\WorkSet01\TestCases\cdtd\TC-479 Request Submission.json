{"TestCaseName": "Request Submission", "TestCaseCode": "TC-479", "Steps": [{"TestCaseReference": {"Name": "<PERSON><PERSON>", "TestCasePath": "WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json", "ParamsReference": "WorkSet01/Params/LoginTestCaseParams.json", "Params": {"TestData": {"UserName": "sherif1234", "Password": "Password1", "ChallengeAnswer": "bmw"}}}}, {"Name": "Click on Cancel Button", "Command": "click", "Target": "id=popup_cancel", "ElementToValidateThatScreenLoaded": "css=#popbuttons > div > button.btn.Cancel.back_gradient2", "ContinueOnError": true}, {"Name": "Click on <PERSON><PERSON>", "Command": "click", "Target": "css=#popbuttons > div > button.btn.Cancel.back_gradient2", "ContinueOnError": true, "CustomDelayBeforeStepExecustionInMilliseconds": 5000}, {"Name": "Click on biometric alert", "Command": "Click", "Target": "{{Selectors.BioMetricAlert}}", "ContinueOnError": true}, {"Name": "Click on Deposits button from side menu", "Command": "Click", "Target": "{{Select<PERSON>.Depo<PERSON>}}"}, {"Name": "Click on Time Deposits button from side menu", "Command": "Click", "Target": "{{Selectors.TimeDeposits}}"}, {"Name": "Click on Select Deposit button", "Command": "Click", "Target": "{{Selectors.SelectCertificateBtn}}"}, {"Name": "Click on Deposit", "Command": "Click", "Target": "{{Select<PERSON>.Deposit}}"}, {"Name": "Enter duration as 1 Day", "Command": "type", "Target": "{{Selectors.durationDay}}", "Value": "1"}, {"Name": "Type Amount", "Command": "type", "Target": "{{Selectors.Amount}}", "value": "1000"}, {"Name": "<PERSON>lick on Select Account <PERSON><PERSON>", "Command": "click", "Target": "{{Selectors.SelectAccountBtn}}"}, {"Name": "Click on Account", "Command": "click", "Target": "{{Selectors.Account}}"}, {"Name": "Click on Yes", "Command": "click", "Target": "{{Selectors.SameAccOption}}"}, {"Name": "Click on Continue <PERSON> and navigate to confirmation screen", "Command": "click", "Target": "{{Selectors.ContinueBookProductBtn}}"}]}