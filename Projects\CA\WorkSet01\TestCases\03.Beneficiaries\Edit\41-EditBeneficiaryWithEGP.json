{"TestCaseName": "Edit Banks Inside Egypt Screen: All Fields Editable for EGP Currency", "TestCaseCode": "TC-41", "Steps": [{"TestCaseReference": {"Name": "<PERSON><PERSON>", "TestCasePath": "WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json", "ParamsReference": "WorkSet01/Params/LoginTestCaseParams.json"}}, {"Name": "Click on change later button on the password expiration popup message", "Command": "Click", "Target": "{{Selectors.popupCancel}}", "ContinueOnError": true}, {"Name": "Click on biometric alert", "Command": "Click", "Target": "{{Selectors.BioMetricAlert}}", "ContinueOnError": true}, {"Name": "Execute JS code to activate token", "Command": "executescript", "Value": "Globals.ActivationState = 'ACTIVATED';"}, {"Name": "Click on Beneficiaries", "Command": "click", "Target": "{{Selectors.BeneficiariesToButt<PERSON>}}"}, {"Name": "Click on Local Transfer option", "Command": "Click", "Target": "{{Selectors.LocalTransfer}}"}, {"Name": "<PERSON><PERSON> EGP Beneficiary to edit", "Command": "Click", "Target": "css=#Benf3"}, {"Name": "Click on Edit Beneficiary button", "Command": "Click", "Target": "{{Selectors.EditBTN}}"}, {"Name": "Edit Beneficiary nickname", "Command": "type", "Target": "{{Selectors.BenNickNameTextbox}}", "Value": "TestNickName2"}, {"Name": "Edit Beneficiary full name", "Command": "type", "Target": "{{Selectors.BenFullNameTextbox}}", "Value": "TestFullNameOther"}, {"Name": "Edit Beneficiary account number", "Command": "type", "Target": "{{Selectors.BenAccNumberTextbox}}", "Value": "*************"}, {"Name": "Edit Beneficiary bank", "Command": "click", "Target": "{{Selectors.EditBenBankselect}}"}, {"Name": "select Beneficiary bank", "Command": "click", "Target": "css=#Banks > div > div:nth-child(7) > span"}, {"Name": "Click on save button", "Command": "Click", "Target": "{{Selectors.SaveLocalBenButton}}"}, {"Name": "Click on continue button", "Command": "Click", "Target": "{{Selectors.ContinueBtn}}"}]}