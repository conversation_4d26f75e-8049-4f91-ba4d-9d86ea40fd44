================================ Start of LogFile ================================
2025-08-07 11:11:39.697 [Info] Checking for work directory: ..\..\..\Projects\CA
2025-08-07 11:11:39.730 [Info] Using work directory: D:\TFS\DevWork\R and D Work\TestAutomationFramework\TestAutomationFramework\Projects\CA
2025-08-07 11:11:39.731 [Info] No Args for SuitePaths, scanning: WorkSet01\TestSuites, WorkSet02\TestSuites, WorkSet03\TestSuites, WorkSet04\TestSuites, MainWorkSet\TestSuites
2025-08-07 11:11:39.733 [Info] Retrieving all test cases.
2025-08-07 11:11:39.734 [Info] Retrieving test suite paths.
2025-08-07 11:11:39.734 [Info] Retrieved 0 test suite paths.
2025-08-07 11:11:39.735 [Info] Retrieved 1 test suite paths.
2025-08-07 11:11:39.735 [Info] Retrieved 99 test suite paths.
2025-08-07 11:11:39.735 [Info] Retrieved 154 test suite paths.
2025-08-07 11:11:39.736 [Info] Retrieved 164 test suite paths.
2025-08-07 11:11:39.739 [Info] Loading RunningInstructions configuration...
2025-08-07 11:11:39.740 [Info] Attempting to load configuration file: RunningInstructions.json
2025-08-07 11:11:39.740 [Info] Successfully loaded configuration file: RunningInstructions.json
2025-08-07 11:11:39.752 [Info] Attempting to load configuration file: RunningInstructions.local.json
2025-08-07 11:11:39.753 [Info] Successfully loaded configuration file: RunningInstructions.local.json
2025-08-07 11:11:39.753 [Info] Merging local RunningInstructions with main configuration.
2025-08-07 11:11:39.753 [Info] Successfully merged local RunningInstructions with main configuration.
2025-08-07 11:11:39.768 [Debug] Applying suite filtering with regexes: .*workset01.*testsuites.*
2025-08-07 11:11:39.776 [Info] Loading test suite: WorkSet01\TestSuites\Sanity.json
2025-08-07 11:11:39.776 [Info] Attempting to load test suite: WorkSet01\TestSuites\Sanity.json
2025-08-07 11:11:39.782 [Info] Successfully loaded test suite: WorkSet01\TestSuites\Sanity.json
2025-08-07 11:11:39.782 [Debug] Loading test case: WorkSet01/TestCases/021.HappyPoints/29.json
2025-08-07 11:11:39.782 [Info] Loading test case from: WorkSet01/TestCases/021.HappyPoints/29.json
2025-08-07 11:11:39.782 [Info] Attempting to load configuration file: WorkSet01/TestCases/021.HappyPoints/29.json
2025-08-07 11:11:39.783 [Info] Successfully loaded configuration file: WorkSet01/TestCases/021.HappyPoints/29.json
2025-08-07 11:11:39.796 [Info] No filtering configuration found. Including test case 'Happy Points valid redemption'.
2025-08-07 11:11:39.798 [Info] Attempting to load configuration file: GlobalParams.json
2025-08-07 11:11:39.798 [Info] Successfully loaded configuration file: GlobalParams.json
2025-08-07 11:11:39.810 [Debug] Resolving parameters for test case: Happy Points valid redemption
2025-08-07 11:11:39.811 [Debug] Merging params
2025-08-07 11:11:39.811 [Debug] Merged basic params
2025-08-07 11:11:39.811 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-07 11:11:39.811 [Debug] Merged SpecialEnvParams params
2025-08-07 11:11:39.811 [Debug] Merged params
2025-08-07 11:11:39.811 [Debug] Loading parameters from reference file: WorkSet01/Params/HappyPointsParams.json
2025-08-07 11:11:39.811 [Info] Attempting to load configuration file: WorkSet01/Params/HappyPointsParams.json
2025-08-07 11:11:39.811 [Info] Successfully loaded configuration file: WorkSet01/Params/HappyPointsParams.json
2025-08-07 11:11:39.811 [Debug] Merging params
2025-08-07 11:11:39.811 [Debug] Merged basic params
2025-08-07 11:11:39.811 [Debug] Merged params
2025-08-07 11:11:39.811 [Debug] Merging params
2025-08-07 11:11:39.811 [Debug] Merging params
2025-08-07 11:11:39.811 [Debug] Resolving placeholders in test steps for test case: Happy Points valid redemption
2025-08-07 11:11:39.811 [Debug] Resolving TestCaseReference in step: 
2025-08-07 11:11:39.811 [Info] Loading test case from: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:11:39.811 [Info] Attempting to load configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:11:39.811 [Info] Successfully loaded configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:11:39.813 [Debug] Resolving parameters for test case: Login
2025-08-07 11:11:39.813 [Debug] Merging params
2025-08-07 11:11:39.813 [Debug] Merged basic params
2025-08-07 11:11:39.813 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-07 11:11:39.813 [Debug] Merged SpecialEnvParams params
2025-08-07 11:11:39.813 [Debug] Merged params
2025-08-07 11:11:39.813 [Debug] Loading parameters from reference file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:11:39.813 [Info] Attempting to load configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:11:39.813 [Info] Successfully loaded configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:11:39.813 [Debug] Merging params
2025-08-07 11:11:39.813 [Debug] Merged basic params
2025-08-07 11:11:39.813 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-07 11:11:39.813 [Debug] Merged SpecialEnvParams params
2025-08-07 11:11:39.813 [Debug] Merged params
2025-08-07 11:11:39.813 [Debug] Merging params
2025-08-07 11:11:39.813 [Debug] Merging params
2025-08-07 11:11:39.813 [Debug] Resolving placeholders in test steps for test case: Login
2025-08-07 11:11:39.814 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-07 11:11:39.814 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-07 11:11:39.814 [Debug] Resolving placeholders in string: {{Selectors.FinishButton}}
2025-08-07 11:11:39.814 [Debug] Resolved string: id=finish
2025-08-07 11:11:39.815 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-07 11:11:39.815 [Debug] Resolved string: Click on Login Button
2025-08-07 11:11:39.815 [Debug] Resolving placeholders in string: {{Selectors.LoginButton}}
2025-08-07 11:11:39.815 [Debug] Resolved string: id=LoginBtn
2025-08-07 11:11:39.815 [Debug] Resolving placeholders in string: Type the User Name
2025-08-07 11:11:39.815 [Debug] Resolved string: Type the User Name
2025-08-07 11:11:39.815 [Debug] Resolving placeholders in string: {{Selectors.UserNameField}}
2025-08-07 11:11:39.815 [Debug] Resolved string: id=UserName
2025-08-07 11:11:39.815 [Debug] Resolving placeholders in string: {{TestData.UserName}}
2025-08-07 11:11:39.815 [Debug] Resolved string: Mm_azmy
2025-08-07 11:11:39.815 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-07 11:11:39.815 [Debug] Resolved string: Click on Continue Button
2025-08-07 11:11:39.815 [Debug] Resolving placeholders in string: {{Selectors.ContinueButton}}
2025-08-07 11:11:39.815 [Debug] Resolved string: id=btn
2025-08-07 11:11:39.815 [Debug] Resolving placeholders in string: Type the Password
2025-08-07 11:11:39.815 [Debug] Resolved string: Type the Password
2025-08-07 11:11:39.815 [Debug] Resolving placeholders in string: {{Selectors.PasswordField}}
2025-08-07 11:11:39.815 [Debug] Resolved string: id=Password
2025-08-07 11:11:39.815 [Debug] Resolving placeholders in string: $('[{{Selectors.PasswordField}}]').val('{{TestData.Password}}')
2025-08-07 11:11:39.815 [Debug] Resolved string: $('[id=Password]').val('Asdf2025')
2025-08-07 11:11:39.815 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-07 11:11:39.815 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-07 11:11:39.815 [Debug] Resolving placeholders in string: {{Selectors.ChallengeQuestionField}}
2025-08-07 11:11:39.815 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-07 11:11:39.815 [Debug] Resolving placeholders in string: {{TestData.ChallengeAnswer}}
2025-08-07 11:11:39.815 [Debug] Resolved string: armada
2025-08-07 11:11:39.815 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-07 11:11:39.815 [Debug] Resolved string: Click on the Login Button
2025-08-07 11:11:39.815 [Debug] Resolving placeholders in string: {{Selectors.LoginAuthButton}}
2025-08-07 11:11:39.815 [Debug] Resolved string: id=LoginAuthBtn
2025-08-07 11:11:39.815 [Info] Successfully resolved parameters for test case: Login
2025-08-07 11:11:39.815 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-07 11:11:39.815 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-07 11:11:39.815 [Debug] Resolving placeholders in string: id=finish
2025-08-07 11:11:39.815 [Debug] Resolved string: id=finish
2025-08-07 11:11:39.815 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-07 11:11:39.815 [Debug] Resolved string: Click on Login Button
2025-08-07 11:11:39.816 [Debug] Resolving placeholders in string: id=LoginBtn
2025-08-07 11:11:39.816 [Debug] Resolved string: id=LoginBtn
2025-08-07 11:11:39.816 [Debug] Resolving placeholders in string: Type the User Name
2025-08-07 11:11:39.816 [Debug] Resolved string: Type the User Name
2025-08-07 11:11:39.816 [Debug] Resolving placeholders in string: id=UserName
2025-08-07 11:11:39.816 [Debug] Resolved string: id=UserName
2025-08-07 11:11:39.816 [Debug] Resolving placeholders in string: Mm_azmy
2025-08-07 11:11:39.816 [Debug] Resolved string: Mm_azmy
2025-08-07 11:11:39.816 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-07 11:11:39.816 [Debug] Resolved string: Click on Continue Button
2025-08-07 11:11:39.816 [Debug] Resolving placeholders in string: id=btn
2025-08-07 11:11:39.816 [Debug] Resolved string: id=btn
2025-08-07 11:11:39.816 [Debug] Resolving placeholders in string: Type the Password
2025-08-07 11:11:39.816 [Debug] Resolved string: Type the Password
2025-08-07 11:11:39.816 [Debug] Resolving placeholders in string: id=Password
2025-08-07 11:11:39.816 [Debug] Resolved string: id=Password
2025-08-07 11:11:39.816 [Debug] Resolving placeholders in string: $('[id=Password]').val('Asdf2025')
2025-08-07 11:11:39.816 [Debug] Resolved string: $('[id=Password]').val('Asdf2025')
2025-08-07 11:11:39.816 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-07 11:11:39.816 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-07 11:11:39.816 [Debug] Resolving placeholders in string: id=ChallengeQuestionAnswer
2025-08-07 11:11:39.816 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-07 11:11:39.816 [Debug] Resolving placeholders in string: armada
2025-08-07 11:11:39.816 [Debug] Resolved string: armada
2025-08-07 11:11:39.816 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-07 11:11:39.816 [Debug] Resolved string: Click on the Login Button
2025-08-07 11:11:39.816 [Debug] Resolving placeholders in string: id=LoginAuthBtn
2025-08-07 11:11:39.816 [Debug] Resolved string: id=LoginAuthBtn
2025-08-07 11:11:39.816 [Debug] Resolving placeholders in string: Click on change later button on the password expiration popup message
2025-08-07 11:11:39.816 [Debug] Resolved string: Click on change later button on the password expiration popup message
2025-08-07 11:11:39.816 [Debug] Resolving placeholders in string: {{Selectors.popupCancel}}
2025-08-07 11:11:39.816 [Debug] Resolved string: id=popup_cancel
2025-08-07 11:11:39.816 [Debug] Resolving placeholders in string: Click on biometric alert
2025-08-07 11:11:39.816 [Debug] Resolved string: Click on biometric alert
2025-08-07 11:11:39.816 [Debug] Resolving placeholders in string: {{Selectors.BioMetricAlert}}
2025-08-07 11:11:39.816 [Debug] Resolved string: css=#popbuttons > div > button.btn.Cancel.back_gradient2
2025-08-07 11:11:39.816 [Debug] Resolving placeholders in string: Click on Happy points option from side menu
2025-08-07 11:11:39.816 [Debug] Resolved string: Click on Happy points option from side menu
2025-08-07 11:11:39.816 [Debug] Resolving placeholders in string: {{Selectors.HappyPointsOption}}
2025-08-07 11:11:39.816 [Debug] Resolved string: xpath=//div[@class='mainmenuitems']//label[contains(text(), 'Happy Points')]
2025-08-07 11:11:39.816 [Debug] Resolving placeholders in string: Click on Redeem Online button
2025-08-07 11:11:39.816 [Debug] Resolved string: Click on Redeem Online button
2025-08-07 11:11:39.816 [Debug] Resolving placeholders in string: {{Selectors.RedeemOnline}}
2025-08-07 11:11:39.816 [Debug] Resolved string: css=#BuyOnlineBTN > div
2025-08-07 11:11:39.817 [Debug] Resolving placeholders in string: Choose a merchant
2025-08-07 11:11:39.817 [Debug] Resolved string: Choose a merchant
2025-08-07 11:11:39.817 [Debug] Resolving placeholders in string: {{Selectors.SelectMerchant}}
2025-08-07 11:11:39.817 [Debug] Resolved string: css=#RequestItem0
2025-08-07 11:11:39.817 [Debug] Resolving placeholders in string: Click Redeem button
2025-08-07 11:11:39.817 [Debug] Resolved string: Click Redeem button
2025-08-07 11:11:39.817 [Debug] Resolving placeholders in string: {{Selectors.RedeemButton}}
2025-08-07 11:11:39.817 [Debug] Resolved string: id=RedeemID
2025-08-07 11:11:39.817 [Debug] Resolving placeholders in string: Enter the number of points to redeem
2025-08-07 11:11:39.817 [Debug] Resolved string: Enter the number of points to redeem
2025-08-07 11:11:39.817 [Debug] Resolving placeholders in string: {{Selectors.PointsTextbox}}
2025-08-07 11:11:39.817 [Debug] Resolved string: id=redeempointsID
2025-08-07 11:11:39.817 [Debug] Resolving placeholders in string: 15000
2025-08-07 11:11:39.817 [Debug] Resolved string: 15000
2025-08-07 11:11:39.817 [Debug] Resolving placeholders in string: Click on create cupon button
2025-08-07 11:11:39.817 [Debug] Resolved string: Click on create cupon button
2025-08-07 11:11:39.817 [Debug] Resolving placeholders in string: {{Selectors.CreateCupon}}
2025-08-07 11:11:39.817 [Debug] Resolved string: id=CCoponID
2025-08-07 11:11:39.817 [Debug] Resolving placeholders in string: Click on Continue button
2025-08-07 11:11:39.817 [Debug] Resolved string: Click on Continue button
2025-08-07 11:11:39.817 [Debug] Resolving placeholders in string: {{Selectors.CuntinueConfirmCupon}}
2025-08-07 11:11:39.817 [Debug] Resolved string: id=RequestRedemptionConfrimationSbmtBtn
2025-08-07 11:11:39.817 [Debug] Resolving placeholders in string: Ensure that it redirected to the token page
2025-08-07 11:11:39.817 [Debug] Resolved string: Ensure that it redirected to the token page
2025-08-07 11:11:39.817 [Debug] Resolving placeholders in string: {{Selectors.OTPbox}}
2025-08-07 11:11:39.817 [Debug] Resolved string: id=OTPInpt
2025-08-07 11:11:39.817 [Info] Successfully resolved parameters for test case: Happy Points valid redemption
2025-08-07 11:11:39.818 [Debug] Loading test case: WorkSet01/TestCases/021.HappyPoints/31.json
2025-08-07 11:11:39.818 [Info] Loading test case from: WorkSet01/TestCases/021.HappyPoints/31.json
2025-08-07 11:11:39.818 [Info] Attempting to load configuration file: WorkSet01/TestCases/021.HappyPoints/31.json
2025-08-07 11:11:39.818 [Info] Successfully loaded configuration file: WorkSet01/TestCases/021.HappyPoints/31.json
2025-08-07 11:11:39.818 [Info] No filtering configuration found. Including test case 'Redeem less than 10000 points'.
2025-08-07 11:11:39.818 [Debug] Resolving parameters for test case: Redeem less than 10000 points
2025-08-07 11:11:39.818 [Debug] Merging params
2025-08-07 11:11:39.818 [Debug] Merged basic params
2025-08-07 11:11:39.818 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-07 11:11:39.818 [Debug] Merged SpecialEnvParams params
2025-08-07 11:11:39.818 [Debug] Merged params
2025-08-07 11:11:39.818 [Debug] Loading parameters from reference file: WorkSet01/Params/HappyPointsParams.json
2025-08-07 11:11:39.818 [Info] Attempting to load configuration file: WorkSet01/Params/HappyPointsParams.json
2025-08-07 11:11:39.819 [Info] Successfully loaded configuration file: WorkSet01/Params/HappyPointsParams.json
2025-08-07 11:11:39.819 [Debug] Merging params
2025-08-07 11:11:39.819 [Debug] Merged basic params
2025-08-07 11:11:39.819 [Debug] Merged params
2025-08-07 11:11:39.819 [Debug] Merging params
2025-08-07 11:11:39.819 [Debug] Merging params
2025-08-07 11:11:39.819 [Debug] Resolving placeholders in test steps for test case: Redeem less than 10000 points
2025-08-07 11:11:39.819 [Debug] Resolving TestCaseReference in step: 
2025-08-07 11:11:39.819 [Info] Loading test case from: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:11:39.826 [Info] Attempting to load configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:11:39.826 [Info] Successfully loaded configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:11:39.827 [Debug] Resolving parameters for test case: Login
2025-08-07 11:11:39.827 [Debug] Merging params
2025-08-07 11:11:39.827 [Debug] Merged basic params
2025-08-07 11:11:39.827 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-07 11:11:39.827 [Debug] Merged SpecialEnvParams params
2025-08-07 11:11:39.827 [Debug] Merged params
2025-08-07 11:11:39.827 [Debug] Loading parameters from reference file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:11:39.827 [Info] Attempting to load configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:11:39.827 [Info] Successfully loaded configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:11:39.827 [Debug] Merging params
2025-08-07 11:11:39.827 [Debug] Merged basic params
2025-08-07 11:11:39.827 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-07 11:11:39.827 [Debug] Merged SpecialEnvParams params
2025-08-07 11:11:39.827 [Debug] Merged params
2025-08-07 11:11:39.827 [Debug] Merging params
2025-08-07 11:11:39.827 [Debug] Merging params
2025-08-07 11:11:39.827 [Debug] Resolving placeholders in test steps for test case: Login
2025-08-07 11:11:39.827 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-07 11:11:39.827 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-07 11:11:39.827 [Debug] Resolving placeholders in string: {{Selectors.FinishButton}}
2025-08-07 11:11:39.827 [Debug] Resolved string: id=finish
2025-08-07 11:11:39.827 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-07 11:11:39.827 [Debug] Resolved string: Click on Login Button
2025-08-07 11:11:39.827 [Debug] Resolving placeholders in string: {{Selectors.LoginButton}}
2025-08-07 11:11:39.827 [Debug] Resolved string: id=LoginBtn
2025-08-07 11:11:39.827 [Debug] Resolving placeholders in string: Type the User Name
2025-08-07 11:11:39.827 [Debug] Resolved string: Type the User Name
2025-08-07 11:11:39.827 [Debug] Resolving placeholders in string: {{Selectors.UserNameField}}
2025-08-07 11:11:39.827 [Debug] Resolved string: id=UserName
2025-08-07 11:11:39.827 [Debug] Resolving placeholders in string: {{TestData.UserName}}
2025-08-07 11:11:39.827 [Debug] Resolved string: Mm_azmy
2025-08-07 11:11:39.827 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-07 11:11:39.827 [Debug] Resolved string: Click on Continue Button
2025-08-07 11:11:39.827 [Debug] Resolving placeholders in string: {{Selectors.ContinueButton}}
2025-08-07 11:11:39.827 [Debug] Resolved string: id=btn
2025-08-07 11:11:39.827 [Debug] Resolving placeholders in string: Type the Password
2025-08-07 11:11:39.827 [Debug] Resolved string: Type the Password
2025-08-07 11:11:39.827 [Debug] Resolving placeholders in string: {{Selectors.PasswordField}}
2025-08-07 11:11:39.827 [Debug] Resolved string: id=Password
2025-08-07 11:11:39.827 [Debug] Resolving placeholders in string: $('[{{Selectors.PasswordField}}]').val('{{TestData.Password}}')
2025-08-07 11:11:39.827 [Debug] Resolved string: $('[id=Password]').val('Asdf2025')
2025-08-07 11:11:39.827 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-07 11:11:39.827 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-07 11:11:39.827 [Debug] Resolving placeholders in string: {{Selectors.ChallengeQuestionField}}
2025-08-07 11:11:39.827 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-07 11:11:39.827 [Debug] Resolving placeholders in string: {{TestData.ChallengeAnswer}}
2025-08-07 11:11:39.827 [Debug] Resolved string: armada
2025-08-07 11:11:39.827 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-07 11:11:39.827 [Debug] Resolved string: Click on the Login Button
2025-08-07 11:11:39.827 [Debug] Resolving placeholders in string: {{Selectors.LoginAuthButton}}
2025-08-07 11:11:39.827 [Debug] Resolved string: id=LoginAuthBtn
2025-08-07 11:11:39.827 [Info] Successfully resolved parameters for test case: Login
2025-08-07 11:11:39.827 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-07 11:11:39.827 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-07 11:11:39.827 [Debug] Resolving placeholders in string: id=finish
2025-08-07 11:11:39.827 [Debug] Resolved string: id=finish
2025-08-07 11:11:39.827 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-07 11:11:39.827 [Debug] Resolved string: Click on Login Button
2025-08-07 11:11:39.827 [Debug] Resolving placeholders in string: id=LoginBtn
2025-08-07 11:11:39.827 [Debug] Resolved string: id=LoginBtn
2025-08-07 11:11:39.827 [Debug] Resolving placeholders in string: Type the User Name
2025-08-07 11:11:39.827 [Debug] Resolved string: Type the User Name
2025-08-07 11:11:39.827 [Debug] Resolving placeholders in string: id=UserName
2025-08-07 11:11:39.827 [Debug] Resolved string: id=UserName
2025-08-07 11:11:39.827 [Debug] Resolving placeholders in string: Mm_azmy
2025-08-07 11:11:39.828 [Debug] Resolved string: Mm_azmy
2025-08-07 11:11:39.828 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-07 11:11:39.828 [Debug] Resolved string: Click on Continue Button
2025-08-07 11:11:39.828 [Debug] Resolving placeholders in string: id=btn
2025-08-07 11:11:39.828 [Debug] Resolved string: id=btn
2025-08-07 11:11:39.828 [Debug] Resolving placeholders in string: Type the Password
2025-08-07 11:11:39.828 [Debug] Resolved string: Type the Password
2025-08-07 11:11:39.828 [Debug] Resolving placeholders in string: id=Password
2025-08-07 11:11:39.828 [Debug] Resolved string: id=Password
2025-08-07 11:11:39.828 [Debug] Resolving placeholders in string: $('[id=Password]').val('Asdf2025')
2025-08-07 11:11:39.828 [Debug] Resolved string: $('[id=Password]').val('Asdf2025')
2025-08-07 11:11:39.828 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-07 11:11:39.828 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-07 11:11:39.828 [Debug] Resolving placeholders in string: id=ChallengeQuestionAnswer
2025-08-07 11:11:39.828 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-07 11:11:39.828 [Debug] Resolving placeholders in string: armada
2025-08-07 11:11:39.828 [Debug] Resolved string: armada
2025-08-07 11:11:39.828 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-07 11:11:39.828 [Debug] Resolved string: Click on the Login Button
2025-08-07 11:11:39.828 [Debug] Resolving placeholders in string: id=LoginAuthBtn
2025-08-07 11:11:39.828 [Debug] Resolved string: id=LoginAuthBtn
2025-08-07 11:11:39.828 [Debug] Resolving placeholders in string: Click on Happy points option from side menu
2025-08-07 11:11:39.828 [Debug] Resolved string: Click on Happy points option from side menu
2025-08-07 11:11:39.828 [Debug] Resolving placeholders in string: {{Selectors.HappyPointsOption}}
2025-08-07 11:11:39.828 [Debug] Resolved string: xpath=//div[@class='mainmenuitems']//label[contains(text(), 'Happy Points')]
2025-08-07 11:11:39.828 [Debug] Resolving placeholders in string: Click on Redeem Online button
2025-08-07 11:11:39.828 [Debug] Resolved string: Click on Redeem Online button
2025-08-07 11:11:39.828 [Debug] Resolving placeholders in string: {{Selectors.RedeemOnline}}
2025-08-07 11:11:39.828 [Debug] Resolved string: css=#BuyOnlineBTN > div
2025-08-07 11:11:39.828 [Debug] Resolving placeholders in string: Choose a merchant
2025-08-07 11:11:39.828 [Debug] Resolved string: Choose a merchant
2025-08-07 11:11:39.828 [Debug] Resolving placeholders in string: {{Selectors.SelectMerchant}}
2025-08-07 11:11:39.828 [Debug] Resolved string: css=#RequestItem0
2025-08-07 11:11:39.828 [Debug] Resolving placeholders in string: Click Redeem button
2025-08-07 11:11:39.828 [Debug] Resolved string: Click Redeem button
2025-08-07 11:11:39.828 [Debug] Resolving placeholders in string: {{Selectors.RedeemButton}}
2025-08-07 11:11:39.828 [Debug] Resolved string: id=RedeemID
2025-08-07 11:11:39.828 [Debug] Resolving placeholders in string: Enter the number of points to redeem
2025-08-07 11:11:39.828 [Debug] Resolved string: Enter the number of points to redeem
2025-08-07 11:11:39.828 [Debug] Resolving placeholders in string: {{Selectors.PointsTextbox}}
2025-08-07 11:11:39.828 [Debug] Resolved string: id=redeempointsID
2025-08-07 11:11:39.828 [Debug] Resolving placeholders in string: 900
2025-08-07 11:11:39.828 [Debug] Resolved string: 900
2025-08-07 11:11:39.828 [Debug] Resolving placeholders in string: Click on create cupon button
2025-08-07 11:11:39.828 [Debug] Resolved string: Click on create cupon button
2025-08-07 11:11:39.828 [Debug] Resolving placeholders in string: {{Selectors.CreateCupon}}
2025-08-07 11:11:39.828 [Debug] Resolved string: id=CCoponID
2025-08-07 11:11:39.828 [Debug] Resolving placeholders in string: Click on Continue button
2025-08-07 11:11:39.828 [Debug] Resolved string: Click on Continue button
2025-08-07 11:11:39.828 [Debug] Resolving placeholders in string: {{Selectors.CuntinueConfirmCupon}}
2025-08-07 11:11:39.828 [Debug] Resolved string: id=RequestRedemptionConfrimationSbmtBtn
2025-08-07 11:11:39.828 [Debug] Resolving placeholders in string: Ensure that it redirected to the token page
2025-08-07 11:11:39.828 [Debug] Resolved string: Ensure that it redirected to the token page
2025-08-07 11:11:39.828 [Debug] Resolving placeholders in string: {{Selectors.OTPbox}}
2025-08-07 11:11:39.828 [Debug] Resolved string: id=OTPInpt
2025-08-07 11:11:39.828 [Info] Successfully resolved parameters for test case: Redeem less than 10000 points
2025-08-07 11:11:39.828 [Debug] Loading test case: WorkSet01/TestCases/021.HappyPoints/32.json
2025-08-07 11:11:39.828 [Info] Loading test case from: WorkSet01/TestCases/021.HappyPoints/32.json
2025-08-07 11:11:39.828 [Info] Attempting to load configuration file: WorkSet01/TestCases/021.HappyPoints/32.json
2025-08-07 11:11:39.828 [Info] Successfully loaded configuration file: WorkSet01/TestCases/021.HappyPoints/32.json
2025-08-07 11:11:39.829 [Info] No filtering configuration found. Including test case 'Verify That User Can't Redeem Happy Points Using Wrong SMS'.
2025-08-07 11:11:39.829 [Debug] Resolving parameters for test case: Verify That User Can't Redeem Happy Points Using Wrong SMS
2025-08-07 11:11:39.829 [Debug] Merging params
2025-08-07 11:11:39.829 [Debug] Merged basic params
2025-08-07 11:11:39.829 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-07 11:11:39.829 [Debug] Merged SpecialEnvParams params
2025-08-07 11:11:39.829 [Debug] Merged params
2025-08-07 11:11:39.829 [Debug] Loading parameters from reference file: WorkSet01/Params/HappyPointsParams.json
2025-08-07 11:11:39.829 [Info] Attempting to load configuration file: WorkSet01/Params/HappyPointsParams.json
2025-08-07 11:11:39.831 [Info] Successfully loaded configuration file: WorkSet01/Params/HappyPointsParams.json
2025-08-07 11:11:39.831 [Debug] Merging params
2025-08-07 11:11:39.831 [Debug] Merged basic params
2025-08-07 11:11:39.831 [Debug] Merged params
2025-08-07 11:11:39.831 [Debug] Merging params
2025-08-07 11:11:39.831 [Debug] Merging params
2025-08-07 11:11:39.831 [Debug] Resolving placeholders in test steps for test case: Verify That User Can't Redeem Happy Points Using Wrong SMS
2025-08-07 11:11:39.831 [Debug] Resolving TestCaseReference in step: 
2025-08-07 11:11:39.831 [Info] Loading test case from: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:11:39.831 [Info] Attempting to load configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:11:39.831 [Info] Successfully loaded configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:11:39.832 [Debug] Resolving parameters for test case: Login
2025-08-07 11:11:39.832 [Debug] Merging params
2025-08-07 11:11:39.832 [Debug] Merged basic params
2025-08-07 11:11:39.832 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-07 11:11:39.832 [Debug] Merged SpecialEnvParams params
2025-08-07 11:11:39.832 [Debug] Merged params
2025-08-07 11:11:39.832 [Debug] Loading parameters from reference file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:11:39.832 [Info] Attempting to load configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:11:39.832 [Info] Successfully loaded configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:11:39.832 [Debug] Merging params
2025-08-07 11:11:39.832 [Debug] Merged basic params
2025-08-07 11:11:39.832 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-07 11:11:39.832 [Debug] Merged SpecialEnvParams params
2025-08-07 11:11:39.832 [Debug] Merged params
2025-08-07 11:11:39.832 [Debug] Merging params
2025-08-07 11:11:39.832 [Debug] Merging params
2025-08-07 11:11:39.833 [Debug] Resolving placeholders in test steps for test case: Login
2025-08-07 11:11:39.833 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-07 11:11:39.833 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-07 11:11:39.833 [Debug] Resolving placeholders in string: {{Selectors.FinishButton}}
2025-08-07 11:11:39.833 [Debug] Resolved string: id=finish
2025-08-07 11:11:39.833 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-07 11:11:39.833 [Debug] Resolved string: Click on Login Button
2025-08-07 11:11:39.833 [Debug] Resolving placeholders in string: {{Selectors.LoginButton}}
2025-08-07 11:11:39.833 [Debug] Resolved string: id=LoginBtn
2025-08-07 11:11:39.833 [Debug] Resolving placeholders in string: Type the User Name
2025-08-07 11:11:39.833 [Debug] Resolved string: Type the User Name
2025-08-07 11:11:39.833 [Debug] Resolving placeholders in string: {{Selectors.UserNameField}}
2025-08-07 11:11:39.833 [Debug] Resolved string: id=UserName
2025-08-07 11:11:39.833 [Debug] Resolving placeholders in string: {{TestData.UserName}}
2025-08-07 11:11:39.833 [Debug] Resolved string: Mm_azmy
2025-08-07 11:11:39.833 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-07 11:11:39.833 [Debug] Resolved string: Click on Continue Button
2025-08-07 11:11:39.833 [Debug] Resolving placeholders in string: {{Selectors.ContinueButton}}
2025-08-07 11:11:39.833 [Debug] Resolved string: id=btn
2025-08-07 11:11:39.833 [Debug] Resolving placeholders in string: Type the Password
2025-08-07 11:11:39.833 [Debug] Resolved string: Type the Password
2025-08-07 11:11:39.833 [Debug] Resolving placeholders in string: {{Selectors.PasswordField}}
2025-08-07 11:11:39.833 [Debug] Resolved string: id=Password
2025-08-07 11:11:39.833 [Debug] Resolving placeholders in string: $('[{{Selectors.PasswordField}}]').val('{{TestData.Password}}')
2025-08-07 11:11:39.833 [Debug] Resolved string: $('[id=Password]').val('Asdf2025')
2025-08-07 11:11:39.834 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-07 11:11:39.834 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-07 11:11:39.834 [Debug] Resolving placeholders in string: {{Selectors.ChallengeQuestionField}}
2025-08-07 11:11:39.834 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-07 11:11:39.834 [Debug] Resolving placeholders in string: {{TestData.ChallengeAnswer}}
2025-08-07 11:11:39.834 [Debug] Resolved string: armada
2025-08-07 11:11:39.834 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-07 11:11:39.834 [Debug] Resolved string: Click on the Login Button
2025-08-07 11:11:39.834 [Debug] Resolving placeholders in string: {{Selectors.LoginAuthButton}}
2025-08-07 11:11:39.834 [Debug] Resolved string: id=LoginAuthBtn
2025-08-07 11:11:39.834 [Info] Successfully resolved parameters for test case: Login
2025-08-07 11:11:39.834 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-07 11:11:39.834 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-07 11:11:39.834 [Debug] Resolving placeholders in string: id=finish
2025-08-07 11:11:39.834 [Debug] Resolved string: id=finish
2025-08-07 11:11:39.834 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-07 11:11:39.834 [Debug] Resolved string: Click on Login Button
2025-08-07 11:11:39.834 [Debug] Resolving placeholders in string: id=LoginBtn
2025-08-07 11:11:39.834 [Debug] Resolved string: id=LoginBtn
2025-08-07 11:11:39.834 [Debug] Resolving placeholders in string: Type the User Name
2025-08-07 11:11:39.834 [Debug] Resolved string: Type the User Name
2025-08-07 11:11:39.834 [Debug] Resolving placeholders in string: id=UserName
2025-08-07 11:11:39.834 [Debug] Resolved string: id=UserName
2025-08-07 11:11:39.834 [Debug] Resolving placeholders in string: Mm_azmy
2025-08-07 11:11:39.835 [Debug] Resolved string: Mm_azmy
2025-08-07 11:11:39.835 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-07 11:11:39.835 [Debug] Resolved string: Click on Continue Button
2025-08-07 11:11:39.835 [Debug] Resolving placeholders in string: id=btn
2025-08-07 11:11:39.835 [Debug] Resolved string: id=btn
2025-08-07 11:11:39.835 [Debug] Resolving placeholders in string: Type the Password
2025-08-07 11:11:39.835 [Debug] Resolved string: Type the Password
2025-08-07 11:11:39.835 [Debug] Resolving placeholders in string: id=Password
2025-08-07 11:11:39.835 [Debug] Resolved string: id=Password
2025-08-07 11:11:39.835 [Debug] Resolving placeholders in string: $('[id=Password]').val('Asdf2025')
2025-08-07 11:11:39.835 [Debug] Resolved string: $('[id=Password]').val('Asdf2025')
2025-08-07 11:11:39.835 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-07 11:11:39.835 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-07 11:11:39.835 [Debug] Resolving placeholders in string: id=ChallengeQuestionAnswer
2025-08-07 11:11:39.835 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-07 11:11:39.835 [Debug] Resolving placeholders in string: armada
2025-08-07 11:11:39.835 [Debug] Resolved string: armada
2025-08-07 11:11:39.835 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-07 11:11:39.835 [Debug] Resolved string: Click on the Login Button
2025-08-07 11:11:39.835 [Debug] Resolving placeholders in string: id=LoginAuthBtn
2025-08-07 11:11:39.835 [Debug] Resolved string: id=LoginAuthBtn
2025-08-07 11:11:39.835 [Debug] Resolving placeholders in string: Click on Happy points option from side menu
2025-08-07 11:11:39.835 [Debug] Resolved string: Click on Happy points option from side menu
2025-08-07 11:11:39.835 [Debug] Resolving placeholders in string: {{Selectors.HappyPointsOption}}
2025-08-07 11:11:39.835 [Debug] Resolved string: xpath=//div[@class='mainmenuitems']//label[contains(text(), 'Happy Points')]
2025-08-07 11:11:39.835 [Debug] Resolving placeholders in string: Click on Redeem Online button
2025-08-07 11:11:39.835 [Debug] Resolved string: Click on Redeem Online button
2025-08-07 11:11:39.835 [Debug] Resolving placeholders in string: {{Selectors.RedeemOnline}}
2025-08-07 11:11:39.835 [Debug] Resolved string: css=#BuyOnlineBTN > div
2025-08-07 11:11:39.835 [Debug] Resolving placeholders in string: Choose a merchant
2025-08-07 11:11:39.836 [Debug] Resolved string: Choose a merchant
2025-08-07 11:11:39.836 [Debug] Resolving placeholders in string: {{Selectors.SelectMerchant}}
2025-08-07 11:11:39.836 [Debug] Resolved string: css=#RequestItem0
2025-08-07 11:11:39.836 [Debug] Resolving placeholders in string: Click Redeem button
2025-08-07 11:11:39.836 [Debug] Resolved string: Click Redeem button
2025-08-07 11:11:39.836 [Debug] Resolving placeholders in string: {{Selectors.RedeemButton}}
2025-08-07 11:11:39.836 [Debug] Resolved string: id=RedeemID
2025-08-07 11:11:39.836 [Debug] Resolving placeholders in string: Enter the number of points to redeem
2025-08-07 11:11:39.836 [Debug] Resolved string: Enter the number of points to redeem
2025-08-07 11:11:39.836 [Debug] Resolving placeholders in string: {{Selectors.PointsTextbox}}
2025-08-07 11:11:39.836 [Debug] Resolved string: id=redeempointsID
2025-08-07 11:11:39.836 [Debug] Resolving placeholders in string: 1000
2025-08-07 11:11:39.836 [Debug] Resolved string: 1000
2025-08-07 11:11:39.837 [Debug] Resolving placeholders in string: Click on create cupon button
2025-08-07 11:11:39.837 [Debug] Resolved string: Click on create cupon button
2025-08-07 11:11:39.837 [Debug] Resolving placeholders in string: {{Selectors.CreateCupon}}
2025-08-07 11:11:39.837 [Debug] Resolved string: id=CCoponID
2025-08-07 11:11:39.837 [Debug] Resolving placeholders in string: Click on Continue button
2025-08-07 11:11:39.837 [Debug] Resolved string: Click on Continue button
2025-08-07 11:11:39.837 [Debug] Resolving placeholders in string: {{Selectors.CuntinueConfirmCupon}}
2025-08-07 11:11:39.838 [Debug] Resolved string: id=RequestRedemptionConfrimationSbmtBtn
2025-08-07 11:11:39.838 [Debug] Resolving placeholders in string: Enter wrong OTP
2025-08-07 11:11:39.838 [Debug] Resolved string: Enter wrong OTP
2025-08-07 11:11:39.838 [Debug] Resolving placeholders in string: {{Selectors.OTPbox}}
2025-08-07 11:11:39.838 [Debug] Resolved string: id=OTPInpt
2025-08-07 11:11:39.838 [Debug] Resolving placeholders in string: 0000000
2025-08-07 11:11:39.838 [Debug] Resolved string: 0000000
2025-08-07 11:11:39.838 [Debug] Resolving placeholders in string: Click on Confirm OTP button
2025-08-07 11:11:39.838 [Debug] Resolved string: Click on Confirm OTP button
2025-08-07 11:11:39.838 [Debug] Resolving placeholders in string: {{Selectors.OTPconfirmButton}}
2025-08-07 11:11:39.838 [Debug] Resolved string: id=ConfirmSbmtBtn
2025-08-07 11:11:39.838 [Debug] Resolving placeholders in string: Assert the error message: 
2025-08-07 11:11:39.838 [Debug] Resolved string: Assert the error message: 
2025-08-07 11:11:39.838 [Debug] Resolving placeholders in string: {{Selectors.popupMessage}}
2025-08-07 11:11:39.839 [Debug] Resolved string: id=popup_message
2025-08-07 11:11:39.839 [Debug] Resolving placeholders in string: Wrong activation code
2025-08-07 11:11:39.839 [Debug] Resolved string: Wrong activation code
2025-08-07 11:11:39.839 [Info] Successfully resolved parameters for test case: Verify That User Can't Redeem Happy Points Using Wrong SMS
2025-08-07 11:11:39.839 [Debug] Loading test case: WorkSet01/TestCases/021.HappyPoints/157-MorePointsThanAvailable.json
2025-08-07 11:11:39.839 [Info] Loading test case from: WorkSet01/TestCases/021.HappyPoints/157-MorePointsThanAvailable.json
2025-08-07 11:11:39.839 [Info] Attempting to load configuration file: WorkSet01/TestCases/021.HappyPoints/157-MorePointsThanAvailable.json
2025-08-07 11:11:39.839 [Info] Successfully loaded configuration file: WorkSet01/TestCases/021.HappyPoints/157-MorePointsThanAvailable.json
2025-08-07 11:11:39.840 [Info] No filtering configuration found. Including test case 'Redeem Online - input points more than the available'.
2025-08-07 11:11:39.840 [Debug] Resolving parameters for test case: Redeem Online - input points more than the available
2025-08-07 11:11:39.840 [Debug] Merging params
2025-08-07 11:11:39.840 [Debug] Merged basic params
2025-08-07 11:11:39.840 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-07 11:11:39.840 [Debug] Merged SpecialEnvParams params
2025-08-07 11:11:39.840 [Debug] Merged params
2025-08-07 11:11:39.840 [Debug] Loading parameters from reference file: WorkSet01/Params/HappyPointsParams.json
2025-08-07 11:11:39.840 [Info] Attempting to load configuration file: WorkSet01/Params/HappyPointsParams.json
2025-08-07 11:11:39.840 [Info] Successfully loaded configuration file: WorkSet01/Params/HappyPointsParams.json
2025-08-07 11:11:39.840 [Debug] Merging params
2025-08-07 11:11:39.841 [Debug] Merged basic params
2025-08-07 11:11:39.841 [Debug] Merged params
2025-08-07 11:11:39.841 [Debug] Merging params
2025-08-07 11:11:39.841 [Debug] Merging params
2025-08-07 11:11:39.841 [Debug] Resolving placeholders in test steps for test case: Redeem Online - input points more than the available
2025-08-07 11:11:39.841 [Debug] Resolving TestCaseReference in step: 
2025-08-07 11:11:39.841 [Info] Loading test case from: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:11:39.841 [Info] Attempting to load configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:11:39.841 [Info] Successfully loaded configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:11:39.842 [Debug] Resolving parameters for test case: Login
2025-08-07 11:11:39.842 [Debug] Merging params
2025-08-07 11:11:39.842 [Debug] Merged basic params
2025-08-07 11:11:39.842 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-07 11:11:39.842 [Debug] Merged SpecialEnvParams params
2025-08-07 11:11:39.842 [Debug] Merged params
2025-08-07 11:11:39.842 [Debug] Loading parameters from reference file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:11:39.842 [Info] Attempting to load configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:11:39.842 [Info] Successfully loaded configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:11:39.842 [Debug] Merging params
2025-08-07 11:11:39.842 [Debug] Merged basic params
2025-08-07 11:11:39.843 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-07 11:11:39.843 [Debug] Merged SpecialEnvParams params
2025-08-07 11:11:39.843 [Debug] Merged params
2025-08-07 11:11:39.843 [Debug] Merging params
2025-08-07 11:11:39.843 [Debug] Merging params
2025-08-07 11:11:39.843 [Debug] Resolving placeholders in test steps for test case: Login
2025-08-07 11:11:39.843 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-07 11:11:39.843 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-07 11:11:39.843 [Debug] Resolving placeholders in string: {{Selectors.FinishButton}}
2025-08-07 11:11:39.843 [Debug] Resolved string: id=finish
2025-08-07 11:11:39.843 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-07 11:11:39.843 [Debug] Resolved string: Click on Login Button
2025-08-07 11:11:39.843 [Debug] Resolving placeholders in string: {{Selectors.LoginButton}}
2025-08-07 11:11:39.843 [Debug] Resolved string: id=LoginBtn
2025-08-07 11:11:39.843 [Debug] Resolving placeholders in string: Type the User Name
2025-08-07 11:11:39.843 [Debug] Resolved string: Type the User Name
2025-08-07 11:11:39.843 [Debug] Resolving placeholders in string: {{Selectors.UserNameField}}
2025-08-07 11:11:39.844 [Debug] Resolved string: id=UserName
2025-08-07 11:11:39.844 [Debug] Resolving placeholders in string: {{TestData.UserName}}
2025-08-07 11:11:39.844 [Debug] Resolved string: Mm_azmy
2025-08-07 11:11:39.844 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-07 11:11:39.844 [Debug] Resolved string: Click on Continue Button
2025-08-07 11:11:39.844 [Debug] Resolving placeholders in string: {{Selectors.ContinueButton}}
2025-08-07 11:11:39.844 [Debug] Resolved string: id=btn
2025-08-07 11:11:39.844 [Debug] Resolving placeholders in string: Type the Password
2025-08-07 11:11:39.844 [Debug] Resolved string: Type the Password
2025-08-07 11:11:39.844 [Debug] Resolving placeholders in string: {{Selectors.PasswordField}}
2025-08-07 11:11:39.844 [Debug] Resolved string: id=Password
2025-08-07 11:11:39.844 [Debug] Resolving placeholders in string: $('[{{Selectors.PasswordField}}]').val('{{TestData.Password}}')
2025-08-07 11:11:39.844 [Debug] Resolved string: $('[id=Password]').val('Asdf2025')
2025-08-07 11:11:39.844 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-07 11:11:39.844 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-07 11:11:39.844 [Debug] Resolving placeholders in string: {{Selectors.ChallengeQuestionField}}
2025-08-07 11:11:39.844 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-07 11:11:39.844 [Debug] Resolving placeholders in string: {{TestData.ChallengeAnswer}}
2025-08-07 11:11:39.844 [Debug] Resolved string: armada
2025-08-07 11:11:39.844 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-07 11:11:39.849 [Debug] Resolved string: Click on the Login Button
2025-08-07 11:11:39.849 [Debug] Resolving placeholders in string: {{Selectors.LoginAuthButton}}
2025-08-07 11:11:39.849 [Debug] Resolved string: id=LoginAuthBtn
2025-08-07 11:11:39.849 [Info] Successfully resolved parameters for test case: Login
2025-08-07 11:11:39.849 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-07 11:11:39.849 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-07 11:11:39.849 [Debug] Resolving placeholders in string: id=finish
2025-08-07 11:11:39.849 [Debug] Resolved string: id=finish
2025-08-07 11:11:39.849 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-07 11:11:39.849 [Debug] Resolved string: Click on Login Button
2025-08-07 11:11:39.849 [Debug] Resolving placeholders in string: id=LoginBtn
2025-08-07 11:11:39.849 [Debug] Resolved string: id=LoginBtn
2025-08-07 11:11:39.849 [Debug] Resolving placeholders in string: Type the User Name
2025-08-07 11:11:39.849 [Debug] Resolved string: Type the User Name
2025-08-07 11:11:39.849 [Debug] Resolving placeholders in string: id=UserName
2025-08-07 11:11:39.849 [Debug] Resolved string: id=UserName
2025-08-07 11:11:39.849 [Debug] Resolving placeholders in string: Mm_azmy
2025-08-07 11:11:39.849 [Debug] Resolved string: Mm_azmy
2025-08-07 11:11:39.849 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-07 11:11:39.849 [Debug] Resolved string: Click on Continue Button
2025-08-07 11:11:39.849 [Debug] Resolving placeholders in string: id=btn
2025-08-07 11:11:39.849 [Debug] Resolved string: id=btn
2025-08-07 11:11:39.849 [Debug] Resolving placeholders in string: Type the Password
2025-08-07 11:11:39.849 [Debug] Resolved string: Type the Password
2025-08-07 11:11:39.849 [Debug] Resolving placeholders in string: id=Password
2025-08-07 11:11:39.849 [Debug] Resolved string: id=Password
2025-08-07 11:11:39.849 [Debug] Resolving placeholders in string: $('[id=Password]').val('Asdf2025')
2025-08-07 11:11:39.849 [Debug] Resolved string: $('[id=Password]').val('Asdf2025')
2025-08-07 11:11:39.849 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-07 11:11:39.849 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-07 11:11:39.850 [Debug] Resolving placeholders in string: id=ChallengeQuestionAnswer
2025-08-07 11:11:39.850 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-07 11:11:39.850 [Debug] Resolving placeholders in string: armada
2025-08-07 11:11:39.850 [Debug] Resolved string: armada
2025-08-07 11:11:39.852 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-07 11:11:39.852 [Debug] Resolved string: Click on the Login Button
2025-08-07 11:11:39.852 [Debug] Resolving placeholders in string: id=LoginAuthBtn
2025-08-07 11:11:39.852 [Debug] Resolved string: id=LoginAuthBtn
2025-08-07 11:11:39.852 [Debug] Resolving placeholders in string: Click on Happy points option from side menu
2025-08-07 11:11:39.852 [Debug] Resolved string: Click on Happy points option from side menu
2025-08-07 11:11:39.852 [Debug] Resolving placeholders in string: {{Selectors.HappyPointsOption}}
2025-08-07 11:11:39.852 [Debug] Resolved string: xpath=//div[@class='mainmenuitems']//label[contains(text(), 'Happy Points')]
2025-08-07 11:11:39.852 [Debug] Resolving placeholders in string: Click on Redeem Online button
2025-08-07 11:11:39.852 [Debug] Resolved string: Click on Redeem Online button
2025-08-07 11:11:39.852 [Debug] Resolving placeholders in string: {{Selectors.RedeemOnline}}
2025-08-07 11:11:39.852 [Debug] Resolved string: css=#BuyOnlineBTN > div
2025-08-07 11:11:39.852 [Debug] Resolving placeholders in string: Choose a merchant
2025-08-07 11:11:39.852 [Debug] Resolved string: Choose a merchant
2025-08-07 11:11:39.852 [Debug] Resolving placeholders in string: {{Selectors.SelectMerchant}}
2025-08-07 11:11:39.852 [Debug] Resolved string: css=#RequestItem0
2025-08-07 11:11:39.852 [Debug] Resolving placeholders in string: Click Redeem button
2025-08-07 11:11:39.852 [Debug] Resolved string: Click Redeem button
2025-08-07 11:11:39.852 [Debug] Resolving placeholders in string: {{Selectors.RedeemButton}}
2025-08-07 11:11:39.852 [Debug] Resolved string: id=RedeemID
2025-08-07 11:11:39.852 [Debug] Resolving placeholders in string: Enter the number of points to redeem
2025-08-07 11:11:39.852 [Debug] Resolved string: Enter the number of points to redeem
2025-08-07 11:11:39.852 [Debug] Resolving placeholders in string: {{Selectors.PointsTextbox}}
2025-08-07 11:11:39.852 [Debug] Resolved string: id=redeempointsID
2025-08-07 11:11:39.852 [Debug] Resolving placeholders in string: 50000
2025-08-07 11:11:39.852 [Debug] Resolved string: 50000
2025-08-07 11:11:39.852 [Debug] Resolving placeholders in string: Click on create cupon button and show the error message
2025-08-07 11:11:39.852 [Debug] Resolved string: Click on create cupon button and show the error message
2025-08-07 11:11:39.852 [Debug] Resolving placeholders in string: {{Selectors.CreateCupon}}
2025-08-07 11:11:39.852 [Debug] Resolved string: id=CCoponID
2025-08-07 11:11:39.852 [Info] Successfully resolved parameters for test case: Redeem Online - input points more than the available
2025-08-07 11:11:39.852 [Debug] Loading test case: WorkSet01/TestCases/03.Beneficiaries/Add/TC-255 AddingOtherCAEaccount.json
2025-08-07 11:11:39.856 [Info] Loading test case from: WorkSet01/TestCases/03.Beneficiaries/Add/TC-255 AddingOtherCAEaccount.json
2025-08-07 11:11:39.856 [Info] Attempting to load configuration file: WorkSet01/TestCases/03.Beneficiaries/Add/TC-255 AddingOtherCAEaccount.json
2025-08-07 11:11:39.856 [Info] Successfully loaded configuration file: WorkSet01/TestCases/03.Beneficiaries/Add/TC-255 AddingOtherCAEaccount.json
2025-08-07 11:11:39.857 [Info] No filtering configuration found. Including test case 'Add new beneficiary account'.
2025-08-07 11:11:39.857 [Debug] Resolving parameters for test case: Add new beneficiary account
2025-08-07 11:11:39.857 [Debug] Merging params
2025-08-07 11:11:39.857 [Debug] Merged basic params
2025-08-07 11:11:39.857 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-07 11:11:39.857 [Debug] Merged SpecialEnvParams params
2025-08-07 11:11:39.857 [Debug] Merged params
2025-08-07 11:11:39.857 [Debug] Loading parameters from reference file: WorkSet01/Params/BeneficiariesParams.json
2025-08-07 11:11:39.857 [Info] Attempting to load configuration file: WorkSet01/Params/BeneficiariesParams.json
2025-08-07 11:11:39.857 [Info] Successfully loaded configuration file: WorkSet01/Params/BeneficiariesParams.json
2025-08-07 11:11:39.858 [Debug] Merging params
2025-08-07 11:11:39.858 [Debug] Merged basic params
2025-08-07 11:11:39.858 [Debug] Merged params
2025-08-07 11:11:39.858 [Debug] Merging params
2025-08-07 11:11:39.858 [Debug] Merging params
2025-08-07 11:11:39.858 [Debug] Resolving placeholders in test steps for test case: Add new beneficiary account
2025-08-07 11:11:39.858 [Debug] Resolving TestCaseReference in step: 
2025-08-07 11:11:39.858 [Info] Loading test case from: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:11:39.858 [Info] Attempting to load configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:11:39.858 [Info] Successfully loaded configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:11:39.858 [Debug] Resolving parameters for test case: Login
2025-08-07 11:11:39.858 [Debug] Merging params
2025-08-07 11:11:39.858 [Debug] Merged basic params
2025-08-07 11:11:39.858 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-07 11:11:39.858 [Debug] Merged SpecialEnvParams params
2025-08-07 11:11:39.858 [Debug] Merged params
2025-08-07 11:11:39.858 [Debug] Loading parameters from reference file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:11:39.858 [Info] Attempting to load configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:11:39.861 [Info] Successfully loaded configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:11:39.861 [Debug] Merging params
2025-08-07 11:11:39.861 [Debug] Merged basic params
2025-08-07 11:11:39.861 [Debug] Merged params
2025-08-07 11:11:39.861 [Debug] Merging params
2025-08-07 11:11:39.861 [Debug] Merging params
2025-08-07 11:11:39.861 [Debug] Resolving placeholders in test steps for test case: Login
2025-08-07 11:11:39.861 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-07 11:11:39.861 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-07 11:11:39.861 [Debug] Resolving placeholders in string: {{Selectors.FinishButton}}
2025-08-07 11:11:39.861 [Debug] Resolved string: id=finish
2025-08-07 11:11:39.861 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-07 11:11:39.861 [Debug] Resolved string: Click on Login Button
2025-08-07 11:11:39.861 [Debug] Resolving placeholders in string: {{Selectors.LoginButton}}
2025-08-07 11:11:39.861 [Debug] Resolved string: id=LoginBtn
2025-08-07 11:11:39.861 [Debug] Resolving placeholders in string: Type the User Name
2025-08-07 11:11:39.861 [Debug] Resolved string: Type the User Name
2025-08-07 11:11:39.861 [Debug] Resolving placeholders in string: {{Selectors.UserNameField}}
2025-08-07 11:11:39.861 [Debug] Resolved string: id=UserName
2025-08-07 11:11:39.861 [Debug] Resolving placeholders in string: {{TestData.UserName}}
2025-08-07 11:11:39.861 [Debug] Resolved string: mahmoudsayed022
2025-08-07 11:11:39.861 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-07 11:11:39.862 [Debug] Resolved string: Click on Continue Button
2025-08-07 11:11:39.862 [Debug] Resolving placeholders in string: {{Selectors.ContinueButton}}
2025-08-07 11:11:39.862 [Debug] Resolved string: id=btn
2025-08-07 11:11:39.862 [Debug] Resolving placeholders in string: Type the Password
2025-08-07 11:11:39.862 [Debug] Resolved string: Type the Password
2025-08-07 11:11:39.862 [Debug] Resolving placeholders in string: {{Selectors.PasswordField}}
2025-08-07 11:11:39.862 [Debug] Resolved string: id=Password
2025-08-07 11:11:39.864 [Debug] Resolving placeholders in string: $('[{{Selectors.PasswordField}}]').val('{{TestData.Password}}')
2025-08-07 11:11:39.864 [Debug] Resolved string: $('[id=Password]').val('Password1')
2025-08-07 11:11:39.864 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-07 11:11:39.864 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-07 11:11:39.864 [Debug] Resolving placeholders in string: {{Selectors.ChallengeQuestionField}}
2025-08-07 11:11:39.864 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-07 11:11:39.864 [Debug] Resolving placeholders in string: {{TestData.ChallengeAnswer}}
2025-08-07 11:11:39.864 [Debug] Resolved string: bmw
2025-08-07 11:11:39.864 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-07 11:11:39.864 [Debug] Resolved string: Click on the Login Button
2025-08-07 11:11:39.864 [Debug] Resolving placeholders in string: {{Selectors.LoginAuthButton}}
2025-08-07 11:11:39.865 [Debug] Resolved string: id=LoginAuthBtn
2025-08-07 11:11:39.865 [Info] Successfully resolved parameters for test case: Login
2025-08-07 11:11:39.865 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-07 11:11:39.865 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-07 11:11:39.865 [Debug] Resolving placeholders in string: id=finish
2025-08-07 11:11:39.865 [Debug] Resolved string: id=finish
2025-08-07 11:11:39.865 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-07 11:11:39.865 [Debug] Resolved string: Click on Login Button
2025-08-07 11:11:39.865 [Debug] Resolving placeholders in string: id=LoginBtn
2025-08-07 11:11:39.865 [Debug] Resolved string: id=LoginBtn
2025-08-07 11:11:39.865 [Debug] Resolving placeholders in string: Type the User Name
2025-08-07 11:11:39.865 [Debug] Resolved string: Type the User Name
2025-08-07 11:11:39.865 [Debug] Resolving placeholders in string: id=UserName
2025-08-07 11:11:39.865 [Debug] Resolved string: id=UserName
2025-08-07 11:11:39.865 [Debug] Resolving placeholders in string: mahmoudsayed022
2025-08-07 11:11:39.865 [Debug] Resolved string: mahmoudsayed022
2025-08-07 11:11:39.865 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-07 11:11:39.868 [Debug] Resolved string: Click on Continue Button
2025-08-07 11:11:39.868 [Debug] Resolving placeholders in string: id=btn
2025-08-07 11:11:39.868 [Debug] Resolved string: id=btn
2025-08-07 11:11:39.868 [Debug] Resolving placeholders in string: Type the Password
2025-08-07 11:11:39.868 [Debug] Resolved string: Type the Password
2025-08-07 11:11:39.868 [Debug] Resolving placeholders in string: id=Password
2025-08-07 11:11:39.868 [Debug] Resolved string: id=Password
2025-08-07 11:11:39.868 [Debug] Resolving placeholders in string: $('[id=Password]').val('Password1')
2025-08-07 11:11:39.868 [Debug] Resolved string: $('[id=Password]').val('Password1')
2025-08-07 11:11:39.868 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-07 11:11:39.868 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-07 11:11:39.868 [Debug] Resolving placeholders in string: id=ChallengeQuestionAnswer
2025-08-07 11:11:39.868 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-07 11:11:39.868 [Debug] Resolving placeholders in string: bmw
2025-08-07 11:11:39.868 [Debug] Resolved string: bmw
2025-08-07 11:11:39.868 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-07 11:11:39.868 [Debug] Resolved string: Click on the Login Button
2025-08-07 11:11:39.868 [Debug] Resolving placeholders in string: id=LoginAuthBtn
2025-08-07 11:11:39.868 [Debug] Resolved string: id=LoginAuthBtn
2025-08-07 11:11:39.868 [Debug] Resolving placeholders in string: Click on change later button on the password expiration popup message
2025-08-07 11:11:39.868 [Debug] Resolved string: Click on change later button on the password expiration popup message
2025-08-07 11:11:39.868 [Debug] Resolving placeholders in string: {{Selectors.popupCancel}}
2025-08-07 11:11:39.868 [Debug] Resolved string: id=popup_cancel
2025-08-07 11:11:39.868 [Debug] Resolving placeholders in string: Click on biometric alert
2025-08-07 11:11:39.868 [Debug] Resolved string: Click on biometric alert
2025-08-07 11:11:39.868 [Debug] Resolving placeholders in string: {{Selectors.BioMetricAlert}}
2025-08-07 11:11:39.868 [Debug] Resolved string: css=#popbuttons > div > button.btn.Cancel.back_gradient2
2025-08-07 11:11:39.875 [Debug] Resolving placeholders in string: Execute JS code to activate token
2025-08-07 11:11:39.875 [Debug] Resolved string: Execute JS code to activate token
2025-08-07 11:11:39.875 [Debug] Resolving placeholders in string: Globals.ActivationState = 'ACTIVATED';
2025-08-07 11:11:39.875 [Debug] Resolved string: Globals.ActivationState = 'ACTIVATED';
2025-08-07 11:11:39.875 [Debug] Resolving placeholders in string: Click on Beneficiaries
2025-08-07 11:11:39.875 [Debug] Resolved string: Click on Beneficiaries
2025-08-07 11:11:39.875 [Debug] Resolving placeholders in string: {{Selectors.BeneficiariesToButton}}
2025-08-07 11:11:39.875 [Debug] Resolved string: xpath=//div[@class='mainmenuitems']//label[contains(text(), 'Beneficiaries')]
2025-08-07 11:11:39.875 [Debug] Resolving placeholders in string: Click on Other CAE Accounts
2025-08-07 11:11:39.875 [Debug] Resolved string: Click on Other CAE Accounts
2025-08-07 11:11:39.875 [Debug] Resolving placeholders in string: {{Selectors.OtherCAEAccounts}}
2025-08-07 11:11:39.875 [Debug] Resolved string: xpath=//div[@class='submenuitem']//label[contains(text(), 'Other CAE Accounts')]
2025-08-07 11:11:39.875 [Debug] Resolving placeholders in string: Click on Add Beneficiary
2025-08-07 11:11:39.875 [Debug] Resolved string: Click on Add Beneficiary
2025-08-07 11:11:39.875 [Debug] Resolving placeholders in string: {{Selectors.AddNewBeneficiaryButtonOtheCAEaccounts}}
2025-08-07 11:11:39.875 [Debug] Resolved string: id=AddDigitalBankBeneficiary
2025-08-07 11:11:39.875 [Debug] Resolving placeholders in string: Type the Nickname
2025-08-07 11:11:39.875 [Debug] Resolved string: Type the Nickname
2025-08-07 11:11:39.875 [Debug] Resolving placeholders in string: {{Selectors.BeneficiaryNameTextbox}}
2025-08-07 11:11:39.875 [Debug] Resolved string: id=BeneficiaryName
2025-08-07 11:11:39.875 [Debug] Resolving placeholders in string: OtherCAE_account_2
2025-08-07 11:11:39.875 [Debug] Resolved string: OtherCAE_account_2
2025-08-07 11:11:39.875 [Debug] Resolving placeholders in string: Type Account number
2025-08-07 11:11:39.875 [Debug] Resolved string: Type Account number
2025-08-07 11:11:39.875 [Debug] Resolving placeholders in string: {{Selectors.BeneficiaryAccountNumber}}
2025-08-07 11:11:39.875 [Debug] Resolved string: id=BeneficiaryAccountNumber
2025-08-07 11:11:39.878 [Debug] Resolving placeholders in string: ************** 
2025-08-07 11:11:39.878 [Debug] Resolved string: ************** 
2025-08-07 11:11:39.878 [Debug] Resolving placeholders in string: Click on Save button
2025-08-07 11:11:39.878 [Debug] Resolved string: Click on Save button
2025-08-07 11:11:39.878 [Debug] Resolving placeholders in string: {{Selectors.SaveButton}}
2025-08-07 11:11:39.879 [Debug] Resolved string: id=Save
2025-08-07 11:11:39.879 [Debug] Resolving placeholders in string: Click on continue
2025-08-07 11:11:39.879 [Debug] Resolved string: Click on continue
2025-08-07 11:11:39.879 [Debug] Resolving placeholders in string: {{Selectors.ContinueToSaveBeneficiary}}
2025-08-07 11:11:39.879 [Debug] Resolved string: id=DigitalBankBenfContinueDeleteBtn
2025-08-07 11:11:39.879 [Debug] Resolving placeholders in string: Enter Token number
2025-08-07 11:11:39.879 [Debug] Resolved string: Enter Token number
2025-08-07 11:11:39.879 [Debug] Resolving placeholders in string: {{Selectors.TokenInput}}
2025-08-07 11:11:39.879 [Debug] Resolved string: id=TokenNUMBER
2025-08-07 11:11:39.879 [Debug] Resolving placeholders in string: 123456
2025-08-07 11:11:39.879 [Debug] Resolved string: 123456
2025-08-07 11:11:39.879 [Debug] Resolving placeholders in string: Click on confirm and show the confirmation page
2025-08-07 11:11:39.879 [Debug] Resolved string: Click on confirm and show the confirmation page
2025-08-07 11:11:39.879 [Debug] Resolving placeholders in string: {{Selectors.btnTokenConfirm}}
2025-08-07 11:11:39.879 [Debug] Resolved string: id=btnTokenConfirm
2025-08-07 11:11:39.879 [Info] Successfully resolved parameters for test case: Add new beneficiary account
2025-08-07 11:11:39.879 [Debug] Loading test case: WorkSet01/TestCases/03.Beneficiaries/Add/TC-256 AddingOtherCAEcard.json
2025-08-07 11:11:39.879 [Info] Loading test case from: WorkSet01/TestCases/03.Beneficiaries/Add/TC-256 AddingOtherCAEcard.json
2025-08-07 11:11:39.879 [Info] Attempting to load configuration file: WorkSet01/TestCases/03.Beneficiaries/Add/TC-256 AddingOtherCAEcard.json
2025-08-07 11:11:39.879 [Info] Successfully loaded configuration file: WorkSet01/TestCases/03.Beneficiaries/Add/TC-256 AddingOtherCAEcard.json
2025-08-07 11:11:39.882 [Info] No filtering configuration found. Including test case 'Add new beneficiary card'.
2025-08-07 11:11:39.882 [Debug] Resolving parameters for test case: Add new beneficiary card
2025-08-07 11:11:39.882 [Debug] Merging params
2025-08-07 11:11:39.882 [Debug] Merged basic params
2025-08-07 11:11:39.882 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-07 11:11:39.882 [Debug] Merged SpecialEnvParams params
2025-08-07 11:11:39.882 [Debug] Merged params
2025-08-07 11:11:39.882 [Debug] Loading parameters from reference file: WorkSet01/Params/BeneficiariesParams.json
2025-08-07 11:11:39.883 [Info] Attempting to load configuration file: WorkSet01/Params/BeneficiariesParams.json
2025-08-07 11:11:39.883 [Info] Successfully loaded configuration file: WorkSet01/Params/BeneficiariesParams.json
2025-08-07 11:11:39.883 [Debug] Merging params
2025-08-07 11:11:39.883 [Debug] Merged basic params
2025-08-07 11:11:39.883 [Debug] Merged params
2025-08-07 11:11:39.883 [Debug] Merging params
2025-08-07 11:11:39.883 [Debug] Merging params
2025-08-07 11:11:39.883 [Debug] Resolving placeholders in test steps for test case: Add new beneficiary card
2025-08-07 11:11:39.883 [Debug] Resolving TestCaseReference in step: 
2025-08-07 11:11:39.883 [Info] Loading test case from: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:11:39.883 [Info] Attempting to load configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:11:39.883 [Info] Successfully loaded configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:11:39.884 [Debug] Resolving parameters for test case: Login
2025-08-07 11:11:39.884 [Debug] Merging params
2025-08-07 11:11:39.884 [Debug] Merged basic params
2025-08-07 11:11:39.884 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-07 11:11:39.886 [Debug] Merged SpecialEnvParams params
2025-08-07 11:11:39.886 [Debug] Merged params
2025-08-07 11:11:39.886 [Debug] Loading parameters from reference file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:11:39.886 [Info] Attempting to load configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:11:39.886 [Info] Successfully loaded configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:11:39.887 [Debug] Merging params
2025-08-07 11:11:39.887 [Debug] Merged basic params
2025-08-07 11:11:39.887 [Debug] Merged params
2025-08-07 11:11:39.887 [Debug] Merging params
2025-08-07 11:11:39.887 [Debug] Merging params
2025-08-07 11:11:39.887 [Debug] Resolving placeholders in test steps for test case: Login
2025-08-07 11:11:39.887 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-07 11:11:39.887 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-07 11:11:39.887 [Debug] Resolving placeholders in string: {{Selectors.FinishButton}}
2025-08-07 11:11:39.887 [Debug] Resolved string: id=finish
2025-08-07 11:11:39.887 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-07 11:11:39.887 [Debug] Resolved string: Click on Login Button
2025-08-07 11:11:39.887 [Debug] Resolving placeholders in string: {{Selectors.LoginButton}}
2025-08-07 11:11:39.887 [Debug] Resolved string: id=LoginBtn
2025-08-07 11:11:39.887 [Debug] Resolving placeholders in string: Type the User Name
2025-08-07 11:11:39.887 [Debug] Resolved string: Type the User Name
2025-08-07 11:11:39.887 [Debug] Resolving placeholders in string: {{Selectors.UserNameField}}
2025-08-07 11:11:39.887 [Debug] Resolved string: id=UserName
2025-08-07 11:11:39.887 [Debug] Resolving placeholders in string: {{TestData.UserName}}
2025-08-07 11:11:39.892 [Debug] Resolved string: mahmoudsayed022
2025-08-07 11:11:39.892 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-07 11:11:39.892 [Debug] Resolved string: Click on Continue Button
2025-08-07 11:11:39.892 [Debug] Resolving placeholders in string: {{Selectors.ContinueButton}}
2025-08-07 11:11:39.892 [Debug] Resolved string: id=btn
2025-08-07 11:11:39.892 [Debug] Resolving placeholders in string: Type the Password
2025-08-07 11:11:39.892 [Debug] Resolved string: Type the Password
2025-08-07 11:11:39.892 [Debug] Resolving placeholders in string: {{Selectors.PasswordField}}
2025-08-07 11:11:39.892 [Debug] Resolved string: id=Password
2025-08-07 11:11:39.892 [Debug] Resolving placeholders in string: $('[{{Selectors.PasswordField}}]').val('{{TestData.Password}}')
2025-08-07 11:11:39.892 [Debug] Resolved string: $('[id=Password]').val('Password1')
2025-08-07 11:11:39.892 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-07 11:11:39.892 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-07 11:11:39.892 [Debug] Resolving placeholders in string: {{Selectors.ChallengeQuestionField}}
2025-08-07 11:11:39.892 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-07 11:11:39.892 [Debug] Resolving placeholders in string: {{TestData.ChallengeAnswer}}
2025-08-07 11:11:39.892 [Debug] Resolved string: bmw
2025-08-07 11:11:39.892 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-07 11:11:39.892 [Debug] Resolved string: Click on the Login Button
2025-08-07 11:11:39.892 [Debug] Resolving placeholders in string: {{Selectors.LoginAuthButton}}
2025-08-07 11:11:39.892 [Debug] Resolved string: id=LoginAuthBtn
2025-08-07 11:11:39.892 [Info] Successfully resolved parameters for test case: Login
2025-08-07 11:11:39.892 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-07 11:11:39.894 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-07 11:11:39.894 [Debug] Resolving placeholders in string: id=finish
2025-08-07 11:11:39.894 [Debug] Resolved string: id=finish
2025-08-07 11:11:39.894 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-07 11:11:39.894 [Debug] Resolved string: Click on Login Button
2025-08-07 11:11:39.894 [Debug] Resolving placeholders in string: id=LoginBtn
2025-08-07 11:11:39.894 [Debug] Resolved string: id=LoginBtn
2025-08-07 11:11:39.895 [Debug] Resolving placeholders in string: Type the User Name
2025-08-07 11:11:39.895 [Debug] Resolved string: Type the User Name
2025-08-07 11:11:39.895 [Debug] Resolving placeholders in string: id=UserName
2025-08-07 11:11:39.895 [Debug] Resolved string: id=UserName
2025-08-07 11:11:39.895 [Debug] Resolving placeholders in string: mahmoudsayed022
2025-08-07 11:11:39.895 [Debug] Resolved string: mahmoudsayed022
2025-08-07 11:11:39.895 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-07 11:11:39.895 [Debug] Resolved string: Click on Continue Button
2025-08-07 11:11:39.895 [Debug] Resolving placeholders in string: id=btn
2025-08-07 11:11:39.895 [Debug] Resolved string: id=btn
2025-08-07 11:11:39.895 [Debug] Resolving placeholders in string: Type the Password
2025-08-07 11:11:39.895 [Debug] Resolved string: Type the Password
2025-08-07 11:11:39.895 [Debug] Resolving placeholders in string: id=Password
2025-08-07 11:11:39.895 [Debug] Resolved string: id=Password
2025-08-07 11:11:39.895 [Debug] Resolving placeholders in string: $('[id=Password]').val('Password1')
2025-08-07 11:11:39.895 [Debug] Resolved string: $('[id=Password]').val('Password1')
2025-08-07 11:11:39.897 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-07 11:11:39.897 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-07 11:11:39.897 [Debug] Resolving placeholders in string: id=ChallengeQuestionAnswer
2025-08-07 11:11:39.897 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-07 11:11:39.897 [Debug] Resolving placeholders in string: bmw
2025-08-07 11:11:39.897 [Debug] Resolved string: bmw
2025-08-07 11:11:39.897 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-07 11:11:39.897 [Debug] Resolved string: Click on the Login Button
2025-08-07 11:11:39.897 [Debug] Resolving placeholders in string: id=LoginAuthBtn
2025-08-07 11:11:39.897 [Debug] Resolved string: id=LoginAuthBtn
2025-08-07 11:11:39.898 [Debug] Resolving placeholders in string: Click on change later button on the password expiration popup message
2025-08-07 11:11:39.898 [Debug] Resolved string: Click on change later button on the password expiration popup message
2025-08-07 11:11:39.898 [Debug] Resolving placeholders in string: {{Selectors.popupCancel}}
2025-08-07 11:11:39.898 [Debug] Resolved string: id=popup_cancel
2025-08-07 11:11:39.898 [Debug] Resolving placeholders in string: Click on biometric alert
2025-08-07 11:11:39.898 [Debug] Resolved string: Click on biometric alert
2025-08-07 11:11:39.898 [Debug] Resolving placeholders in string: {{Selectors.BioMetricAlert}}
2025-08-07 11:11:39.898 [Debug] Resolved string: css=#popbuttons > div > button.btn.Cancel.back_gradient2
2025-08-07 11:11:39.898 [Debug] Resolving placeholders in string: Execute JS code to activate token
2025-08-07 11:11:39.898 [Debug] Resolved string: Execute JS code to activate token
2025-08-07 11:11:39.898 [Debug] Resolving placeholders in string: Globals.ActivationState = 'ACTIVATED';
2025-08-07 11:11:39.898 [Debug] Resolved string: Globals.ActivationState = 'ACTIVATED';
2025-08-07 11:11:39.900 [Debug] Resolving placeholders in string: Click on Beneficiaries
2025-08-07 11:11:39.900 [Debug] Resolved string: Click on Beneficiaries
2025-08-07 11:11:39.900 [Debug] Resolving placeholders in string: {{Selectors.BeneficiariesToButton}}
2025-08-07 11:11:39.900 [Debug] Resolved string: xpath=//div[@class='mainmenuitems']//label[contains(text(), 'Beneficiaries')]
2025-08-07 11:11:39.900 [Debug] Resolving placeholders in string: Click on Other CAE Cards
2025-08-07 11:11:39.900 [Debug] Resolved string: Click on Other CAE Cards
2025-08-07 11:11:39.900 [Debug] Resolving placeholders in string: {{Selectors.OtherCAECards}}
2025-08-07 11:11:39.900 [Debug] Resolved string: xpath=//div[@class='submenuitem']//label[contains(text(), 'Other CAE Cards')]
2025-08-07 11:11:39.900 [Debug] Resolving placeholders in string: Click on Add Beneficiary
2025-08-07 11:11:39.900 [Debug] Resolved string: Click on Add Beneficiary
2025-08-07 11:11:39.900 [Debug] Resolving placeholders in string: xpath=//button[text()='Add Beneficiary']
2025-08-07 11:11:39.900 [Debug] Resolved string: xpath=//button[text()='Add Beneficiary']
2025-08-07 11:11:39.900 [Debug] Resolving placeholders in string: Type the Nickname
2025-08-07 11:11:39.900 [Debug] Resolved string: Type the Nickname
2025-08-07 11:11:39.900 [Debug] Resolving placeholders in string: {{Selectors.BeneficiaryNameTextbox}}
2025-08-07 11:11:39.900 [Debug] Resolved string: id=BeneficiaryName
2025-08-07 11:11:39.900 [Debug] Resolving placeholders in string: OtherCAE_card_2
2025-08-07 11:11:39.900 [Debug] Resolved string: OtherCAE_card_2
2025-08-07 11:11:39.900 [Debug] Resolving placeholders in string: Type Card number
2025-08-07 11:11:39.900 [Debug] Resolved string: Type Card number
2025-08-07 11:11:39.900 [Debug] Resolving placeholders in string: {{Selectors.BeneficiaryAccountNumber}}
2025-08-07 11:11:39.903 [Debug] Resolved string: id=BeneficiaryAccountNumber
2025-08-07 11:11:39.903 [Debug] Resolving placeholders in string: ****************
2025-08-07 11:11:39.903 [Debug] Resolved string: ****************
2025-08-07 11:11:39.903 [Debug] Resolving placeholders in string: Click on Save button
2025-08-07 11:11:39.903 [Debug] Resolved string: Click on Save button
2025-08-07 11:11:39.903 [Debug] Resolving placeholders in string: {{Selectors.SaveButton}}
2025-08-07 11:11:39.903 [Debug] Resolved string: id=Save
2025-08-07 11:11:39.903 [Debug] Resolving placeholders in string: Click on continue
2025-08-07 11:11:39.904 [Debug] Resolved string: Click on continue
2025-08-07 11:11:39.904 [Debug] Resolving placeholders in string: {{Selectors.ContinueToSaveBeneficiary}}
2025-08-07 11:11:39.904 [Debug] Resolved string: id=DigitalBankBenfContinueDeleteBtn
2025-08-07 11:11:39.904 [Debug] Resolving placeholders in string: Enter Token number
2025-08-07 11:11:39.904 [Debug] Resolved string: Enter Token number
2025-08-07 11:11:39.904 [Debug] Resolving placeholders in string: {{Selectors.TokenInput}}
2025-08-07 11:11:39.904 [Debug] Resolved string: id=TokenNUMBER
2025-08-07 11:11:39.904 [Debug] Resolving placeholders in string: 123456
2025-08-07 11:11:39.904 [Debug] Resolved string: 123456
2025-08-07 11:11:39.904 [Debug] Resolving placeholders in string: Click on confirm and show the confirmation page
2025-08-07 11:11:39.904 [Debug] Resolved string: Click on confirm and show the confirmation page
2025-08-07 11:11:39.904 [Debug] Resolving placeholders in string: {{Selectors.btnTokenConfirm}}
2025-08-07 11:11:39.904 [Debug] Resolved string: id=btnTokenConfirm
2025-08-07 11:11:39.907 [Info] Successfully resolved parameters for test case: Add new beneficiary card
2025-08-07 11:11:39.907 [Debug] Loading test case: WorkSet01/TestCases/03.Beneficiaries/Verify/251-Existing Account beneficiaries displayed.json
2025-08-07 11:11:39.908 [Info] Loading test case from: WorkSet01/TestCases/03.Beneficiaries/Verify/251-Existing Account beneficiaries displayed.json
2025-08-07 11:11:39.908 [Info] Attempting to load configuration file: WorkSet01/TestCases/03.Beneficiaries/Verify/251-Existing Account beneficiaries displayed.json
2025-08-07 11:11:39.908 [Info] Successfully loaded configuration file: WorkSet01/TestCases/03.Beneficiaries/Verify/251-Existing Account beneficiaries displayed.json
2025-08-07 11:11:39.908 [Info] No filtering configuration found. Including test case 'Existing Account beneficiaries displayed'.
2025-08-07 11:11:39.908 [Debug] Resolving parameters for test case: Existing Account beneficiaries displayed
2025-08-07 11:11:39.908 [Debug] Merging params
2025-08-07 11:11:39.908 [Debug] Merged basic params
2025-08-07 11:11:39.908 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-07 11:11:39.908 [Debug] Merged SpecialEnvParams params
2025-08-07 11:11:39.908 [Debug] Merged params
2025-08-07 11:11:39.908 [Debug] Loading parameters from reference file: WorkSet01/Params/BeneficiariesParams.json
2025-08-07 11:11:39.908 [Info] Attempting to load configuration file: WorkSet01/Params/BeneficiariesParams.json
2025-08-07 11:11:39.909 [Info] Successfully loaded configuration file: WorkSet01/Params/BeneficiariesParams.json
2025-08-07 11:11:39.909 [Debug] Merging params
2025-08-07 11:11:39.909 [Debug] Merged basic params
2025-08-07 11:11:39.909 [Debug] Merged params
2025-08-07 11:11:39.909 [Debug] Merging params
2025-08-07 11:11:39.909 [Debug] Merging params
2025-08-07 11:11:39.913 [Debug] Resolving placeholders in test steps for test case: Existing Account beneficiaries displayed
2025-08-07 11:11:39.913 [Debug] Resolving TestCaseReference in step: 
2025-08-07 11:11:39.913 [Info] Loading test case from: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:11:39.913 [Info] Attempting to load configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:11:39.914 [Info] Successfully loaded configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:11:39.914 [Debug] Resolving parameters for test case: Login
2025-08-07 11:11:39.914 [Debug] Merging params
2025-08-07 11:11:39.914 [Debug] Merged basic params
2025-08-07 11:11:39.914 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-07 11:11:39.914 [Debug] Merged SpecialEnvParams params
2025-08-07 11:11:39.914 [Debug] Merged params
2025-08-07 11:11:39.914 [Debug] Loading parameters from reference file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:11:39.914 [Info] Attempting to load configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:11:39.914 [Info] Successfully loaded configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:11:39.914 [Debug] Merging params
2025-08-07 11:11:39.914 [Debug] Merged basic params
2025-08-07 11:11:39.914 [Debug] Merged params
2025-08-07 11:11:39.914 [Debug] Merging params
2025-08-07 11:11:39.915 [Debug] Merging params
2025-08-07 11:11:39.915 [Debug] Resolving placeholders in test steps for test case: Login
2025-08-07 11:11:39.918 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-07 11:11:39.918 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-07 11:11:39.918 [Debug] Resolving placeholders in string: {{Selectors.FinishButton}}
2025-08-07 11:11:39.918 [Debug] Resolved string: id=finish
2025-08-07 11:11:39.918 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-07 11:11:39.918 [Debug] Resolved string: Click on Login Button
2025-08-07 11:11:39.918 [Debug] Resolving placeholders in string: {{Selectors.LoginButton}}
2025-08-07 11:11:39.919 [Debug] Resolved string: id=LoginBtn
2025-08-07 11:11:39.919 [Debug] Resolving placeholders in string: Type the User Name
2025-08-07 11:11:39.919 [Debug] Resolved string: Type the User Name
2025-08-07 11:11:39.919 [Debug] Resolving placeholders in string: {{Selectors.UserNameField}}
2025-08-07 11:11:39.919 [Debug] Resolved string: id=UserName
2025-08-07 11:11:39.919 [Debug] Resolving placeholders in string: {{TestData.UserName}}
2025-08-07 11:11:39.919 [Debug] Resolved string: mahmoudsayed022
2025-08-07 11:11:39.919 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-07 11:11:39.919 [Debug] Resolved string: Click on Continue Button
2025-08-07 11:11:39.919 [Debug] Resolving placeholders in string: {{Selectors.ContinueButton}}
2025-08-07 11:11:39.919 [Debug] Resolved string: id=btn
2025-08-07 11:11:39.919 [Debug] Resolving placeholders in string: Type the Password
2025-08-07 11:11:39.919 [Debug] Resolved string: Type the Password
2025-08-07 11:11:39.923 [Debug] Resolving placeholders in string: {{Selectors.PasswordField}}
2025-08-07 11:11:39.923 [Debug] Resolved string: id=Password
2025-08-07 11:11:39.923 [Debug] Resolving placeholders in string: $('[{{Selectors.PasswordField}}]').val('{{TestData.Password}}')
2025-08-07 11:11:39.924 [Debug] Resolved string: $('[id=Password]').val('Password1')
2025-08-07 11:11:39.924 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-07 11:11:39.924 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-07 11:11:39.924 [Debug] Resolving placeholders in string: {{Selectors.ChallengeQuestionField}}
2025-08-07 11:11:39.924 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-07 11:11:39.924 [Debug] Resolving placeholders in string: {{TestData.ChallengeAnswer}}
2025-08-07 11:11:39.924 [Debug] Resolved string: bmw
2025-08-07 11:11:39.924 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-07 11:11:39.924 [Debug] Resolved string: Click on the Login Button
2025-08-07 11:11:39.924 [Debug] Resolving placeholders in string: {{Selectors.LoginAuthButton}}
2025-08-07 11:11:39.924 [Debug] Resolved string: id=LoginAuthBtn
2025-08-07 11:11:39.924 [Info] Successfully resolved parameters for test case: Login
2025-08-07 11:11:39.924 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-07 11:11:39.924 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-07 11:11:39.924 [Debug] Resolving placeholders in string: id=finish
2025-08-07 11:11:39.924 [Debug] Resolved string: id=finish
2025-08-07 11:11:39.926 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-07 11:11:39.926 [Debug] Resolved string: Click on Login Button
2025-08-07 11:11:39.926 [Debug] Resolving placeholders in string: id=LoginBtn
2025-08-07 11:11:39.926 [Debug] Resolved string: id=LoginBtn
2025-08-07 11:11:39.926 [Debug] Resolving placeholders in string: Type the User Name
2025-08-07 11:11:39.926 [Debug] Resolved string: Type the User Name
2025-08-07 11:11:39.926 [Debug] Resolving placeholders in string: id=UserName
2025-08-07 11:11:39.927 [Debug] Resolved string: id=UserName
2025-08-07 11:11:39.927 [Debug] Resolving placeholders in string: mahmoudsayed022
2025-08-07 11:11:39.927 [Debug] Resolved string: mahmoudsayed022
2025-08-07 11:11:39.927 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-07 11:11:39.927 [Debug] Resolved string: Click on Continue Button
2025-08-07 11:11:39.927 [Debug] Resolving placeholders in string: id=btn
2025-08-07 11:11:39.927 [Debug] Resolved string: id=btn
2025-08-07 11:11:39.927 [Debug] Resolving placeholders in string: Type the Password
2025-08-07 11:11:39.927 [Debug] Resolved string: Type the Password
2025-08-07 11:11:39.927 [Debug] Resolving placeholders in string: id=Password
2025-08-07 11:11:39.927 [Debug] Resolved string: id=Password
2025-08-07 11:11:39.927 [Debug] Resolving placeholders in string: $('[id=Password]').val('Password1')
2025-08-07 11:11:39.929 [Debug] Resolved string: $('[id=Password]').val('Password1')
2025-08-07 11:11:39.930 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-07 11:11:39.930 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-07 11:11:39.930 [Debug] Resolving placeholders in string: id=ChallengeQuestionAnswer
2025-08-07 11:11:39.930 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-07 11:11:39.930 [Debug] Resolving placeholders in string: bmw
2025-08-07 11:11:39.930 [Debug] Resolved string: bmw
2025-08-07 11:11:39.930 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-07 11:11:39.930 [Debug] Resolved string: Click on the Login Button
2025-08-07 11:11:39.930 [Debug] Resolving placeholders in string: id=LoginAuthBtn
2025-08-07 11:11:39.930 [Debug] Resolved string: id=LoginAuthBtn
2025-08-07 11:11:39.930 [Debug] Resolving placeholders in string: Click on change later button on the password expiration popup message
2025-08-07 11:11:39.930 [Debug] Resolved string: Click on change later button on the password expiration popup message
2025-08-07 11:11:39.930 [Debug] Resolving placeholders in string: {{Selectors.popupCancel}}
2025-08-07 11:11:39.930 [Debug] Resolved string: id=popup_cancel
2025-08-07 11:11:39.930 [Debug] Resolving placeholders in string: Click on biometric alert
2025-08-07 11:11:39.930 [Debug] Resolved string: Click on biometric alert
2025-08-07 11:11:39.930 [Debug] Resolving placeholders in string: {{Selectors.BioMetricAlert}}
2025-08-07 11:11:39.930 [Debug] Resolved string: css=#popbuttons > div > button.btn.Cancel.back_gradient2
2025-08-07 11:11:39.932 [Debug] Resolving placeholders in string: Execute JS code to activate token
2025-08-07 11:11:39.932 [Debug] Resolved string: Execute JS code to activate token
2025-08-07 11:11:39.932 [Debug] Resolving placeholders in string: Globals.ActivationState = 'ACTIVATED';
2025-08-07 11:11:39.932 [Debug] Resolved string: Globals.ActivationState = 'ACTIVATED';
2025-08-07 11:11:39.932 [Debug] Resolving placeholders in string: Click on Beneficiaries
2025-08-07 11:11:39.932 [Debug] Resolved string: Click on Beneficiaries
2025-08-07 11:11:39.932 [Debug] Resolving placeholders in string: {{Selectors.BeneficiariesToButton}}
2025-08-07 11:11:39.932 [Debug] Resolved string: xpath=//div[@class='mainmenuitems']//label[contains(text(), 'Beneficiaries')]
2025-08-07 11:11:39.932 [Debug] Resolving placeholders in string: Click on Other CAE Accounts
2025-08-07 11:11:39.932 [Debug] Resolved string: Click on Other CAE Accounts
2025-08-07 11:11:39.933 [Debug] Resolving placeholders in string: {{Selectors.OtherCAEAccounts}}
2025-08-07 11:11:39.933 [Debug] Resolved string: xpath=//div[@class='submenuitem']//label[contains(text(), 'Other CAE Accounts')]
2025-08-07 11:11:39.933 [Debug] Resolving placeholders in string: Check if the Account List appears
2025-08-07 11:11:39.933 [Debug] Resolved string: Check if the Account List appears
2025-08-07 11:11:39.933 [Debug] Resolving placeholders in string: {{Selectors.BeneficiaryHeader}}
2025-08-07 11:11:39.933 [Debug] Resolved string: id=BeneficiaryHeader
2025-08-07 11:11:39.933 [Info] Successfully resolved parameters for test case: Existing Account beneficiaries displayed
2025-08-07 11:11:39.933 [Debug] Loading test case: WorkSet01/TestCases/03.Beneficiaries/Verify/252-Existing Card beneficiaries displayed.json
2025-08-07 11:11:39.935 [Info] Loading test case from: WorkSet01/TestCases/03.Beneficiaries/Verify/252-Existing Card beneficiaries displayed.json
2025-08-07 11:11:39.935 [Info] Attempting to load configuration file: WorkSet01/TestCases/03.Beneficiaries/Verify/252-Existing Card beneficiaries displayed.json
2025-08-07 11:11:39.935 [Info] Successfully loaded configuration file: WorkSet01/TestCases/03.Beneficiaries/Verify/252-Existing Card beneficiaries displayed.json
2025-08-07 11:11:39.935 [Info] No filtering configuration found. Including test case 'Existing Card beneficiaries displayed'.
2025-08-07 11:11:39.936 [Debug] Resolving parameters for test case: Existing Card beneficiaries displayed
2025-08-07 11:11:39.936 [Debug] Merging params
2025-08-07 11:11:39.936 [Debug] Merged basic params
2025-08-07 11:11:39.936 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-07 11:11:39.936 [Debug] Merged SpecialEnvParams params
2025-08-07 11:11:39.936 [Debug] Merged params
2025-08-07 11:11:39.936 [Debug] Loading parameters from reference file: WorkSet01/Params/BeneficiariesParams.json
2025-08-07 11:11:39.936 [Info] Attempting to load configuration file: WorkSet01/Params/BeneficiariesParams.json
2025-08-07 11:11:39.937 [Info] Successfully loaded configuration file: WorkSet01/Params/BeneficiariesParams.json
2025-08-07 11:11:39.937 [Debug] Merging params
2025-08-07 11:11:39.938 [Debug] Merged basic params
2025-08-07 11:11:39.938 [Debug] Merged params
2025-08-07 11:11:39.938 [Debug] Merging params
2025-08-07 11:11:39.938 [Debug] Merging params
2025-08-07 11:11:39.942 [Debug] Resolving placeholders in test steps for test case: Existing Card beneficiaries displayed
2025-08-07 11:11:39.942 [Debug] Resolving TestCaseReference in step: 
2025-08-07 11:11:39.942 [Info] Loading test case from: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:11:39.942 [Info] Attempting to load configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:11:39.942 [Info] Successfully loaded configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:11:39.942 [Debug] Resolving parameters for test case: Login
2025-08-07 11:11:39.942 [Debug] Merging params
2025-08-07 11:11:39.942 [Debug] Merged basic params
2025-08-07 11:11:39.942 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-07 11:11:39.942 [Debug] Merged SpecialEnvParams params
2025-08-07 11:11:39.942 [Debug] Merged params
2025-08-07 11:11:39.943 [Debug] Loading parameters from reference file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:11:39.943 [Info] Attempting to load configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:11:39.943 [Info] Successfully loaded configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:11:39.943 [Debug] Merging params
2025-08-07 11:11:39.943 [Debug] Merged basic params
2025-08-07 11:11:39.943 [Debug] Merged params
2025-08-07 11:11:39.943 [Debug] Merging params
2025-08-07 11:11:39.947 [Debug] Merging params
2025-08-07 11:11:39.947 [Debug] Resolving placeholders in test steps for test case: Login
2025-08-07 11:11:39.947 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-07 11:11:39.947 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-07 11:11:39.947 [Debug] Resolving placeholders in string: {{Selectors.FinishButton}}
2025-08-07 11:11:39.947 [Debug] Resolved string: id=finish
2025-08-07 11:11:39.947 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-07 11:11:39.947 [Debug] Resolved string: Click on Login Button
2025-08-07 11:11:39.947 [Debug] Resolving placeholders in string: {{Selectors.LoginButton}}
2025-08-07 11:11:39.947 [Debug] Resolved string: id=LoginBtn
2025-08-07 11:11:39.947 [Debug] Resolving placeholders in string: Type the User Name
2025-08-07 11:11:39.947 [Debug] Resolved string: Type the User Name
2025-08-07 11:11:39.947 [Debug] Resolving placeholders in string: {{Selectors.UserNameField}}
2025-08-07 11:11:39.947 [Debug] Resolved string: id=UserName
2025-08-07 11:11:39.947 [Debug] Resolving placeholders in string: {{TestData.UserName}}
2025-08-07 11:11:39.947 [Debug] Resolved string: mahmoudsayed022
2025-08-07 11:11:39.947 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-07 11:11:39.949 [Debug] Resolved string: Click on Continue Button
2025-08-07 11:11:39.949 [Debug] Resolving placeholders in string: {{Selectors.ContinueButton}}
2025-08-07 11:11:39.949 [Debug] Resolved string: id=btn
2025-08-07 11:11:39.949 [Debug] Resolving placeholders in string: Type the Password
2025-08-07 11:11:39.949 [Debug] Resolved string: Type the Password
2025-08-07 11:11:39.949 [Debug] Resolving placeholders in string: {{Selectors.PasswordField}}
2025-08-07 11:11:39.949 [Debug] Resolved string: id=Password
2025-08-07 11:11:39.949 [Debug] Resolving placeholders in string: $('[{{Selectors.PasswordField}}]').val('{{TestData.Password}}')
2025-08-07 11:11:39.949 [Debug] Resolved string: $('[id=Password]').val('Password1')
2025-08-07 11:11:39.949 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-07 11:11:39.949 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-07 11:11:39.949 [Debug] Resolving placeholders in string: {{Selectors.ChallengeQuestionField}}
2025-08-07 11:11:39.950 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-07 11:11:39.950 [Debug] Resolving placeholders in string: {{TestData.ChallengeAnswer}}
2025-08-07 11:11:39.950 [Debug] Resolved string: bmw
2025-08-07 11:11:39.950 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-07 11:11:39.950 [Debug] Resolved string: Click on the Login Button
2025-08-07 11:11:39.952 [Debug] Resolving placeholders in string: {{Selectors.LoginAuthButton}}
2025-08-07 11:11:39.952 [Debug] Resolved string: id=LoginAuthBtn
2025-08-07 11:11:39.952 [Info] Successfully resolved parameters for test case: Login
2025-08-07 11:11:39.952 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-07 11:11:39.952 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-07 11:11:39.952 [Debug] Resolving placeholders in string: id=finish
2025-08-07 11:11:39.952 [Debug] Resolved string: id=finish
2025-08-07 11:11:39.952 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-07 11:11:39.952 [Debug] Resolved string: Click on Login Button
2025-08-07 11:11:39.952 [Debug] Resolving placeholders in string: id=LoginBtn
2025-08-07 11:11:39.952 [Debug] Resolved string: id=LoginBtn
2025-08-07 11:11:39.952 [Debug] Resolving placeholders in string: Type the User Name
2025-08-07 11:11:39.952 [Debug] Resolved string: Type the User Name
2025-08-07 11:11:39.952 [Debug] Resolving placeholders in string: id=UserName
2025-08-07 11:11:39.952 [Debug] Resolved string: id=UserName
2025-08-07 11:11:39.952 [Debug] Resolving placeholders in string: mahmoudsayed022
2025-08-07 11:11:39.953 [Debug] Resolved string: mahmoudsayed022
2025-08-07 11:11:39.956 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-07 11:11:39.956 [Debug] Resolved string: Click on Continue Button
2025-08-07 11:11:39.956 [Debug] Resolving placeholders in string: id=btn
2025-08-07 11:11:39.956 [Debug] Resolved string: id=btn
2025-08-07 11:11:39.956 [Debug] Resolving placeholders in string: Type the Password
2025-08-07 11:11:39.956 [Debug] Resolved string: Type the Password
2025-08-07 11:11:39.956 [Debug] Resolving placeholders in string: id=Password
2025-08-07 11:11:39.956 [Debug] Resolved string: id=Password
2025-08-07 11:11:39.956 [Debug] Resolving placeholders in string: $('[id=Password]').val('Password1')
2025-08-07 11:11:39.956 [Debug] Resolved string: $('[id=Password]').val('Password1')
2025-08-07 11:11:39.956 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-07 11:11:39.956 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-07 11:11:39.956 [Debug] Resolving placeholders in string: id=ChallengeQuestionAnswer
2025-08-07 11:11:39.956 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-07 11:11:39.956 [Debug] Resolving placeholders in string: bmw
2025-08-07 11:11:39.956 [Debug] Resolved string: bmw
2025-08-07 11:11:39.956 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-07 11:11:39.959 [Debug] Resolved string: Click on the Login Button
2025-08-07 11:11:39.959 [Debug] Resolving placeholders in string: id=LoginAuthBtn
2025-08-07 11:11:39.959 [Debug] Resolved string: id=LoginAuthBtn
2025-08-07 11:11:39.959 [Debug] Resolving placeholders in string: Click on change later button on the password expiration popup message
2025-08-07 11:11:39.959 [Debug] Resolved string: Click on change later button on the password expiration popup message
2025-08-07 11:11:39.959 [Debug] Resolving placeholders in string: {{Selectors.popupCancel}}
2025-08-07 11:11:39.959 [Debug] Resolved string: id=popup_cancel
2025-08-07 11:11:39.959 [Debug] Resolving placeholders in string: Click on biometric alert
2025-08-07 11:11:39.959 [Debug] Resolved string: Click on biometric alert
2025-08-07 11:11:39.959 [Debug] Resolving placeholders in string: {{Selectors.BioMetricAlert}}
2025-08-07 11:11:39.959 [Debug] Resolved string: css=#popbuttons > div > button.btn.Cancel.back_gradient2
2025-08-07 11:11:39.959 [Debug] Resolving placeholders in string: Execute JS code to activate token
2025-08-07 11:11:39.959 [Debug] Resolved string: Execute JS code to activate token
2025-08-07 11:11:39.959 [Debug] Resolving placeholders in string: Globals.ActivationState = 'ACTIVATED';
2025-08-07 11:11:39.959 [Debug] Resolved string: Globals.ActivationState = 'ACTIVATED';
2025-08-07 11:11:39.959 [Debug] Resolving placeholders in string: Click on Beneficiaries
2025-08-07 11:11:39.962 [Debug] Resolved string: Click on Beneficiaries
2025-08-07 11:11:39.962 [Debug] Resolving placeholders in string: {{Selectors.BeneficiariesToButton}}
2025-08-07 11:11:39.962 [Debug] Resolved string: xpath=//div[@class='mainmenuitems']//label[contains(text(), 'Beneficiaries')]
2025-08-07 11:11:39.962 [Debug] Resolving placeholders in string: Click on Other CAE Cards
2025-08-07 11:11:39.962 [Debug] Resolved string: Click on Other CAE Cards
2025-08-07 11:11:39.962 [Debug] Resolving placeholders in string: {{Selectors.OtherCAECards}}
2025-08-07 11:11:39.962 [Debug] Resolved string: xpath=//div[@class='submenuitem']//label[contains(text(), 'Other CAE Cards')]
2025-08-07 11:11:39.962 [Debug] Resolving placeholders in string: Check if the Account List appears
2025-08-07 11:11:39.962 [Debug] Resolved string: Check if the Account List appears
2025-08-07 11:11:39.962 [Debug] Resolving placeholders in string: {{Selectors.BeneficiaryHeader}}
2025-08-07 11:11:39.962 [Debug] Resolved string: id=BeneficiaryHeader
2025-08-07 11:11:39.962 [Info] Successfully resolved parameters for test case: Existing Card beneficiaries displayed
2025-08-07 11:11:39.962 [Debug] Loading test case: WorkSet01/TestCases/03.Beneficiaries/Verify/TC-145 Existing bankinsideegypt beneficiaries displayed.json
2025-08-07 11:11:39.962 [Info] Loading test case from: WorkSet01/TestCases/03.Beneficiaries/Verify/TC-145 Existing bankinsideegypt beneficiaries displayed.json
2025-08-07 11:11:39.962 [Info] Attempting to load configuration file: WorkSet01/TestCases/03.Beneficiaries/Verify/TC-145 Existing bankinsideegypt beneficiaries displayed.json
2025-08-07 11:11:39.962 [Info] Successfully loaded configuration file: WorkSet01/TestCases/03.Beneficiaries/Verify/TC-145 Existing bankinsideegypt beneficiaries displayed.json
2025-08-07 11:11:39.965 [Info] No filtering configuration found. Including test case 'Existing Banks Inside Egypt beneficiaries displayed'.
2025-08-07 11:11:39.965 [Debug] Resolving parameters for test case: Existing Banks Inside Egypt beneficiaries displayed
2025-08-07 11:11:39.965 [Debug] Merging params
2025-08-07 11:11:39.965 [Debug] Merged basic params
2025-08-07 11:11:39.965 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-07 11:11:39.965 [Debug] Merged SpecialEnvParams params
2025-08-07 11:11:39.965 [Debug] Merged params
2025-08-07 11:11:39.965 [Debug] Loading parameters from reference file: WorkSet01/Params/BeneficiariesParams.json
2025-08-07 11:11:39.965 [Info] Attempting to load configuration file: WorkSet01/Params/BeneficiariesParams.json
2025-08-07 11:11:39.965 [Info] Successfully loaded configuration file: WorkSet01/Params/BeneficiariesParams.json
2025-08-07 11:11:39.966 [Debug] Merging params
2025-08-07 11:11:39.966 [Debug] Merged basic params
2025-08-07 11:11:39.966 [Debug] Merged params
2025-08-07 11:11:39.966 [Debug] Merging params
2025-08-07 11:11:39.966 [Debug] Merging params
2025-08-07 11:11:39.966 [Debug] Resolving placeholders in test steps for test case: Existing Banks Inside Egypt beneficiaries displayed
2025-08-07 11:11:39.968 [Debug] Resolving TestCaseReference in step: 
2025-08-07 11:11:39.968 [Info] Loading test case from: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:11:39.968 [Info] Attempting to load configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:11:39.968 [Info] Successfully loaded configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:11:39.969 [Debug] Resolving parameters for test case: Login
2025-08-07 11:11:39.969 [Debug] Merging params
2025-08-07 11:11:39.969 [Debug] Merged basic params
2025-08-07 11:11:39.969 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-07 11:11:39.969 [Debug] Merged SpecialEnvParams params
2025-08-07 11:11:39.969 [Debug] Merged params
2025-08-07 11:11:39.969 [Debug] Loading parameters from reference file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:11:39.969 [Info] Attempting to load configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:11:39.969 [Info] Successfully loaded configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:11:39.969 [Debug] Merging params
2025-08-07 11:11:39.969 [Debug] Merged basic params
2025-08-07 11:11:39.970 [Debug] Merged params
2025-08-07 11:11:39.973 [Debug] Merging params
2025-08-07 11:11:39.973 [Debug] Merging params
2025-08-07 11:11:39.973 [Debug] Resolving placeholders in test steps for test case: Login
2025-08-07 11:11:39.973 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-07 11:11:39.973 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-07 11:11:39.973 [Debug] Resolving placeholders in string: {{Selectors.FinishButton}}
2025-08-07 11:11:39.974 [Debug] Resolved string: id=finish
2025-08-07 11:11:39.974 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-07 11:11:39.974 [Debug] Resolved string: Click on Login Button
2025-08-07 11:11:39.974 [Debug] Resolving placeholders in string: {{Selectors.LoginButton}}
2025-08-07 11:11:39.974 [Debug] Resolved string: id=LoginBtn
2025-08-07 11:11:39.974 [Debug] Resolving placeholders in string: Type the User Name
2025-08-07 11:11:39.974 [Debug] Resolved string: Type the User Name
2025-08-07 11:11:39.974 [Debug] Resolving placeholders in string: {{Selectors.UserNameField}}
2025-08-07 11:11:39.974 [Debug] Resolved string: id=UserName
2025-08-07 11:11:39.974 [Debug] Resolving placeholders in string: {{TestData.UserName}}
2025-08-07 11:11:39.976 [Debug] Resolved string: mahmoudsayed022
2025-08-07 11:11:39.976 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-07 11:11:39.976 [Debug] Resolved string: Click on Continue Button
2025-08-07 11:11:39.976 [Debug] Resolving placeholders in string: {{Selectors.ContinueButton}}
2025-08-07 11:11:39.976 [Debug] Resolved string: id=btn
2025-08-07 11:11:39.976 [Debug] Resolving placeholders in string: Type the Password
2025-08-07 11:11:39.976 [Debug] Resolved string: Type the Password
2025-08-07 11:11:39.976 [Debug] Resolving placeholders in string: {{Selectors.PasswordField}}
2025-08-07 11:11:39.976 [Debug] Resolved string: id=Password
2025-08-07 11:11:39.976 [Debug] Resolving placeholders in string: $('[{{Selectors.PasswordField}}]').val('{{TestData.Password}}')
2025-08-07 11:11:39.976 [Debug] Resolved string: $('[id=Password]').val('Password1')
2025-08-07 11:11:39.976 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-07 11:11:39.976 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-07 11:11:39.976 [Debug] Resolving placeholders in string: {{Selectors.ChallengeQuestionField}}
2025-08-07 11:11:39.976 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-07 11:11:39.980 [Debug] Resolving placeholders in string: {{TestData.ChallengeAnswer}}
2025-08-07 11:11:39.980 [Debug] Resolved string: bmw
2025-08-07 11:11:39.980 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-07 11:11:39.980 [Debug] Resolved string: Click on the Login Button
2025-08-07 11:11:39.980 [Debug] Resolving placeholders in string: {{Selectors.LoginAuthButton}}
2025-08-07 11:11:39.980 [Debug] Resolved string: id=LoginAuthBtn
2025-08-07 11:11:39.980 [Info] Successfully resolved parameters for test case: Login
2025-08-07 11:11:39.980 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-07 11:11:39.980 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-07 11:11:39.980 [Debug] Resolving placeholders in string: id=finish
2025-08-07 11:11:39.980 [Debug] Resolved string: id=finish
2025-08-07 11:11:39.980 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-07 11:11:39.980 [Debug] Resolved string: Click on Login Button
2025-08-07 11:11:39.980 [Debug] Resolving placeholders in string: id=LoginBtn
2025-08-07 11:11:39.980 [Debug] Resolved string: id=LoginBtn
2025-08-07 11:11:39.983 [Debug] Resolving placeholders in string: Type the User Name
2025-08-07 11:11:39.983 [Debug] Resolved string: Type the User Name
2025-08-07 11:11:39.983 [Debug] Resolving placeholders in string: id=UserName
2025-08-07 11:11:39.983 [Debug] Resolved string: id=UserName
2025-08-07 11:11:39.983 [Debug] Resolving placeholders in string: mahmoudsayed022
2025-08-07 11:11:39.983 [Debug] Resolved string: mahmoudsayed022
2025-08-07 11:11:39.983 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-07 11:11:39.983 [Debug] Resolved string: Click on Continue Button
2025-08-07 11:11:39.983 [Debug] Resolving placeholders in string: id=btn
2025-08-07 11:11:39.983 [Debug] Resolved string: id=btn
2025-08-07 11:11:39.983 [Debug] Resolving placeholders in string: Type the Password
2025-08-07 11:11:39.983 [Debug] Resolved string: Type the Password
2025-08-07 11:11:39.983 [Debug] Resolving placeholders in string: id=Password
2025-08-07 11:11:39.983 [Debug] Resolved string: id=Password
2025-08-07 11:11:39.983 [Debug] Resolving placeholders in string: $('[id=Password]').val('Password1')
2025-08-07 11:11:39.985 [Debug] Resolved string: $('[id=Password]').val('Password1')
2025-08-07 11:11:39.985 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-07 11:11:39.985 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-07 11:11:39.985 [Debug] Resolving placeholders in string: id=ChallengeQuestionAnswer
2025-08-07 11:11:39.985 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-07 11:11:39.985 [Debug] Resolving placeholders in string: bmw
2025-08-07 11:11:39.986 [Debug] Resolved string: bmw
2025-08-07 11:11:39.986 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-07 11:11:39.986 [Debug] Resolved string: Click on the Login Button
2025-08-07 11:11:39.986 [Debug] Resolving placeholders in string: id=LoginAuthBtn
2025-08-07 11:11:39.986 [Debug] Resolved string: id=LoginAuthBtn
2025-08-07 11:11:39.986 [Debug] Resolving placeholders in string: Click on change later button on the password expiration popup message
2025-08-07 11:11:39.986 [Debug] Resolved string: Click on change later button on the password expiration popup message
2025-08-07 11:11:39.986 [Debug] Resolving placeholders in string: {{Selectors.popupCancel}}
2025-08-07 11:11:39.986 [Debug] Resolved string: id=popup_cancel
2025-08-07 11:11:39.989 [Debug] Resolving placeholders in string: Click on biometric alert
2025-08-07 11:11:39.989 [Debug] Resolved string: Click on biometric alert
2025-08-07 11:11:39.989 [Debug] Resolving placeholders in string: {{Selectors.BioMetricAlert}}
2025-08-07 11:11:39.989 [Debug] Resolved string: css=#popbuttons > div > button.btn.Cancel.back_gradient2
2025-08-07 11:11:39.989 [Debug] Resolving placeholders in string: Execute JS code to activate token
2025-08-07 11:11:39.989 [Debug] Resolved string: Execute JS code to activate token
2025-08-07 11:11:39.989 [Debug] Resolving placeholders in string: Globals.ActivationState = 'ACTIVATED';
2025-08-07 11:11:39.989 [Debug] Resolved string: Globals.ActivationState = 'ACTIVATED';
2025-08-07 11:11:39.989 [Debug] Resolving placeholders in string: Click on Beneficiaries
2025-08-07 11:11:39.989 [Debug] Resolved string: Click on Beneficiaries
2025-08-07 11:11:39.990 [Debug] Resolving placeholders in string: {{Selectors.BeneficiariesToButton}}
2025-08-07 11:11:39.990 [Debug] Resolved string: xpath=//div[@class='mainmenuitems']//label[contains(text(), 'Beneficiaries')]
2025-08-07 11:11:39.990 [Debug] Resolving placeholders in string: Click on Banks Inside Egypt
2025-08-07 11:11:39.990 [Debug] Resolved string: Click on Banks Inside Egypt
2025-08-07 11:11:39.990 [Debug] Resolving placeholders in string: {{Selectors.BanksInsideEgypt}}
2025-08-07 11:11:39.992 [Debug] Resolved string: xpath=//div[@class='submenuitem']//label[contains(text(), 'Banks Inside Egypt')]
2025-08-07 11:11:39.992 [Debug] Resolving placeholders in string: Check if the Account List appears
2025-08-07 11:11:39.992 [Debug] Resolved string: Check if the Account List appears
2025-08-07 11:11:39.992 [Debug] Resolving placeholders in string: {{Selectors.BeneficiaryHeader}}
2025-08-07 11:11:39.992 [Debug] Resolved string: id=BeneficiaryHeader
2025-08-07 11:11:39.992 [Info] Successfully resolved parameters for test case: Existing Banks Inside Egypt beneficiaries displayed
2025-08-07 11:11:39.992 [Debug] Loading test case: WorkSet01/TestCases/03.Beneficiaries/Delete/147-Delete Existing beneficiary.json
2025-08-07 11:11:39.992 [Info] Loading test case from: WorkSet01/TestCases/03.Beneficiaries/Delete/147-Delete Existing beneficiary.json
2025-08-07 11:11:39.992 [Info] Attempting to load configuration file: WorkSet01/TestCases/03.Beneficiaries/Delete/147-Delete Existing beneficiary.json
2025-08-07 11:11:39.993 [Info] Successfully loaded configuration file: WorkSet01/TestCases/03.Beneficiaries/Delete/147-Delete Existing beneficiary.json
2025-08-07 11:11:39.993 [Info] No filtering configuration found. Including test case 'Delete Existing beneficiary'.
2025-08-07 11:11:39.993 [Debug] Resolving parameters for test case: Delete Existing beneficiary
2025-08-07 11:11:39.993 [Debug] Merging params
2025-08-07 11:11:39.993 [Debug] Merged basic params
2025-08-07 11:11:39.993 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-07 11:11:39.995 [Debug] Merged SpecialEnvParams params
2025-08-07 11:11:39.995 [Debug] Merged params
2025-08-07 11:11:39.995 [Debug] Loading parameters from reference file: WorkSet01/Params/BeneficiariesParams.json
2025-08-07 11:11:39.995 [Info] Attempting to load configuration file: WorkSet01/Params/BeneficiariesParams.json
2025-08-07 11:11:39.995 [Info] Successfully loaded configuration file: WorkSet01/Params/BeneficiariesParams.json
2025-08-07 11:11:39.996 [Debug] Merging params
2025-08-07 11:11:39.996 [Debug] Merged basic params
2025-08-07 11:11:39.996 [Debug] Merged params
2025-08-07 11:11:39.996 [Debug] Merging params
2025-08-07 11:11:39.996 [Debug] Merging params
2025-08-07 11:11:39.996 [Debug] Resolving placeholders in test steps for test case: Delete Existing beneficiary
2025-08-07 11:11:39.996 [Debug] Resolving TestCaseReference in step: 
2025-08-07 11:11:39.996 [Info] Loading test case from: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:11:39.996 [Info] Attempting to load configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:11:39.998 [Info] Successfully loaded configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:11:39.999 [Debug] Resolving parameters for test case: Login
2025-08-07 11:11:39.999 [Debug] Merging params
2025-08-07 11:11:39.999 [Debug] Merged basic params
2025-08-07 11:11:39.999 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-07 11:11:39.999 [Debug] Merged SpecialEnvParams params
2025-08-07 11:11:39.999 [Debug] Merged params
2025-08-07 11:11:39.999 [Debug] Loading parameters from reference file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:11:39.999 [Info] Attempting to load configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:11:39.999 [Info] Successfully loaded configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:11:39.999 [Debug] Merging params
2025-08-07 11:11:39.999 [Debug] Merged basic params
2025-08-07 11:11:39.999 [Debug] Merged params
2025-08-07 11:11:39.999 [Debug] Merging params
2025-08-07 11:11:40.002 [Debug] Merging params
2025-08-07 11:11:40.002 [Debug] Resolving placeholders in test steps for test case: Login
2025-08-07 11:11:40.008 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-07 11:11:40.008 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-07 11:11:40.008 [Debug] Resolving placeholders in string: {{Selectors.FinishButton}}
2025-08-07 11:11:40.008 [Debug] Resolved string: id=finish
2025-08-07 11:11:40.008 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-07 11:11:40.008 [Debug] Resolved string: Click on Login Button
2025-08-07 11:11:40.008 [Debug] Resolving placeholders in string: {{Selectors.LoginButton}}
2025-08-07 11:11:40.008 [Debug] Resolved string: id=LoginBtn
2025-08-07 11:11:40.008 [Debug] Resolving placeholders in string: Type the User Name
2025-08-07 11:11:40.008 [Debug] Resolved string: Type the User Name
2025-08-07 11:11:40.009 [Debug] Resolving placeholders in string: {{Selectors.UserNameField}}
2025-08-07 11:11:40.009 [Debug] Resolved string: id=UserName
2025-08-07 11:11:40.011 [Debug] Resolving placeholders in string: {{TestData.UserName}}
2025-08-07 11:11:40.011 [Debug] Resolved string: mahmoudsayed022
2025-08-07 11:11:40.011 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-07 11:11:40.011 [Debug] Resolved string: Click on Continue Button
2025-08-07 11:11:40.011 [Debug] Resolving placeholders in string: {{Selectors.ContinueButton}}
2025-08-07 11:11:40.011 [Debug] Resolved string: id=btn
2025-08-07 11:11:40.011 [Debug] Resolving placeholders in string: Type the Password
2025-08-07 11:11:40.011 [Debug] Resolved string: Type the Password
2025-08-07 11:11:40.011 [Debug] Resolving placeholders in string: {{Selectors.PasswordField}}
2025-08-07 11:11:40.011 [Debug] Resolved string: id=Password
2025-08-07 11:11:40.011 [Debug] Resolving placeholders in string: $('[{{Selectors.PasswordField}}]').val('{{TestData.Password}}')
2025-08-07 11:11:40.011 [Debug] Resolved string: $('[id=Password]').val('Password1')
2025-08-07 11:11:40.011 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-07 11:11:40.011 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-07 11:11:40.015 [Debug] Resolving placeholders in string: {{Selectors.ChallengeQuestionField}}
2025-08-07 11:11:40.015 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-07 11:11:40.015 [Debug] Resolving placeholders in string: {{TestData.ChallengeAnswer}}
2025-08-07 11:11:40.015 [Debug] Resolved string: bmw
2025-08-07 11:11:40.015 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-07 11:11:40.015 [Debug] Resolved string: Click on the Login Button
2025-08-07 11:11:40.015 [Debug] Resolving placeholders in string: {{Selectors.LoginAuthButton}}
2025-08-07 11:11:40.015 [Debug] Resolved string: id=LoginAuthBtn
2025-08-07 11:11:40.015 [Info] Successfully resolved parameters for test case: Login
2025-08-07 11:11:40.015 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-07 11:11:40.015 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-07 11:11:40.015 [Debug] Resolving placeholders in string: id=finish
2025-08-07 11:11:40.015 [Debug] Resolved string: id=finish
2025-08-07 11:11:40.015 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-07 11:11:40.017 [Debug] Resolved string: Click on Login Button
2025-08-07 11:11:40.017 [Debug] Resolving placeholders in string: id=LoginBtn
2025-08-07 11:11:40.018 [Debug] Resolved string: id=LoginBtn
2025-08-07 11:11:40.018 [Debug] Resolving placeholders in string: Type the User Name
2025-08-07 11:11:40.018 [Debug] Resolved string: Type the User Name
2025-08-07 11:11:40.018 [Debug] Resolving placeholders in string: id=UserName
2025-08-07 11:11:40.018 [Debug] Resolved string: id=UserName
2025-08-07 11:11:40.018 [Debug] Resolving placeholders in string: mahmoudsayed022
2025-08-07 11:11:40.018 [Debug] Resolved string: mahmoudsayed022
2025-08-07 11:11:40.018 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-07 11:11:40.018 [Debug] Resolved string: Click on Continue Button
2025-08-07 11:11:40.018 [Debug] Resolving placeholders in string: id=btn
2025-08-07 11:11:40.018 [Debug] Resolved string: id=btn
2025-08-07 11:11:40.018 [Debug] Resolving placeholders in string: Type the Password
2025-08-07 11:11:40.021 [Debug] Resolved string: Type the Password
2025-08-07 11:11:40.021 [Debug] Resolving placeholders in string: id=Password
2025-08-07 11:11:40.021 [Debug] Resolved string: id=Password
2025-08-07 11:11:40.021 [Debug] Resolving placeholders in string: $('[id=Password]').val('Password1')
2025-08-07 11:11:40.021 [Debug] Resolved string: $('[id=Password]').val('Password1')
2025-08-07 11:11:40.021 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-07 11:11:40.021 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-07 11:11:40.021 [Debug] Resolving placeholders in string: id=ChallengeQuestionAnswer
2025-08-07 11:11:40.021 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-07 11:11:40.022 [Debug] Resolving placeholders in string: bmw
2025-08-07 11:11:40.022 [Debug] Resolved string: bmw
2025-08-07 11:11:40.022 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-07 11:11:40.022 [Debug] Resolved string: Click on the Login Button
2025-08-07 11:11:40.022 [Debug] Resolving placeholders in string: id=LoginAuthBtn
2025-08-07 11:11:40.024 [Debug] Resolved string: id=LoginAuthBtn
2025-08-07 11:11:40.024 [Debug] Resolving placeholders in string: Click on change later button on the password expiration popup message
2025-08-07 11:11:40.025 [Debug] Resolved string: Click on change later button on the password expiration popup message
2025-08-07 11:11:40.025 [Debug] Resolving placeholders in string: {{Selectors.popupCancel}}
2025-08-07 11:11:40.025 [Debug] Resolved string: id=popup_cancel
2025-08-07 11:11:40.025 [Debug] Resolving placeholders in string: Click on biometric alert
2025-08-07 11:11:40.025 [Debug] Resolved string: Click on biometric alert
2025-08-07 11:11:40.025 [Debug] Resolving placeholders in string: {{Selectors.BioMetricAlert}}
2025-08-07 11:11:40.025 [Debug] Resolved string: css=#popbuttons > div > button.btn.Cancel.back_gradient2
2025-08-07 11:11:40.025 [Debug] Resolving placeholders in string: Click on biometric alert
2025-08-07 11:11:40.025 [Debug] Resolved string: Click on biometric alert
2025-08-07 11:11:40.025 [Debug] Resolving placeholders in string: {{Selectors.KYCskip}}
2025-08-07 11:11:40.025 [Debug] Resolved string: {{Selectors.KYCskip}}
2025-08-07 11:11:40.028 [Debug] Resolving placeholders in string: Execute JS code to activate token
2025-08-07 11:11:40.028 [Debug] Resolved string: Execute JS code to activate token
2025-08-07 11:11:40.028 [Debug] Resolving placeholders in string: Globals.ActivationState = 'ACTIVATED';
2025-08-07 11:11:40.028 [Debug] Resolved string: Globals.ActivationState = 'ACTIVATED';
2025-08-07 11:11:40.029 [Debug] Resolving placeholders in string: Click on Beneficiaries
2025-08-07 11:11:40.029 [Debug] Resolved string: Click on Beneficiaries
2025-08-07 11:11:40.029 [Debug] Resolving placeholders in string: {{Selectors.BeneficiariesToButton}}
2025-08-07 11:11:40.029 [Debug] Resolved string: xpath=//div[@class='mainmenuitems']//label[contains(text(), 'Beneficiaries')]
2025-08-07 11:11:40.029 [Debug] Resolving placeholders in string: Click on Local Transfer option
2025-08-07 11:11:40.029 [Debug] Resolved string: Click on Local Transfer option
2025-08-07 11:11:40.029 [Debug] Resolving placeholders in string: {{Selectors.LocalTransfer}}
2025-08-07 11:11:40.029 [Debug] Resolved string: xpath=//div[@class='submenuitem']//label[contains(text(), 'Local Transfer')]
2025-08-07 11:11:40.029 [Debug] Resolving placeholders in string: Click on Delete button
2025-08-07 11:11:40.031 [Debug] Resolved string: Click on Delete button
2025-08-07 11:11:40.031 [Debug] Resolving placeholders in string: id=DeleteBtn
2025-08-07 11:11:40.032 [Debug] Resolved string: id=DeleteBtn
2025-08-07 11:11:40.032 [Debug] Resolving placeholders in string: Click on continue button
2025-08-07 11:11:40.032 [Debug] Resolved string: Click on continue button
2025-08-07 11:11:40.032 [Debug] Resolving placeholders in string: id=DelLocBenSumContinue
2025-08-07 11:11:40.032 [Debug] Resolved string: id=DelLocBenSumContinue
2025-08-07 11:11:40.032 [Info] Successfully resolved parameters for test case: Delete Existing beneficiary
2025-08-07 11:11:40.032 [Debug] Loading test case: WorkSet01/TestCases/03.Beneficiaries/Delete/TC-254 Delete an Existing Card beneficiary.json
2025-08-07 11:11:40.032 [Info] Loading test case from: WorkSet01/TestCases/03.Beneficiaries/Delete/TC-254 Delete an Existing Card beneficiary.json
2025-08-07 11:11:40.032 [Info] Attempting to load configuration file: WorkSet01/TestCases/03.Beneficiaries/Delete/TC-254 Delete an Existing Card beneficiary.json
2025-08-07 11:11:40.033 [Info] Successfully loaded configuration file: WorkSet01/TestCases/03.Beneficiaries/Delete/TC-254 Delete an Existing Card beneficiary.json
2025-08-07 11:11:40.033 [Info] No filtering configuration found. Including test case 'Delete an Existing Card beneficiary'.
2025-08-07 11:11:40.036 [Debug] Resolving parameters for test case: Delete an Existing Card beneficiary
2025-08-07 11:11:40.036 [Debug] Merging params
2025-08-07 11:11:40.036 [Debug] Merged basic params
2025-08-07 11:11:40.036 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-07 11:11:40.036 [Debug] Merged SpecialEnvParams params
2025-08-07 11:11:40.036 [Debug] Merged params
2025-08-07 11:11:40.036 [Debug] Loading parameters from reference file: WorkSet01/Params/BeneficiariesParams.json
2025-08-07 11:11:40.036 [Info] Attempting to load configuration file: WorkSet01/Params/BeneficiariesParams.json
2025-08-07 11:11:40.037 [Info] Successfully loaded configuration file: WorkSet01/Params/BeneficiariesParams.json
2025-08-07 11:11:40.037 [Debug] Merging params
2025-08-07 11:11:40.038 [Debug] Merged basic params
2025-08-07 11:11:40.038 [Debug] Merged params
2025-08-07 11:11:40.038 [Debug] Merging params
2025-08-07 11:11:40.040 [Debug] Merging params
2025-08-07 11:11:40.040 [Debug] Resolving placeholders in test steps for test case: Delete an Existing Card beneficiary
2025-08-07 11:11:40.040 [Debug] Resolving TestCaseReference in step: 
2025-08-07 11:11:40.040 [Info] Loading test case from: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:11:40.041 [Info] Attempting to load configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:11:40.041 [Info] Successfully loaded configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:11:40.041 [Debug] Resolving parameters for test case: Login
2025-08-07 11:11:40.041 [Debug] Merging params
2025-08-07 11:11:40.041 [Debug] Merged basic params
2025-08-07 11:11:40.041 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-07 11:11:40.041 [Debug] Merged SpecialEnvParams params
2025-08-07 11:11:40.042 [Debug] Merged params
2025-08-07 11:11:40.042 [Debug] Loading parameters from reference file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:11:40.044 [Info] Attempting to load configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:11:40.044 [Info] Successfully loaded configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:11:40.045 [Debug] Merging params
2025-08-07 11:11:40.045 [Debug] Merged basic params
2025-08-07 11:11:40.045 [Debug] Merged params
2025-08-07 11:11:40.045 [Debug] Merging params
2025-08-07 11:11:40.045 [Debug] Merging params
2025-08-07 11:11:40.045 [Debug] Resolving placeholders in test steps for test case: Login
2025-08-07 11:11:40.045 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-07 11:11:40.045 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-07 11:11:40.045 [Debug] Resolving placeholders in string: {{Selectors.FinishButton}}
2025-08-07 11:11:40.045 [Debug] Resolved string: id=finish
2025-08-07 11:11:40.045 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-07 11:11:40.047 [Debug] Resolved string: Click on Login Button
2025-08-07 11:11:40.047 [Debug] Resolving placeholders in string: {{Selectors.LoginButton}}
2025-08-07 11:11:40.047 [Debug] Resolved string: id=LoginBtn
2025-08-07 11:11:40.047 [Debug] Resolving placeholders in string: Type the User Name
2025-08-07 11:11:40.048 [Debug] Resolved string: Type the User Name
2025-08-07 11:11:40.048 [Debug] Resolving placeholders in string: {{Selectors.UserNameField}}
2025-08-07 11:11:40.048 [Debug] Resolved string: id=UserName
2025-08-07 11:11:40.048 [Debug] Resolving placeholders in string: {{TestData.UserName}}
2025-08-07 11:11:40.048 [Debug] Resolved string: mahmoudsayed022
2025-08-07 11:11:40.048 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-07 11:11:40.048 [Debug] Resolved string: Click on Continue Button
2025-08-07 11:11:40.048 [Debug] Resolving placeholders in string: {{Selectors.ContinueButton}}
2025-08-07 11:11:40.048 [Debug] Resolved string: id=btn
2025-08-07 11:11:40.050 [Debug] Resolving placeholders in string: Type the Password
2025-08-07 11:11:40.050 [Debug] Resolved string: Type the Password
2025-08-07 11:11:40.050 [Debug] Resolving placeholders in string: {{Selectors.PasswordField}}
2025-08-07 11:11:40.050 [Debug] Resolved string: id=Password
2025-08-07 11:11:40.050 [Debug] Resolving placeholders in string: $('[{{Selectors.PasswordField}}]').val('{{TestData.Password}}')
2025-08-07 11:11:40.050 [Debug] Resolved string: $('[id=Password]').val('Password1')
2025-08-07 11:11:40.050 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-07 11:11:40.050 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-07 11:11:40.051 [Debug] Resolving placeholders in string: {{Selectors.ChallengeQuestionField}}
2025-08-07 11:11:40.051 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-07 11:11:40.051 [Debug] Resolving placeholders in string: {{TestData.ChallengeAnswer}}
2025-08-07 11:11:40.051 [Debug] Resolved string: bmw
2025-08-07 11:11:40.051 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-07 11:11:40.055 [Debug] Resolved string: Click on the Login Button
2025-08-07 11:11:40.056 [Debug] Resolving placeholders in string: {{Selectors.LoginAuthButton}}
2025-08-07 11:11:40.056 [Debug] Resolved string: id=LoginAuthBtn
2025-08-07 11:11:40.056 [Info] Successfully resolved parameters for test case: Login
2025-08-07 11:11:40.056 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-07 11:11:40.056 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-07 11:11:40.056 [Debug] Resolving placeholders in string: id=finish
2025-08-07 11:11:40.056 [Debug] Resolved string: id=finish
2025-08-07 11:11:40.056 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-07 11:11:40.056 [Debug] Resolved string: Click on Login Button
2025-08-07 11:11:40.056 [Debug] Resolving placeholders in string: id=LoginBtn
2025-08-07 11:11:40.056 [Debug] Resolved string: id=LoginBtn
2025-08-07 11:11:40.056 [Debug] Resolving placeholders in string: Type the User Name
2025-08-07 11:11:40.059 [Debug] Resolved string: Type the User Name
2025-08-07 11:11:40.059 [Debug] Resolving placeholders in string: id=UserName
2025-08-07 11:11:40.059 [Debug] Resolved string: id=UserName
2025-08-07 11:11:40.059 [Debug] Resolving placeholders in string: mahmoudsayed022
2025-08-07 11:11:40.059 [Debug] Resolved string: mahmoudsayed022
2025-08-07 11:11:40.059 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-07 11:11:40.059 [Debug] Resolved string: Click on Continue Button
2025-08-07 11:11:40.059 [Debug] Resolving placeholders in string: id=btn
2025-08-07 11:11:40.059 [Debug] Resolved string: id=btn
2025-08-07 11:11:40.059 [Debug] Resolving placeholders in string: Type the Password
2025-08-07 11:11:40.059 [Debug] Resolved string: Type the Password
2025-08-07 11:11:40.059 [Debug] Resolving placeholders in string: id=Password
2025-08-07 11:11:40.059 [Debug] Resolved string: id=Password
2025-08-07 11:11:40.061 [Debug] Resolving placeholders in string: $('[id=Password]').val('Password1')
2025-08-07 11:11:40.062 [Debug] Resolved string: $('[id=Password]').val('Password1')
2025-08-07 11:11:40.062 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-07 11:11:40.062 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-07 11:11:40.062 [Debug] Resolving placeholders in string: id=ChallengeQuestionAnswer
2025-08-07 11:11:40.062 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-07 11:11:40.062 [Debug] Resolving placeholders in string: bmw
2025-08-07 11:11:40.062 [Debug] Resolved string: bmw
2025-08-07 11:11:40.062 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-07 11:11:40.062 [Debug] Resolved string: Click on the Login Button
2025-08-07 11:11:40.062 [Debug] Resolving placeholders in string: id=LoginAuthBtn
2025-08-07 11:11:40.062 [Debug] Resolved string: id=LoginAuthBtn
2025-08-07 11:11:40.064 [Debug] Resolving placeholders in string: Click on change later button on the password expiration popup message
2025-08-07 11:11:40.065 [Debug] Resolved string: Click on change later button on the password expiration popup message
2025-08-07 11:11:40.065 [Debug] Resolving placeholders in string: {{Selectors.popupCancel}}
2025-08-07 11:11:40.065 [Debug] Resolved string: id=popup_cancel
2025-08-07 11:11:40.065 [Debug] Resolving placeholders in string: Click on biometric alert
2025-08-07 11:11:40.065 [Debug] Resolved string: Click on biometric alert
2025-08-07 11:11:40.065 [Debug] Resolving placeholders in string: {{Selectors.BioMetricAlert}}
2025-08-07 11:11:40.065 [Debug] Resolved string: css=#popbuttons > div > button.btn.Cancel.back_gradient2
2025-08-07 11:11:40.065 [Debug] Resolving placeholders in string: Click on biometric alert
2025-08-07 11:11:40.065 [Debug] Resolved string: Click on biometric alert
2025-08-07 11:11:40.065 [Debug] Resolving placeholders in string: {{Selectors.KYCskip}}
2025-08-07 11:11:40.065 [Debug] Resolved string: {{Selectors.KYCskip}}
2025-08-07 11:11:40.067 [Debug] Resolving placeholders in string: Execute JS code to activate token
2025-08-07 11:11:40.067 [Debug] Resolved string: Execute JS code to activate token
2025-08-07 11:11:40.067 [Debug] Resolving placeholders in string: Globals.ActivationState = 'ACTIVATED';
2025-08-07 11:11:40.067 [Debug] Resolved string: Globals.ActivationState = 'ACTIVATED';
2025-08-07 11:11:40.067 [Debug] Resolving placeholders in string: Click on Beneficiaries
2025-08-07 11:11:40.068 [Debug] Resolved string: Click on Beneficiaries
2025-08-07 11:11:40.068 [Debug] Resolving placeholders in string: {{Selectors.BeneficiariesToButton}}
2025-08-07 11:11:40.068 [Debug] Resolved string: xpath=//div[@class='mainmenuitems']//label[contains(text(), 'Beneficiaries')]
2025-08-07 11:11:40.068 [Debug] Resolving placeholders in string: Click on Other CAE Cards
2025-08-07 11:11:40.068 [Debug] Resolved string: Click on Other CAE Cards
2025-08-07 11:11:40.068 [Debug] Resolving placeholders in string: {{Selectors.OtherCAECards}}
2025-08-07 11:11:40.068 [Debug] Resolved string: xpath=//div[@class='submenuitem']//label[contains(text(), 'Other CAE Cards')]
2025-08-07 11:11:40.070 [Debug] Resolving placeholders in string: Click on Delete button
2025-08-07 11:11:40.071 [Debug] Resolved string: Click on Delete button
2025-08-07 11:11:40.071 [Debug] Resolving placeholders in string: id=DeleteBeneficiary
2025-08-07 11:11:40.071 [Debug] Resolved string: id=DeleteBeneficiary
2025-08-07 11:11:40.071 [Debug] Resolving placeholders in string: Click on continue button and show confirmation
2025-08-07 11:11:40.071 [Debug] Resolved string: Click on continue button and show confirmation
2025-08-07 11:11:40.071 [Debug] Resolving placeholders in string: id=DigitalBankBenfContinueDeleteBtn
2025-08-07 11:11:40.071 [Debug] Resolved string: id=DigitalBankBenfContinueDeleteBtn
2025-08-07 11:11:40.071 [Info] Successfully resolved parameters for test case: Delete an Existing Card beneficiary
2025-08-07 11:11:40.071 [Debug] Loading test case: WorkSet01/TestCases/03.Beneficiaries/Delete/TC-253 Delete an Existing Account beneficiary.json
2025-08-07 11:11:40.071 [Info] Loading test case from: WorkSet01/TestCases/03.Beneficiaries/Delete/TC-253 Delete an Existing Account beneficiary.json
2025-08-07 11:11:40.071 [Info] Attempting to load configuration file: WorkSet01/TestCases/03.Beneficiaries/Delete/TC-253 Delete an Existing Account beneficiary.json
2025-08-07 11:11:40.074 [Info] Successfully loaded configuration file: WorkSet01/TestCases/03.Beneficiaries/Delete/TC-253 Delete an Existing Account beneficiary.json
2025-08-07 11:11:40.075 [Info] No filtering configuration found. Including test case 'Delete an Existing Account beneficiary'.
2025-08-07 11:11:40.075 [Debug] Resolving parameters for test case: Delete an Existing Account beneficiary
2025-08-07 11:11:40.075 [Debug] Merging params
2025-08-07 11:11:40.075 [Debug] Merged basic params
2025-08-07 11:11:40.075 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-07 11:11:40.075 [Debug] Merged SpecialEnvParams params
2025-08-07 11:11:40.075 [Debug] Merged params
2025-08-07 11:11:40.075 [Debug] Loading parameters from reference file: WorkSet01/Params/BeneficiariesParams.json
2025-08-07 11:11:40.075 [Info] Attempting to load configuration file: WorkSet01/Params/BeneficiariesParams.json
2025-08-07 11:11:40.075 [Info] Successfully loaded configuration file: WorkSet01/Params/BeneficiariesParams.json
2025-08-07 11:11:40.076 [Debug] Merging params
2025-08-07 11:11:40.078 [Debug] Merged basic params
2025-08-07 11:11:40.078 [Debug] Merged params
2025-08-07 11:11:40.078 [Debug] Merging params
2025-08-07 11:11:40.078 [Debug] Merging params
2025-08-07 11:11:40.078 [Debug] Resolving placeholders in test steps for test case: Delete an Existing Account beneficiary
2025-08-07 11:11:40.078 [Debug] Resolving TestCaseReference in step: 
2025-08-07 11:11:40.078 [Info] Loading test case from: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:11:40.078 [Info] Attempting to load configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:11:40.078 [Info] Successfully loaded configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:11:40.079 [Debug] Resolving parameters for test case: Login
2025-08-07 11:11:40.079 [Debug] Merging params
2025-08-07 11:11:40.079 [Debug] Merged basic params
2025-08-07 11:11:40.081 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-07 11:11:40.081 [Debug] Merged SpecialEnvParams params
2025-08-07 11:11:40.081 [Debug] Merged params
2025-08-07 11:11:40.081 [Debug] Loading parameters from reference file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:11:40.081 [Info] Attempting to load configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:11:40.082 [Info] Successfully loaded configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:11:40.082 [Debug] Merging params
2025-08-07 11:11:40.082 [Debug] Merged basic params
2025-08-07 11:11:40.082 [Debug] Merged params
2025-08-07 11:11:40.082 [Debug] Merging params
2025-08-07 11:11:40.082 [Debug] Merging params
2025-08-07 11:11:40.082 [Debug] Resolving placeholders in test steps for test case: Login
2025-08-07 11:11:40.084 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-07 11:11:40.085 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-07 11:11:40.085 [Debug] Resolving placeholders in string: {{Selectors.FinishButton}}
2025-08-07 11:11:40.085 [Debug] Resolved string: id=finish
2025-08-07 11:11:40.085 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-07 11:11:40.085 [Debug] Resolved string: Click on Login Button
2025-08-07 11:11:40.085 [Debug] Resolving placeholders in string: {{Selectors.LoginButton}}
2025-08-07 11:11:40.085 [Debug] Resolved string: id=LoginBtn
2025-08-07 11:11:40.085 [Debug] Resolving placeholders in string: Type the User Name
2025-08-07 11:11:40.085 [Debug] Resolved string: Type the User Name
2025-08-07 11:11:40.085 [Debug] Resolving placeholders in string: {{Selectors.UserNameField}}
2025-08-07 11:11:40.085 [Debug] Resolved string: id=UserName
2025-08-07 11:11:40.088 [Debug] Resolving placeholders in string: {{TestData.UserName}}
2025-08-07 11:11:40.088 [Debug] Resolved string: mahmoudsayed022
2025-08-07 11:11:40.088 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-07 11:11:40.088 [Debug] Resolved string: Click on Continue Button
2025-08-07 11:11:40.088 [Debug] Resolving placeholders in string: {{Selectors.ContinueButton}}
2025-08-07 11:11:40.088 [Debug] Resolved string: id=btn
2025-08-07 11:11:40.088 [Debug] Resolving placeholders in string: Type the Password
2025-08-07 11:11:40.089 [Debug] Resolved string: Type the Password
2025-08-07 11:11:40.089 [Debug] Resolving placeholders in string: {{Selectors.PasswordField}}
2025-08-07 11:11:40.089 [Debug] Resolved string: id=Password
2025-08-07 11:11:40.089 [Debug] Resolving placeholders in string: $('[{{Selectors.PasswordField}}]').val('{{TestData.Password}}')
2025-08-07 11:11:40.089 [Debug] Resolved string: $('[id=Password]').val('Password1')
2025-08-07 11:11:40.091 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-07 11:11:40.091 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-07 11:11:40.091 [Debug] Resolving placeholders in string: {{Selectors.ChallengeQuestionField}}
2025-08-07 11:11:40.092 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-07 11:11:40.092 [Debug] Resolving placeholders in string: {{TestData.ChallengeAnswer}}
2025-08-07 11:11:40.092 [Debug] Resolved string: bmw
2025-08-07 11:11:40.092 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-07 11:11:40.092 [Debug] Resolved string: Click on the Login Button
2025-08-07 11:11:40.092 [Debug] Resolving placeholders in string: {{Selectors.LoginAuthButton}}
2025-08-07 11:11:40.092 [Debug] Resolved string: id=LoginAuthBtn
2025-08-07 11:11:40.092 [Info] Successfully resolved parameters for test case: Login
2025-08-07 11:11:40.092 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-07 11:11:40.095 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-07 11:11:40.096 [Debug] Resolving placeholders in string: id=finish
2025-08-07 11:11:40.096 [Debug] Resolved string: id=finish
2025-08-07 11:11:40.096 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-07 11:11:40.096 [Debug] Resolved string: Click on Login Button
2025-08-07 11:11:40.096 [Debug] Resolving placeholders in string: id=LoginBtn
2025-08-07 11:11:40.096 [Debug] Resolved string: id=LoginBtn
2025-08-07 11:11:40.096 [Debug] Resolving placeholders in string: Type the User Name
2025-08-07 11:11:40.096 [Debug] Resolved string: Type the User Name
2025-08-07 11:11:40.096 [Debug] Resolving placeholders in string: id=UserName
2025-08-07 11:11:40.096 [Debug] Resolved string: id=UserName
2025-08-07 11:11:40.096 [Debug] Resolving placeholders in string: mahmoudsayed022
2025-08-07 11:11:40.098 [Debug] Resolved string: mahmoudsayed022
2025-08-07 11:11:40.098 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-07 11:11:40.099 [Debug] Resolved string: Click on Continue Button
2025-08-07 11:11:40.099 [Debug] Resolving placeholders in string: id=btn
2025-08-07 11:11:40.099 [Debug] Resolved string: id=btn
2025-08-07 11:11:40.099 [Debug] Resolving placeholders in string: Type the Password
2025-08-07 11:11:40.099 [Debug] Resolved string: Type the Password
2025-08-07 11:11:40.099 [Debug] Resolving placeholders in string: id=Password
2025-08-07 11:11:40.099 [Debug] Resolved string: id=Password
2025-08-07 11:11:40.099 [Debug] Resolving placeholders in string: $('[id=Password]').val('Password1')
2025-08-07 11:11:40.099 [Debug] Resolved string: $('[id=Password]').val('Password1')
2025-08-07 11:11:40.101 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-07 11:11:40.101 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-07 11:11:40.101 [Debug] Resolving placeholders in string: id=ChallengeQuestionAnswer
2025-08-07 11:11:40.101 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-07 11:11:40.101 [Debug] Resolving placeholders in string: bmw
2025-08-07 11:11:40.101 [Debug] Resolved string: bmw
2025-08-07 11:11:40.101 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-07 11:11:40.102 [Debug] Resolved string: Click on the Login Button
2025-08-07 11:11:40.102 [Debug] Resolving placeholders in string: id=LoginAuthBtn
2025-08-07 11:11:40.102 [Debug] Resolved string: id=LoginAuthBtn
2025-08-07 11:11:40.102 [Debug] Resolving placeholders in string: Click on change later button on the password expiration popup message
2025-08-07 11:11:40.105 [Debug] Resolved string: Click on change later button on the password expiration popup message
2025-08-07 11:11:40.105 [Debug] Resolving placeholders in string: {{Selectors.popupCancel}}
2025-08-07 11:11:40.105 [Debug] Resolved string: id=popup_cancel
2025-08-07 11:11:40.105 [Debug] Resolving placeholders in string: Click on biometric alert
2025-08-07 11:11:40.105 [Debug] Resolved string: Click on biometric alert
2025-08-07 11:11:40.105 [Debug] Resolving placeholders in string: {{Selectors.BioMetricAlert}}
2025-08-07 11:11:40.105 [Debug] Resolved string: css=#popbuttons > div > button.btn.Cancel.back_gradient2
2025-08-07 11:11:40.105 [Debug] Resolving placeholders in string: Click on biometric alert
2025-08-07 11:11:40.105 [Debug] Resolved string: Click on biometric alert
2025-08-07 11:11:40.106 [Debug] Resolving placeholders in string: {{Selectors.KYCskip}}
2025-08-07 11:11:40.106 [Debug] Resolved string: {{Selectors.KYCskip}}
2025-08-07 11:11:40.108 [Debug] Resolving placeholders in string: Execute JS code to activate token
2025-08-07 11:11:40.108 [Debug] Resolved string: Execute JS code to activate token
2025-08-07 11:11:40.108 [Debug] Resolving placeholders in string: Globals.ActivationState = 'ACTIVATED';
2025-08-07 11:11:40.108 [Debug] Resolved string: Globals.ActivationState = 'ACTIVATED';
2025-08-07 11:11:40.108 [Debug] Resolving placeholders in string: Click on Beneficiaries
2025-08-07 11:11:40.108 [Debug] Resolved string: Click on Beneficiaries
2025-08-07 11:11:40.108 [Debug] Resolving placeholders in string: {{Selectors.BeneficiariesToButton}}
2025-08-07 11:11:40.108 [Debug] Resolved string: xpath=//div[@class='mainmenuitems']//label[contains(text(), 'Beneficiaries')]
2025-08-07 11:11:40.108 [Debug] Resolving placeholders in string: Click on Other CAE Accounts
2025-08-07 11:11:40.109 [Debug] Resolved string: Click on Other CAE Accounts
2025-08-07 11:11:40.109 [Debug] Resolving placeholders in string: {{Selectors.OtherCAEAccounts}}
2025-08-07 11:11:40.111 [Debug] Resolved string: xpath=//div[@class='submenuitem']//label[contains(text(), 'Other CAE Accounts')]
2025-08-07 11:11:40.111 [Debug] Resolving placeholders in string: Click on Delete button
2025-08-07 11:11:40.111 [Debug] Resolved string: Click on Delete button
2025-08-07 11:11:40.111 [Debug] Resolving placeholders in string: id=DeleteBeneficiary
2025-08-07 11:11:40.111 [Debug] Resolved string: id=DeleteBeneficiary
2025-08-07 11:11:40.111 [Debug] Resolving placeholders in string: Click on continue button and show confirmation
2025-08-07 11:11:40.111 [Debug] Resolved string: Click on continue button and show confirmation
2025-08-07 11:11:40.111 [Debug] Resolving placeholders in string: id=DigitalBankBenfContinueDeleteBtn
2025-08-07 11:11:40.111 [Debug] Resolved string: id=DigitalBankBenfContinueDeleteBtn
2025-08-07 11:11:40.111 [Info] Successfully resolved parameters for test case: Delete an Existing Account beneficiary
2025-08-07 11:11:40.111 [Debug] Loading test case: WorkSet01/TestCases/04.ATMdispute/Request Status/TC-430 Ensure that all request displayed Dispute Request.json
2025-08-07 11:11:40.113 [Info] Loading test case from: WorkSet01/TestCases/04.ATMdispute/Request Status/TC-430 Ensure that all request displayed Dispute Request.json
2025-08-07 11:11:40.114 [Info] Attempting to load configuration file: WorkSet01/TestCases/04.ATMdispute/Request Status/TC-430 Ensure that all request displayed Dispute Request.json
2025-08-07 11:11:40.114 [Info] Successfully loaded configuration file: WorkSet01/TestCases/04.ATMdispute/Request Status/TC-430 Ensure that all request displayed Dispute Request.json
2025-08-07 11:11:40.114 [Info] No filtering configuration found. Including test case ' Ensure that all request displayed -Dispute Request-'.
2025-08-07 11:11:40.114 [Debug] Resolving parameters for test case:  Ensure that all request displayed -Dispute Request-
2025-08-07 11:11:40.114 [Debug] Merging params
2025-08-07 11:11:40.114 [Debug] Merged basic params
2025-08-07 11:11:40.114 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-07 11:11:40.114 [Debug] Merged SpecialEnvParams params
2025-08-07 11:11:40.114 [Debug] Merged params
2025-08-07 11:11:40.115 [Debug] Loading parameters from reference file: WorkSet01/Params/CardServicesParams.json
2025-08-07 11:11:40.117 [Info] Attempting to load configuration file: WorkSet01/Params/CardServicesParams.json
2025-08-07 11:11:40.117 [Info] Successfully loaded configuration file: WorkSet01/Params/CardServicesParams.json
2025-08-07 11:11:40.117 [Debug] Merging params
2025-08-07 11:11:40.117 [Debug] Merged basic params
2025-08-07 11:11:40.117 [Debug] Merged params
2025-08-07 11:11:40.117 [Debug] Merging params
2025-08-07 11:11:40.117 [Debug] Merging params
2025-08-07 11:11:40.117 [Debug] Resolving placeholders in test steps for test case:  Ensure that all request displayed -Dispute Request-
2025-08-07 11:11:40.117 [Debug] Resolving TestCaseReference in step: 
2025-08-07 11:11:40.117 [Info] Loading test case from: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:11:40.118 [Info] Attempting to load configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:11:40.120 [Info] Successfully loaded configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:11:40.120 [Debug] Resolving parameters for test case: Login
2025-08-07 11:11:40.120 [Debug] Merging params
2025-08-07 11:11:40.121 [Debug] Merged basic params
2025-08-07 11:11:40.121 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-07 11:11:40.121 [Debug] Merged SpecialEnvParams params
2025-08-07 11:11:40.121 [Debug] Merged params
2025-08-07 11:11:40.121 [Debug] Loading parameters from reference file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:11:40.121 [Info] Attempting to load configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:11:40.121 [Info] Successfully loaded configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:11:40.122 [Debug] Merging params
2025-08-07 11:11:40.124 [Debug] Merged basic params
2025-08-07 11:11:40.124 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-07 11:11:40.124 [Debug] Merged SpecialEnvParams params
2025-08-07 11:11:40.124 [Debug] Merged params
2025-08-07 11:11:40.124 [Debug] Merging params
2025-08-07 11:11:40.124 [Debug] Merging params
2025-08-07 11:11:40.124 [Debug] Resolving placeholders in test steps for test case: Login
2025-08-07 11:11:40.124 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-07 11:11:40.124 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-07 11:11:40.124 [Debug] Resolving placeholders in string: {{Selectors.FinishButton}}
2025-08-07 11:11:40.124 [Debug] Resolved string: id=finish
2025-08-07 11:11:40.127 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-07 11:11:40.127 [Debug] Resolved string: Click on Login Button
2025-08-07 11:11:40.127 [Debug] Resolving placeholders in string: {{Selectors.LoginButton}}
2025-08-07 11:11:40.127 [Debug] Resolved string: id=LoginBtn
2025-08-07 11:11:40.127 [Debug] Resolving placeholders in string: Type the User Name
2025-08-07 11:11:40.127 [Debug] Resolved string: Type the User Name
2025-08-07 11:11:40.127 [Debug] Resolving placeholders in string: {{Selectors.UserNameField}}
2025-08-07 11:11:40.127 [Debug] Resolved string: id=UserName
2025-08-07 11:11:40.127 [Debug] Resolving placeholders in string: {{TestData.UserName}}
2025-08-07 11:11:40.127 [Debug] Resolved string: Mm_azmy
2025-08-07 11:11:40.127 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-07 11:11:40.129 [Debug] Resolved string: Click on Continue Button
2025-08-07 11:11:40.129 [Debug] Resolving placeholders in string: {{Selectors.ContinueButton}}
2025-08-07 11:11:40.130 [Debug] Resolved string: id=btn
2025-08-07 11:11:40.130 [Debug] Resolving placeholders in string: Type the Password
2025-08-07 11:11:40.130 [Debug] Resolved string: Type the Password
2025-08-07 11:11:40.130 [Debug] Resolving placeholders in string: {{Selectors.PasswordField}}
2025-08-07 11:11:40.130 [Debug] Resolved string: id=Password
2025-08-07 11:11:40.130 [Debug] Resolving placeholders in string: $('[{{Selectors.PasswordField}}]').val('{{TestData.Password}}')
2025-08-07 11:11:40.130 [Debug] Resolved string: $('[id=Password]').val('Asdf2025')
2025-08-07 11:11:40.130 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-07 11:11:40.130 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-07 11:11:40.132 [Debug] Resolving placeholders in string: {{Selectors.ChallengeQuestionField}}
2025-08-07 11:11:40.132 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-07 11:11:40.132 [Debug] Resolving placeholders in string: {{TestData.ChallengeAnswer}}
2025-08-07 11:11:40.132 [Debug] Resolved string: armada
2025-08-07 11:11:40.132 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-07 11:11:40.132 [Debug] Resolved string: Click on the Login Button
2025-08-07 11:11:40.132 [Debug] Resolving placeholders in string: {{Selectors.LoginAuthButton}}
2025-08-07 11:11:40.132 [Debug] Resolved string: id=LoginAuthBtn
2025-08-07 11:11:40.132 [Info] Successfully resolved parameters for test case: Login
2025-08-07 11:11:40.133 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-07 11:11:40.133 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-07 11:11:40.135 [Debug] Resolving placeholders in string: id=finish
2025-08-07 11:11:40.135 [Debug] Resolved string: id=finish
2025-08-07 11:11:40.135 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-07 11:11:40.135 [Debug] Resolved string: Click on Login Button
2025-08-07 11:11:40.135 [Debug] Resolving placeholders in string: id=LoginBtn
2025-08-07 11:11:40.135 [Debug] Resolved string: id=LoginBtn
2025-08-07 11:11:40.135 [Debug] Resolving placeholders in string: Type the User Name
2025-08-07 11:11:40.135 [Debug] Resolved string: Type the User Name
2025-08-07 11:11:40.135 [Debug] Resolving placeholders in string: id=UserName
2025-08-07 11:11:40.135 [Debug] Resolved string: id=UserName
2025-08-07 11:11:40.135 [Debug] Resolving placeholders in string: Mm_azmy
2025-08-07 11:11:40.140 [Debug] Resolved string: Mm_azmy
2025-08-07 11:11:40.140 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-07 11:11:40.140 [Debug] Resolved string: Click on Continue Button
2025-08-07 11:11:40.140 [Debug] Resolving placeholders in string: id=btn
2025-08-07 11:11:40.140 [Debug] Resolved string: id=btn
2025-08-07 11:11:40.140 [Debug] Resolving placeholders in string: Type the Password
2025-08-07 11:11:40.140 [Debug] Resolved string: Type the Password
2025-08-07 11:11:40.140 [Debug] Resolving placeholders in string: id=Password
2025-08-07 11:11:40.140 [Debug] Resolved string: id=Password
2025-08-07 11:11:40.140 [Debug] Resolving placeholders in string: $('[id=Password]').val('Asdf2025')
2025-08-07 11:11:40.141 [Debug] Resolved string: $('[id=Password]').val('Asdf2025')
2025-08-07 11:11:40.143 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-07 11:11:40.143 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-07 11:11:40.143 [Debug] Resolving placeholders in string: id=ChallengeQuestionAnswer
2025-08-07 11:11:40.143 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-07 11:11:40.143 [Debug] Resolving placeholders in string: armada
2025-08-07 11:11:40.143 [Debug] Resolved string: armada
2025-08-07 11:11:40.143 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-07 11:11:40.143 [Debug] Resolved string: Click on the Login Button
2025-08-07 11:11:40.143 [Debug] Resolving placeholders in string: id=LoginAuthBtn
2025-08-07 11:11:40.143 [Debug] Resolved string: id=LoginAuthBtn
2025-08-07 11:11:40.143 [Debug] Resolving placeholders in string: Click on Cards option on the side menu
2025-08-07 11:11:40.145 [Debug] Resolved string: Click on Cards option on the side menu
2025-08-07 11:11:40.146 [Debug] Resolving placeholders in string: {{Selectors.CardsButton}}
2025-08-07 11:11:40.146 [Debug] Resolved string: xpath=//div[@class='mainmenuitems']//label[contains(text(), 'Cards')]
2025-08-07 11:11:40.146 [Debug] Resolving placeholders in string: Click on Card Services
2025-08-07 11:11:40.146 [Debug] Resolved string: Click on Card Services
2025-08-07 11:11:40.146 [Debug] Resolving placeholders in string: {{Selectors.CardServices}}
2025-08-07 11:11:40.146 [Debug] Resolved string: xpath=//div[@class='submenuitem']//label[contains(text(), 'Card services')]
2025-08-07 11:11:40.146 [Debug] Resolving placeholders in string: Click on request status and Ensure that all request displayed
2025-08-07 11:11:40.146 [Debug] Resolved string: Click on request status and Ensure that all request displayed
2025-08-07 11:11:40.146 [Debug] Resolving placeholders in string: {{Selectors.RequestStatues}}
2025-08-07 11:11:40.146 [Debug] Resolved string: id=RequestStatus
2025-08-07 11:11:40.148 [Info] Successfully resolved parameters for test case:  Ensure that all request displayed -Dispute Request-
2025-08-07 11:11:40.148 [Debug] Loading test case: WorkSet01/TestCases/04.ATMdispute/128-ATM Dispute, click View Details.json
2025-08-07 11:11:40.148 [Info] Loading test case from: WorkSet01/TestCases/04.ATMdispute/128-ATM Dispute, click View Details.json
2025-08-07 11:11:40.149 [Info] Attempting to load configuration file: WorkSet01/TestCases/04.ATMdispute/128-ATM Dispute, click View Details.json
2025-08-07 11:11:40.149 [Info] Successfully loaded configuration file: WorkSet01/TestCases/04.ATMdispute/128-ATM Dispute, click View Details.json
2025-08-07 11:11:40.149 [Info] No filtering configuration found. Including test case 'ATM Dispute, click View Details'.
2025-08-07 11:11:40.149 [Debug] Resolving parameters for test case: ATM Dispute, click View Details
2025-08-07 11:11:40.149 [Debug] Merging params
2025-08-07 11:11:40.149 [Debug] Merged basic params
2025-08-07 11:11:40.149 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-07 11:11:40.151 [Debug] Merged SpecialEnvParams params
2025-08-07 11:11:40.152 [Debug] Merged params
2025-08-07 11:11:40.152 [Debug] Loading parameters from reference file: WorkSet01/Params/ATMdisputeParams.json
2025-08-07 11:11:40.152 [Info] Attempting to load configuration file: WorkSet01/Params/ATMdisputeParams.json
2025-08-07 11:11:40.152 [Info] Successfully loaded configuration file: WorkSet01/Params/ATMdisputeParams.json
2025-08-07 11:11:40.152 [Debug] Merging params
2025-08-07 11:11:40.152 [Debug] Merged basic params
2025-08-07 11:11:40.152 [Debug] Merged params
2025-08-07 11:11:40.152 [Debug] Merging params
2025-08-07 11:11:40.152 [Debug] Merging params
2025-08-07 11:11:40.155 [Debug] Resolving placeholders in test steps for test case: ATM Dispute, click View Details
2025-08-07 11:11:40.155 [Debug] Resolving TestCaseReference in step: 
2025-08-07 11:11:40.155 [Info] Loading test case from: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:11:40.155 [Info] Attempting to load configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:11:40.156 [Info] Successfully loaded configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:11:40.156 [Debug] Resolving parameters for test case: Login
2025-08-07 11:11:40.156 [Debug] Merging params
2025-08-07 11:11:40.156 [Debug] Merged basic params
2025-08-07 11:11:40.156 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-07 11:11:40.156 [Debug] Merged SpecialEnvParams params
2025-08-07 11:11:40.158 [Debug] Merged params
2025-08-07 11:11:40.159 [Debug] Loading parameters from reference file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:11:40.159 [Info] Attempting to load configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:11:40.159 [Info] Successfully loaded configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:11:40.159 [Debug] Merging params
2025-08-07 11:11:40.159 [Debug] Merged basic params
2025-08-07 11:11:40.159 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-07 11:11:40.159 [Debug] Merged SpecialEnvParams params
2025-08-07 11:11:40.159 [Debug] Merged params
2025-08-07 11:11:40.159 [Debug] Merging params
2025-08-07 11:11:40.162 [Debug] Merging params
2025-08-07 11:11:40.162 [Debug] Resolving placeholders in test steps for test case: Login
2025-08-07 11:11:40.162 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-07 11:11:40.162 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-07 11:11:40.162 [Debug] Resolving placeholders in string: {{Selectors.FinishButton}}
2025-08-07 11:11:40.162 [Debug] Resolved string: id=finish
2025-08-07 11:11:40.162 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-07 11:11:40.162 [Debug] Resolved string: Click on Login Button
2025-08-07 11:11:40.162 [Debug] Resolving placeholders in string: {{Selectors.LoginButton}}
2025-08-07 11:11:40.162 [Debug] Resolved string: id=LoginBtn
2025-08-07 11:11:40.164 [Debug] Resolving placeholders in string: Type the User Name
2025-08-07 11:11:40.164 [Debug] Resolved string: Type the User Name
2025-08-07 11:11:40.164 [Debug] Resolving placeholders in string: {{Selectors.UserNameField}}
2025-08-07 11:11:40.164 [Debug] Resolved string: id=UserName
2025-08-07 11:11:40.164 [Debug] Resolving placeholders in string: {{TestData.UserName}}
2025-08-07 11:11:40.164 [Debug] Resolved string: Mm_azmy
2025-08-07 11:11:40.165 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-07 11:11:40.165 [Debug] Resolved string: Click on Continue Button
2025-08-07 11:11:40.165 [Debug] Resolving placeholders in string: {{Selectors.ContinueButton}}
2025-08-07 11:11:40.165 [Debug] Resolved string: id=btn
2025-08-07 11:11:40.167 [Debug] Resolving placeholders in string: Type the Password
2025-08-07 11:11:40.167 [Debug] Resolved string: Type the Password
2025-08-07 11:11:40.167 [Debug] Resolving placeholders in string: {{Selectors.PasswordField}}
2025-08-07 11:11:40.167 [Debug] Resolved string: id=Password
2025-08-07 11:11:40.167 [Debug] Resolving placeholders in string: $('[{{Selectors.PasswordField}}]').val('{{TestData.Password}}')
2025-08-07 11:11:40.167 [Debug] Resolved string: $('[id=Password]').val('Asdf2025')
2025-08-07 11:11:40.167 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-07 11:11:40.167 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-07 11:11:40.167 [Debug] Resolving placeholders in string: {{Selectors.ChallengeQuestionField}}
2025-08-07 11:11:40.167 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-07 11:11:40.170 [Debug] Resolving placeholders in string: {{TestData.ChallengeAnswer}}
2025-08-07 11:11:40.170 [Debug] Resolved string: armada
2025-08-07 11:11:40.170 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-07 11:11:40.170 [Debug] Resolved string: Click on the Login Button
2025-08-07 11:11:40.170 [Debug] Resolving placeholders in string: {{Selectors.LoginAuthButton}}
2025-08-07 11:11:40.170 [Debug] Resolved string: id=LoginAuthBtn
2025-08-07 11:11:40.170 [Info] Successfully resolved parameters for test case: Login
2025-08-07 11:11:40.170 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-07 11:11:40.170 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-07 11:11:40.171 [Debug] Resolving placeholders in string: id=finish
2025-08-07 11:11:40.173 [Debug] Resolved string: id=finish
2025-08-07 11:11:40.173 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-07 11:11:40.173 [Debug] Resolved string: Click on Login Button
2025-08-07 11:11:40.173 [Debug] Resolving placeholders in string: id=LoginBtn
2025-08-07 11:11:40.173 [Debug] Resolved string: id=LoginBtn
2025-08-07 11:11:40.173 [Debug] Resolving placeholders in string: Type the User Name
2025-08-07 11:11:40.174 [Debug] Resolved string: Type the User Name
2025-08-07 11:11:40.174 [Debug] Resolving placeholders in string: id=UserName
2025-08-07 11:11:40.174 [Debug] Resolved string: id=UserName
2025-08-07 11:11:40.174 [Debug] Resolving placeholders in string: Mm_azmy
2025-08-07 11:11:40.176 [Debug] Resolved string: Mm_azmy
2025-08-07 11:11:40.176 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-07 11:11:40.176 [Debug] Resolved string: Click on Continue Button
2025-08-07 11:11:40.176 [Debug] Resolving placeholders in string: id=btn
2025-08-07 11:11:40.176 [Debug] Resolved string: id=btn
2025-08-07 11:11:40.176 [Debug] Resolving placeholders in string: Type the Password
2025-08-07 11:11:40.176 [Debug] Resolved string: Type the Password
2025-08-07 11:11:40.176 [Debug] Resolving placeholders in string: id=Password
2025-08-07 11:11:40.176 [Debug] Resolved string: id=Password
2025-08-07 11:11:40.177 [Debug] Resolving placeholders in string: $('[id=Password]').val('Asdf2025')
2025-08-07 11:11:40.179 [Debug] Resolved string: $('[id=Password]').val('Asdf2025')
2025-08-07 11:11:40.179 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-07 11:11:40.179 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-07 11:11:40.179 [Debug] Resolving placeholders in string: id=ChallengeQuestionAnswer
2025-08-07 11:11:40.179 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-07 11:11:40.179 [Debug] Resolving placeholders in string: armada
2025-08-07 11:11:40.179 [Debug] Resolved string: armada
2025-08-07 11:11:40.179 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-07 11:11:40.179 [Debug] Resolved string: Click on the Login Button
2025-08-07 11:11:40.179 [Debug] Resolving placeholders in string: id=LoginAuthBtn
2025-08-07 11:11:40.181 [Debug] Resolved string: id=LoginAuthBtn
2025-08-07 11:11:40.182 [Debug] Resolving placeholders in string: Click on Cards option on the side menu
2025-08-07 11:11:40.182 [Debug] Resolved string: Click on Cards option on the side menu
2025-08-07 11:11:40.182 [Debug] Resolving placeholders in string: {{Selectors.CardsButton}}
2025-08-07 11:11:40.182 [Debug] Resolved string: xpath=//div[@class='mainmenuitems']//label[contains(text(), 'Cards')]
2025-08-07 11:11:40.182 [Debug] Resolving placeholders in string: Click on Card Services
2025-08-07 11:11:40.182 [Debug] Resolved string: Click on Card Services
2025-08-07 11:11:40.182 [Debug] Resolving placeholders in string: {{Selectors.CardServices}}
2025-08-07 11:11:40.182 [Debug] Resolved string: xpath=//div[@class='submenuitem']//label[contains(text(), 'Card services')]
2025-08-07 11:11:40.182 [Debug] Resolving placeholders in string: Click on ATM dispute
2025-08-07 11:11:40.184 [Debug] Resolved string: Click on ATM dispute
2025-08-07 11:11:40.184 [Debug] Resolving placeholders in string: {{Selectors.ATMdisputeButton}}
2025-08-07 11:11:40.184 [Debug] Resolved string: id=ATMDispute
2025-08-07 11:11:40.184 [Debug] Resolving placeholders in string: Click on dispute type
2025-08-07 11:11:40.185 [Debug] Resolved string: Click on dispute type
2025-08-07 11:11:40.185 [Debug] Resolving placeholders in string: {{Selectors.SelectDisputeType}}
2025-08-07 11:11:40.185 [Debug] Resolved string: xpath=//div[@class='details_container']//span[normalize-space(text())='Select...']
2025-08-07 11:11:40.185 [Debug] Resolving placeholders in string: Choose dispute type
2025-08-07 11:11:40.185 [Debug] Resolved string: Choose dispute type
2025-08-07 11:11:40.185 [Debug] Resolving placeholders in string: {{Selectors.DisputeType}}
2025-08-07 11:11:40.187 [Debug] Resolved string: xpath=//div[@class='details_container']//span[normalize-space(text())='Withdrawal did not dispense']
2025-08-07 11:11:40.187 [Debug] Resolving placeholders in string: Click on Select Cards button
2025-08-07 11:11:40.188 [Debug] Resolved string: Click on Select Cards button
2025-08-07 11:11:40.188 [Debug] Resolving placeholders in string: {{Selectors.SelectButton}}
2025-08-07 11:11:40.188 [Debug] Resolved string: xpath=//div[@class='details_container']//button[normalize-space(text())='Select']
2025-08-07 11:11:40.188 [Debug] Resolving placeholders in string: Choose a card with ATM transactions
2025-08-07 11:11:40.188 [Debug] Resolved string: Choose a card with ATM transactions
2025-08-07 11:11:40.188 [Debug] Resolving placeholders in string: {{Selectors.CardWithTransactions}}
2025-08-07 11:11:40.188 [Debug] Resolved string: xpath=//div[@class='module_list_container cardData']//h4[normalize-space(text())='4204XXXXXXXX2562']
2025-08-07 11:11:40.188 [Debug] Resolving placeholders in string: Click on select a transaction
2025-08-07 11:11:40.190 [Debug] Resolved string: Click on select a transaction
2025-08-07 11:11:40.191 [Debug] Resolving placeholders in string: {{Selectors.SelectTransactionButton}}
2025-08-07 11:11:40.191 [Debug] Resolved string: xpath=//div[@class='details_container']//button[normalize-space(text())='Select Transaction']
2025-08-07 11:11:40.191 [Debug] Resolving placeholders in string: Choose a transaction
2025-08-07 11:11:40.191 [Debug] Resolved string: Choose a transaction
2025-08-07 11:11:40.191 [Debug] Resolving placeholders in string: {{Selectors.Transaction}}
2025-08-07 11:11:40.191 [Debug] Resolved string: css=#Transaction > div:nth-child(1) > div.list_header
2025-08-07 11:11:40.191 [Debug] Resolving placeholders in string: Click on continue
2025-08-07 11:11:40.191 [Debug] Resolved string: Click on continue
2025-08-07 11:11:40.191 [Debug] Resolving placeholders in string: {{Selectors.ContinueButtonPilot1}}
2025-08-07 11:11:40.193 [Debug] Resolved string: xpath=//div[@class='details_container']//button[normalize-space(text())='Continue']
2025-08-07 11:11:40.193 [Debug] Resolving placeholders in string: Enter the disputed amount
2025-08-07 11:11:40.193 [Debug] Resolved string: Enter the disputed amount
2025-08-07 11:11:40.193 [Debug] Resolving placeholders in string: {{Selectors.DisputedAmmountField}}
2025-08-07 11:11:40.193 [Debug] Resolved string: xpath=//div[@class='details_container']//input[normalize-space(@placeholder)='Enter Disputed amount']
2025-08-07 11:11:40.193 [Debug] Resolving placeholders in string: 0
2025-08-07 11:11:40.194 [Debug] Resolved string: 0
2025-08-07 11:11:40.194 [Debug] Resolving placeholders in string: Enter the dispute note
2025-08-07 11:11:40.194 [Debug] Resolved string: Enter the dispute note
2025-08-07 11:11:40.194 [Debug] Resolving placeholders in string: {{Selectors.DisputeNoteField}}
2025-08-07 11:11:40.197 [Debug] Resolved string: xpath=//div[@class='details_container']//textarea[normalize-space(@placeholder)='']
2025-08-07 11:11:40.197 [Debug] Resolving placeholders in string: This is a test for the feature by the dev team. Please ignore this request.
2025-08-07 11:11:40.197 [Debug] Resolved string: This is a test for the feature by the dev team. Please ignore this request.
2025-08-07 11:11:40.197 [Debug] Resolving placeholders in string: Click on the agree terms checkbox
2025-08-07 11:11:40.198 [Debug] Resolved string: Click on the agree terms checkbox
2025-08-07 11:11:40.198 [Debug] Resolving placeholders in string: {{Selectors.TermsCheckbox}}
2025-08-07 11:11:40.198 [Debug] Resolved string: xpath=//div[@class='details_container']//input[@type='checkbox']
2025-08-07 11:11:40.198 [Debug] Resolving placeholders in string: Click on continue button
2025-08-07 11:11:40.198 [Debug] Resolved string: Click on continue button
2025-08-07 11:11:40.198 [Debug] Resolving placeholders in string: {{Selectors.ContinueButtonPilot2}}
2025-08-07 11:11:40.200 [Debug] Resolved string: xpath=//div[@class='details_container']//button[normalize-space(text())='Confirm']
2025-08-07 11:11:40.200 [Debug] Resolving placeholders in string: Click on confirm button
2025-08-07 11:11:40.200 [Debug] Resolved string: Click on confirm button
2025-08-07 11:11:40.200 [Debug] Resolving placeholders in string: {{Selectors.ConfirmDispute}}
2025-08-07 11:11:40.200 [Debug] Resolved string: id=DisputePreConfirmationBtn
2025-08-07 11:11:40.200 [Debug] Resolving placeholders in string: Click on Request Details
2025-08-07 11:11:40.200 [Debug] Resolved string: Click on Request Details
2025-08-07 11:11:40.200 [Debug] Resolving placeholders in string: {{Selectors.SubmitProdRequestDetailsBtn}}
2025-08-07 11:11:40.201 [Debug] Resolved string: id=SubmitProdRequestDetailsBtn
2025-08-07 11:11:40.201 [Info] Successfully resolved parameters for test case: ATM Dispute, click View Details
2025-08-07 11:11:40.203 [Debug] Loading test case: WorkSet01/TestCases/012.CardServices/DirectDebit/130-DirectDebitRequest.json
2025-08-07 11:11:40.203 [Info] Loading test case from: WorkSet01/TestCases/012.CardServices/DirectDebit/130-DirectDebitRequest.json
2025-08-07 11:11:40.204 [Info] Attempting to load configuration file: WorkSet01/TestCases/012.CardServices/DirectDebit/130-DirectDebitRequest.json
2025-08-07 11:11:40.204 [Info] Successfully loaded configuration file: WorkSet01/TestCases/012.CardServices/DirectDebit/130-DirectDebitRequest.json
2025-08-07 11:11:40.208 [Info] No filtering configuration found. Including test case 'Direct Debit, standard users'.
2025-08-07 11:11:40.208 [Debug] Resolving parameters for test case: Direct Debit, standard users
2025-08-07 11:11:40.208 [Debug] Merging params
2025-08-07 11:11:40.208 [Debug] Merged basic params
2025-08-07 11:11:40.208 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-07 11:11:40.208 [Debug] Merged SpecialEnvParams params
2025-08-07 11:11:40.210 [Debug] Merged params
2025-08-07 11:11:40.210 [Debug] Loading parameters from reference file: WorkSet01/Params/BlockCardParams.json
2025-08-07 11:11:40.210 [Info] Attempting to load configuration file: WorkSet01/Params/BlockCardParams.json
2025-08-07 11:11:40.211 [Info] Successfully loaded configuration file: WorkSet01/Params/BlockCardParams.json
2025-08-07 11:11:40.211 [Debug] Merging params
2025-08-07 11:11:40.211 [Debug] Merged basic params
2025-08-07 11:11:40.211 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-07 11:11:40.211 [Debug] Merged SpecialEnvParams params
2025-08-07 11:11:40.211 [Debug] Merged params
2025-08-07 11:11:40.213 [Debug] Merging params
2025-08-07 11:11:40.213 [Debug] Merging params
2025-08-07 11:11:40.213 [Debug] Resolving placeholders in test steps for test case: Direct Debit, standard users
2025-08-07 11:11:40.213 [Debug] Resolving TestCaseReference in step: 
2025-08-07 11:11:40.214 [Info] Loading test case from: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:11:40.214 [Info] Attempting to load configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:11:40.214 [Info] Successfully loaded configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:11:40.214 [Debug] Resolving parameters for test case: Login
2025-08-07 11:11:40.214 [Debug] Merging params
2025-08-07 11:11:40.216 [Debug] Merged basic params
2025-08-07 11:11:40.216 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-07 11:11:40.216 [Debug] Merged SpecialEnvParams params
2025-08-07 11:11:40.216 [Debug] Merged params
2025-08-07 11:11:40.217 [Debug] Loading parameters from reference file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:11:40.217 [Info] Attempting to load configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:11:40.217 [Info] Successfully loaded configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:11:40.217 [Debug] Merging params
2025-08-07 11:11:40.217 [Debug] Merged basic params
2025-08-07 11:11:40.220 [Debug] Merged params
2025-08-07 11:11:40.220 [Debug] Merging params
2025-08-07 11:11:40.220 [Debug] Merging params
2025-08-07 11:11:40.220 [Debug] Resolving placeholders in test steps for test case: Login
2025-08-07 11:11:40.220 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-07 11:11:40.220 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-07 11:11:40.220 [Debug] Resolving placeholders in string: {{Selectors.FinishButton}}
2025-08-07 11:11:40.220 [Debug] Resolved string: id=finish
2025-08-07 11:11:40.220 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-07 11:11:40.223 [Debug] Resolved string: Click on Login Button
2025-08-07 11:11:40.223 [Debug] Resolving placeholders in string: {{Selectors.LoginButton}}
2025-08-07 11:11:40.223 [Debug] Resolved string: id=LoginBtn
2025-08-07 11:11:40.223 [Debug] Resolving placeholders in string: Type the User Name
2025-08-07 11:11:40.223 [Debug] Resolved string: Type the User Name
2025-08-07 11:11:40.223 [Debug] Resolving placeholders in string: {{Selectors.UserNameField}}
2025-08-07 11:11:40.223 [Debug] Resolved string: id=UserName
2025-08-07 11:11:40.224 [Debug] Resolving placeholders in string: {{TestData.UserName}}
2025-08-07 11:11:40.224 [Debug] Resolved string: mahmoudsayed022
2025-08-07 11:11:40.226 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-07 11:11:40.226 [Debug] Resolved string: Click on Continue Button
2025-08-07 11:11:40.226 [Debug] Resolving placeholders in string: {{Selectors.ContinueButton}}
2025-08-07 11:11:40.226 [Debug] Resolved string: id=btn
2025-08-07 11:11:40.226 [Debug] Resolving placeholders in string: Type the Password
2025-08-07 11:11:40.226 [Debug] Resolved string: Type the Password
2025-08-07 11:11:40.226 [Debug] Resolving placeholders in string: {{Selectors.PasswordField}}
2025-08-07 11:11:40.226 [Debug] Resolved string: id=Password
2025-08-07 11:11:40.226 [Debug] Resolving placeholders in string: $('[{{Selectors.PasswordField}}]').val('{{TestData.Password}}')
2025-08-07 11:11:40.229 [Debug] Resolved string: $('[id=Password]').val('Password1')
2025-08-07 11:11:40.229 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-07 11:11:40.229 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-07 11:11:40.229 [Debug] Resolving placeholders in string: {{Selectors.ChallengeQuestionField}}
2025-08-07 11:11:40.229 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-07 11:11:40.229 [Debug] Resolving placeholders in string: {{TestData.ChallengeAnswer}}
2025-08-07 11:11:40.229 [Debug] Resolved string: bmw
2025-08-07 11:11:40.229 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-07 11:11:40.229 [Debug] Resolved string: Click on the Login Button
2025-08-07 11:11:40.231 [Debug] Resolving placeholders in string: {{Selectors.LoginAuthButton}}
2025-08-07 11:11:40.231 [Debug] Resolved string: id=LoginAuthBtn
2025-08-07 11:11:40.232 [Info] Successfully resolved parameters for test case: Login
2025-08-07 11:11:40.232 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-07 11:11:40.232 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-07 11:11:40.232 [Debug] Resolving placeholders in string: id=finish
2025-08-07 11:11:40.232 [Debug] Resolved string: id=finish
2025-08-07 11:11:40.232 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-07 11:11:40.232 [Debug] Resolved string: Click on Login Button
2025-08-07 11:11:40.234 [Debug] Resolving placeholders in string: id=LoginBtn
2025-08-07 11:11:40.234 [Debug] Resolved string: id=LoginBtn
2025-08-07 11:11:40.234 [Debug] Resolving placeholders in string: Type the User Name
2025-08-07 11:11:40.235 [Debug] Resolved string: Type the User Name
2025-08-07 11:11:40.235 [Debug] Resolving placeholders in string: id=UserName
2025-08-07 11:11:40.235 [Debug] Resolved string: id=UserName
2025-08-07 11:11:40.235 [Debug] Resolving placeholders in string: mahmoudsayed022
2025-08-07 11:11:40.235 [Debug] Resolved string: mahmoudsayed022
2025-08-07 11:11:40.235 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-07 11:11:40.238 [Debug] Resolved string: Click on Continue Button
2025-08-07 11:11:40.238 [Debug] Resolving placeholders in string: id=btn
2025-08-07 11:11:40.238 [Debug] Resolved string: id=btn
2025-08-07 11:11:40.238 [Debug] Resolving placeholders in string: Type the Password
2025-08-07 11:11:40.238 [Debug] Resolved string: Type the Password
2025-08-07 11:11:40.238 [Debug] Resolving placeholders in string: id=Password
2025-08-07 11:11:40.239 [Debug] Resolved string: id=Password
2025-08-07 11:11:40.239 [Debug] Resolving placeholders in string: $('[id=Password]').val('Password1')
2025-08-07 11:11:40.239 [Debug] Resolved string: $('[id=Password]').val('Password1')
2025-08-07 11:11:40.241 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-07 11:11:40.241 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-07 11:11:40.241 [Debug] Resolving placeholders in string: id=ChallengeQuestionAnswer
2025-08-07 11:11:40.241 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-07 11:11:40.241 [Debug] Resolving placeholders in string: bmw
2025-08-07 11:11:40.242 [Debug] Resolved string: bmw
2025-08-07 11:11:40.242 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-07 11:11:40.242 [Debug] Resolved string: Click on the Login Button
2025-08-07 11:11:40.242 [Debug] Resolving placeholders in string: id=LoginAuthBtn
2025-08-07 11:11:40.244 [Debug] Resolved string: id=LoginAuthBtn
2025-08-07 11:11:40.244 [Debug] Resolving placeholders in string: Click on change later button on the password expiration popup message
2025-08-07 11:11:40.244 [Debug] Resolved string: Click on change later button on the password expiration popup message
2025-08-07 11:11:40.244 [Debug] Resolving placeholders in string: {{Selectors.popupCancel}}
2025-08-07 11:11:40.244 [Debug] Resolved string: id=popup_cancel
2025-08-07 11:11:40.244 [Debug] Resolving placeholders in string: Click on biometric alert
2025-08-07 11:11:40.244 [Debug] Resolved string: Click on biometric alert
2025-08-07 11:11:40.244 [Debug] Resolving placeholders in string: {{Selectors.BioMetricAlert}}
2025-08-07 11:11:40.245 [Debug] Resolved string: css=#popbuttons > div > button.btn.Cancel.back_gradient2
2025-08-07 11:11:40.247 [Debug] Resolving placeholders in string: Execute JS code to activate token
2025-08-07 11:11:40.247 [Debug] Resolved string: Execute JS code to activate token
2025-08-07 11:11:40.247 [Debug] Resolving placeholders in string: Globals.ActivationState = 'ACTIVATED';
2025-08-07 11:11:40.247 [Debug] Resolved string: Globals.ActivationState = 'ACTIVATED';
2025-08-07 11:11:40.247 [Debug] Resolving placeholders in string: Click on Cards button from side menu
2025-08-07 11:11:40.247 [Debug] Resolved string: Click on Cards button from side menu
2025-08-07 11:11:40.247 [Debug] Resolving placeholders in string: {{Selectors.CardsButton}}
2025-08-07 11:11:40.247 [Debug] Resolved string: xpath=//div[@class='mainmenuitems']//label[contains(text(), 'Cards')]
2025-08-07 11:11:40.247 [Debug] Resolving placeholders in string: Click on Cards Services button from side menu
2025-08-07 11:11:40.249 [Debug] Resolved string: Click on Cards Services button from side menu
2025-08-07 11:11:40.250 [Debug] Resolving placeholders in string: {{Selectors.CardServices}}
2025-08-07 11:11:40.250 [Debug] Resolved string: xpath=//div[@class='submenuitem']//label[contains(text(), 'Card services')]
2025-08-07 11:11:40.250 [Debug] Resolving placeholders in string: Click on Direct Debit
2025-08-07 11:11:40.250 [Debug] Resolved string: Click on Direct Debit
2025-08-07 11:11:40.250 [Debug] Resolving placeholders in string: {{Selectors.DirectDebitBtn}}
2025-08-07 11:11:40.250 [Debug] Resolved string: id=DirectDebit
2025-08-07 11:11:40.250 [Debug] Resolving placeholders in string: select card 
2025-08-07 11:11:40.250 [Debug] Resolved string: select card 
2025-08-07 11:11:40.254 [Debug] Resolving placeholders in string: {{Selectors.CardSelectedinDirectDebit}}
2025-08-07 11:11:40.254 [Debug] Resolved string: xpath=//div[@class='module_list_container cardData']/*[1]
2025-08-07 11:11:40.254 [Debug] Resolving placeholders in string: click account button 
2025-08-07 11:11:40.255 [Debug] Resolved string: click account button 
2025-08-07 11:11:40.255 [Debug] Resolving placeholders in string: {{Selectors.SelectAccountBtn}}
2025-08-07 11:11:40.255 [Debug] Resolved string: id=SelectAccountBtn
2025-08-07 11:11:40.255 [Debug] Resolving placeholders in string: select account
2025-08-07 11:11:40.255 [Debug] Resolved string: select account
2025-08-07 11:11:40.256 [Debug] Resolving placeholders in string: {{Selectors.DebitAccount}}
2025-08-07 11:11:40.258 [Debug] Resolved string: xpath=//div[@class='module_list_container']//h4[contains(text(), '**************')]
2025-08-07 11:11:40.258 [Debug] Resolving placeholders in string: click Debited Percentage select
2025-08-07 11:11:40.258 [Debug] Resolved string: click Debited Percentage select
2025-08-07 11:11:40.258 [Debug] Resolving placeholders in string: {{Selectors.DebitedPercentageSelect}}
2025-08-07 11:11:40.258 [Debug] Resolved string: css=#CardsSelectionErrorHandle > div.select
2025-08-07 11:11:40.258 [Debug] Resolving placeholders in string: click Debited Percentage select option
2025-08-07 11:11:40.258 [Debug] Resolved string: click Debited Percentage select option
2025-08-07 11:11:40.258 [Debug] Resolving placeholders in string: {{Selectors.DebitedPercentageSelectOption}}
2025-08-07 11:11:40.258 [Debug] Resolved string: css=#CardsSelectionErrorHandle > div.options.back_white_solid.dropdown-active.d-block > span:nth-child(1)
2025-08-07 11:11:40.260 [Debug] Resolving placeholders in string: click ageree checkbox
2025-08-07 11:11:40.261 [Debug] Resolved string: click ageree checkbox
2025-08-07 11:11:40.261 [Debug] Resolving placeholders in string: {{Selectors.AgreeCheckBoxinDebit}}
2025-08-07 11:11:40.261 [Debug] Resolved string: css=#wginsDirectDebit_Default > div > div.section_body > div > div > div.terms.AgreeTermsAndConditions > input[type=checkbox]
2025-08-07 11:11:40.261 [Debug] Resolving placeholders in string: click on continue button
2025-08-07 11:11:40.261 [Debug] Resolved string: click on continue button
2025-08-07 11:11:40.261 [Debug] Resolving placeholders in string: {{Selectors.SubmitDirectDebitReq}}
2025-08-07 11:11:40.261 [Debug] Resolved string: id=SubmitDirectDebitReq
2025-08-07 11:11:40.261 [Debug] Resolving placeholders in string: click on confirm button
2025-08-07 11:11:40.263 [Debug] Resolved string: click on confirm button
2025-08-07 11:11:40.263 [Debug] Resolving placeholders in string: {{Selectors.DirectDebitConfirm}}
2025-08-07 11:11:40.264 [Debug] Resolved string: id=DirectDebitConfirm
2025-08-07 11:11:40.264 [Debug] Resolving placeholders in string: Enter Token number
2025-08-07 11:11:40.264 [Debug] Resolved string: Enter Token number
2025-08-07 11:11:40.264 [Debug] Resolving placeholders in string: {{Selectors.TokenInput}}
2025-08-07 11:11:40.264 [Debug] Resolved string: id=TokenNUMBER
2025-08-07 11:11:40.264 [Debug] Resolving placeholders in string: 123456
2025-08-07 11:11:40.264 [Debug] Resolved string: 123456
2025-08-07 11:11:40.266 [Debug] Resolving placeholders in string: Click on confirm and show the confirmation page
2025-08-07 11:11:40.266 [Debug] Resolved string: Click on confirm and show the confirmation page
2025-08-07 11:11:40.266 [Debug] Resolving placeholders in string: {{Selectors.btnTokenConfirm}}
2025-08-07 11:11:40.266 [Debug] Resolved string: id=btnTokenConfirm
2025-08-07 11:11:40.266 [Info] Successfully resolved parameters for test case: Direct Debit, standard users
2025-08-07 11:11:40.267 [Debug] Loading test case: WorkSet01/TestCases/012.CardServices/ActivateCard/121-Activate Card, check that inactive card in backend are displayed.json
2025-08-07 11:11:40.267 [Info] Loading test case from: WorkSet01/TestCases/012.CardServices/ActivateCard/121-Activate Card, check that inactive card in backend are displayed.json
2025-08-07 11:11:40.267 [Info] Attempting to load configuration file: WorkSet01/TestCases/012.CardServices/ActivateCard/121-Activate Card, check that inactive card in backend are displayed.json
2025-08-07 11:11:40.267 [Info] Successfully loaded configuration file: WorkSet01/TestCases/012.CardServices/ActivateCard/121-Activate Card, check that inactive card in backend are displayed.json
2025-08-07 11:11:40.270 [Info] No filtering configuration found. Including test case 'Activate Card, check that inactive card in backend are displayed'.
2025-08-07 11:11:40.270 [Debug] Resolving parameters for test case: Activate Card, check that inactive card in backend are displayed
2025-08-07 11:11:40.270 [Debug] Merging params
2025-08-07 11:11:40.270 [Debug] Merged basic params
2025-08-07 11:11:40.270 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-07 11:11:40.270 [Debug] Merged SpecialEnvParams params
2025-08-07 11:11:40.270 [Debug] Merged params
2025-08-07 11:11:40.270 [Debug] Loading parameters from reference file: WorkSet01/Params/ActivateCardParm.json
2025-08-07 11:11:40.271 [Info] Attempting to load configuration file: WorkSet01/Params/ActivateCardParm.json
2025-08-07 11:11:40.274 [Info] Successfully loaded configuration file: WorkSet01/Params/ActivateCardParm.json
2025-08-07 11:11:40.274 [Debug] Merging params
2025-08-07 11:11:40.274 [Debug] Merged basic params
2025-08-07 11:11:40.274 [Debug] Merged params
2025-08-07 11:11:40.274 [Debug] Merging params
2025-08-07 11:11:40.274 [Debug] Merging params
2025-08-07 11:11:40.274 [Debug] Resolving placeholders in test steps for test case: Activate Card, check that inactive card in backend are displayed
2025-08-07 11:11:40.274 [Debug] Resolving TestCaseReference in step: 
2025-08-07 11:11:40.275 [Info] Loading test case from: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:11:40.277 [Info] Attempting to load configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:11:40.277 [Info] Successfully loaded configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:11:40.278 [Debug] Resolving parameters for test case: Login
2025-08-07 11:11:40.278 [Debug] Merging params
2025-08-07 11:11:40.278 [Debug] Merged basic params
2025-08-07 11:11:40.278 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-07 11:11:40.278 [Debug] Merged SpecialEnvParams params
2025-08-07 11:11:40.278 [Debug] Merged params
2025-08-07 11:11:40.278 [Debug] Loading parameters from reference file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:11:40.280 [Info] Attempting to load configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:11:40.281 [Info] Successfully loaded configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:11:40.281 [Debug] Merging params
2025-08-07 11:11:40.281 [Debug] Merged basic params
2025-08-07 11:11:40.281 [Debug] Merged params
2025-08-07 11:11:40.281 [Debug] Merging params
2025-08-07 11:11:40.281 [Debug] Merging params
2025-08-07 11:11:40.282 [Debug] Resolving placeholders in test steps for test case: Login
2025-08-07 11:11:40.282 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-07 11:11:40.284 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-07 11:11:40.284 [Debug] Resolving placeholders in string: {{Selectors.FinishButton}}
2025-08-07 11:11:40.285 [Debug] Resolved string: id=finish
2025-08-07 11:11:40.285 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-07 11:11:40.285 [Debug] Resolved string: Click on Login Button
2025-08-07 11:11:40.285 [Debug] Resolving placeholders in string: {{Selectors.LoginButton}}
2025-08-07 11:11:40.285 [Debug] Resolved string: id=LoginBtn
2025-08-07 11:11:40.285 [Debug] Resolving placeholders in string: Type the User Name
2025-08-07 11:11:40.285 [Debug] Resolved string: Type the User Name
2025-08-07 11:11:40.288 [Debug] Resolving placeholders in string: {{Selectors.UserNameField}}
2025-08-07 11:11:40.288 [Debug] Resolved string: id=UserName
2025-08-07 11:11:40.288 [Debug] Resolving placeholders in string: {{TestData.UserName}}
2025-08-07 11:11:40.288 [Debug] Resolved string: mahmoudsayed022
2025-08-07 11:11:40.288 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-07 11:11:40.288 [Debug] Resolved string: Click on Continue Button
2025-08-07 11:11:40.288 [Debug] Resolving placeholders in string: {{Selectors.ContinueButton}}
2025-08-07 11:11:40.289 [Debug] Resolved string: id=btn
2025-08-07 11:11:40.289 [Debug] Resolving placeholders in string: Type the Password
2025-08-07 11:11:40.291 [Debug] Resolved string: Type the Password
2025-08-07 11:11:40.291 [Debug] Resolving placeholders in string: {{Selectors.PasswordField}}
2025-08-07 11:11:40.291 [Debug] Resolved string: id=Password
2025-08-07 11:11:40.291 [Debug] Resolving placeholders in string: $('[{{Selectors.PasswordField}}]').val('{{TestData.Password}}')
2025-08-07 11:11:40.292 [Debug] Resolved string: $('[id=Password]').val('Password1')
2025-08-07 11:11:40.292 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-07 11:11:40.292 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-07 11:11:40.292 [Debug] Resolving placeholders in string: {{Selectors.ChallengeQuestionField}}
2025-08-07 11:11:40.292 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-07 11:11:40.294 [Debug] Resolving placeholders in string: {{TestData.ChallengeAnswer}}
2025-08-07 11:11:40.294 [Debug] Resolved string: bmw
2025-08-07 11:11:40.294 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-07 11:11:40.294 [Debug] Resolved string: Click on the Login Button
2025-08-07 11:11:40.294 [Debug] Resolving placeholders in string: {{Selectors.LoginAuthButton}}
2025-08-07 11:11:40.294 [Debug] Resolved string: id=LoginAuthBtn
2025-08-07 11:11:40.294 [Info] Successfully resolved parameters for test case: Login
2025-08-07 11:11:40.295 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-07 11:11:40.295 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-07 11:11:40.297 [Debug] Resolving placeholders in string: id=finish
2025-08-07 11:11:40.297 [Debug] Resolved string: id=finish
2025-08-07 11:11:40.297 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-07 11:11:40.297 [Debug] Resolved string: Click on Login Button
2025-08-07 11:11:40.297 [Debug] Resolving placeholders in string: id=LoginBtn
2025-08-07 11:11:40.298 [Debug] Resolved string: id=LoginBtn
2025-08-07 11:11:40.298 [Debug] Resolving placeholders in string: Type the User Name
2025-08-07 11:11:40.298 [Debug] Resolved string: Type the User Name
2025-08-07 11:11:40.298 [Debug] Resolving placeholders in string: id=UserName
2025-08-07 11:11:40.300 [Debug] Resolved string: id=UserName
2025-08-07 11:11:40.300 [Debug] Resolving placeholders in string: mahmoudsayed022
2025-08-07 11:11:40.300 [Debug] Resolved string: mahmoudsayed022
2025-08-07 11:11:40.300 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-07 11:11:40.300 [Debug] Resolved string: Click on Continue Button
2025-08-07 11:11:40.300 [Debug] Resolving placeholders in string: id=btn
2025-08-07 11:11:40.300 [Debug] Resolved string: id=btn
2025-08-07 11:11:40.301 [Debug] Resolving placeholders in string: Type the Password
2025-08-07 11:11:40.301 [Debug] Resolved string: Type the Password
2025-08-07 11:11:40.303 [Debug] Resolving placeholders in string: id=Password
2025-08-07 11:11:40.303 [Debug] Resolved string: id=Password
2025-08-07 11:11:40.304 [Debug] Resolving placeholders in string: $('[id=Password]').val('Password1')
2025-08-07 11:11:40.304 [Debug] Resolved string: $('[id=Password]').val('Password1')
2025-08-07 11:11:40.304 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-07 11:11:40.304 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-07 11:11:40.304 [Debug] Resolving placeholders in string: id=ChallengeQuestionAnswer
2025-08-07 11:11:40.304 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-07 11:11:40.308 [Debug] Resolving placeholders in string: bmw
2025-08-07 11:11:40.309 [Debug] Resolved string: bmw
2025-08-07 11:11:40.309 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-07 11:11:40.309 [Debug] Resolved string: Click on the Login Button
2025-08-07 11:11:40.309 [Debug] Resolving placeholders in string: id=LoginAuthBtn
2025-08-07 11:11:40.309 [Debug] Resolved string: id=LoginAuthBtn
2025-08-07 11:11:40.309 [Debug] Resolving placeholders in string: Click on change later button on the password expiration popup message
2025-08-07 11:11:40.309 [Debug] Resolved string: Click on change later button on the password expiration popup message
2025-08-07 11:11:40.312 [Debug] Resolving placeholders in string: {{Selectors.popupCancel}}
2025-08-07 11:11:40.312 [Debug] Resolved string: id=popup_cancel
2025-08-07 11:11:40.312 [Debug] Resolving placeholders in string: Click on biometric alert
2025-08-07 11:11:40.312 [Debug] Resolved string: Click on biometric alert
2025-08-07 11:11:40.312 [Debug] Resolving placeholders in string: {{Selectors.BioMetricAlert}}
2025-08-07 11:11:40.312 [Debug] Resolved string: css=#popbuttons > div > button.btn.Cancel.back_gradient2
2025-08-07 11:11:40.312 [Debug] Resolving placeholders in string: Click on Cards button from side menu
2025-08-07 11:11:40.313 [Debug] Resolved string: Click on Cards button from side menu
2025-08-07 11:11:40.315 [Debug] Resolving placeholders in string: {{Selectors.CardsButton}}
2025-08-07 11:11:40.316 [Debug] Resolved string: xpath=//div[@class='mainmenuitems']//label[contains(text(), 'Cards')]
2025-08-07 11:11:40.316 [Debug] Resolving placeholders in string: Click on Cards Services button from side menu
2025-08-07 11:11:40.316 [Debug] Resolved string: Click on Cards Services button from side menu
2025-08-07 11:11:40.316 [Debug] Resolving placeholders in string: {{Selectors.CardServices}}
2025-08-07 11:11:40.316 [Debug] Resolved string: xpath=//div[@class='submenuitem']//label[contains(text(), 'Card services')]
2025-08-07 11:11:40.316 [Debug] Resolving placeholders in string: Click on Activate Card and show the cards list
2025-08-07 11:11:40.316 [Debug] Resolved string: Click on Activate Card and show the cards list
2025-08-07 11:11:40.318 [Debug] Resolving placeholders in string: {{Selectors.ActivateCard}}
2025-08-07 11:11:40.318 [Debug] Resolved string: id=ActivateCard
2025-08-07 11:11:40.319 [Debug] Resolving placeholders in string: Check if the cards list appear
2025-08-07 11:11:40.319 [Debug] Resolved string: Check if the cards list appear
2025-08-07 11:11:40.319 [Debug] Resolving placeholders in string: {{Selectors.CardList}}
2025-08-07 11:11:40.319 [Debug] Resolved string: css=#CardsSelectionErrorHandle
2025-08-07 11:11:40.319 [Info] Successfully resolved parameters for test case: Activate Card, check that inactive card in backend are displayed
2025-08-07 11:11:40.319 [Debug] Loading test case: WorkSet01/TestCases/012.CardServices/BlockCard/120-ClickBlockCardInquiry.json
2025-08-07 11:11:40.323 [Info] Loading test case from: WorkSet01/TestCases/012.CardServices/BlockCard/120-ClickBlockCardInquiry.json
2025-08-07 11:11:40.323 [Info] Attempting to load configuration file: WorkSet01/TestCases/012.CardServices/BlockCard/120-ClickBlockCardInquiry.json
2025-08-07 11:11:40.323 [Info] Successfully loaded configuration file: WorkSet01/TestCases/012.CardServices/BlockCard/120-ClickBlockCardInquiry.json
2025-08-07 11:11:40.323 [Info] No filtering configuration found. Including test case 'Click Block card'.
2025-08-07 11:11:40.324 [Debug] Resolving parameters for test case: Click Block card
2025-08-07 11:11:40.324 [Debug] Merging params
2025-08-07 11:11:40.324 [Debug] Merged basic params
2025-08-07 11:11:40.324 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-07 11:11:40.326 [Debug] Merged SpecialEnvParams params
2025-08-07 11:11:40.326 [Debug] Merged params
2025-08-07 11:11:40.326 [Debug] Loading parameters from reference file: WorkSet01/Params/BlockCardParams.json
2025-08-07 11:11:40.326 [Info] Attempting to load configuration file: WorkSet01/Params/BlockCardParams.json
2025-08-07 11:11:40.327 [Info] Successfully loaded configuration file: WorkSet01/Params/BlockCardParams.json
2025-08-07 11:11:40.327 [Debug] Merging params
2025-08-07 11:11:40.327 [Debug] Merged basic params
2025-08-07 11:11:40.327 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-07 11:11:40.330 [Debug] Merged SpecialEnvParams params
2025-08-07 11:11:40.330 [Debug] Merged params
2025-08-07 11:11:40.330 [Debug] Merging params
2025-08-07 11:11:40.330 [Debug] Merging params
2025-08-07 11:11:40.330 [Debug] Resolving placeholders in test steps for test case: Click Block card
2025-08-07 11:11:40.330 [Debug] Resolving TestCaseReference in step: 
2025-08-07 11:11:40.330 [Info] Loading test case from: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:11:40.330 [Info] Attempting to load configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:11:40.333 [Info] Successfully loaded configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:11:40.333 [Debug] Resolving parameters for test case: Login
2025-08-07 11:11:40.333 [Debug] Merging params
2025-08-07 11:11:40.333 [Debug] Merged basic params
2025-08-07 11:11:40.333 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-07 11:11:40.333 [Debug] Merged SpecialEnvParams params
2025-08-07 11:11:40.333 [Debug] Merged params
2025-08-07 11:11:40.333 [Debug] Loading parameters from reference file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:11:40.336 [Info] Attempting to load configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:11:40.337 [Info] Successfully loaded configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:11:40.337 [Debug] Merging params
2025-08-07 11:11:40.337 [Debug] Merged basic params
2025-08-07 11:11:40.337 [Debug] Merged params
2025-08-07 11:11:40.338 [Debug] Merging params
2025-08-07 11:11:40.338 [Debug] Merging params
2025-08-07 11:11:40.338 [Debug] Resolving placeholders in test steps for test case: Login
2025-08-07 11:11:40.341 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-07 11:11:40.341 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-07 11:11:40.341 [Debug] Resolving placeholders in string: {{Selectors.FinishButton}}
2025-08-07 11:11:40.341 [Debug] Resolved string: id=finish
2025-08-07 11:11:40.341 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-07 11:11:40.341 [Debug] Resolved string: Click on Login Button
2025-08-07 11:11:40.341 [Debug] Resolving placeholders in string: {{Selectors.LoginButton}}
2025-08-07 11:11:40.341 [Debug] Resolved string: id=LoginBtn
2025-08-07 11:11:40.344 [Debug] Resolving placeholders in string: Type the User Name
2025-08-07 11:11:40.344 [Debug] Resolved string: Type the User Name
2025-08-07 11:11:40.344 [Debug] Resolving placeholders in string: {{Selectors.UserNameField}}
2025-08-07 11:11:40.344 [Debug] Resolved string: id=UserName
2025-08-07 11:11:40.344 [Debug] Resolving placeholders in string: {{TestData.UserName}}
2025-08-07 11:11:40.344 [Debug] Resolved string: mahmoudsayed022
2025-08-07 11:11:40.344 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-07 11:11:40.345 [Debug] Resolved string: Click on Continue Button
2025-08-07 11:11:40.347 [Debug] Resolving placeholders in string: {{Selectors.ContinueButton}}
2025-08-07 11:11:40.347 [Debug] Resolved string: id=btn
2025-08-07 11:11:40.347 [Debug] Resolving placeholders in string: Type the Password
2025-08-07 11:11:40.347 [Debug] Resolved string: Type the Password
2025-08-07 11:11:40.347 [Debug] Resolving placeholders in string: {{Selectors.PasswordField}}
2025-08-07 11:11:40.347 [Debug] Resolved string: id=Password
2025-08-07 11:11:40.347 [Debug] Resolving placeholders in string: $('[{{Selectors.PasswordField}}]').val('{{TestData.Password}}')
2025-08-07 11:11:40.347 [Debug] Resolved string: $('[id=Password]').val('Password1')
2025-08-07 11:11:40.350 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-07 11:11:40.350 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-07 11:11:40.350 [Debug] Resolving placeholders in string: {{Selectors.ChallengeQuestionField}}
2025-08-07 11:11:40.350 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-07 11:11:40.350 [Debug] Resolving placeholders in string: {{TestData.ChallengeAnswer}}
2025-08-07 11:11:40.350 [Debug] Resolved string: bmw
2025-08-07 11:11:40.350 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-07 11:11:40.350 [Debug] Resolved string: Click on the Login Button
2025-08-07 11:11:40.353 [Debug] Resolving placeholders in string: {{Selectors.LoginAuthButton}}
2025-08-07 11:11:40.353 [Debug] Resolved string: id=LoginAuthBtn
2025-08-07 11:11:40.353 [Info] Successfully resolved parameters for test case: Login
2025-08-07 11:11:40.354 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-07 11:11:40.354 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-07 11:11:40.354 [Debug] Resolving placeholders in string: id=finish
2025-08-07 11:11:40.354 [Debug] Resolved string: id=finish
2025-08-07 11:11:40.354 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-07 11:11:40.357 [Debug] Resolved string: Click on Login Button
2025-08-07 11:11:40.357 [Debug] Resolving placeholders in string: id=LoginBtn
2025-08-07 11:11:40.357 [Debug] Resolved string: id=LoginBtn
2025-08-07 11:11:40.357 [Debug] Resolving placeholders in string: Type the User Name
2025-08-07 11:11:40.358 [Debug] Resolved string: Type the User Name
2025-08-07 11:11:40.358 [Debug] Resolving placeholders in string: id=UserName
2025-08-07 11:11:40.358 [Debug] Resolved string: id=UserName
2025-08-07 11:11:40.358 [Debug] Resolving placeholders in string: mahmoudsayed022
2025-08-07 11:11:40.361 [Debug] Resolved string: mahmoudsayed022
2025-08-07 11:11:40.361 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-07 11:11:40.361 [Debug] Resolved string: Click on Continue Button
2025-08-07 11:11:40.361 [Debug] Resolving placeholders in string: id=btn
2025-08-07 11:11:40.362 [Debug] Resolved string: id=btn
2025-08-07 11:11:40.362 [Debug] Resolving placeholders in string: Type the Password
2025-08-07 11:11:40.362 [Debug] Resolved string: Type the Password
2025-08-07 11:11:40.362 [Debug] Resolving placeholders in string: id=Password
2025-08-07 11:11:40.365 [Debug] Resolved string: id=Password
2025-08-07 11:11:40.365 [Debug] Resolving placeholders in string: $('[id=Password]').val('Password1')
2025-08-07 11:11:40.365 [Debug] Resolved string: $('[id=Password]').val('Password1')
2025-08-07 11:11:40.366 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-07 11:11:40.366 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-07 11:11:40.366 [Debug] Resolving placeholders in string: id=ChallengeQuestionAnswer
2025-08-07 11:11:40.366 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-07 11:11:40.366 [Debug] Resolving placeholders in string: bmw
2025-08-07 11:11:40.370 [Debug] Resolved string: bmw
2025-08-07 11:11:40.370 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-07 11:11:40.370 [Debug] Resolved string: Click on the Login Button
2025-08-07 11:11:40.370 [Debug] Resolving placeholders in string: id=LoginAuthBtn
2025-08-07 11:11:40.370 [Debug] Resolved string: id=LoginAuthBtn
2025-08-07 11:11:40.370 [Debug] Resolving placeholders in string: Click on change later button on the password expiration popup message
2025-08-07 11:11:40.371 [Debug] Resolved string: Click on change later button on the password expiration popup message
2025-08-07 11:11:40.371 [Debug] Resolving placeholders in string: {{Selectors.popupCancel}}
2025-08-07 11:11:40.374 [Debug] Resolved string: id=popup_cancel
2025-08-07 11:11:40.374 [Debug] Resolving placeholders in string: Click on biometric alert
2025-08-07 11:11:40.374 [Debug] Resolved string: Click on biometric alert
2025-08-07 11:11:40.375 [Debug] Resolving placeholders in string: {{Selectors.BioMetricAlert}}
2025-08-07 11:11:40.375 [Debug] Resolved string: css=#popbuttons > div > button.btn.Cancel.back_gradient2
2025-08-07 11:11:40.375 [Debug] Resolving placeholders in string: Click on biometric alert
2025-08-07 11:11:40.375 [Debug] Resolved string: Click on biometric alert
2025-08-07 11:11:40.375 [Debug] Resolving placeholders in string: {{Selectors.KYCskip}}
2025-08-07 11:11:40.378 [Debug] Resolved string: css=#popbuttons > div > button.btn.Cancel.back_gradient2
2025-08-07 11:11:40.378 [Debug] Resolving placeholders in string: Execute JS code to activate token
2025-08-07 11:11:40.378 [Debug] Resolved string: Execute JS code to activate token
2025-08-07 11:11:40.378 [Debug] Resolving placeholders in string: Globals.ActivationState = 'ACTIVATED';
2025-08-07 11:11:40.378 [Debug] Resolved string: Globals.ActivationState = 'ACTIVATED';
2025-08-07 11:11:40.378 [Debug] Resolving placeholders in string: Click on Cards button from side menu
2025-08-07 11:11:40.378 [Debug] Resolved string: Click on Cards button from side menu
2025-08-07 11:11:40.378 [Debug] Resolving placeholders in string: {{Selectors.CardsButton}}
2025-08-07 11:11:40.382 [Debug] Resolved string: xpath=//div[@class='mainmenuitems']//label[contains(text(), 'Cards')]
2025-08-07 11:11:40.382 [Debug] Resolving placeholders in string: Click on Card Inquiry and check cards details
2025-08-07 11:11:40.382 [Debug] Resolved string: Click on Card Inquiry and check cards details
2025-08-07 11:11:40.382 [Debug] Resolving placeholders in string: {{Selectors.CardInquiry}}
2025-08-07 11:11:40.382 [Debug] Resolved string: xpath=//div[@class='submenuitem']//label[contains(text(), 'Card Inquiry')]
2025-08-07 11:11:40.382 [Debug] Resolving placeholders in string: Click on a card to block it
2025-08-07 11:11:40.382 [Debug] Resolved string: Click on a card to block it
2025-08-07 11:11:40.382 [Debug] Resolving placeholders in string: {{Selectors.CardToBlockFromInquiry}}
2025-08-07 11:11:40.385 [Debug] Resolved string: xpath=//div[@id='CardsListWorking']//h3[contains(text(), '4023XXXXXXXX0007')]
2025-08-07 11:11:40.385 [Debug] Resolving placeholders in string: Click on Block Button
2025-08-07 11:11:40.385 [Debug] Resolved string: Click on Block Button
2025-08-07 11:11:40.385 [Debug] Resolving placeholders in string: {{Selectors.BlockCardOptionInquiry}}
2025-08-07 11:11:40.385 [Debug] Resolved string: id=Stopbtn
2025-08-07 11:11:40.385 [Debug] Resolving placeholders in string: Click on block card button
2025-08-07 11:11:40.385 [Debug] Resolved string: Click on block card button
2025-08-07 11:11:40.385 [Debug] Resolving placeholders in string: {{Selectors.BlockCardBtnSubmit}}
2025-08-07 11:11:40.389 [Debug] Resolved string: id=SubmitBlockCardReq
2025-08-07 11:11:40.389 [Debug] Resolving placeholders in string: Click on block card confirm
2025-08-07 11:11:40.389 [Debug] Resolved string: Click on block card confirm
2025-08-07 11:11:40.390 [Debug] Resolving placeholders in string: {{Selectors.BlockCardConfirm}}
2025-08-07 11:11:40.390 [Debug] Resolved string: id=BlockCardConfirm
2025-08-07 11:11:40.390 [Info] Successfully resolved parameters for test case: Click Block card
2025-08-07 11:11:40.390 [Debug] Loading test case: WorkSet01/TestCases/012.CardServices/BlockCard/125-CheckBlockedCardsList.json
2025-08-07 11:11:40.390 [Info] Loading test case from: WorkSet01/TestCases/012.CardServices/BlockCard/125-CheckBlockedCardsList.json
2025-08-07 11:11:40.393 [Info] Attempting to load configuration file: WorkSet01/TestCases/012.CardServices/BlockCard/125-CheckBlockedCardsList.json
2025-08-07 11:11:40.394 [Info] Successfully loaded configuration file: WorkSet01/TestCases/012.CardServices/BlockCard/125-CheckBlockedCardsList.json
2025-08-07 11:11:40.394 [Info] No filtering configuration found. Including test case 'Check Blocked Cards List'.
2025-08-07 11:11:40.394 [Debug] Resolving parameters for test case: Check Blocked Cards List
2025-08-07 11:11:40.394 [Debug] Merging params
2025-08-07 11:11:40.394 [Debug] Merged basic params
2025-08-07 11:11:40.394 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-07 11:11:40.394 [Debug] Merged SpecialEnvParams params
2025-08-07 11:11:40.397 [Debug] Merged params
2025-08-07 11:11:40.397 [Debug] Loading parameters from reference file: WorkSet01/Params/BlockCardParams.json
2025-08-07 11:11:40.397 [Info] Attempting to load configuration file: WorkSet01/Params/BlockCardParams.json
2025-08-07 11:11:40.398 [Info] Successfully loaded configuration file: WorkSet01/Params/BlockCardParams.json
2025-08-07 11:11:40.398 [Debug] Merging params
2025-08-07 11:11:40.398 [Debug] Merged basic params
2025-08-07 11:11:40.398 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-07 11:11:40.399 [Debug] Merged SpecialEnvParams params
2025-08-07 11:11:40.402 [Debug] Merged params
2025-08-07 11:11:40.402 [Debug] Merging params
2025-08-07 11:11:40.402 [Debug] Merging params
2025-08-07 11:11:40.402 [Debug] Resolving placeholders in test steps for test case: Check Blocked Cards List
2025-08-07 11:11:40.402 [Debug] Resolving TestCaseReference in step: 
2025-08-07 11:11:40.402 [Info] Loading test case from: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:11:40.402 [Info] Attempting to load configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:11:40.403 [Info] Successfully loaded configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:11:40.409 [Debug] Resolving parameters for test case: Login
2025-08-07 11:11:40.409 [Debug] Merging params
2025-08-07 11:11:40.409 [Debug] Merged basic params
2025-08-07 11:11:40.409 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-07 11:11:40.409 [Debug] Merged SpecialEnvParams params
2025-08-07 11:11:40.409 [Debug] Merged params
2025-08-07 11:11:40.410 [Debug] Loading parameters from reference file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:11:40.410 [Info] Attempting to load configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:11:40.413 [Info] Successfully loaded configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:11:40.413 [Debug] Merging params
2025-08-07 11:11:40.414 [Debug] Merged basic params
2025-08-07 11:11:40.414 [Debug] Merged params
2025-08-07 11:11:40.414 [Debug] Merging params
2025-08-07 11:11:40.414 [Debug] Merging params
2025-08-07 11:11:40.414 [Debug] Resolving placeholders in test steps for test case: Login
2025-08-07 11:11:40.414 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-07 11:11:40.417 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-07 11:11:40.417 [Debug] Resolving placeholders in string: {{Selectors.FinishButton}}
2025-08-07 11:11:40.417 [Debug] Resolved string: id=finish
2025-08-07 11:11:40.417 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-07 11:11:40.417 [Debug] Resolved string: Click on Login Button
2025-08-07 11:11:40.417 [Debug] Resolving placeholders in string: {{Selectors.LoginButton}}
2025-08-07 11:11:40.418 [Debug] Resolved string: id=LoginBtn
2025-08-07 11:11:40.418 [Debug] Resolving placeholders in string: Type the User Name
2025-08-07 11:11:40.421 [Debug] Resolved string: Type the User Name
2025-08-07 11:11:40.421 [Debug] Resolving placeholders in string: {{Selectors.UserNameField}}
2025-08-07 11:11:40.421 [Debug] Resolved string: id=UserName
2025-08-07 11:11:40.421 [Debug] Resolving placeholders in string: {{TestData.UserName}}
2025-08-07 11:11:40.422 [Debug] Resolved string: mahmoudsayed022
2025-08-07 11:11:40.422 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-07 11:11:40.422 [Debug] Resolved string: Click on Continue Button
2025-08-07 11:11:40.422 [Debug] Resolving placeholders in string: {{Selectors.ContinueButton}}
2025-08-07 11:11:40.425 [Debug] Resolved string: id=btn
2025-08-07 11:11:40.425 [Debug] Resolving placeholders in string: Type the Password
2025-08-07 11:11:40.425 [Debug] Resolved string: Type the Password
2025-08-07 11:11:40.425 [Debug] Resolving placeholders in string: {{Selectors.PasswordField}}
2025-08-07 11:11:40.425 [Debug] Resolved string: id=Password
2025-08-07 11:11:40.425 [Debug] Resolving placeholders in string: $('[{{Selectors.PasswordField}}]').val('{{TestData.Password}}')
2025-08-07 11:11:40.425 [Debug] Resolved string: $('[id=Password]').val('Password1')
2025-08-07 11:11:40.425 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-07 11:11:40.428 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-07 11:11:40.428 [Debug] Resolving placeholders in string: {{Selectors.ChallengeQuestionField}}
2025-08-07 11:11:40.428 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-07 11:11:40.428 [Debug] Resolving placeholders in string: {{TestData.ChallengeAnswer}}
2025-08-07 11:11:40.428 [Debug] Resolved string: bmw
2025-08-07 11:11:40.428 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-07 11:11:40.428 [Debug] Resolved string: Click on the Login Button
2025-08-07 11:11:40.428 [Debug] Resolving placeholders in string: {{Selectors.LoginAuthButton}}
2025-08-07 11:11:40.430 [Debug] Resolved string: id=LoginAuthBtn
2025-08-07 11:11:40.431 [Info] Successfully resolved parameters for test case: Login
2025-08-07 11:11:40.431 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-07 11:11:40.431 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-07 11:11:40.431 [Debug] Resolving placeholders in string: id=finish
2025-08-07 11:11:40.431 [Debug] Resolved string: id=finish
2025-08-07 11:11:40.431 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-07 11:11:40.431 [Debug] Resolved string: Click on Login Button
2025-08-07 11:11:40.434 [Debug] Resolving placeholders in string: id=LoginBtn
2025-08-07 11:11:40.434 [Debug] Resolved string: id=LoginBtn
2025-08-07 11:11:40.434 [Debug] Resolving placeholders in string: Type the User Name
2025-08-07 11:11:40.434 [Debug] Resolved string: Type the User Name
2025-08-07 11:11:40.434 [Debug] Resolving placeholders in string: id=UserName
2025-08-07 11:11:40.434 [Debug] Resolved string: id=UserName
2025-08-07 11:11:40.434 [Debug] Resolving placeholders in string: mahmoudsayed022
2025-08-07 11:11:40.434 [Debug] Resolved string: mahmoudsayed022
2025-08-07 11:11:40.437 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-07 11:11:40.437 [Debug] Resolved string: Click on Continue Button
2025-08-07 11:11:40.437 [Debug] Resolving placeholders in string: id=btn
2025-08-07 11:11:40.437 [Debug] Resolved string: id=btn
2025-08-07 11:11:40.437 [Debug] Resolving placeholders in string: Type the Password
2025-08-07 11:11:40.438 [Debug] Resolved string: Type the Password
2025-08-07 11:11:40.438 [Debug] Resolving placeholders in string: id=Password
2025-08-07 11:11:40.438 [Debug] Resolved string: id=Password
2025-08-07 11:11:40.442 [Debug] Resolving placeholders in string: $('[id=Password]').val('Password1')
2025-08-07 11:11:40.442 [Debug] Resolved string: $('[id=Password]').val('Password1')
2025-08-07 11:11:40.442 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-07 11:11:40.442 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-07 11:11:40.442 [Debug] Resolving placeholders in string: id=ChallengeQuestionAnswer
2025-08-07 11:11:40.442 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-07 11:11:40.442 [Debug] Resolving placeholders in string: bmw
2025-08-07 11:11:40.442 [Debug] Resolved string: bmw
2025-08-07 11:11:40.444 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-07 11:11:40.445 [Debug] Resolved string: Click on the Login Button
2025-08-07 11:11:40.445 [Debug] Resolving placeholders in string: id=LoginAuthBtn
2025-08-07 11:11:40.445 [Debug] Resolved string: id=LoginAuthBtn
2025-08-07 11:11:40.445 [Debug] Resolving placeholders in string: Click on change later button on the password expiration popup message
2025-08-07 11:11:40.445 [Debug] Resolved string: Click on change later button on the password expiration popup message
2025-08-07 11:11:40.445 [Debug] Resolving placeholders in string: {{Selectors.popupCancel}}
2025-08-07 11:11:40.445 [Debug] Resolved string: id=popup_cancel
2025-08-07 11:11:40.448 [Debug] Resolving placeholders in string: Click on biometric alert
2025-08-07 11:11:40.448 [Debug] Resolved string: Click on biometric alert
2025-08-07 11:11:40.448 [Debug] Resolving placeholders in string: {{Selectors.BioMetricAlert}}
2025-08-07 11:11:40.448 [Debug] Resolved string: css=#popbuttons > div > button.btn.Cancel.back_gradient2
2025-08-07 11:11:40.448 [Debug] Resolving placeholders in string: Execute JS code to activate token
2025-08-07 11:11:40.448 [Debug] Resolved string: Execute JS code to activate token
2025-08-07 11:11:40.448 [Debug] Resolving placeholders in string: Globals.ActivationState = 'ACTIVATED';
2025-08-07 11:11:40.449 [Debug] Resolved string: Globals.ActivationState = 'ACTIVATED';
2025-08-07 11:11:40.452 [Debug] Resolving placeholders in string: Click on Cards option on the side menu
2025-08-07 11:11:40.452 [Debug] Resolved string: Click on Cards option on the side menu
2025-08-07 11:11:40.452 [Debug] Resolving placeholders in string: {{Selectors.CardsButton}}
2025-08-07 11:11:40.452 [Debug] Resolved string: xpath=//div[@class='mainmenuitems']//label[contains(text(), 'Cards')]
2025-08-07 11:11:40.452 [Debug] Resolving placeholders in string: Click on Card Services
2025-08-07 11:11:40.452 [Debug] Resolved string: Click on Card Services
2025-08-07 11:11:40.453 [Debug] Resolving placeholders in string: {{Selectors.CardServices}}
2025-08-07 11:11:40.455 [Debug] Resolved string: xpath=//div[@class='submenuitem']//label[contains(text(), 'Card services')]
2025-08-07 11:11:40.459 [Debug] Resolving placeholders in string: Click on UnBlock Btn
2025-08-07 11:11:40.460 [Debug] Resolved string: Click on UnBlock Btn
2025-08-07 11:11:40.460 [Debug] Resolving placeholders in string: {{Selectors.UnblockCard}}
2025-08-07 11:11:40.460 [Debug] Resolved string: id=UnblockCard
2025-08-07 11:11:40.460 [Debug] Resolving placeholders in string: Click on Select Card Button and show the blocked cards list
2025-08-07 11:11:40.460 [Debug] Resolved string: Click on Select Card Button and show the blocked cards list
2025-08-07 11:11:40.460 [Debug] Resolving placeholders in string: {{Selectors.SelectCardButton}}
2025-08-07 11:11:40.460 [Debug] Resolved string: id=SelectCardBtn
2025-08-07 11:11:40.462 [Debug] Resolving placeholders in string: Check if the blocked cards appear in the card list.
2025-08-07 11:11:40.463 [Debug] Resolved string: Check if the blocked cards appear in the card list.
2025-08-07 11:11:40.463 [Debug] Resolving placeholders in string: css=#CardsSelectionErrorHandle > div:nth-child(1)
2025-08-07 11:11:40.463 [Debug] Resolved string: css=#CardsSelectionErrorHandle > div:nth-child(1)
2025-08-07 11:11:40.463 [Info] Successfully resolved parameters for test case: Check Blocked Cards List
2025-08-07 11:11:40.463 [Debug] Loading test case: WorkSet01/TestCases/012.CardServices/BlockCard/114-CardInquiry2Widgets.json
2025-08-07 11:11:40.463 [Info] Loading test case from: WorkSet01/TestCases/012.CardServices/BlockCard/114-CardInquiry2Widgets.json
2025-08-07 11:11:40.465 [Info] Attempting to load configuration file: WorkSet01/TestCases/012.CardServices/BlockCard/114-CardInquiry2Widgets.json
2025-08-07 11:11:40.466 [Info] Successfully loaded configuration file: WorkSet01/TestCases/012.CardServices/BlockCard/114-CardInquiry2Widgets.json
2025-08-07 11:11:40.466 [Info] No filtering configuration found. Including test case 'Credit cards and prepaid cards displayed on 2 widgets'.
2025-08-07 11:11:40.466 [Debug] Resolving parameters for test case: Credit cards and prepaid cards displayed on 2 widgets
2025-08-07 11:11:40.466 [Debug] Merging params
2025-08-07 11:11:40.466 [Debug] Merged basic params
2025-08-07 11:11:40.466 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-07 11:11:40.469 [Debug] Merged SpecialEnvParams params
2025-08-07 11:11:40.469 [Debug] Merged params
2025-08-07 11:11:40.469 [Debug] Loading parameters from reference file: WorkSet01/Params/CardServicesParams.json
2025-08-07 11:11:40.469 [Info] Attempting to load configuration file: WorkSet01/Params/CardServicesParams.json
2025-08-07 11:11:40.470 [Info] Successfully loaded configuration file: WorkSet01/Params/CardServicesParams.json
2025-08-07 11:11:40.470 [Debug] Merging params
2025-08-07 11:11:40.471 [Debug] Merged basic params
2025-08-07 11:11:40.474 [Debug] Merged params
2025-08-07 11:11:40.474 [Debug] Merging params
2025-08-07 11:11:40.474 [Debug] Merging params
2025-08-07 11:11:40.474 [Debug] Resolving placeholders in test steps for test case: Credit cards and prepaid cards displayed on 2 widgets
2025-08-07 11:11:40.474 [Debug] Resolving TestCaseReference in step: 
2025-08-07 11:11:40.474 [Info] Loading test case from: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:11:40.474 [Info] Attempting to load configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:11:40.477 [Info] Successfully loaded configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:11:40.477 [Debug] Resolving parameters for test case: Login
2025-08-07 11:11:40.477 [Debug] Merging params
2025-08-07 11:11:40.477 [Debug] Merged basic params
2025-08-07 11:11:40.477 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-07 11:11:40.478 [Debug] Merged SpecialEnvParams params
2025-08-07 11:11:40.478 [Debug] Merged params
2025-08-07 11:11:40.480 [Debug] Loading parameters from reference file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:11:40.480 [Info] Attempting to load configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:11:40.481 [Info] Successfully loaded configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:11:40.481 [Debug] Merging params
2025-08-07 11:11:40.481 [Debug] Merged basic params
2025-08-07 11:11:40.481 [Debug] Merged params
2025-08-07 11:11:40.481 [Debug] Merging params
2025-08-07 11:11:40.483 [Debug] Merging params
2025-08-07 11:11:40.483 [Debug] Resolving placeholders in test steps for test case: Login
2025-08-07 11:11:40.483 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-07 11:11:40.484 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-07 11:11:40.484 [Debug] Resolving placeholders in string: {{Selectors.FinishButton}}
2025-08-07 11:11:40.484 [Debug] Resolved string: id=finish
2025-08-07 11:11:40.484 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-07 11:11:40.487 [Debug] Resolved string: Click on Login Button
2025-08-07 11:11:40.487 [Debug] Resolving placeholders in string: {{Selectors.LoginButton}}
2025-08-07 11:11:40.487 [Debug] Resolved string: id=LoginBtn
2025-08-07 11:11:40.487 [Debug] Resolving placeholders in string: Type the User Name
2025-08-07 11:11:40.488 [Debug] Resolved string: Type the User Name
2025-08-07 11:11:40.488 [Debug] Resolving placeholders in string: {{Selectors.UserNameField}}
2025-08-07 11:11:40.488 [Debug] Resolved string: id=UserName
2025-08-07 11:11:40.491 [Debug] Resolving placeholders in string: {{TestData.UserName}}
2025-08-07 11:11:40.491 [Debug] Resolved string: mahmoudsayed022
2025-08-07 11:11:40.491 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-07 11:11:40.491 [Debug] Resolved string: Click on Continue Button
2025-08-07 11:11:40.491 [Debug] Resolving placeholders in string: {{Selectors.ContinueButton}}
2025-08-07 11:11:40.492 [Debug] Resolved string: id=btn
2025-08-07 11:11:40.492 [Debug] Resolving placeholders in string: Type the Password
2025-08-07 11:11:40.494 [Debug] Resolved string: Type the Password
2025-08-07 11:11:40.494 [Debug] Resolving placeholders in string: {{Selectors.PasswordField}}
2025-08-07 11:11:40.494 [Debug] Resolved string: id=Password
2025-08-07 11:11:40.494 [Debug] Resolving placeholders in string: $('[{{Selectors.PasswordField}}]').val('{{TestData.Password}}')
2025-08-07 11:11:40.494 [Debug] Resolved string: $('[id=Password]').val('Password1')
2025-08-07 11:11:40.494 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-07 11:11:40.494 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-07 11:11:40.498 [Debug] Resolving placeholders in string: {{Selectors.ChallengeQuestionField}}
2025-08-07 11:11:40.498 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-07 11:11:40.498 [Debug] Resolving placeholders in string: {{TestData.ChallengeAnswer}}
2025-08-07 11:11:40.498 [Debug] Resolved string: bmw
2025-08-07 11:11:40.498 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-07 11:11:40.499 [Debug] Resolved string: Click on the Login Button
2025-08-07 11:11:40.499 [Debug] Resolving placeholders in string: {{Selectors.LoginAuthButton}}
2025-08-07 11:11:40.501 [Debug] Resolved string: id=LoginAuthBtn
2025-08-07 11:11:40.501 [Info] Successfully resolved parameters for test case: Login
2025-08-07 11:11:40.501 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-07 11:11:40.501 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-07 11:11:40.501 [Debug] Resolving placeholders in string: id=finish
2025-08-07 11:11:40.502 [Debug] Resolved string: id=finish
2025-08-07 11:11:40.502 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-07 11:11:40.506 [Debug] Resolved string: Click on Login Button
2025-08-07 11:11:40.506 [Debug] Resolving placeholders in string: id=LoginBtn
2025-08-07 11:11:40.506 [Debug] Resolved string: id=LoginBtn
2025-08-07 11:11:40.506 [Debug] Resolving placeholders in string: Type the User Name
2025-08-07 11:11:40.506 [Debug] Resolved string: Type the User Name
2025-08-07 11:11:40.506 [Debug] Resolving placeholders in string: id=UserName
2025-08-07 11:11:40.506 [Debug] Resolved string: id=UserName
2025-08-07 11:11:40.509 [Debug] Resolving placeholders in string: mahmoudsayed022
2025-08-07 11:11:40.509 [Debug] Resolved string: mahmoudsayed022
2025-08-07 11:11:40.509 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-07 11:11:40.509 [Debug] Resolved string: Click on Continue Button
2025-08-07 11:11:40.509 [Debug] Resolving placeholders in string: id=btn
2025-08-07 11:11:40.509 [Debug] Resolved string: id=btn
2025-08-07 11:11:40.509 [Debug] Resolving placeholders in string: Type the Password
2025-08-07 11:11:40.513 [Debug] Resolved string: Type the Password
2025-08-07 11:11:40.513 [Debug] Resolving placeholders in string: id=Password
2025-08-07 11:11:40.513 [Debug] Resolved string: id=Password
2025-08-07 11:11:40.513 [Debug] Resolving placeholders in string: $('[id=Password]').val('Password1')
2025-08-07 11:11:40.513 [Debug] Resolved string: $('[id=Password]').val('Password1')
2025-08-07 11:11:40.513 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-07 11:11:40.514 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-07 11:11:40.516 [Debug] Resolving placeholders in string: id=ChallengeQuestionAnswer
2025-08-07 11:11:40.517 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-07 11:11:40.517 [Debug] Resolving placeholders in string: bmw
2025-08-07 11:11:40.517 [Debug] Resolved string: bmw
2025-08-07 11:11:40.517 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-07 11:11:40.517 [Debug] Resolved string: Click on the Login Button
2025-08-07 11:11:40.517 [Debug] Resolving placeholders in string: id=LoginAuthBtn
2025-08-07 11:11:40.522 [Debug] Resolved string: id=LoginAuthBtn
2025-08-07 11:11:40.522 [Debug] Resolving placeholders in string: Click on change later button on the password expiration popup message
2025-08-07 11:11:40.522 [Debug] Resolved string: Click on change later button on the password expiration popup message
2025-08-07 11:11:40.522 [Debug] Resolving placeholders in string: {{Selectors.popupCancel}}
2025-08-07 11:11:40.523 [Debug] Resolved string: id=popup_cancel
2025-08-07 11:11:40.523 [Debug] Resolving placeholders in string: Execute JS code to activate token
2025-08-07 11:11:40.523 [Debug] Resolved string: Execute JS code to activate token
2025-08-07 11:11:40.526 [Debug] Resolving placeholders in string: Globals.ActivationState = 'ACTIVATED';
2025-08-07 11:11:40.526 [Debug] Resolved string: Globals.ActivationState = 'ACTIVATED';
2025-08-07 11:11:40.526 [Debug] Resolving placeholders in string: Click on Cards option on the side menu
2025-08-07 11:11:40.526 [Debug] Resolved string: Click on Cards option on the side menu
2025-08-07 11:11:40.526 [Debug] Resolving placeholders in string: {{Selectors.CardsButton}}
2025-08-07 11:11:40.526 [Debug] Resolved string: xpath=//div[@class='mainmenuitems']//label[contains(text(), 'Cards')]
2025-08-07 11:11:40.526 [Debug] Resolving placeholders in string: Click on Card Inquiry
2025-08-07 11:11:40.529 [Debug] Resolved string: Click on Card Inquiry
2025-08-07 11:11:40.529 [Debug] Resolving placeholders in string: {{Selectors.CardInquiry}}
2025-08-07 11:11:40.529 [Debug] Resolved string: xpath=//div[@class='submenuitem']//label[contains(text(), 'Card Inquiry')]
2025-08-07 11:11:40.530 [Debug] Resolving placeholders in string: check Credit cards and prepaid cards displayed on 2 widgets
2025-08-07 11:11:40.530 [Debug] Resolved string: check Credit cards and prepaid cards displayed on 2 widgets
2025-08-07 11:11:40.530 [Debug] Resolving placeholders in string: id=CreditCard
2025-08-07 11:11:40.530 [Debug] Resolved string: id=CreditCard
2025-08-07 11:11:40.534 [Info] Successfully resolved parameters for test case: Credit cards and prepaid cards displayed on 2 widgets
2025-08-07 11:11:40.534 [Debug] Loading test case: WorkSet01/TestCases/012.CardServices/BlockCard/113-CardInquiry.json
2025-08-07 11:11:40.534 [Info] Loading test case from: WorkSet01/TestCases/012.CardServices/BlockCard/113-CardInquiry.json
2025-08-07 11:11:40.534 [Info] Attempting to load configuration file: WorkSet01/TestCases/012.CardServices/BlockCard/113-CardInquiry.json
2025-08-07 11:11:40.534 [Info] Successfully loaded configuration file: WorkSet01/TestCases/012.CardServices/BlockCard/113-CardInquiry.json
2025-08-07 11:11:40.535 [Info] No filtering configuration found. Including test case 'All users Cards are displayed successfully'.
2025-08-07 11:11:40.540 [Debug] Resolving parameters for test case: All users Cards are displayed successfully
2025-08-07 11:11:40.548 [Debug] Merging params
2025-08-07 11:11:40.549 [Debug] Merged basic params
2025-08-07 11:11:40.549 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-07 11:11:40.549 [Debug] Merged SpecialEnvParams params
2025-08-07 11:11:40.549 [Debug] Merged params
2025-08-07 11:11:40.549 [Debug] Loading parameters from reference file: WorkSet01/Params/CardServicesParams.json
2025-08-07 11:11:40.550 [Info] Attempting to load configuration file: WorkSet01/Params/CardServicesParams.json
2025-08-07 11:11:40.556 [Info] Successfully loaded configuration file: WorkSet01/Params/CardServicesParams.json
2025-08-07 11:11:40.556 [Debug] Merging params
2025-08-07 11:11:40.556 [Debug] Merged basic params
2025-08-07 11:11:40.557 [Debug] Merged params
2025-08-07 11:11:40.557 [Debug] Merging params
2025-08-07 11:11:40.557 [Debug] Merging params
2025-08-07 11:11:40.557 [Debug] Resolving placeholders in test steps for test case: All users Cards are displayed successfully
2025-08-07 11:11:40.563 [Debug] Resolving TestCaseReference in step: 
2025-08-07 11:11:40.563 [Info] Loading test case from: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:11:40.563 [Info] Attempting to load configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:11:40.563 [Info] Successfully loaded configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:11:40.563 [Debug] Resolving parameters for test case: Login
2025-08-07 11:11:40.564 [Debug] Merging params
2025-08-07 11:11:40.564 [Debug] Merged basic params
2025-08-07 11:11:40.566 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-07 11:11:40.566 [Debug] Merged SpecialEnvParams params
2025-08-07 11:11:40.566 [Debug] Merged params
2025-08-07 11:11:40.567 [Debug] Loading parameters from reference file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:11:40.567 [Info] Attempting to load configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:11:40.568 [Info] Successfully loaded configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:11:40.568 [Debug] Merging params
2025-08-07 11:11:40.574 [Debug] Merged basic params
2025-08-07 11:11:40.574 [Debug] Merged params
2025-08-07 11:11:40.574 [Debug] Merging params
2025-08-07 11:11:40.574 [Debug] Merging params
2025-08-07 11:11:40.574 [Debug] Resolving placeholders in test steps for test case: Login
2025-08-07 11:11:40.574 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-07 11:11:40.575 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-07 11:11:40.578 [Debug] Resolving placeholders in string: {{Selectors.FinishButton}}
2025-08-07 11:11:40.578 [Debug] Resolved string: id=finish
2025-08-07 11:11:40.578 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-07 11:11:40.578 [Debug] Resolved string: Click on Login Button
2025-08-07 11:11:40.578 [Debug] Resolving placeholders in string: {{Selectors.LoginButton}}
2025-08-07 11:11:40.578 [Debug] Resolved string: id=LoginBtn
2025-08-07 11:11:40.579 [Debug] Resolving placeholders in string: Type the User Name
2025-08-07 11:11:40.582 [Debug] Resolved string: Type the User Name
2025-08-07 11:11:40.582 [Debug] Resolving placeholders in string: {{Selectors.UserNameField}}
2025-08-07 11:11:40.582 [Debug] Resolved string: id=UserName
2025-08-07 11:11:40.582 [Debug] Resolving placeholders in string: {{TestData.UserName}}
2025-08-07 11:11:40.582 [Debug] Resolved string: mahmoudsayed022
2025-08-07 11:11:40.582 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-07 11:11:40.585 [Debug] Resolved string: Click on Continue Button
2025-08-07 11:11:40.594 [Debug] Resolving placeholders in string: {{Selectors.ContinueButton}}
2025-08-07 11:11:40.594 [Debug] Resolved string: id=btn
2025-08-07 11:11:40.595 [Debug] Resolving placeholders in string: Type the Password
2025-08-07 11:11:40.595 [Debug] Resolved string: Type the Password
2025-08-07 11:11:40.595 [Debug] Resolving placeholders in string: {{Selectors.PasswordField}}
2025-08-07 11:11:40.595 [Debug] Resolved string: id=Password
2025-08-07 11:11:40.595 [Debug] Resolving placeholders in string: $('[{{Selectors.PasswordField}}]').val('{{TestData.Password}}')
2025-08-07 11:11:40.598 [Debug] Resolved string: $('[id=Password]').val('Password1')
2025-08-07 11:11:40.598 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-07 11:11:40.599 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-07 11:11:40.599 [Debug] Resolving placeholders in string: {{Selectors.ChallengeQuestionField}}
2025-08-07 11:11:40.599 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-07 11:11:40.599 [Debug] Resolving placeholders in string: {{TestData.ChallengeAnswer}}
2025-08-07 11:11:40.600 [Debug] Resolved string: bmw
2025-08-07 11:11:40.602 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-07 11:11:40.605 [Debug] Resolved string: Click on the Login Button
2025-08-07 11:11:40.605 [Debug] Resolving placeholders in string: {{Selectors.LoginAuthButton}}
2025-08-07 11:11:40.605 [Debug] Resolved string: id=LoginAuthBtn
2025-08-07 11:11:40.605 [Info] Successfully resolved parameters for test case: Login
2025-08-07 11:11:40.609 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-07 11:11:40.609 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-07 11:11:40.613 [Debug] Resolving placeholders in string: id=finish
2025-08-07 11:11:40.613 [Debug] Resolved string: id=finish
2025-08-07 11:11:40.613 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-07 11:11:40.613 [Debug] Resolved string: Click on Login Button
2025-08-07 11:11:40.613 [Debug] Resolving placeholders in string: id=LoginBtn
2025-08-07 11:11:40.614 [Debug] Resolved string: id=LoginBtn
2025-08-07 11:11:40.615 [Debug] Resolving placeholders in string: Type the User Name
2025-08-07 11:11:40.618 [Debug] Resolved string: Type the User Name
2025-08-07 11:11:40.618 [Debug] Resolving placeholders in string: id=UserName
2025-08-07 11:11:40.619 [Debug] Resolved string: id=UserName
2025-08-07 11:11:40.619 [Debug] Resolving placeholders in string: mahmoudsayed022
2025-08-07 11:11:40.619 [Debug] Resolved string: mahmoudsayed022
2025-08-07 11:11:40.619 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-07 11:11:40.619 [Debug] Resolved string: Click on Continue Button
2025-08-07 11:11:40.629 [Debug] Resolving placeholders in string: id=btn
2025-08-07 11:11:40.629 [Debug] Resolved string: id=btn
2025-08-07 11:11:40.629 [Debug] Resolving placeholders in string: Type the Password
2025-08-07 11:11:40.629 [Debug] Resolved string: Type the Password
2025-08-07 11:11:40.629 [Debug] Resolving placeholders in string: id=Password
2025-08-07 11:11:40.629 [Debug] Resolved string: id=Password
2025-08-07 11:11:40.630 [Debug] Resolving placeholders in string: $('[id=Password]').val('Password1')
2025-08-07 11:11:40.632 [Debug] Resolved string: $('[id=Password]').val('Password1')
2025-08-07 11:11:40.632 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-07 11:11:40.633 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-07 11:11:40.633 [Debug] Resolving placeholders in string: id=ChallengeQuestionAnswer
2025-08-07 11:11:40.633 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-07 11:11:40.633 [Debug] Resolving placeholders in string: bmw
2025-08-07 11:11:40.633 [Debug] Resolved string: bmw
2025-08-07 11:11:40.635 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-07 11:11:40.635 [Debug] Resolved string: Click on the Login Button
2025-08-07 11:11:40.635 [Debug] Resolving placeholders in string: id=LoginAuthBtn
2025-08-07 11:11:40.635 [Debug] Resolved string: id=LoginAuthBtn
2025-08-07 11:11:40.636 [Debug] Resolving placeholders in string: Click on change later button on the password expiration popup message
2025-08-07 11:11:40.640 [Debug] Resolved string: Click on change later button on the password expiration popup message
2025-08-07 11:11:40.640 [Debug] Resolving placeholders in string: {{Selectors.popupCancel}}
2025-08-07 11:11:40.643 [Debug] Resolved string: id=popup_cancel
2025-08-07 11:11:40.643 [Debug] Resolving placeholders in string: Execute JS code to activate token
2025-08-07 11:11:40.643 [Debug] Resolved string: Execute JS code to activate token
2025-08-07 11:11:40.643 [Debug] Resolving placeholders in string: Globals.ActivationState = 'ACTIVATED';
2025-08-07 11:11:40.644 [Debug] Resolved string: Globals.ActivationState = 'ACTIVATED';
2025-08-07 11:11:40.644 [Debug] Resolving placeholders in string: Click on Cards option on the side menu
2025-08-07 11:11:40.644 [Debug] Resolved string: Click on Cards option on the side menu
2025-08-07 11:11:40.648 [Debug] Resolving placeholders in string: {{Selectors.CardsButton}}
2025-08-07 11:11:40.648 [Debug] Resolved string: xpath=//div[@class='mainmenuitems']//label[contains(text(), 'Cards')]
2025-08-07 11:11:40.648 [Debug] Resolving placeholders in string: Click on Card Inquiry and check cards details
2025-08-07 11:11:40.648 [Debug] Resolved string: Click on Card Inquiry and check cards details
2025-08-07 11:11:40.648 [Debug] Resolving placeholders in string: {{Selectors.CardInquiry}}
2025-08-07 11:11:40.648 [Debug] Resolved string: xpath=//div[@class='submenuitem']//label[contains(text(), 'Card Inquiry')]
2025-08-07 11:11:40.649 [Debug] Resolving placeholders in string: Check if the blocked cards appear in the card list.
2025-08-07 11:11:40.651 [Debug] Resolved string: Check if the blocked cards appear in the card list.
2025-08-07 11:11:40.651 [Debug] Resolving placeholders in string: css=#CardsSelectionErrorHandle > div:nth-child(1)
2025-08-07 11:11:40.651 [Debug] Resolved string: css=#CardsSelectionErrorHandle > div:nth-child(1)
2025-08-07 11:11:40.652 [Info] Successfully resolved parameters for test case: All users Cards are displayed successfully
2025-08-07 11:11:40.652 [Debug] Loading test case: WorkSet01/TestCases/012.CardServices/ActivateCard/57-fullCardActivation.json
2025-08-07 11:11:40.652 [Info] Loading test case from: WorkSet01/TestCases/012.CardServices/ActivateCard/57-fullCardActivation.json
2025-08-07 11:11:40.652 [Info] Attempting to load configuration file: WorkSet01/TestCases/012.CardServices/ActivateCard/57-fullCardActivation.json
2025-08-07 11:11:40.660 [Info] Successfully loaded configuration file: WorkSet01/TestCases/012.CardServices/ActivateCard/57-fullCardActivation.json
2025-08-07 11:11:40.661 [Info] No filtering configuration found. Including test case 'Verify that a user can successfully activate a card'.
2025-08-07 11:11:40.661 [Debug] Resolving parameters for test case: Verify that a user can successfully activate a card
2025-08-07 11:11:40.661 [Debug] Merging params
2025-08-07 11:11:40.661 [Debug] Merged basic params
2025-08-07 11:11:40.661 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-07 11:11:40.662 [Debug] Merged SpecialEnvParams params
2025-08-07 11:11:40.665 [Debug] Merged params
2025-08-07 11:11:40.665 [Debug] Loading parameters from reference file: WorkSet01/Params/ActivateCardParm.json
2025-08-07 11:11:40.665 [Info] Attempting to load configuration file: WorkSet01/Params/ActivateCardParm.json
2025-08-07 11:11:40.666 [Info] Successfully loaded configuration file: WorkSet01/Params/ActivateCardParm.json
2025-08-07 11:11:40.666 [Debug] Merging params
2025-08-07 11:11:40.666 [Debug] Merged basic params
2025-08-07 11:11:40.666 [Debug] Merged params
2025-08-07 11:11:40.678 [Debug] Merging params
2025-08-07 11:11:40.678 [Debug] Merging params
2025-08-07 11:11:40.678 [Debug] Resolving placeholders in test steps for test case: Verify that a user can successfully activate a card
2025-08-07 11:11:40.678 [Debug] Resolving TestCaseReference in step: 
2025-08-07 11:11:40.678 [Info] Loading test case from: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:11:40.678 [Info] Attempting to load configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:11:40.679 [Info] Successfully loaded configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:11:40.683 [Debug] Resolving parameters for test case: Login
2025-08-07 11:11:40.683 [Debug] Merging params
2025-08-07 11:11:40.683 [Debug] Merged basic params
2025-08-07 11:11:40.684 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-07 11:11:40.684 [Debug] Merged SpecialEnvParams params
2025-08-07 11:11:40.684 [Debug] Merged params
2025-08-07 11:11:40.684 [Debug] Loading parameters from reference file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:11:40.691 [Info] Attempting to load configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:11:40.691 [Info] Successfully loaded configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:11:40.691 [Debug] Merging params
2025-08-07 11:11:40.692 [Debug] Merged basic params
2025-08-07 11:11:40.692 [Debug] Merged params
2025-08-07 11:11:40.692 [Debug] Merging params
2025-08-07 11:11:40.692 [Debug] Merging params
2025-08-07 11:11:40.696 [Debug] Resolving placeholders in test steps for test case: Login
2025-08-07 11:11:40.696 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-07 11:11:40.696 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-07 11:11:40.696 [Debug] Resolving placeholders in string: {{Selectors.FinishButton}}
2025-08-07 11:11:40.696 [Debug] Resolved string: id=finish
2025-08-07 11:11:40.696 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-07 11:11:40.697 [Debug] Resolved string: Click on Login Button
2025-08-07 11:11:40.701 [Debug] Resolving placeholders in string: {{Selectors.LoginButton}}
2025-08-07 11:11:40.701 [Debug] Resolved string: id=LoginBtn
2025-08-07 11:11:40.701 [Debug] Resolving placeholders in string: Type the User Name
2025-08-07 11:11:40.701 [Debug] Resolved string: Type the User Name
2025-08-07 11:11:40.701 [Debug] Resolving placeholders in string: {{Selectors.UserNameField}}
2025-08-07 11:11:40.702 [Debug] Resolved string: id=UserName
2025-08-07 11:11:40.702 [Debug] Resolving placeholders in string: {{TestData.UserName}}
2025-08-07 11:11:40.708 [Debug] Resolved string: mahmoudsayed022
2025-08-07 11:11:40.709 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-07 11:11:40.709 [Debug] Resolved string: Click on Continue Button
2025-08-07 11:11:40.709 [Debug] Resolving placeholders in string: {{Selectors.ContinueButton}}
2025-08-07 11:11:40.709 [Debug] Resolved string: id=btn
2025-08-07 11:11:40.709 [Debug] Resolving placeholders in string: Type the Password
2025-08-07 11:11:40.709 [Debug] Resolved string: Type the Password
2025-08-07 11:11:40.712 [Debug] Resolving placeholders in string: {{Selectors.PasswordField}}
2025-08-07 11:11:40.712 [Debug] Resolved string: id=Password
2025-08-07 11:11:40.713 [Debug] Resolving placeholders in string: $('[{{Selectors.PasswordField}}]').val('{{TestData.Password}}')
2025-08-07 11:11:40.713 [Debug] Resolved string: $('[id=Password]').val('Password1')
2025-08-07 11:11:40.713 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-07 11:11:40.713 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-07 11:11:40.713 [Debug] Resolving placeholders in string: {{Selectors.ChallengeQuestionField}}
2025-08-07 11:11:40.716 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-07 11:11:40.717 [Debug] Resolving placeholders in string: {{TestData.ChallengeAnswer}}
2025-08-07 11:11:40.717 [Debug] Resolved string: bmw
2025-08-07 11:11:40.717 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-07 11:11:40.717 [Debug] Resolved string: Click on the Login Button
2025-08-07 11:11:40.717 [Debug] Resolving placeholders in string: {{Selectors.LoginAuthButton}}
2025-08-07 11:11:40.717 [Debug] Resolved string: id=LoginAuthBtn
2025-08-07 11:11:40.725 [Info] Successfully resolved parameters for test case: Login
2025-08-07 11:11:40.725 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-07 11:11:40.725 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-07 11:11:40.725 [Debug] Resolving placeholders in string: id=finish
2025-08-07 11:11:40.725 [Debug] Resolved string: id=finish
2025-08-07 11:11:40.725 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-07 11:11:40.726 [Debug] Resolved string: Click on Login Button
2025-08-07 11:11:40.730 [Debug] Resolving placeholders in string: id=LoginBtn
2025-08-07 11:11:40.730 [Debug] Resolved string: id=LoginBtn
2025-08-07 11:11:40.730 [Debug] Resolving placeholders in string: Type the User Name
2025-08-07 11:11:40.730 [Debug] Resolved string: Type the User Name
2025-08-07 11:11:40.730 [Debug] Resolving placeholders in string: id=UserName
2025-08-07 11:11:40.730 [Debug] Resolved string: id=UserName
2025-08-07 11:11:40.731 [Debug] Resolving placeholders in string: mahmoudsayed022
2025-08-07 11:11:40.734 [Debug] Resolved string: mahmoudsayed022
2025-08-07 11:11:40.734 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-07 11:11:40.734 [Debug] Resolved string: Click on Continue Button
2025-08-07 11:11:40.735 [Debug] Resolving placeholders in string: id=btn
2025-08-07 11:11:40.735 [Debug] Resolved string: id=btn
2025-08-07 11:11:40.735 [Debug] Resolving placeholders in string: Type the Password
2025-08-07 11:11:40.736 [Debug] Resolved string: Type the Password
2025-08-07 11:11:40.743 [Debug] Resolving placeholders in string: id=Password
2025-08-07 11:11:40.743 [Debug] Resolved string: id=Password
2025-08-07 11:11:40.743 [Debug] Resolving placeholders in string: $('[id=Password]').val('Password1')
2025-08-07 11:11:40.744 [Debug] Resolved string: $('[id=Password]').val('Password1')
2025-08-07 11:11:40.744 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-07 11:11:40.744 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-07 11:11:40.744 [Debug] Resolving placeholders in string: id=ChallengeQuestionAnswer
2025-08-07 11:11:40.747 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-07 11:11:40.748 [Debug] Resolving placeholders in string: bmw
2025-08-07 11:11:40.748 [Debug] Resolved string: bmw
2025-08-07 11:11:40.748 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-07 11:11:40.748 [Debug] Resolved string: Click on the Login Button
2025-08-07 11:11:40.748 [Debug] Resolving placeholders in string: id=LoginAuthBtn
2025-08-07 11:11:40.749 [Debug] Resolved string: id=LoginAuthBtn
2025-08-07 11:11:40.752 [Debug] Resolving placeholders in string: Click on change later button on the password expiration popup message
2025-08-07 11:11:40.752 [Debug] Resolved string: Click on change later button on the password expiration popup message
2025-08-07 11:11:40.752 [Debug] Resolving placeholders in string: {{Selectors.popupCancel}}
2025-08-07 11:11:40.756 [Debug] Resolved string: id=popup_cancel
2025-08-07 11:11:40.756 [Debug] Resolving placeholders in string: Click on biometric alert
2025-08-07 11:11:40.757 [Debug] Resolved string: Click on biometric alert
2025-08-07 11:11:40.757 [Debug] Resolving placeholders in string: {{Selectors.BioMetricAlert}}
2025-08-07 11:11:40.758 [Debug] Resolved string: css=#popbuttons > div > button.btn.Cancel.back_gradient2
2025-08-07 11:11:40.759 [Debug] Resolving placeholders in string: Click on Cards button from side menu
2025-08-07 11:11:40.759 [Debug] Resolved string: Click on Cards button from side menu
2025-08-07 11:11:40.759 [Debug] Resolving placeholders in string: {{Selectors.CardsButton}}
2025-08-07 11:11:40.760 [Debug] Resolved string: xpath=//div[@class='mainmenuitems']//label[contains(text(), 'Cards')]
2025-08-07 11:11:40.760 [Debug] Resolving placeholders in string: Click on Cards Services button from side menu
2025-08-07 11:11:40.761 [Debug] Resolved string: Click on Cards Services button from side menu
2025-08-07 11:11:40.761 [Debug] Resolving placeholders in string: {{Selectors.CardServices}}
2025-08-07 11:11:40.762 [Debug] Resolved string: xpath=//div[@class='submenuitem']//label[contains(text(), 'Card services')]
2025-08-07 11:11:40.762 [Debug] Resolving placeholders in string: Click on Activate Card
2025-08-07 11:11:40.762 [Debug] Resolved string: Click on Activate Card
2025-08-07 11:11:40.762 [Debug] Resolving placeholders in string: {{Selectors.ActivateCard}}
2025-08-07 11:11:40.762 [Debug] Resolved string: id=ActivateCard
2025-08-07 11:11:40.762 [Debug] Resolving placeholders in string: Click on card that need to be  Activated
2025-08-07 11:11:40.763 [Debug] Resolved string: Click on card that need to be  Activated
2025-08-07 11:11:40.763 [Debug] Resolving placeholders in string: {{Selectors.DeActivatedCard}}
2025-08-07 11:11:40.763 [Debug] Resolved string: css=#CardsSelectionErrorHandle > div:nth-child(2) > div > div.balance_cardList > h4
2025-08-07 11:11:40.763 [Debug] Resolving placeholders in string: Click on Continue
2025-08-07 11:11:40.764 [Debug] Resolved string: Click on Continue
2025-08-07 11:11:40.764 [Debug] Resolving placeholders in string: {{Selectors.ContinueBtn}}
2025-08-07 11:11:40.765 [Debug] Resolved string: id=btncontinue
2025-08-07 11:11:40.765 [Debug] Resolving placeholders in string: Enter new PIN
2025-08-07 11:11:40.766 [Debug] Resolved string: Enter new PIN
2025-08-07 11:11:40.766 [Debug] Resolving placeholders in string: {{Selectors.NewPINfield}}
2025-08-07 11:11:40.766 [Debug] Resolved string: id=ACpinNumber
2025-08-07 11:11:40.766 [Debug] Resolving placeholders in string: 1234
2025-08-07 11:11:40.766 [Debug] Resolved string: 1234
2025-08-07 11:11:40.766 [Debug] Resolving placeholders in string: Confirm new PIN
2025-08-07 11:11:40.766 [Debug] Resolved string: Confirm new PIN
2025-08-07 11:11:40.767 [Debug] Resolving placeholders in string: {{Selectors.ConfirmPINfield}}
2025-08-07 11:11:40.767 [Debug] Resolved string: id=ACconfirmPin
2025-08-07 11:11:40.767 [Debug] Resolving placeholders in string: 1234
2025-08-07 11:11:40.767 [Debug] Resolved string: 1234
2025-08-07 11:11:40.767 [Debug] Resolving placeholders in string: Enter a valid OTP
2025-08-07 11:11:40.767 [Debug] Resolved string: Enter a valid OTP
2025-08-07 11:11:40.768 [Debug] Resolving placeholders in string: {{Selectors.OTPfield}}
2025-08-07 11:11:40.768 [Debug] Resolved string: id=ACsmsCode
2025-08-07 11:11:40.769 [Debug] Resolving placeholders in string: 123456
2025-08-07 11:11:40.769 [Debug] Resolved string: 123456
2025-08-07 11:11:40.770 [Debug] Resolving placeholders in string: Click on Continue and showing activation is successfull
2025-08-07 11:11:40.770 [Debug] Resolved string: Click on Continue and showing activation is successfull
2025-08-07 11:11:40.770 [Debug] Resolving placeholders in string: {{Selectors.ContinueBtn2}}
2025-08-07 11:11:40.770 [Debug] Resolved string: id=ACcontinueBtn
2025-08-07 11:11:40.770 [Info] Successfully resolved parameters for test case: Verify that a user can successfully activate a card
2025-08-07 11:11:40.770 [Debug] Loading test case: WorkSet01/TestCases/012.CardServices/BlockCard/52-UnBlockCard.json
2025-08-07 11:11:40.771 [Info] Loading test case from: WorkSet01/TestCases/012.CardServices/BlockCard/52-UnBlockCard.json
2025-08-07 11:11:40.772 [Info] Attempting to load configuration file: WorkSet01/TestCases/012.CardServices/BlockCard/52-UnBlockCard.json
2025-08-07 11:11:40.772 [Info] Successfully loaded configuration file: WorkSet01/TestCases/012.CardServices/BlockCard/52-UnBlockCard.json
2025-08-07 11:11:40.773 [Info] No filtering configuration found. Including test case 'Verify that a user can successfully unblock a card'.
2025-08-07 11:11:40.773 [Debug] Resolving parameters for test case: Verify that a user can successfully unblock a card
2025-08-07 11:11:40.773 [Debug] Merging params
2025-08-07 11:11:40.773 [Debug] Merged basic params
2025-08-07 11:11:40.773 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-07 11:11:40.774 [Debug] Merged SpecialEnvParams params
2025-08-07 11:11:40.774 [Debug] Merged params
2025-08-07 11:11:40.775 [Debug] Loading parameters from reference file: WorkSet01/Params/BlockCardParams.json
2025-08-07 11:11:40.775 [Info] Attempting to load configuration file: WorkSet01/Params/BlockCardParams.json
2025-08-07 11:11:40.776 [Info] Successfully loaded configuration file: WorkSet01/Params/BlockCardParams.json
2025-08-07 11:11:40.777 [Debug] Merging params
2025-08-07 11:11:40.777 [Debug] Merged basic params
2025-08-07 11:11:40.778 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-07 11:11:40.778 [Debug] Merged SpecialEnvParams params
2025-08-07 11:11:40.778 [Debug] Merged params
2025-08-07 11:11:40.778 [Debug] Merging params
2025-08-07 11:11:40.778 [Debug] Merging params
2025-08-07 11:11:40.778 [Debug] Resolving placeholders in test steps for test case: Verify that a user can successfully unblock a card
2025-08-07 11:11:40.778 [Debug] Resolving TestCaseReference in step: 
2025-08-07 11:11:40.779 [Info] Loading test case from: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:11:40.779 [Info] Attempting to load configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:11:40.779 [Info] Successfully loaded configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:11:40.780 [Debug] Resolving parameters for test case: Login
2025-08-07 11:11:40.780 [Debug] Merging params
2025-08-07 11:11:40.780 [Debug] Merged basic params
2025-08-07 11:11:40.780 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-07 11:11:40.780 [Debug] Merged SpecialEnvParams params
2025-08-07 11:11:40.781 [Debug] Merged params
2025-08-07 11:11:40.781 [Debug] Loading parameters from reference file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:11:40.781 [Info] Attempting to load configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:11:40.781 [Info] Successfully loaded configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:11:40.782 [Debug] Merging params
2025-08-07 11:11:40.782 [Debug] Merged basic params
2025-08-07 11:11:40.782 [Debug] Merged params
2025-08-07 11:11:40.782 [Debug] Merging params
2025-08-07 11:11:40.783 [Debug] Merging params
2025-08-07 11:11:40.783 [Debug] Resolving placeholders in test steps for test case: Login
2025-08-07 11:11:40.783 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-07 11:11:40.783 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-07 11:11:40.784 [Debug] Resolving placeholders in string: {{Selectors.FinishButton}}
2025-08-07 11:11:40.784 [Debug] Resolved string: id=finish
2025-08-07 11:11:40.785 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-07 11:11:40.785 [Debug] Resolved string: Click on Login Button
2025-08-07 11:11:40.786 [Debug] Resolving placeholders in string: {{Selectors.LoginButton}}
2025-08-07 11:11:40.786 [Debug] Resolved string: id=LoginBtn
2025-08-07 11:11:40.787 [Debug] Resolving placeholders in string: Type the User Name
2025-08-07 11:11:40.787 [Debug] Resolved string: Type the User Name
2025-08-07 11:11:40.788 [Debug] Resolving placeholders in string: {{Selectors.UserNameField}}
2025-08-07 11:11:40.788 [Debug] Resolved string: id=UserName
2025-08-07 11:11:40.788 [Debug] Resolving placeholders in string: {{TestData.UserName}}
2025-08-07 11:11:40.789 [Debug] Resolved string: mahmoudsayed022
2025-08-07 11:11:40.789 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-07 11:11:40.790 [Debug] Resolved string: Click on Continue Button
2025-08-07 11:11:40.790 [Debug] Resolving placeholders in string: {{Selectors.ContinueButton}}
2025-08-07 11:11:40.791 [Debug] Resolved string: id=btn
2025-08-07 11:11:40.792 [Debug] Resolving placeholders in string: Type the Password
2025-08-07 11:11:40.793 [Debug] Resolved string: Type the Password
2025-08-07 11:11:40.793 [Debug] Resolving placeholders in string: {{Selectors.PasswordField}}
2025-08-07 11:11:40.794 [Debug] Resolved string: id=Password
2025-08-07 11:11:40.794 [Debug] Resolving placeholders in string: $('[{{Selectors.PasswordField}}]').val('{{TestData.Password}}')
2025-08-07 11:11:40.795 [Debug] Resolved string: $('[id=Password]').val('Password1')
2025-08-07 11:11:40.796 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-07 11:11:40.796 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-07 11:11:40.797 [Debug] Resolving placeholders in string: {{Selectors.ChallengeQuestionField}}
2025-08-07 11:11:40.797 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-07 11:11:40.798 [Debug] Resolving placeholders in string: {{TestData.ChallengeAnswer}}
2025-08-07 11:11:40.798 [Debug] Resolved string: bmw
2025-08-07 11:11:40.799 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-07 11:11:40.799 [Debug] Resolved string: Click on the Login Button
2025-08-07 11:11:40.800 [Debug] Resolving placeholders in string: {{Selectors.LoginAuthButton}}
2025-08-07 11:11:40.800 [Debug] Resolved string: id=LoginAuthBtn
2025-08-07 11:11:40.800 [Info] Successfully resolved parameters for test case: Login
2025-08-07 11:11:40.800 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-07 11:11:40.800 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-07 11:11:40.801 [Debug] Resolving placeholders in string: id=finish
2025-08-07 11:11:40.801 [Debug] Resolved string: id=finish
2025-08-07 11:11:40.801 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-07 11:11:40.801 [Debug] Resolved string: Click on Login Button
2025-08-07 11:11:40.801 [Debug] Resolving placeholders in string: id=LoginBtn
2025-08-07 11:11:40.802 [Debug] Resolved string: id=LoginBtn
2025-08-07 11:11:40.802 [Debug] Resolving placeholders in string: Type the User Name
2025-08-07 11:11:40.802 [Debug] Resolved string: Type the User Name
2025-08-07 11:11:40.802 [Debug] Resolving placeholders in string: id=UserName
2025-08-07 11:11:40.802 [Debug] Resolved string: id=UserName
2025-08-07 11:11:40.802 [Debug] Resolving placeholders in string: mahmoudsayed022
2025-08-07 11:11:40.802 [Debug] Resolved string: mahmoudsayed022
2025-08-07 11:11:40.802 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-07 11:11:40.803 [Debug] Resolved string: Click on Continue Button
2025-08-07 11:11:40.803 [Debug] Resolving placeholders in string: id=btn
2025-08-07 11:11:40.803 [Debug] Resolved string: id=btn
2025-08-07 11:11:40.803 [Debug] Resolving placeholders in string: Type the Password
2025-08-07 11:11:40.803 [Debug] Resolved string: Type the Password
2025-08-07 11:11:40.804 [Debug] Resolving placeholders in string: id=Password
2025-08-07 11:11:40.805 [Debug] Resolved string: id=Password
2025-08-07 11:11:40.805 [Debug] Resolving placeholders in string: $('[id=Password]').val('Password1')
2025-08-07 11:11:40.805 [Debug] Resolved string: $('[id=Password]').val('Password1')
2025-08-07 11:11:40.806 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-07 11:11:40.806 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-07 11:11:40.806 [Debug] Resolving placeholders in string: id=ChallengeQuestionAnswer
2025-08-07 11:11:40.807 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-07 11:11:40.807 [Debug] Resolving placeholders in string: bmw
2025-08-07 11:11:40.809 [Debug] Resolved string: bmw
2025-08-07 11:11:40.809 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-07 11:11:40.810 [Debug] Resolved string: Click on the Login Button
2025-08-07 11:11:40.810 [Debug] Resolving placeholders in string: id=LoginAuthBtn
2025-08-07 11:11:40.810 [Debug] Resolved string: id=LoginAuthBtn
2025-08-07 11:11:40.810 [Debug] Resolving placeholders in string: Click on change later button on the password expiration popup message
2025-08-07 11:11:40.810 [Debug] Resolved string: Click on change later button on the password expiration popup message
2025-08-07 11:11:40.811 [Debug] Resolving placeholders in string: {{Selectors.popupCancel}}
2025-08-07 11:11:40.811 [Debug] Resolved string: id=popup_cancel
2025-08-07 11:11:40.811 [Debug] Resolving placeholders in string: Click on biometric alert
2025-08-07 11:11:40.811 [Debug] Resolved string: Click on biometric alert
2025-08-07 11:11:40.811 [Debug] Resolving placeholders in string: {{Selectors.BioMetricAlert}}
2025-08-07 11:11:40.811 [Debug] Resolved string: css=#popbuttons > div > button.btn.Cancel.back_gradient2
2025-08-07 11:11:40.811 [Debug] Resolving placeholders in string: Click on Cards button from side menu
2025-08-07 11:11:40.811 [Debug] Resolved string: Click on Cards button from side menu
2025-08-07 11:11:40.811 [Debug] Resolving placeholders in string: {{Selectors.CardsButton}}
2025-08-07 11:11:40.812 [Debug] Resolved string: xpath=//div[@class='mainmenuitems']//label[contains(text(), 'Cards')]
2025-08-07 11:11:40.812 [Debug] Resolving placeholders in string: Execute JS code to activate token
2025-08-07 11:11:40.812 [Debug] Resolved string: Execute JS code to activate token
2025-08-07 11:11:40.812 [Debug] Resolving placeholders in string: Globals.ActivationState = 'ACTIVATED';
2025-08-07 11:11:40.813 [Debug] Resolved string: Globals.ActivationState = 'ACTIVATED';
2025-08-07 11:11:40.813 [Debug] Resolving placeholders in string: Click on Cards Services button from side menu
2025-08-07 11:11:40.813 [Debug] Resolved string: Click on Cards Services button from side menu
2025-08-07 11:11:40.813 [Debug] Resolving placeholders in string: {{Selectors.CardServices}}
2025-08-07 11:11:40.813 [Debug] Resolved string: xpath=//div[@class='submenuitem']//label[contains(text(), 'Card services')]
2025-08-07 11:11:40.814 [Debug] Resolving placeholders in string: Click on UnBlock Btn
2025-08-07 11:11:40.814 [Debug] Resolved string: Click on UnBlock Btn
2025-08-07 11:11:40.814 [Debug] Resolving placeholders in string: {{Selectors.UnblockCard}}
2025-08-07 11:11:40.814 [Debug] Resolved string: id=UnblockCard
2025-08-07 11:11:40.814 [Debug] Resolving placeholders in string: Click on Blocked Card
2025-08-07 11:11:40.815 [Debug] Resolved string: Click on Blocked Card
2025-08-07 11:11:40.815 [Debug] Resolving placeholders in string: {{Selectors.BlockedCard}}
2025-08-07 11:11:40.815 [Debug] Resolved string: css=#CardsSelectionErrorHandle > div
2025-08-07 11:11:40.815 [Debug] Resolving placeholders in string: Click on Agree Check Box
2025-08-07 11:11:40.815 [Debug] Resolved string: Click on Agree Check Box
2025-08-07 11:11:40.815 [Debug] Resolving placeholders in string: {{Selectors.AgreeCheckBox}}
2025-08-07 11:11:40.815 [Debug] Resolved string: css=#wginsUnblockCard_Default > div > div > div > div.details_items_container.back_white > div.terms.AgreeTermsAndConditions > input[type=checkbox]
2025-08-07 11:11:40.815 [Debug] Resolving placeholders in string: Click on Continue
2025-08-07 11:11:40.815 [Debug] Resolved string: Click on Continue
2025-08-07 11:11:40.816 [Debug] Resolving placeholders in string: {{Selectors.ContinueBtn}}
2025-08-07 11:11:40.816 [Debug] Resolved string: id=SubmitUnblockCardReq
2025-08-07 11:11:40.816 [Debug] Resolving placeholders in string: Type valid Token
2025-08-07 11:11:40.816 [Debug] Resolved string: Type valid Token
2025-08-07 11:11:40.816 [Debug] Resolving placeholders in string: {{Selectors.TokenInput}}
2025-08-07 11:11:40.817 [Debug] Resolved string: id=TokenNUMBER
2025-08-07 11:11:40.817 [Debug] Resolving placeholders in string: 123456
2025-08-07 11:11:40.817 [Debug] Resolved string: 123456
2025-08-07 11:11:40.817 [Debug] Resolving placeholders in string: Click On Confirm
2025-08-07 11:11:40.817 [Debug] Resolved string: Click On Confirm
2025-08-07 11:11:40.817 [Debug] Resolving placeholders in string: {{Selectors.ConfirmBtn}}
2025-08-07 11:11:40.818 [Debug] Resolved string: id=btnTokenConfirm
2025-08-07 11:11:40.818 [Info] Successfully resolved parameters for test case: Verify that a user can successfully unblock a card
2025-08-07 11:11:40.818 [Debug] Loading test case: WorkSet01/TestCases/012.CardServices/BlockCard/48-CheckCardsList.json
2025-08-07 11:11:40.818 [Info] Loading test case from: WorkSet01/TestCases/012.CardServices/BlockCard/48-CheckCardsList.json
2025-08-07 11:11:40.818 [Info] Attempting to load configuration file: WorkSet01/TestCases/012.CardServices/BlockCard/48-CheckCardsList.json
2025-08-07 11:11:40.819 [Info] Successfully loaded configuration file: WorkSet01/TestCases/012.CardServices/BlockCard/48-CheckCardsList.json
2025-08-07 11:11:40.820 [Info] No filtering configuration found. Including test case 'Check Cards List'.
2025-08-07 11:11:40.820 [Debug] Resolving parameters for test case: Check Cards List
2025-08-07 11:11:40.821 [Debug] Merging params
2025-08-07 11:11:40.821 [Debug] Merged basic params
2025-08-07 11:11:40.821 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-07 11:11:40.825 [Debug] Merged SpecialEnvParams params
2025-08-07 11:11:40.825 [Debug] Merged params
2025-08-07 11:11:40.826 [Debug] Loading parameters from reference file: WorkSet01/Params/BlockCardParams.json
2025-08-07 11:11:40.826 [Info] Attempting to load configuration file: WorkSet01/Params/BlockCardParams.json
2025-08-07 11:11:40.826 [Info] Successfully loaded configuration file: WorkSet01/Params/BlockCardParams.json
2025-08-07 11:11:40.826 [Debug] Merging params
2025-08-07 11:11:40.827 [Debug] Merged basic params
2025-08-07 11:11:40.827 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-07 11:11:40.828 [Debug] Merged SpecialEnvParams params
2025-08-07 11:11:40.829 [Debug] Merged params
2025-08-07 11:11:40.829 [Debug] Merging params
2025-08-07 11:11:40.829 [Debug] Merging params
2025-08-07 11:11:40.829 [Debug] Resolving placeholders in test steps for test case: Check Cards List
2025-08-07 11:11:40.829 [Debug] Resolving TestCaseReference in step: 
2025-08-07 11:11:40.829 [Info] Loading test case from: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:11:40.830 [Info] Attempting to load configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:11:40.830 [Info] Successfully loaded configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:11:40.831 [Debug] Resolving parameters for test case: Login
2025-08-07 11:11:40.831 [Debug] Merging params
2025-08-07 11:11:40.831 [Debug] Merged basic params
2025-08-07 11:11:40.831 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-07 11:11:40.831 [Debug] Merged SpecialEnvParams params
2025-08-07 11:11:40.832 [Debug] Merged params
2025-08-07 11:11:40.832 [Debug] Loading parameters from reference file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:11:40.833 [Info] Attempting to load configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:11:40.833 [Info] Successfully loaded configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:11:40.834 [Debug] Merging params
2025-08-07 11:11:40.834 [Debug] Merged basic params
2025-08-07 11:11:40.834 [Debug] Merged params
2025-08-07 11:11:40.834 [Debug] Merging params
2025-08-07 11:11:40.834 [Debug] Merging params
2025-08-07 11:11:40.835 [Debug] Resolving placeholders in test steps for test case: Login
2025-08-07 11:11:40.835 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-07 11:11:40.835 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-07 11:11:40.835 [Debug] Resolving placeholders in string: {{Selectors.FinishButton}}
2025-08-07 11:11:40.835 [Debug] Resolved string: id=finish
2025-08-07 11:11:40.835 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-07 11:11:40.835 [Debug] Resolved string: Click on Login Button
2025-08-07 11:11:40.835 [Debug] Resolving placeholders in string: {{Selectors.LoginButton}}
2025-08-07 11:11:40.836 [Debug] Resolved string: id=LoginBtn
2025-08-07 11:11:40.837 [Debug] Resolving placeholders in string: Type the User Name
2025-08-07 11:11:40.837 [Debug] Resolved string: Type the User Name
2025-08-07 11:11:40.838 [Debug] Resolving placeholders in string: {{Selectors.UserNameField}}
2025-08-07 11:11:40.838 [Debug] Resolved string: id=UserName
2025-08-07 11:11:40.838 [Debug] Resolving placeholders in string: {{TestData.UserName}}
2025-08-07 11:11:40.838 [Debug] Resolved string: mahmoudsayed022
2025-08-07 11:11:40.838 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-07 11:11:40.838 [Debug] Resolved string: Click on Continue Button
2025-08-07 11:11:40.839 [Debug] Resolving placeholders in string: {{Selectors.ContinueButton}}
2025-08-07 11:11:40.839 [Debug] Resolved string: id=btn
2025-08-07 11:11:40.839 [Debug] Resolving placeholders in string: Type the Password
2025-08-07 11:11:40.839 [Debug] Resolved string: Type the Password
2025-08-07 11:11:40.840 [Debug] Resolving placeholders in string: {{Selectors.PasswordField}}
2025-08-07 11:11:40.840 [Debug] Resolved string: id=Password
2025-08-07 11:11:40.840 [Debug] Resolving placeholders in string: $('[{{Selectors.PasswordField}}]').val('{{TestData.Password}}')
2025-08-07 11:11:40.840 [Debug] Resolved string: $('[id=Password]').val('Password1')
2025-08-07 11:11:40.840 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-07 11:11:40.840 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-07 11:11:40.841 [Debug] Resolving placeholders in string: {{Selectors.ChallengeQuestionField}}
2025-08-07 11:11:40.841 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-07 11:11:40.842 [Debug] Resolving placeholders in string: {{TestData.ChallengeAnswer}}
2025-08-07 11:11:40.842 [Debug] Resolved string: bmw
2025-08-07 11:11:40.843 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-07 11:11:40.844 [Debug] Resolved string: Click on the Login Button
2025-08-07 11:11:40.844 [Debug] Resolving placeholders in string: {{Selectors.LoginAuthButton}}
2025-08-07 11:11:40.844 [Debug] Resolved string: id=LoginAuthBtn
2025-08-07 11:11:40.844 [Info] Successfully resolved parameters for test case: Login
2025-08-07 11:11:40.844 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-07 11:11:40.844 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-07 11:11:40.844 [Debug] Resolving placeholders in string: id=finish
2025-08-07 11:11:40.845 [Debug] Resolved string: id=finish
2025-08-07 11:11:40.845 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-07 11:11:40.845 [Debug] Resolved string: Click on Login Button
2025-08-07 11:11:40.845 [Debug] Resolving placeholders in string: id=LoginBtn
2025-08-07 11:11:40.845 [Debug] Resolved string: id=LoginBtn
2025-08-07 11:11:40.845 [Debug] Resolving placeholders in string: Type the User Name
2025-08-07 11:11:40.846 [Debug] Resolved string: Type the User Name
2025-08-07 11:11:40.846 [Debug] Resolving placeholders in string: id=UserName
2025-08-07 11:11:40.846 [Debug] Resolved string: id=UserName
2025-08-07 11:11:40.846 [Debug] Resolving placeholders in string: mahmoudsayed022
2025-08-07 11:11:40.846 [Debug] Resolved string: mahmoudsayed022
2025-08-07 11:11:40.847 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-07 11:11:40.847 [Debug] Resolved string: Click on Continue Button
2025-08-07 11:11:40.847 [Debug] Resolving placeholders in string: id=btn
2025-08-07 11:11:40.847 [Debug] Resolved string: id=btn
2025-08-07 11:11:40.847 [Debug] Resolving placeholders in string: Type the Password
2025-08-07 11:11:40.848 [Debug] Resolved string: Type the Password
2025-08-07 11:11:40.848 [Debug] Resolving placeholders in string: id=Password
2025-08-07 11:11:40.848 [Debug] Resolved string: id=Password
2025-08-07 11:11:40.848 [Debug] Resolving placeholders in string: $('[id=Password]').val('Password1')
2025-08-07 11:11:40.848 [Debug] Resolved string: $('[id=Password]').val('Password1')
2025-08-07 11:11:40.848 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-07 11:11:40.848 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-07 11:11:40.849 [Debug] Resolving placeholders in string: id=ChallengeQuestionAnswer
2025-08-07 11:11:40.849 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-07 11:11:40.849 [Debug] Resolving placeholders in string: bmw
2025-08-07 11:11:40.849 [Debug] Resolved string: bmw
2025-08-07 11:11:40.849 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-07 11:11:40.849 [Debug] Resolved string: Click on the Login Button
2025-08-07 11:11:40.850 [Debug] Resolving placeholders in string: id=LoginAuthBtn
2025-08-07 11:11:40.850 [Debug] Resolved string: id=LoginAuthBtn
2025-08-07 11:11:40.850 [Debug] Resolving placeholders in string: Click on change later button on the password expiration popup message
2025-08-07 11:11:40.850 [Debug] Resolved string: Click on change later button on the password expiration popup message
2025-08-07 11:11:40.850 [Debug] Resolving placeholders in string: {{Selectors.popupCancel}}
2025-08-07 11:11:40.850 [Debug] Resolved string: id=popup_cancel
2025-08-07 11:11:40.851 [Debug] Resolving placeholders in string: Click on biometric alert
2025-08-07 11:11:40.851 [Debug] Resolved string: Click on biometric alert
2025-08-07 11:11:40.851 [Debug] Resolving placeholders in string: {{Selectors.BioMetricAlert}}
2025-08-07 11:11:40.851 [Debug] Resolved string: css=#popbuttons > div > button.btn.Cancel.back_gradient2
2025-08-07 11:11:40.851 [Debug] Resolving placeholders in string: Execute JS code to activate token
2025-08-07 11:11:40.852 [Debug] Resolved string: Execute JS code to activate token
2025-08-07 11:11:40.852 [Debug] Resolving placeholders in string: Globals.ActivationState = 'ACTIVATED';
2025-08-07 11:11:40.852 [Debug] Resolved string: Globals.ActivationState = 'ACTIVATED';
2025-08-07 11:11:40.852 [Debug] Resolving placeholders in string: Click on Cards option on the side menu
2025-08-07 11:11:40.852 [Debug] Resolved string: Click on Cards option on the side menu
2025-08-07 11:11:40.853 [Debug] Resolving placeholders in string: {{Selectors.CardsButton}}
2025-08-07 11:11:40.853 [Debug] Resolved string: xpath=//div[@class='mainmenuitems']//label[contains(text(), 'Cards')]
2025-08-07 11:11:40.853 [Debug] Resolving placeholders in string: Click on Card Services
2025-08-07 11:11:40.853 [Debug] Resolved string: Click on Card Services
2025-08-07 11:11:40.853 [Debug] Resolving placeholders in string: {{Selectors.CardServices}}
2025-08-07 11:11:40.853 [Debug] Resolved string: xpath=//div[@class='submenuitem']//label[contains(text(), 'Card services')]
2025-08-07 11:11:40.853 [Debug] Resolving placeholders in string: Click on Block Card option
2025-08-07 11:11:40.854 [Debug] Resolved string: Click on Block Card option
2025-08-07 11:11:40.854 [Debug] Resolving placeholders in string: {{Selectors.BlockCardOption}}
2025-08-07 11:11:40.854 [Debug] Resolved string: id=BlockCard
2025-08-07 11:11:40.854 [Debug] Resolving placeholders in string: Click on Select Card Button and show the cards list
2025-08-07 11:11:40.854 [Debug] Resolved string: Click on Select Card Button and show the cards list
2025-08-07 11:11:40.854 [Debug] Resolving placeholders in string: {{Selectors.SelectCardButton}}
2025-08-07 11:11:40.855 [Debug] Resolved string: id=SelectCardBtn
2025-08-07 11:11:40.855 [Debug] Resolving placeholders in string: Check if the blocked cards appear in the card list.
2025-08-07 11:11:40.856 [Debug] Resolved string: Check if the blocked cards appear in the card list.
2025-08-07 11:11:40.856 [Debug] Resolving placeholders in string: css=#CardsSelectionErrorHandle > div:nth-child(1)
2025-08-07 11:11:40.857 [Debug] Resolved string: css=#CardsSelectionErrorHandle > div:nth-child(1)
2025-08-07 11:11:40.857 [Info] Successfully resolved parameters for test case: Check Cards List
2025-08-07 11:11:40.857 [Debug] Loading test case: WorkSet01/TestCases/04.ATMdispute/43-CompleteRequest.json
2025-08-07 11:11:40.857 [Info] Loading test case from: WorkSet01/TestCases/04.ATMdispute/43-CompleteRequest.json
2025-08-07 11:11:40.857 [Info] Attempting to load configuration file: WorkSet01/TestCases/04.ATMdispute/43-CompleteRequest.json
2025-08-07 11:11:40.858 [Info] Successfully loaded configuration file: WorkSet01/TestCases/04.ATMdispute/43-CompleteRequest.json
2025-08-07 11:11:40.858 [Info] No filtering configuration found. Including test case 'Complete ATM dispute request'.
2025-08-07 11:11:40.859 [Debug] Resolving parameters for test case: Complete ATM dispute request
2025-08-07 11:11:40.859 [Debug] Merging params
2025-08-07 11:11:40.859 [Debug] Merged basic params
2025-08-07 11:11:40.859 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-07 11:11:40.859 [Debug] Merged SpecialEnvParams params
2025-08-07 11:11:40.859 [Debug] Merged params
2025-08-07 11:11:40.860 [Debug] Loading parameters from reference file: WorkSet01/Params/ATMdisputeParams.json
2025-08-07 11:11:40.860 [Info] Attempting to load configuration file: WorkSet01/Params/ATMdisputeParams.json
2025-08-07 11:11:40.860 [Info] Successfully loaded configuration file: WorkSet01/Params/ATMdisputeParams.json
2025-08-07 11:11:40.861 [Debug] Merging params
2025-08-07 11:11:40.861 [Debug] Merged basic params
2025-08-07 11:11:40.861 [Debug] Merged params
2025-08-07 11:11:40.861 [Debug] Merging params
2025-08-07 11:11:40.861 [Debug] Merging params
2025-08-07 11:11:40.861 [Debug] Resolving placeholders in test steps for test case: Complete ATM dispute request
2025-08-07 11:11:40.862 [Debug] Resolving TestCaseReference in step: 
2025-08-07 11:11:40.862 [Info] Loading test case from: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:11:40.862 [Info] Attempting to load configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:11:40.863 [Info] Successfully loaded configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:11:40.864 [Debug] Resolving parameters for test case: Login
2025-08-07 11:11:40.864 [Debug] Merging params
2025-08-07 11:11:40.864 [Debug] Merged basic params
2025-08-07 11:11:40.865 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-07 11:11:40.865 [Debug] Merged SpecialEnvParams params
2025-08-07 11:11:40.865 [Debug] Merged params
2025-08-07 11:11:40.865 [Debug] Loading parameters from reference file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:11:40.865 [Info] Attempting to load configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:11:40.865 [Info] Successfully loaded configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:11:40.866 [Debug] Merging params
2025-08-07 11:11:40.866 [Debug] Merged basic params
2025-08-07 11:11:40.866 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-07 11:11:40.867 [Debug] Merged SpecialEnvParams params
2025-08-07 11:11:40.867 [Debug] Merged params
2025-08-07 11:11:40.867 [Debug] Merging params
2025-08-07 11:11:40.867 [Debug] Merging params
2025-08-07 11:11:40.867 [Debug] Resolving placeholders in test steps for test case: Login
2025-08-07 11:11:40.867 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-07 11:11:40.867 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-07 11:11:40.868 [Debug] Resolving placeholders in string: {{Selectors.FinishButton}}
2025-08-07 11:11:40.868 [Debug] Resolved string: id=finish
2025-08-07 11:11:40.868 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-07 11:11:40.868 [Debug] Resolved string: Click on Login Button
2025-08-07 11:11:40.868 [Debug] Resolving placeholders in string: {{Selectors.LoginButton}}
2025-08-07 11:11:40.868 [Debug] Resolved string: id=LoginBtn
2025-08-07 11:11:40.868 [Debug] Resolving placeholders in string: Type the User Name
2025-08-07 11:11:40.869 [Debug] Resolved string: Type the User Name
2025-08-07 11:11:40.869 [Debug] Resolving placeholders in string: {{Selectors.UserNameField}}
2025-08-07 11:11:40.869 [Debug] Resolved string: id=UserName
2025-08-07 11:11:40.869 [Debug] Resolving placeholders in string: {{TestData.UserName}}
2025-08-07 11:11:40.869 [Debug] Resolved string: Mm_azmy
2025-08-07 11:11:40.869 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-07 11:11:40.870 [Debug] Resolved string: Click on Continue Button
2025-08-07 11:11:40.871 [Debug] Resolving placeholders in string: {{Selectors.ContinueButton}}
2025-08-07 11:11:40.872 [Debug] Resolved string: id=btn
2025-08-07 11:11:40.872 [Debug] Resolving placeholders in string: Type the Password
2025-08-07 11:11:40.872 [Debug] Resolved string: Type the Password
2025-08-07 11:11:40.872 [Debug] Resolving placeholders in string: {{Selectors.PasswordField}}
2025-08-07 11:11:40.872 [Debug] Resolved string: id=Password
2025-08-07 11:11:40.873 [Debug] Resolving placeholders in string: $('[{{Selectors.PasswordField}}]').val('{{TestData.Password}}')
2025-08-07 11:11:40.875 [Debug] Resolved string: $('[id=Password]').val('Asdf2025')
2025-08-07 11:11:40.875 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-07 11:11:40.875 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-07 11:11:40.875 [Debug] Resolving placeholders in string: {{Selectors.ChallengeQuestionField}}
2025-08-07 11:11:40.875 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-07 11:11:40.875 [Debug] Resolving placeholders in string: {{TestData.ChallengeAnswer}}
2025-08-07 11:11:40.875 [Debug] Resolved string: armada
2025-08-07 11:11:40.875 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-07 11:11:40.875 [Debug] Resolved string: Click on the Login Button
2025-08-07 11:11:40.875 [Debug] Resolving placeholders in string: {{Selectors.LoginAuthButton}}
2025-08-07 11:11:40.875 [Debug] Resolved string: id=LoginAuthBtn
2025-08-07 11:11:40.876 [Info] Successfully resolved parameters for test case: Login
2025-08-07 11:11:40.876 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-07 11:11:40.876 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-07 11:11:40.876 [Debug] Resolving placeholders in string: id=finish
2025-08-07 11:11:40.876 [Debug] Resolved string: id=finish
2025-08-07 11:11:40.877 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-07 11:11:40.877 [Debug] Resolved string: Click on Login Button
2025-08-07 11:11:40.877 [Debug] Resolving placeholders in string: id=LoginBtn
2025-08-07 11:11:40.877 [Debug] Resolved string: id=LoginBtn
2025-08-07 11:11:40.877 [Debug] Resolving placeholders in string: Type the User Name
2025-08-07 11:11:40.878 [Debug] Resolved string: Type the User Name
2025-08-07 11:11:40.878 [Debug] Resolving placeholders in string: id=UserName
2025-08-07 11:11:40.878 [Debug] Resolved string: id=UserName
2025-08-07 11:11:40.878 [Debug] Resolving placeholders in string: Mm_azmy
2025-08-07 11:11:40.878 [Debug] Resolved string: Mm_azmy
2025-08-07 11:11:40.878 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-07 11:11:40.878 [Debug] Resolved string: Click on Continue Button
2025-08-07 11:11:40.879 [Debug] Resolving placeholders in string: id=btn
2025-08-07 11:11:40.879 [Debug] Resolved string: id=btn
2025-08-07 11:11:40.879 [Debug] Resolving placeholders in string: Type the Password
2025-08-07 11:11:40.879 [Debug] Resolved string: Type the Password
2025-08-07 11:11:40.880 [Debug] Resolving placeholders in string: id=Password
2025-08-07 11:11:40.880 [Debug] Resolved string: id=Password
2025-08-07 11:11:40.880 [Debug] Resolving placeholders in string: $('[id=Password]').val('Asdf2025')
2025-08-07 11:11:40.880 [Debug] Resolved string: $('[id=Password]').val('Asdf2025')
2025-08-07 11:11:40.880 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-07 11:11:40.881 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-07 11:11:40.881 [Debug] Resolving placeholders in string: id=ChallengeQuestionAnswer
2025-08-07 11:11:40.881 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-07 11:11:40.881 [Debug] Resolving placeholders in string: armada
2025-08-07 11:11:40.881 [Debug] Resolved string: armada
2025-08-07 11:11:40.881 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-07 11:11:40.882 [Debug] Resolved string: Click on the Login Button
2025-08-07 11:11:40.882 [Debug] Resolving placeholders in string: id=LoginAuthBtn
2025-08-07 11:11:40.882 [Debug] Resolved string: id=LoginAuthBtn
2025-08-07 11:11:40.882 [Debug] Resolving placeholders in string: Click on change later button on the password expiration popup message
2025-08-07 11:11:40.882 [Debug] Resolved string: Click on change later button on the password expiration popup message
2025-08-07 11:11:40.883 [Debug] Resolving placeholders in string: {{Selectors.popupCancel}}
2025-08-07 11:11:40.883 [Debug] Resolved string: id=popup_cancel
2025-08-07 11:11:40.884 [Debug] Resolving placeholders in string: Click on biometric alert
2025-08-07 11:11:40.884 [Debug] Resolved string: Click on biometric alert
2025-08-07 11:11:40.884 [Debug] Resolving placeholders in string: {{Selectors.BioMetricAlert}}
2025-08-07 11:11:40.884 [Debug] Resolved string: css=#popbuttons > div > button.btn.Cancel.back_gradient2
2025-08-07 11:11:40.884 [Debug] Resolving placeholders in string: Execute JS code to activate token
2025-08-07 11:11:40.884 [Debug] Resolved string: Execute JS code to activate token
2025-08-07 11:11:40.884 [Debug] Resolving placeholders in string: Globals.ActivationState = 'ACTIVATED';
2025-08-07 11:11:40.885 [Debug] Resolved string: Globals.ActivationState = 'ACTIVATED';
2025-08-07 11:11:40.885 [Debug] Resolving placeholders in string: Click on Cards option on the side menu
2025-08-07 11:11:40.885 [Debug] Resolved string: Click on Cards option on the side menu
2025-08-07 11:11:40.885 [Debug] Resolving placeholders in string: {{Selectors.CardsButton}}
2025-08-07 11:11:40.885 [Debug] Resolved string: xpath=//div[@class='mainmenuitems']//label[contains(text(), 'Cards')]
2025-08-07 11:11:40.885 [Debug] Resolving placeholders in string: Click on Card Services
2025-08-07 11:11:40.885 [Debug] Resolved string: Click on Card Services
2025-08-07 11:11:40.885 [Debug] Resolving placeholders in string: {{Selectors.CardServices}}
2025-08-07 11:11:40.886 [Debug] Resolved string: xpath=//div[@class='submenuitem']//label[contains(text(), 'Card services')]
2025-08-07 11:11:40.886 [Debug] Resolving placeholders in string: Click on ATM dispute
2025-08-07 11:11:40.887 [Debug] Resolved string: Click on ATM dispute
2025-08-07 11:11:40.888 [Debug] Resolving placeholders in string: {{Selectors.ATMdisputeButton}}
2025-08-07 11:11:40.888 [Debug] Resolved string: id=ATMDispute
2025-08-07 11:11:40.888 [Debug] Resolving placeholders in string: Click on dispute type
2025-08-07 11:11:40.888 [Debug] Resolved string: Click on dispute type
2025-08-07 11:11:40.888 [Debug] Resolving placeholders in string: {{Selectors.SelectDisputeType}}
2025-08-07 11:11:40.888 [Debug] Resolved string: xpath=//div[@class='details_container']//span[normalize-space(text())='Select...']
2025-08-07 11:11:40.888 [Debug] Resolving placeholders in string: Choose dispute type
2025-08-07 11:11:40.889 [Debug] Resolved string: Choose dispute type
2025-08-07 11:11:40.889 [Debug] Resolving placeholders in string: {{Selectors.DisputeType}}
2025-08-07 11:11:40.889 [Debug] Resolved string: xpath=//div[@class='details_container']//span[normalize-space(text())='Withdrawal did not dispense']
2025-08-07 11:11:40.890 [Debug] Resolving placeholders in string: Click on Select Cards button
2025-08-07 11:11:40.890 [Debug] Resolved string: Click on Select Cards button
2025-08-07 11:11:40.890 [Debug] Resolving placeholders in string: {{Selectors.SelectButton}}
2025-08-07 11:11:40.890 [Debug] Resolved string: xpath=//div[@class='details_container']//button[normalize-space(text())='Select']
2025-08-07 11:11:40.890 [Debug] Resolving placeholders in string: Choose a card with ATM transactions
2025-08-07 11:11:40.890 [Debug] Resolved string: Choose a card with ATM transactions
2025-08-07 11:11:40.891 [Debug] Resolving placeholders in string: {{Selectors.CardWithTransactions}}
2025-08-07 11:11:40.891 [Debug] Resolved string: xpath=//div[@class='module_list_container cardData']//h4[normalize-space(text())='4204XXXXXXXX2562']
2025-08-07 11:11:40.892 [Debug] Resolving placeholders in string: Click on select a transaction
2025-08-07 11:11:40.892 [Debug] Resolved string: Click on select a transaction
2025-08-07 11:11:40.893 [Debug] Resolving placeholders in string: {{Selectors.SelectTransactionButton}}
2025-08-07 11:11:40.893 [Debug] Resolved string: xpath=//div[@class='details_container']//button[normalize-space(text())='Select Transaction']
2025-08-07 11:11:40.893 [Debug] Resolving placeholders in string: Choose a transaction
2025-08-07 11:11:40.893 [Debug] Resolved string: Choose a transaction
2025-08-07 11:11:40.893 [Debug] Resolving placeholders in string: {{Selectors.Transaction}}
2025-08-07 11:11:40.893 [Debug] Resolved string: css=#Transaction > div:nth-child(1) > div.list_header
2025-08-07 11:11:40.894 [Debug] Resolving placeholders in string: Click on continue
2025-08-07 11:11:40.894 [Debug] Resolved string: Click on continue
2025-08-07 11:11:40.894 [Debug] Resolving placeholders in string: {{Selectors.ContinueButtonPilot1}}
2025-08-07 11:11:40.894 [Debug] Resolved string: xpath=//div[@class='details_container']//button[normalize-space(text())='Continue']
2025-08-07 11:11:40.894 [Debug] Resolving placeholders in string: Enter the disputed amount
2025-08-07 11:11:40.894 [Debug] Resolved string: Enter the disputed amount
2025-08-07 11:11:40.894 [Debug] Resolving placeholders in string: {{Selectors.DisputedAmmountField}}
2025-08-07 11:11:40.894 [Debug] Resolved string: xpath=//div[@class='details_container']//input[normalize-space(@placeholder)='Enter Disputed amount']
2025-08-07 11:11:40.895 [Debug] Resolving placeholders in string: 0
2025-08-07 11:11:40.895 [Debug] Resolved string: 0
2025-08-07 11:11:40.895 [Debug] Resolving placeholders in string: Enter the dispute note
2025-08-07 11:11:40.895 [Debug] Resolved string: Enter the dispute note
2025-08-07 11:11:40.896 [Debug] Resolving placeholders in string: {{Selectors.DisputeNoteField}}
2025-08-07 11:11:40.896 [Debug] Resolved string: xpath=//div[@class='details_container']//textarea[normalize-space(@placeholder)='']
2025-08-07 11:11:40.896 [Debug] Resolving placeholders in string: This is a test for the feature by the dev team. Please ignore this request.
2025-08-07 11:11:40.897 [Debug] Resolved string: This is a test for the feature by the dev team. Please ignore this request.
2025-08-07 11:11:40.897 [Debug] Resolving placeholders in string: Click on the agree terms checkbox
2025-08-07 11:11:40.897 [Debug] Resolved string: Click on the agree terms checkbox
2025-08-07 11:11:40.897 [Debug] Resolving placeholders in string: {{Selectors.TermsCheckbox}}
2025-08-07 11:11:40.897 [Debug] Resolved string: xpath=//div[@class='details_container']//input[@type='checkbox']
2025-08-07 11:11:40.897 [Debug] Resolving placeholders in string: Click on continue button
2025-08-07 11:11:40.898 [Debug] Resolved string: Click on continue button
2025-08-07 11:11:40.898 [Debug] Resolving placeholders in string: {{Selectors.ContinueButtonPilot2}}
2025-08-07 11:11:40.898 [Debug] Resolved string: xpath=//div[@class='details_container']//button[normalize-space(text())='Confirm']
2025-08-07 11:11:40.898 [Debug] Resolving placeholders in string: Click on confirm button
2025-08-07 11:11:40.899 [Debug] Resolved string: Click on confirm button
2025-08-07 11:11:40.899 [Debug] Resolving placeholders in string: {{Selectors.ConfirmDispute}}
2025-08-07 11:11:40.899 [Debug] Resolved string: id=DisputePreConfirmationBtn
2025-08-07 11:11:40.899 [Info] Successfully resolved parameters for test case: Complete ATM dispute request
2025-08-07 11:11:40.899 [Debug] Loading test case: WorkSet01/TestCases/03.Beneficiaries/Add/TC-34 AddinglocalBen.json
2025-08-07 11:11:40.899 [Info] Loading test case from: WorkSet01/TestCases/03.Beneficiaries/Add/TC-34 AddinglocalBen.json
2025-08-07 11:11:40.900 [Info] Attempting to load configuration file: WorkSet01/TestCases/03.Beneficiaries/Add/TC-34 AddinglocalBen.json
2025-08-07 11:11:40.900 [Info] Successfully loaded configuration file: WorkSet01/TestCases/03.Beneficiaries/Add/TC-34 AddinglocalBen.json
2025-08-07 11:11:40.901 [Info] No filtering configuration found. Including test case 'Add local beneficiary with valid data '.
2025-08-07 11:11:40.901 [Debug] Resolving parameters for test case: Add local beneficiary with valid data 
2025-08-07 11:11:40.901 [Debug] Merging params
2025-08-07 11:11:40.901 [Debug] Merged basic params
2025-08-07 11:11:40.901 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-07 11:11:40.902 [Debug] Merged SpecialEnvParams params
2025-08-07 11:11:40.902 [Debug] Merged params
2025-08-07 11:11:40.902 [Debug] Loading parameters from reference file: WorkSet01/Params/VerifictionParams.json
2025-08-07 11:11:40.902 [Info] Attempting to load configuration file: WorkSet01/Params/VerifictionParams.json
2025-08-07 11:11:40.903 [Info] Successfully loaded configuration file: WorkSet01/Params/VerifictionParams.json
2025-08-07 11:11:40.903 [Debug] Merging params
2025-08-07 11:11:40.903 [Debug] Merged basic params
2025-08-07 11:11:40.903 [Debug] Merged params
2025-08-07 11:11:40.904 [Debug] Merging params
2025-08-07 11:11:40.904 [Debug] Merging params
2025-08-07 11:11:40.904 [Debug] Resolving placeholders in test steps for test case: Add local beneficiary with valid data 
2025-08-07 11:11:40.904 [Debug] Resolving TestCaseReference in step: 
2025-08-07 11:11:40.905 [Info] Loading test case from: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:11:40.905 [Info] Attempting to load configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:11:40.905 [Info] Successfully loaded configuration file: WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json
2025-08-07 11:11:40.906 [Debug] Resolving parameters for test case: Login
2025-08-07 11:11:40.906 [Debug] Merging params
2025-08-07 11:11:40.906 [Debug] Merged basic params
2025-08-07 11:11:40.907 [Debug] SpecialEnvParams were found and will start to merge them
2025-08-07 11:11:40.908 [Debug] Merged SpecialEnvParams params
2025-08-07 11:11:40.908 [Debug] Merged params
2025-08-07 11:11:40.908 [Debug] Loading parameters from reference file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:11:40.908 [Info] Attempting to load configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:11:40.908 [Info] Successfully loaded configuration file: WorkSet01/Params/LoginTestCaseParams.json
2025-08-07 11:11:40.909 [Debug] Merging params
2025-08-07 11:11:40.909 [Debug] Merged basic params
2025-08-07 11:11:40.909 [Debug] Merged params
2025-08-07 11:11:40.909 [Debug] Merging params
2025-08-07 11:11:40.909 [Debug] Merging params
2025-08-07 11:11:40.909 [Debug] Resolving placeholders in test steps for test case: Login
2025-08-07 11:11:40.909 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-07 11:11:40.910 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-07 11:11:40.910 [Debug] Resolving placeholders in string: {{Selectors.FinishButton}}
2025-08-07 11:11:40.910 [Debug] Resolved string: id=finish
2025-08-07 11:11:40.910 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-07 11:11:40.910 [Debug] Resolved string: Click on Login Button
2025-08-07 11:11:40.910 [Debug] Resolving placeholders in string: {{Selectors.LoginButton}}
2025-08-07 11:11:40.910 [Debug] Resolved string: id=LoginBtn
2025-08-07 11:11:40.911 [Debug] Resolving placeholders in string: Type the User Name
2025-08-07 11:11:40.911 [Debug] Resolved string: Type the User Name
2025-08-07 11:11:40.911 [Debug] Resolving placeholders in string: {{Selectors.UserNameField}}
2025-08-07 11:11:40.911 [Debug] Resolved string: id=UserName
2025-08-07 11:11:40.911 [Debug] Resolving placeholders in string: {{TestData.UserName}}
2025-08-07 11:11:40.911 [Debug] Resolved string: mahmoudsayed022
2025-08-07 11:11:40.911 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-07 11:11:40.912 [Debug] Resolved string: Click on Continue Button
2025-08-07 11:11:40.912 [Debug] Resolving placeholders in string: {{Selectors.ContinueButton}}
2025-08-07 11:11:40.912 [Debug] Resolved string: id=btn
2025-08-07 11:11:40.912 [Debug] Resolving placeholders in string: Type the Password
2025-08-07 11:11:40.912 [Debug] Resolved string: Type the Password
2025-08-07 11:11:40.912 [Debug] Resolving placeholders in string: {{Selectors.PasswordField}}
2025-08-07 11:11:40.912 [Debug] Resolved string: id=Password
2025-08-07 11:11:40.913 [Debug] Resolving placeholders in string: $('[{{Selectors.PasswordField}}]').val('{{TestData.Password}}')
2025-08-07 11:11:40.913 [Debug] Resolved string: $('[id=Password]').val('Password1')
2025-08-07 11:11:40.913 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-07 11:11:40.913 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-07 11:11:40.913 [Debug] Resolving placeholders in string: {{Selectors.ChallengeQuestionField}}
2025-08-07 11:11:40.913 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-07 11:11:40.913 [Debug] Resolving placeholders in string: {{TestData.ChallengeAnswer}}
2025-08-07 11:11:40.914 [Debug] Resolved string: bmw
2025-08-07 11:11:40.914 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-07 11:11:40.914 [Debug] Resolved string: Click on the Login Button
2025-08-07 11:11:40.914 [Debug] Resolving placeholders in string: {{Selectors.LoginAuthButton}}
2025-08-07 11:11:40.914 [Debug] Resolved string: id=LoginAuthBtn
2025-08-07 11:11:40.914 [Info] Successfully resolved parameters for test case: Login
2025-08-07 11:11:40.914 [Debug] Resolving placeholders in string: Click on Finish Button to skip the demo
2025-08-07 11:11:40.914 [Debug] Resolved string: Click on Finish Button to skip the demo
2025-08-07 11:11:40.915 [Debug] Resolving placeholders in string: id=finish
2025-08-07 11:11:40.915 [Debug] Resolved string: id=finish
2025-08-07 11:11:40.915 [Debug] Resolving placeholders in string: Click on Login Button
2025-08-07 11:11:40.915 [Debug] Resolved string: Click on Login Button
2025-08-07 11:11:40.915 [Debug] Resolving placeholders in string: id=LoginBtn
2025-08-07 11:11:40.915 [Debug] Resolved string: id=LoginBtn
2025-08-07 11:11:40.915 [Debug] Resolving placeholders in string: Type the User Name
2025-08-07 11:11:40.915 [Debug] Resolved string: Type the User Name
2025-08-07 11:11:40.916 [Debug] Resolving placeholders in string: id=UserName
2025-08-07 11:11:40.916 [Debug] Resolved string: id=UserName
2025-08-07 11:11:40.916 [Debug] Resolving placeholders in string: mahmoudsayed022
2025-08-07 11:11:40.916 [Debug] Resolved string: mahmoudsayed022
2025-08-07 11:11:40.916 [Debug] Resolving placeholders in string: Click on Continue Button
2025-08-07 11:11:40.916 [Debug] Resolved string: Click on Continue Button
2025-08-07 11:11:40.917 [Debug] Resolving placeholders in string: id=btn
2025-08-07 11:11:40.917 [Debug] Resolved string: id=btn
2025-08-07 11:11:40.917 [Debug] Resolving placeholders in string: Type the Password
2025-08-07 11:11:40.917 [Debug] Resolved string: Type the Password
2025-08-07 11:11:40.917 [Debug] Resolving placeholders in string: id=Password
2025-08-07 11:11:40.917 [Debug] Resolved string: id=Password
2025-08-07 11:11:40.917 [Debug] Resolving placeholders in string: $('[id=Password]').val('Password1')
2025-08-07 11:11:40.917 [Debug] Resolved string: $('[id=Password]').val('Password1')
2025-08-07 11:11:40.918 [Debug] Resolving placeholders in string: Type the answer for the Challenge Question
2025-08-07 11:11:40.918 [Debug] Resolved string: Type the answer for the Challenge Question
2025-08-07 11:11:40.918 [Debug] Resolving placeholders in string: id=ChallengeQuestionAnswer
2025-08-07 11:11:40.918 [Debug] Resolved string: id=ChallengeQuestionAnswer
2025-08-07 11:11:40.918 [Debug] Resolving placeholders in string: bmw
2025-08-07 11:11:40.918 [Debug] Resolved string: bmw
2025-08-07 11:11:40.918 [Debug] Resolving placeholders in string: Click on the Login Button
2025-08-07 11:11:40.918 [Debug] Resolved string: Click on the Login Button
2025-08-07 11:11:40.919 [Debug] Resolving placeholders in string: id=LoginAuthBtn
2025-08-07 11:11:40.919 [Debug] Resolved string: id=LoginAuthBtn
2025-08-07 11:11:40.919 [Debug] Resolving placeholders in string: Click on change later button on the password expiration popup message
2025-08-07 11:11:40.920 [Debug] Resolved string: Click on change later button on the password expiration popup message
2025-08-07 11:11:40.920 [Debug] Resolving placeholders in string: {{Selectors.popupCancel}}
2025-08-07 11:11:40.921 [Debug] Resolved string: id=popup_cancel
2025-08-07 11:11:40.921 [Debug] Resolving placeholders in string: Click on biometric alert
2025-08-07 11:11:40.921 [Debug] Resolved string: Click on biometric alert
2025-08-07 11:11:40.921 [Debug] Resolving placeholders in string: {{Selectors.BioMetricAlert}}
2025-08-07 11:11:40.922 [Debug] Resolved string: css=#popbuttons > div > button.btn.Cancel.back_gradient2
2025-08-07 11:11:40.922 [Debug] Resolving placeholders in string: Execute JS code to activate token
2025-08-07 11:11:40.922 [Debug] Resolved string: Execute JS code to activate token
2025-08-07 11:11:40.922 [Debug] Resolving placeholders in string: Globals.ActivationState = 'ACTIVATED';
2025-08-07 11:11:40.923 [Debug] Resolved string: Globals.ActivationState = 'ACTIVATED';
2025-08-07 11:11:40.923 [Debug] Resolving placeholders in string: Click on Beneficiaries
2025-08-07 11:11:40.923 [Debug] Resolved string: Click on Beneficiaries
2025-08-07 11:11:40.923 [Debug] Resolving placeholders in string: {{Selectors.BeneficiariesToButton}}
2025-08-07 11:11:40.923 [Debug] Resolved string: xpath=//div[@class='mainmenuitems']//label[contains(text(), 'Beneficiaries')]
2025-08-07 11:11:40.923 [Debug] Resolving placeholders in string: Click on Local Transfer option
2025-08-07 11:11:40.923 [Debug] Resolved string: Click on Local Transfer option
2025-08-07 11:11:40.924 [Debug] Resolving placeholders in string: {{Selectors.LocalTransfer}}
2025-08-07 11:11:40.924 [Debug] Resolved string: xpath=//div[@class='submenuitem']//label[contains(text(), 'Local Transfer')]
2025-08-07 11:11:40.924 [Debug] Resolving placeholders in string: Click on Add Beneficiary
2025-08-07 11:11:40.924 [Debug] Resolved string: Click on Add Beneficiary
2025-08-07 11:11:40.924 [Debug] Resolving placeholders in string: {{Selectors.AddLocalBenBTn}}
2025-08-07 11:11:40.924 [Debug] Resolved string: id=AddLocalBtn
2025-08-07 11:11:40.924 [Debug] Resolving placeholders in string: Type Account number
2025-08-07 11:11:40.924 [Debug] Resolved string: Type Account number
2025-08-07 11:11:40.925 [Debug] Resolving placeholders in string: {{Selectors.AccountNumberInput}}
2025-08-07 11:11:40.925 [Debug] Resolved string: id=BenFAccountNum
2025-08-07 11:11:40.926 [Debug] Resolving placeholders in string: **********
2025-08-07 11:11:40.926 [Debug] Resolved string: **********
2025-08-07 11:11:40.926 [Debug] Resolving placeholders in string: Type the Nick Name
2025-08-07 11:11:40.926 [Debug] Resolved string: Type the Nick Name
2025-08-07 11:11:40.926 [Debug] Resolving placeholders in string: {{Selectors.NickNameInput}}
2025-08-07 11:11:40.926 [Debug] Resolved string: id=BenfNickName
2025-08-07 11:11:40.926 [Debug] Resolving placeholders in string: eedhefdet
2025-08-07 11:11:40.927 [Debug] Resolved string: eedhefdet
2025-08-07 11:11:40.927 [Debug] Resolving placeholders in string: Type the  Beneficiary Full Name
2025-08-07 11:11:40.927 [Debug] Resolved string: Type the  Beneficiary Full Name
2025-08-07 11:11:40.927 [Debug] Resolving placeholders in string: {{Selectors.BeneficiaryFullNameInput}}
2025-08-07 11:11:40.927 [Debug] Resolved string: id=BenFName
2025-08-07 11:11:40.927 [Debug] Resolving placeholders in string: rmrldcrt
2025-08-07 11:11:40.927 [Debug] Resolved string: rmrldcrt
2025-08-07 11:11:40.927 [Debug] Resolving placeholders in string: select bank name
2025-08-07 11:11:40.927 [Debug] Resolved string: select bank name
2025-08-07 11:11:40.928 [Debug] Resolving placeholders in string: {{Selectors.BankNameSelect}}
2025-08-07 11:11:40.928 [Debug] Resolved string: id=SelectBankName
2025-08-07 11:11:40.928 [Debug] Resolving placeholders in string: select bank option
2025-08-07 11:11:40.928 [Debug] Resolved string: select bank option
2025-08-07 11:11:40.928 [Debug] Resolving placeholders in string: {{Selectors.BankOption}}
2025-08-07 11:11:40.929 [Debug] Resolved string: css=#Banks > div > div:nth-child(1) > span
2025-08-07 11:11:40.929 [Debug] Resolving placeholders in string: click on currency dropdown list
2025-08-07 11:11:40.929 [Debug] Resolved string: click on currency dropdown list
2025-08-07 11:11:40.929 [Debug] Resolving placeholders in string: {{Selectors.currencySelect}}
2025-08-07 11:11:40.929 [Debug] Resolved string: id=SelectCurrency_Name
2025-08-07 11:11:40.929 [Debug] Resolving placeholders in string: select currency option 
2025-08-07 11:11:40.930 [Debug] Resolved string: select currency option 
2025-08-07 11:11:40.930 [Debug] Resolving placeholders in string: {{Selectors.currencyoptionEGP}}
2025-08-07 11:11:40.930 [Debug] Resolved string: css=#wginsAddLocalBeneficiary_Default > div > div > div.section_body > div > div.input_group.retail > div.select_warpper > div.options.back_white_solid.dropdown-active.d-block > div > div:nth-child(1)
2025-08-07 11:11:40.930 [Debug] Resolving placeholders in string: Click on continue
2025-08-07 11:11:40.930 [Debug] Resolved string: Click on continue
2025-08-07 11:11:40.930 [Debug] Resolving placeholders in string: {{Selectors.SaveBtn}}
2025-08-07 11:11:40.930 [Debug] Resolved string: id=SavaBtn
2025-08-07 11:11:40.930 [Debug] Resolving placeholders in string: Click on continue
2025-08-07 11:11:40.931 [Debug] Resolved string: Click on continue
2025-08-07 11:11:40.931 [Debug] Resolving placeholders in string: {{Selectors.SecondContinueBen}}
2025-08-07 11:11:40.931 [Debug] Resolved string: id=DelLocBenSumContinue
2025-08-07 11:11:40.931 [Debug] Resolving placeholders in string: Enter Token number
2025-08-07 11:11:40.932 [Debug] Resolved string: Enter Token number
2025-08-07 11:11:40.932 [Debug] Resolving placeholders in string: {{Selectors.TokenInput}}
2025-08-07 11:11:40.932 [Debug] Resolved string: id=TokenNUMBER
2025-08-07 11:11:40.932 [Debug] Resolving placeholders in string: 123456
2025-08-07 11:11:40.932 [Debug] Resolved string: 123456
2025-08-07 11:11:40.932 [Debug] Resolving placeholders in string: Click on confirm and show the confirmation page
2025-08-07 11:11:40.933 [Debug] Resolved string: Click on confirm and show the confirmation page
2025-08-07 11:11:40.933 [Debug] Resolving placeholders in string: {{Selectors.btnTokenConfirm}}
2025-08-07 11:11:40.933 [Debug] Resolved string: id=btnTokenConfirm
2025-08-07 11:11:40.933 [Info] Successfully resolved parameters for test case: Add local beneficiary with valid data 
2025-08-07 11:11:40.933 [Info] Attempting to load configuration file: ExcludedTestData.json
2025-08-07 11:11:40.933 [Info] Successfully loaded configuration file: ExcludedTestData.json
2025-08-07 11:11:40.935 [Info] Retrieved 25 test cases.
2025-08-07 11:11:40.936 [Info] Total test cases found: 25
2025-08-07 11:11:41.036 [Info] Starting test case execution: Click Block card
2025-08-07 11:11:41.049 [Info] Executing test case: Click Block card
2025-08-07 11:11:41.050 [Info] Using browser: System.String[] and BaseUrl: https://digitalbanking.ebseg.com/DigitalBankingCustom2/eBankApplication/ePortal5Core/ePortal5.htm?Menu=New#/EN/Landing
2025-08-07 11:11:41.053 [Info] Initializing WebDriver for browser: Chrome
2025-08-07 11:11:41.054 [Debug] Setting up Chrome WebDriver.
2025-08-07 11:11:44.983 [Info] WebDriver initialized and window maximized.
2025-08-07 11:11:59.425 [Info] Navigated to BaseUrl: https://digitalbanking.ebseg.com/DigitalBankingCustom2/eBankApplication/ePortal5Core/ePortal5.htm?Menu=New#/EN/Landing
2025-08-07 11:11:59.439 [Debug] Executing step: click on target: id=finish
2025-08-07 11:11:59.440 [Debug] Waiting for loading spinner to disappear.
2025-08-07 11:11:59.442 [Debug] Parsing selector: id=LoadingSpinner
2025-08-07 11:11:59.501 [Debug] Delaying step execution by 2000 ms.
2025-08-07 11:12:01.513 [Debug] Locating element with target: id=finish
2025-08-07 11:12:01.513 [Debug] Parsing selector: id=finish
2025-08-07 11:12:04.626 [Debug] Clicking on target: id=finish
2025-08-07 11:12:04.676 [Debug] Waiting for loading spinner to disappear.
2025-08-07 11:12:04.676 [Debug] Parsing selector: id=LoadingSpinner
2025-08-07 11:12:04.863 [Info] The ScreenShot was successfully taken for Step: Click on Finish Button to skip the demo, in TestCase:Click Block card
2025-08-07 11:12:04.867 [Debug] Executing step: click on target: id=LoginBtn
2025-08-07 11:12:04.873 [Debug] Waiting for loading spinner to disappear.
2025-08-07 11:12:04.873 [Debug] Parsing selector: id=LoadingSpinner
2025-08-07 11:12:04.895 [Debug] Delaying step execution by 2000 ms.
2025-08-07 11:12:06.904 [Debug] Locating element with target: id=LoginBtn
2025-08-07 11:12:06.904 [Debug] Parsing selector: id=LoginBtn
2025-08-07 11:12:06.925 [Error] Error executing step: click on target: id=LoginBtn , CustomStatusToReportUponFailure: . Details: Can`t Find Element with target: id=LoginBtn 
2025-08-07 11:12:06.928 [Error] Error executing test case: Click Block card. Details: Can`t Find Element with target: id=LoginBtn 
2025-08-07 11:12:07.019 [Info] WebDriver instance quit.
2025-08-07 11:12:07.019 [Error] Error during test case execution: Click Block card. Details: Can`t Find Element with target: id=LoginBtn 
2025-08-07 11:12:07.162 [Info] Report generated as html
2025-08-07 11:12:11.687 [Info] Report generated as html
2025-08-07 11:12:15.772 [Info] Combined summary report generated successfully