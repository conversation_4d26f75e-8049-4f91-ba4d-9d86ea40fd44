{"TestSuiteName": "Card Services", "TestSuiteItems": [{"Type": "TestCase", "Reference": "WorkSet01/TestCases/012.CardServices/DirectDebit/130-DirectDebitRequest.json", "ParamsReference": "WorkSet01/Params/BlockCardParams.json"}, {"Type": "TestCase", "Reference": "WorkSet01/TestCases/012.CardServices/ActivateCard/121-Activate Card, check that inactive card in backend are displayed.json", "ParamsReference": "WorkSet01/Params/ActivateCardParm.json"}, {"Type": "TestCase", "Reference": "WorkSet01/TestCases/012.CardServices/BlockCard/120-ClickBlockCardInquiry.json", "ParamsReference": "WorkSet01/Params/BlockCardParams.json"}, {"Type": "TestCase", "Reference": "WorkSet01/TestCases/012.CardServices/BlockCard/125-CheckBlockedCardsList.json", "ParamsReference": "WorkSet01/Params/BlockCardParams.json"}, {"Type": "TestCase", "Reference": "WorkSet01/TestCases/012.CardServices/BlockCard/114-CardInquiry2Widgets.json", "ParamsReference": "WorkSet01/Params/CardServicesParams.json"}, {"Type": "TestCase", "Reference": "WorkSet01/TestCases/012.CardServices/BlockCard/113-CardInquiry.json", "ParamsReference": "WorkSet01/Params/CardServicesParams.json"}, {"Type": "TestCase", "Reference": "WorkSet01/TestCases/012.CardServices/ActivateCard/57-fullCardActivation.json", "ParamsReference": "WorkSet01/Params/ActivateCardParm.json"}, {"Type": "TestCase", "Reference": "WorkSet01/TestCases/012.CardServices/BlockCard/52-UnBlockCard.json", "ParamsReference": "WorkSet01/Params/BlockCardParams.json"}, {"Type": "TestCase", "Reference": "WorkSet01/TestCases/012.CardServices/BlockCard/48-CheckCardsList.json", "ParamsReference": "WorkSet01/Params/BlockCardParams.json"}, {"Type": "TestCase", "Reference": "WorkSet01/TestCases/012.CardServices/BlockCard/47-BlockCard.json", "ParamsReference": "WorkSet01/Params/BlockCardParams.json"}]}