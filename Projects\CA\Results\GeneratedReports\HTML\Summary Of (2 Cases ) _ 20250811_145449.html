<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Test Execution Report</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <div class="header">
            <img src="D:\TFS\DevWork\R and D Work\TestAutomationFramework\TestAutomationFramework\Projects\CA\Templates/CA_Logo.png" alt="Company Logo">
            <h1  >Sanity Cases Suite Suite Execution Report</h1>
            <p>Generated on: <strong>2025-08-11-14-54</strong> | Environment: <strong>UAT</strong> | Browser: <strong>Chrome</strong></p>
        </div>
        <h2  >Overall Summary</h2>
<table  class="summary-table overall-summary"  >
    <tr>
        <th>Total Suites</th>
        <th>Total Modules</th>
        <th>Total Test Cases</th>
       <!-- <th>Total Steps</th>-->
        <th>Passed</th>
        <th>Failed</th>
       <!-- <th>Skipped</th>-->
    </tr>
    <tr>
        <td>1</td>
        <td>0</td>
        <td>2</td>
        <!--<td>0</td>-->
        <td>0</td>
        <td>2</td>
       <!-- <td>{{TotalSkipped}}</td>-->
    </tr>
</table>

		<table border="1" cellpadding="5" cellspacing="0" style="border-collapse: collapse; width: 100%;">
    <thead style="background-color: #00796B; color: white;">
        <tr>
            <th>#</th>
            <th>Test Case ID</th>
            <th>Test Case Name</th>
            <th>Status</th>
            <th>Duration</th>
            <th>TestData</th>
            <th>Error/Remarks</th>
			<th>Module</th>
            <th>Suite</th>

        </tr>
    </thead>
    <tbody>
        <!-- TestCaseDetailRow -->
<tr>
    <td>1</td>
    <td>TC-114</td>
    <td>Credit cards and prepaid cards displayed on 2 widgets</td>
    <td>Failed</td>
    <td>00:08</td>
      <td>
<div><strong>UserName</strong>: mahmoudsayed022</div>
<div><strong>ChallengeAnswer</strong>: bmw</div>
</td>

    <td>Error executing step: Type the User Name.</td>


<td rowspan=2>Direct</td>
<td rowspan=2>Sanity Cases Suite</td>
</tr>
<!-- TestCaseDetailRow -->
<tr>
    <td>2</td>
    <td>TC-113</td>
    <td>All users Cards are displayed successfully</td>
    <td>Failed</td>
    <td>00:06</td>
      <td>
<div><strong>UserName</strong>: mahmoudsayed022</div>
<div><strong>ChallengeAnswer</strong>: bmw</div>
</td>

    <td>Error executing step: Click on Login Button.</td>


</tr>

    </tbody>
</table>
        <h2  >3. Sanity Cases Suite</h2>
<table  class="summary-table suite-summary"  >
    <tr>
        <th>Total Modules</th>
        <th>Total Test Cases</th>
        <th>Passed</th>
        <th>Failed</th>
      <!--  <th>Skipped</th>-->
    </tr>
    <tr>
        <td>0</td>
        <td>2</td>
        <td>0</td>
        <td>2</td>
       <!-- <td>{{SuiteSkipped}}</td>-->
    </tr>
</table>
<div class="suite-content">
    <div class="metadata">
    <div class="test-case">
        <strong>3.1.1. Credit cards and prepaid cards displayed on 2 widgets</strong><span style="font-weight: normal;"><i> (TC-114 | Sanity Cases Suite | Direct)</i></span>
    </div>
    <div class="status">
        <span>Status:</span>
        <br>
        <span class="status-failed">Failed</span>
    </div>
    <div class="status">
        <span>Exec Time (mm:ss)</span>
        <br>
        <span>00:08</span>
    </div>
</div>
<div class="metadata error" style="display: block">
    <span>Error Message:</span>
    <span class="status-failed">Error executing step: Type the User Name.</span>
</div>
<table class="test-steps-table">
    <tr>
        <th>Step</th>
        <th>Description</th>
        <th style="display: none">Result</th>
        <th style="display: none">Execution Time</th>
        <th>Screenshot</th>
    </tr>
    <tr>
    <td>1</td>
    <td>Click on Finish Button to skip the demo</td>
    <td style="display: none;">Passed</td>
    <td style="display: none;">0s</td>
    <td><img class='test-step-image' src='D:\TFS\DevWork\R and D Work\TestAutomationFramework\TestAutomationFramework\Projects\CA\Results\ScreenShots\Sanity Cases Suite\2025-08-11 14.54.18.957_Click on Finish Button to skip the demo.png' alt='Step Screenshot'></td>
</tr>
<tr>
    <td>2</td>
    <td>Click on Login Button</td>
    <td style="display: none;">Passed</td>
    <td style="display: none;">0s</td>
    <td><img class='test-step-image' src='D:\TFS\DevWork\R and D Work\TestAutomationFramework\TestAutomationFramework\Projects\CA\Results\ScreenShots\Sanity Cases Suite\2025-08-11 14.54.22.724_Click on Login Button.png' alt='Step Screenshot'></td>
</tr>

    <tr>
        <td class="status-failed" style="display:none" ; colspan="3">Error Details: Can`t Find Element with target: id=UserName </td>
    </tr>
</table>
<div class="metadata">
    <div class="test-case">
        <strong>3.1.2. All users Cards are displayed successfully</strong><span style="font-weight: normal;"><i> (TC-113 | Sanity Cases Suite | Direct)</i></span>
    </div>
    <div class="status">
        <span>Status:</span>
        <br>
        <span class="status-failed">Failed</span>
    </div>
    <div class="status">
        <span>Exec Time (mm:ss)</span>
        <br>
        <span>00:06</span>
    </div>
</div>
<div class="metadata error" style="display: block">
    <span>Error Message:</span>
    <span class="status-failed">Error executing step: Click on Login Button.</span>
</div>
<table class="test-steps-table">
    <tr>
        <th>Step</th>
        <th>Description</th>
        <th style="display: none">Result</th>
        <th style="display: none">Execution Time</th>
        <th>Screenshot</th>
    </tr>
    <tr>
    <td>1</td>
    <td>Click on Finish Button to skip the demo</td>
    <td style="display: none;">Passed</td>
    <td style="display: none;">0s</td>
    <td><img class='test-step-image' src='D:\TFS\DevWork\R and D Work\TestAutomationFramework\TestAutomationFramework\Projects\CA\Results\ScreenShots\Sanity Cases Suite\2025-08-11 14.54.39.570_Click on Finish Button to skip the demo.png' alt='Step Screenshot'></td>
</tr>

    <tr>
        <td class="status-failed" style="display:none" ; colspan="3">Error Details: Can`t Find Element with target: id=LoginBtn </td>
    </tr>
</table>

</div>

    </div>
    <div class="footer">
        <p>End of Report</p>
    </div>
</body>
</html>
