<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Test Execution Report</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <div class="header">
            <img src="D:\TFS\DevWork\R and D Work\TestAutomationFramework\TestAutomationFramework\Projects\CA\Templates/CA_Logo.png" alt="Company Logo">
            <h1  >Beneficiaries Suite Execution Report</h1>
            <p>Generated on: <strong>2025-08-14 11:02 AM</strong> | Environment: <strong>UAT</strong> | Browser: <strong>Chrome</strong> | Duration:<strong>00:00:48</strong></p>
        </div>
        <h2  >Overall Summary</h2>
<table  class="summary-table overall-summary"  >
    <tr>
        <th>Total Suites</th>
        <th>Total Modules</th>
        <th>Total Test Cases</th>
        <!-- <th>Total Steps</th>-->
        <th>Passed</th>
        <th>Failed</th>
        <th>Total Duration</th>

        <!-- <th>Skipped</th>-->
    </tr>
    <tr>
        <td>1</td>
        <td>0</td>
        <td>2</td>
        <!--<td>0</td>-->
        <td>0</td>
        <td>2</td>
        <!-- <td>{{TotalSkipped}}</td>-->
         <td>00:00:48</td>

    </tr>
</table>

		<table border="1" cellpadding="5" cellspacing="0" style="border-collapse: collapse; width: 100%;">
    <thead style="background-color: #00796B; color: white;">
        <tr>
            <th>#</th>
            <th>Test Case ID</th>
            <th>Test Case Name</th>
            <th>Status</th>
            <th>Duration</th>
            <th>TestData</th>
            <th>Error/Remarks</th>
            <th>Screenshot</th>
            <th>Suite</th>

        </tr>
    </thead>
    <tbody>
        <!-- TestCaseDetailRow -->
<tr>
    <td>1</td>
    <td>TC-145</td>
    <td>Existing Banks Inside Egypt beneficiaries displayed</td>
    <td style="color: red; font-weight: bold;">Failed</td>
    <td>00:23</td>
    <td>
<div><strong>UserName</strong>: mahmoudsayed022</div>
<div><strong>ChallengeAnswer</strong>: bmw</div>
</td>

    <td>Error executing step: Click on Continue Button.</td>
    <td><img class='test-step-image' src='D:\TFS\DevWork\R and D Work\TestAutomationFramework\TestAutomationFramework\Projects\CA\Results\ScreenShots\Beneficiaries\2025-08-14 11.02.00.732_Type the User Name.png' alt='Step Screenshot'></td>
<td rowspan=2>Beneficiaries</td>
</tr>
<!-- TestCaseDetailRow -->
<tr>
    <td>2</td>
    <td>TC-146</td>
    <td>Edit Existing beneficiary</td>
    <td style="color: red; font-weight: bold;">Failed</td>
    <td>00:25</td>
    <td>
<div><strong>UserName</strong>: sherif1234</div>
<div><strong>ChallengeAnswer</strong>: bmw</div>
</td>

    <td>Error executing step: Type the Password.</td>
    <td><img class='test-step-image' src='D:\TFS\DevWork\R and D Work\TestAutomationFramework\TestAutomationFramework\Projects\CA\Results\ScreenShots\Beneficiaries\2025-08-14 11.02.02.860_Click on Continue Button.png' alt='Step Screenshot'></td>
</tr>

    </tbody>
</table>
        <h2  >3. Beneficiaries</h2>
<table  class="summary-table suite-summary"  >
    <tr>
        <th>Total Modules</th>
        <th>Total Test Cases</th>
        <th>Passed</th>
        <th>Failed</th>
      <!--  <th>Skipped</th>-->
    </tr>
    <tr>
        <td>0</td>
        <td>2</td>
        <td>0</td>
        <td>2</td>
       <!-- <td>{{SuiteSkipped}}</td>-->
    </tr>
</table>
<div class="suite-content">
    <div class="metadata">
    <div class="test-case">
        <strong>3.1.1. Existing Banks Inside Egypt beneficiaries displayed</strong><span style="font-weight: normal;"><i> (TC-145 | Beneficiaries)</i></span>
    </div>
    <div class="status">
        <span>Status:</span>
        <br>
        <span class="status-failed">Failed</span>
    </div>
    <div class="status">
        <span>Exec Time (mm:ss)</span>
        <br>
        <span>00:23</span>
    </div>
</div>
<div class="metadata error" style="display: block">
    <span>Error Message:</span>
    <span class="status-failed">Error executing step: Click on Continue Button.</span>
</div>
<table class="test-steps-table">
    <tr>
        <th>Step</th>
        <th>Description</th>
        <th style="display: none">Result</th>
        <th style="display: none">Execution Time</th>
        <th>Screenshot</th>
    </tr>
    <tr>
    <td>1</td>
    <td>Click on Finish Button to skip the demo</td>
    <td style="display: none;">Passed</td>
    <td style="display: none;">0s</td>
    <td><img class='test-step-image' src='D:\TFS\DevWork\R and D Work\TestAutomationFramework\TestAutomationFramework\Projects\CA\Results\ScreenShots\Beneficiaries\2025-08-14 11.01.55.192_Click on Finish Button to skip the demo.png' alt='Step Screenshot'></td>
</tr>
<tr>
    <td>2</td>
    <td>Click on Login Button</td>
    <td style="display: none;">Passed</td>
    <td style="display: none;">0s</td>
    <td><img class='test-step-image' src='D:\TFS\DevWork\R and D Work\TestAutomationFramework\TestAutomationFramework\Projects\CA\Results\ScreenShots\Beneficiaries\2025-08-14 11.01.59.662_Click on Login Button.png' alt='Step Screenshot'></td>
</tr>
<tr>
    <td>3</td>
    <td>Type the User Name</td>
    <td style="display: none;">Passed</td>
    <td style="display: none;">0s</td>
    <td><img class='test-step-image' src='D:\TFS\DevWork\R and D Work\TestAutomationFramework\TestAutomationFramework\Projects\CA\Results\ScreenShots\Beneficiaries\2025-08-14 11.02.00.732_Type the User Name.png' alt='Step Screenshot'></td>
</tr>

    <tr>
        <td class="status-failed" style="display:none" ; colspan="3">Error Details: Can`t Find Element with target: id=btn </td>
    </tr>
</table>
<div class="metadata">
    <div class="test-case">
        <strong>3.1.2. Edit Existing beneficiary</strong><span style="font-weight: normal;"><i> (TC-146 | Beneficiaries)</i></span>
    </div>
    <div class="status">
        <span>Status:</span>
        <br>
        <span class="status-failed">Failed</span>
    </div>
    <div class="status">
        <span>Exec Time (mm:ss)</span>
        <br>
        <span>00:25</span>
    </div>
</div>
<div class="metadata error" style="display: block">
    <span>Error Message:</span>
    <span class="status-failed">Error executing step: Type the Password.</span>
</div>
<table class="test-steps-table">
    <tr>
        <th>Step</th>
        <th>Description</th>
        <th style="display: none">Result</th>
        <th style="display: none">Execution Time</th>
        <th>Screenshot</th>
    </tr>
    <tr>
    <td>1</td>
    <td>Click on Finish Button to skip the demo</td>
    <td style="display: none;">Passed</td>
    <td style="display: none;">0s</td>
    <td><img class='test-step-image' src='D:\TFS\DevWork\R and D Work\TestAutomationFramework\TestAutomationFramework\Projects\CA\Results\ScreenShots\Beneficiaries\2025-08-14 11.01.55.871_Click on Finish Button to skip the demo.png' alt='Step Screenshot'></td>
</tr>
<tr>
    <td>2</td>
    <td>Click on Login Button</td>
    <td style="display: none;">Passed</td>
    <td style="display: none;">0s</td>
    <td><img class='test-step-image' src='D:\TFS\DevWork\R and D Work\TestAutomationFramework\TestAutomationFramework\Projects\CA\Results\ScreenShots\Beneficiaries\2025-08-14 11.01.59.994_Click on Login Button.png' alt='Step Screenshot'></td>
</tr>
<tr>
    <td>3</td>
    <td>Type the User Name</td>
    <td style="display: none;">Passed</td>
    <td style="display: none;">0s</td>
    <td><img class='test-step-image' src='D:\TFS\DevWork\R and D Work\TestAutomationFramework\TestAutomationFramework\Projects\CA\Results\ScreenShots\Beneficiaries\2025-08-14 11.02.01.046_Type the User Name.png' alt='Step Screenshot'></td>
</tr>
<tr>
    <td>4</td>
    <td>Click on Continue Button</td>
    <td style="display: none;">Passed</td>
    <td style="display: none;">0s</td>
    <td><img class='test-step-image' src='D:\TFS\DevWork\R and D Work\TestAutomationFramework\TestAutomationFramework\Projects\CA\Results\ScreenShots\Beneficiaries\2025-08-14 11.02.02.860_Click on Continue Button.png' alt='Step Screenshot'></td>
</tr>

    <tr>
        <td class="status-failed" style="display:none" ; colspan="3">Error Details: invalid session id: session deleted as the browser has closed the connection
from disconnected: not connected to DevTools
  (Session info: chrome=139.0.7258.66)</td>
    </tr>
</table>

</div>

    </div>
    <div class="footer">
        <p>End of Report</p>
    </div>
</body>
</html>
