{"ModuleName": "Happy Points module", "TestCases": [{"TestCasePath": "WorkSet01/TestCases/021.HappyPoints/29.json", "ParamsReference": "WorkSet01/Params/HappyPointsParams.json"}, {"TestCasePath": "WorkSet01/TestCases/021.HappyPoints/31.json", "ParamsReference": "WorkSet01/Params/HappyPointsParams.json"}, {"TestCasePath": "WorkSet01/TestCases/021.HappyPoints/32.json", "ParamsReference": "WorkSet01/Params/HappyPointsParams.json"}, {"TestCasePath": "WorkSet01/TestCases/021.HappyPoints/157-MorePointsThanAvailable.json", "ParamsReference": "WorkSet01/Params/HappyPointsParams.json"}]}