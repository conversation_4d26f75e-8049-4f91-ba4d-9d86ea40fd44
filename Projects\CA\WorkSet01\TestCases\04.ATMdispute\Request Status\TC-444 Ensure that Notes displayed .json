{"TestCaseName": "Ensure that Notes displayed", "TestCaseCode": "TC-444", "Steps": [{"TestCaseReference": {"Name": "<PERSON><PERSON>", "TestCasePath": "WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json", "ParamsReference": "WorkSet01/Params/LoginTestCaseParams.json", "Params": {"TestData": {"UserName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Password": "Password2", "ChallengeAnswer": "bmw"}}}}, {"Name": "Click on Cards option on the side menu", "Command": "Click", "Target": "{{Selectors.CardsButton}}"}, {"Name": "Click on Card Services", "Command": "Click", "Target": "{{Selectors.CardServices}}"}, {"Name": "Click on request status", "Command": "click", "Target": "{{Selectors.RequestStatues}}"}, {"Name": "Choose a Dispute request", "Command": "click", "Target": "css=#RequestItem2"}, {"Name": "Ensure that notes displayed", "Command": "click", "Target": "css=#PH4775 > div:nth-child(4) > p", "ElementToValidateThatScreenLoaded": "css=#PH4775 > div:nth-child(4) > p"}]}