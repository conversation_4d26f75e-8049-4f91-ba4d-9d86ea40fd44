<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Test Execution Report</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <div class="header">
            <img src="D:\TFS\DevWork\R and D Work\TestAutomationFramework\TestAutomationFramework\Projects\CA\Templates/CA_Logo.png" alt="Company Logo">
            <h1  style="display:none;" >Cards Inquiry Suite Execution Report</h1>
            <p>Generated on: <strong>2025-08-14 11:27 AM</strong> | Environment: <strong>UAT</strong> | Browser: <strong>Chrome</strong> | Duration:<strong>00:00:22</strong></p>
        </div>
        <h2  style="display:none;" >Overall Summary</h2>
<table  class="summary-table overall-summary"  style="display:none;" >
    <tr>
        <th>Total Suites</th>
        <th>Total Modules</th>
        <th>Total Test Cases</th>
        <!-- <th>Total Steps</th>-->
        <th>Passed</th>
        <th>Failed</th>
        <th>Total Duration</th>

        <!-- <th>Skipped</th>-->
    </tr>
    <tr>
        <td>1</td>
        <td>0</td>
        <td>1</td>
        <!--<td>0</td>-->
        <td>0</td>
        <td>1</td>
        <!-- <td>{{TotalSkipped}}</td>-->
         <td>00:00:22</td>

    </tr>
</table>

		
        <h2  style="display:none;" >3. Cards Inquiry</h2>
<table  class="summary-table suite-summary"  style="display:none;" >
    <tr>
        <th>Total Modules</th>
        <th>Total Test Cases</th>
        <th>Passed</th>
        <th>Failed</th>
      <!--  <th>Skipped</th>-->
    </tr>
    <tr>
        <td>0</td>
        <td>1</td>
        <td>0</td>
        <td>1</td>
       <!-- <td>{{SuiteSkipped}}</td>-->
    </tr>
</table>
<div class="suite-content">
    <div class="metadata">
    <div class="test-case">
        <strong>3.1.1. Click Pay From</strong><span style="font-weight: normal;"><i> (TC-118 | Cards Inquiry)</i></span>
    </div>
    <div class="status">
        <span>Status:</span>
        <br>
        <span class="status-failed">Failed</span>
    </div>
    <div class="status">
        <span>Exec Time (mm:ss)</span>
        <br>
        <span>00:22</span>
    </div>
</div>
<div class="metadata error" style="display: block">
    <span>Error Message:</span>
    <span class="status-failed">Error executing step: Click on the Login Button.</span>
</div>
<table class="test-steps-table">
    <tr>
        <th>Step</th>
        <th>Description</th>
        <th style="display: none">Result</th>
        <th style="display: none">Execution Time</th>
        <th>Screenshot</th>
    </tr>
    <tr>
    <td>1</td>
    <td>Click on Finish Button to skip the demo</td>
    <td style="display: none;">Passed</td>
    <td style="display: none;">0s</td>
    <td><img class='test-step-image' src='D:\TFS\DevWork\R and D Work\TestAutomationFramework\TestAutomationFramework\Projects\CA\Results\ScreenShots\Cards Inquiry\2025-08-14 11.26.55.851_Click on Finish Button to skip the demo.png' alt='Step Screenshot'></td>
</tr>
<tr>
    <td>2</td>
    <td>Click on Login Button</td>
    <td style="display: none;">Passed</td>
    <td style="display: none;">0s</td>
    <td><img class='test-step-image' src='D:\TFS\DevWork\R and D Work\TestAutomationFramework\TestAutomationFramework\Projects\CA\Results\ScreenShots\Cards Inquiry\2025-08-14 11.26.58.871_Click on Login Button.png' alt='Step Screenshot'></td>
</tr>
<tr>
    <td>3</td>
    <td>Type the User Name</td>
    <td style="display: none;">Passed</td>
    <td style="display: none;">0s</td>
    <td><img class='test-step-image' src='D:\TFS\DevWork\R and D Work\TestAutomationFramework\TestAutomationFramework\Projects\CA\Results\ScreenShots\Cards Inquiry\2025-08-14 11.26.59.862_Type the User Name.png' alt='Step Screenshot'></td>
</tr>
<tr>
    <td>4</td>
    <td>Click on Continue Button</td>
    <td style="display: none;">Passed</td>
    <td style="display: none;">0s</td>
    <td><img class='test-step-image' src='D:\TFS\DevWork\R and D Work\TestAutomationFramework\TestAutomationFramework\Projects\CA\Results\ScreenShots\Cards Inquiry\2025-08-14 11.27.01.823_Click on Continue Button.png' alt='Step Screenshot'></td>
</tr>
<tr>
    <td>5</td>
    <td>Type the Password</td>
    <td style="display: none;">Passed</td>
    <td style="display: none;">0s</td>
    <td><img class='test-step-image' src='D:\TFS\DevWork\R and D Work\TestAutomationFramework\TestAutomationFramework\Projects\CA\Results\ScreenShots\Cards Inquiry\2025-08-14 11.27.02.950_Type the Password.png' alt='Step Screenshot'></td>
</tr>
<tr>
    <td>6</td>
    <td>Type the answer for the Challenge Question</td>
    <td style="display: none;">Passed</td>
    <td style="display: none;">0s</td>
    <td><img class='test-step-image' src='D:\TFS\DevWork\R and D Work\TestAutomationFramework\TestAutomationFramework\Projects\CA\Results\ScreenShots\Cards Inquiry\2025-08-14 11.27.04.466_Type the answer for the Challenge Question.png' alt='Step Screenshot'></td>
</tr>

    <tr>
        <td class="status-failed" style="display:none" ; colspan="3">Error Details: Can`t Find Element with target: id=LoginAuthBtn </td>
    </tr>
</table>

</div>

    </div>
    <div class="footer">
        <p>End of Report</p>
    </div>
</body>
</html>
