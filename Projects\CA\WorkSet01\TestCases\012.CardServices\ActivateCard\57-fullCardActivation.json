{"TestCaseName": "Verify that a user can successfully activate a card", "TestCaseCode": "TC-57", "Steps": [{"TestCaseReference": {"Name": "<PERSON><PERSON>", "TestCasePath": "WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json", "ParamsReference": "WorkSet01/Params/LoginTestCaseParams.json", "Params": {"TestData": {"UserName": "mahmoudsayed022", "Password": "Password1", "ChallengeAnswer": "bmw"}}}}, {"Name": "Click on change later button on the password expiration popup message", "Command": "Click", "Target": "{{Selectors.popupCancel}}", "ContinueOnError": true}, {"Name": "Click on biometric alert", "Command": "Click", "Target": "{{Selectors.BioMetricAlert}}", "ContinueOnError": true}, {"Name": "Click on Cards button from side menu", "Command": "Click", "Target": "{{Selectors.CardsButton}}"}, {"Name": "Click on Cards Services button from side menu", "Command": "Click", "Target": "{{Selectors.CardServices}}"}, {"Name": "Click on Activate Card", "Command": "Click", "Target": "{{Selectors.ActivateCard}}"}, {"Name": "Click on card that need to be  Activated", "Command": "Click", "Target": "{{Selectors.DeActivatedCard}}"}, {"Name": "Click on Continue", "Command": "Click", "Target": "{{Selectors.ContinueBtn}}"}, {"Name": "Enter New PIN", "Command": "type", "Target": "{{Selectors.NewPI<PERSON>field}}", "Value": "{{TestData.New_PIN}}"}, {"Name": "Confirm New PIN", "Command": "type", "Target": "{{Selectors.ConfirmPINfield}}", "Value": "{{TestData.New_PIN}}"}, {"Name": "Enter a valid OTP", "Command": "type", "Target": "{{Selectors.O<PERSON>field}}", "Value": "{{TestData.OTP}}"}, {"Name": "Click on Continue and showing activation is successfull", "Command": "Click", "Target": "{{Selectors.ContinueBtn2}}"}]}