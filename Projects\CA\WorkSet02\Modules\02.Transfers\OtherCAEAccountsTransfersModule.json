{"ModuleName": "Other CAE Accounts Transfers Module", "TestCases": [{"TestCasePath": "WorkSet02/TestCases/02.Transfers/OtherCAEAccountsTransfers/96-Account Has Sufficent Amount With Valid <PERSON>.json", "ParamsReference": "WorkSet02/Params/PayToParams.json"}, {"TestCasePath": "WorkSet02/TestCases/02.Transfers/OtherCAEAccountsTransfers/98-AccountHasInsufficientAmount.json", "ParamsReference": "WorkSet02/Params/PayToParams.json"}, {"TestCasePath": "WorkSet02/TestCases/02.Transfers/OtherCAEAccountsTransfers/99-AccountHasSufficentAmountButWithInValidToken.json", "ParamsReference": "WorkSet02/Params/PayToParams.json"}]}