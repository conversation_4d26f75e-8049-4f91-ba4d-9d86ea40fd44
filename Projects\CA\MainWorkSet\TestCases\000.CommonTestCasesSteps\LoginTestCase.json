﻿{
  "TestCaseName": "Login",
  "TestCaseCode": "TC-000",
  
  "Steps": [
    {
      "Name": "Click on Finish <PERSON>ton to skip the demo",
      "Command": "click",
      "Target": "{{Selectors.FinishButton}}",
      "ContinueOnError": true,
      "CustomDelayBeforeStepExecustionInMilliseconds": 2000
    },
    {
      "Name": "Click on Login Button",
      "Command": "click",
      "Target": "{{Selectors.LoginButton}}",
	  "CustomDelayBeforeStepExecustionInMilliseconds": 2000

    },
    {
      "Name": "Type the User Name",
      "Command": "type",
      "Target": "{{Selectors.UserNameField}}",
      "Value": "{{TestData.UserName}}"
    },
    {
      "Name": "Click on Continue Button",
      "Command": "click",
      "Target": "{{Selectors.ContinueButton}}"
    },
    {
      "Name": "Type the Password",
      "Command": "executescript",
      "Target": "{{Selectors.PasswordField}}",
      "Value": "$('[{{Selectors.PasswordField}}]').val('{{TestData.Password}}')"
    },
    {
      "Name": "Type the answer for the Challenge Question",
      "Command": "type",
      "Target": "{{Selectors.ChallengeQuestionField}}",
      "Value": "{{TestData.ChallengeAnswer}}"
    },
    {
      "Name": "Click on the Login Button",
      "Command": "click",
      "Target": "{{Selectors.LoginAuthButton}}"
     
    },
    {
            "Name": "Click on change later button on the password expiration popup message",
            "Command": "Click",
            "Target": "{{Selectors.popupCancel}}",
            "ContinueOnError": true
    },
    {
            "Name": "Click on biometric alert",
            "Command": "Click",
            "Target": "{{Selectors.BioMetricAlert}}",
            "ContinueOnError": true
    }
  ]
}

