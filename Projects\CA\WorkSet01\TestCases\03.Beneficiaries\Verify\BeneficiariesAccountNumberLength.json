{"TestCaseName": "Verify That Entering More Than 34 Characters In The Account/IBAN/Credit Card/Wallet Field Is Not Allowed", "TestCaseCode": "TC-39", "Steps": [{"TestCaseReference": {"Name": "<PERSON><PERSON>", "TestCasePath": "WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json", "ParamsReference": "WorkSet01/Params/LoginTestCaseParams.json"}}, {"Name": "Click on change later button on the password expiration popup message", "Command": "Click", "Target": "{{Selectors.popupCancel}}", "ContinueOnError": true}, {"Name": "Click on biometric alert", "Command": "Click", "Target": "{{Selectors.BioMetricAlert}}", "ContinueOnError": true}, {"Name": "Execute JS code to activate token", "Command": "executescript", "Value": "Globals.ActivationState = 'ACTIVATED';"}, {"Name": "Click on Beneficiaries", "Command": "click", "Target": "{{Selectors.BeneficiariesToButt<PERSON>}}"}, {"Name": "Click on Local Transfer option", "Command": "Click", "Target": "{{Selectors.LocalTransfer}}"}, {"Name": "Click on Add Beneficiary", "Command": "Click", "Target": "{{Selectors.AddLocalBenBTn}}"}, {"Name": "Type 35 digits.", "Command": "type", "Target": "{{Selectors.AccountNumberInput}}", "Value": "11111111111111111111111111111153210"}, {"Name": "Verify the nubmer of digits is 34 digit", "Command": "lengthequals", "Target": "{{Selectors.AccountNumberInput}}", "Value": "1111111111111111111111111111115321"}]}