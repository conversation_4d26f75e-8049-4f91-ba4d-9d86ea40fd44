{"ModuleName": "Card services module", "TestCases": [{"TestCasePath": "WorkSet01/TestCases/012.CardServices/TC-373 Ensure that instruction displayed.json", "ParamsReference": "WorkSet01/Params/BlockCardParams.json"}, {"TestCasePath": "WorkSet01/TestCases/012.CardServices/DirectDebit/55-DirectDebitStaff.json", "ParamsReference": "WorkSet01/Params/BlockCardParams.json"}, {"TestCasePath": "WorkSet01/TestCases/012.CardServices/ActivateCard/57-fullCardActivation.json", "ParamsReference": "WorkSet01/Params/ActivateCardParm.json"}, {"TestCasePath": "WorkSet01/TestCases/012.CardServices/DirectDebit/130-DirectDebitRequest.json", "ParamsReference": "WorkSet01/Params/BlockCardParams.json"}, {"TestCasePath": "WorkSet01/TestCases/012.CardServices/ActivateCard/121-Activate Card, check that inactive card in backend are displayed.json", "ParamsReference": "WorkSet01/Params/ActivateCardParm.json"}, {"TestCasePath": "WorkSet01/TestCases/012.CardServices/ActivateCard/58-incorrect OTP prevents card activation.json", "ParamsReference": "WorkSet01/Params/ActivateCardParm.json"}, {"TestCasePath": "WorkSet01/TestCases/012.CardServices/BlockCard/48-CheckCardsList.json", "ParamsReference": "WorkSet01/Params/BlockCardParams.json"}, {"TestCasePath": "WorkSet01/TestCases/012.CardServices/BlockCard/125-CheckBlockedCardsList.json", "ParamsReference": "WorkSet01/Params/BlockCardParams.json"}, {"TestCasePath": "WorkSet01/TestCases/012.CardServices/BlockCard/113-CardInquiry.json", "ParamsReference": "WorkSet01/Params/CardServicesParams.json"}, {"TestCasePath": "WorkSet01/TestCases/012.CardServices/BlockCard/114-CardInquiry2Widgets.json", "ParamsReference": "WorkSet01/Params/CardServicesParams.json"}, {"TestCasePath": "WorkSet01/TestCases/012.CardServices/BlockCard/52-UnBlockCard.json", "ParamsReference": "WorkSet01/Params/BlockCardParams.json"}, {"TestCasePath": "WorkSet01/TestCases/012.CardServices/BlockCard/120-ClickBlockCardInquiry.json", "ParamsReference": "WorkSet01/Params/BlockCardParams.json"}, {"TestCasePath": "WorkSet01/TestCases/012.CardServices/Sprint3/115-click on card number.json", "ParamsReference": "WorkSet01/Params/CardServicesParams.json"}, {"TestCasePath": "WorkSet01/TestCases/02.Transfers/Prepaid Cards/TC-136 Prepaid Card.json", "ParamsReference": "WorkSet01/Params/PayToParams.json"}]}