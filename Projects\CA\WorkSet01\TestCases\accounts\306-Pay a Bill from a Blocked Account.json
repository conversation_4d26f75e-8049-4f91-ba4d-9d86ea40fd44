{"TestCaseName": "Pay a Bill from a Blocked Account", "TestCaseCode": "TC-306", "Steps": [{"TestCaseReference": {"Name": "<PERSON><PERSON>", "TestCasePath": "WorkSet01/TestCases/00.CommonTestCasesSteps/LoginTestCase.json", "ParamsReference": "WorkSet01/Params/LoginTestCaseParams.json"}}, {"Name": "Click on change later button on the password expiration popup message", "Command": "Click", "Target": "{{Selectors.popupCancel}}", "ContinueOnError": true}, {"Name": "Click on biometric alert", "Command": "Click", "Target": "{{Selectors.BioMetricAlert}}", "ContinueOnError": true}, {"Name": "Execute JS code to activate token", "Command": "executescript", "Value": "Globals.ActivationState = 'ACTIVATED';"}, {"Name": "Click on Accounts", "Command": "Click", "Target": "{{Selectors.AccountBtn}}"}, {"Name": "Click on Accounts", "Command": "Click", "Target": "{{Selectors.SupAccountsBtn}}"}, {"Name": "Click on Account with Disabled status", "Command": "Click", "Target": "{{Selectors.AccountDisabled}}"}, {"Name": "Click on Pay bills from account and ensure the error message appears", "Command": "Click", "Target": "{{Selectors.PayFromAccountBtn}}"}]}