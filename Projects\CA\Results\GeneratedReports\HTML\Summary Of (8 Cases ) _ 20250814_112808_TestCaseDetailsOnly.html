<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Test Execution Report</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <div class="header">
            <img src="D:\TFS\DevWork\R and D Work\TestAutomationFramework\TestAutomationFramework\Projects\CA\Templates/CA_Logo.png" alt="Company Logo">
            <h1 {{HideForSingleTestExecutionFlag}}>Cards Inquiry, Fawry Services, Happy Points Suites Execution Report</h1>
            <p>Generated on: <strong>2025-08-14 11:28 AM</strong> | Environment: <strong>UAT</strong> | Browser: <strong>Chrome</strong> | Duration:<strong>00:02:18</strong></p>
        </div>
        <h2 {{HideForSingleTestExecutionFlag}}>Overall Summary</h2>
<table  class="summary-table overall-summary" {{HideForSingleTestExecutionFlag}}>
    <tr>
        <th>Total Suites</th>
        <th>Total Modules</th>
        <th>Total Test Cases</th>
        <!-- <th>Total Steps</th>-->
        <th>Passed</th>
        <th>Failed</th>
        <th>Total Duration</th>

        <!-- <th>Skipped</th>-->
    </tr>
    <tr>
        <td>3</td>
        <td>0</td>
        <td>6</td>
        <!--<td>0</td>-->
        <td>0</td>
        <td>6</td>
        <!-- <td>{{TotalSkipped}}</td>-->
         <td>00:02:18</td>

    </tr>
</table>

		<table border="1" cellpadding="5" cellspacing="0" style="border-collapse: collapse; width: 100%;">
    <thead style="background-color: #00796B; color: white;">
        <tr>
            <th>#</th>
            <th>Test Case ID</th>
            <th>Test Case Name</th>
            <th>Status</th>
            <th>Duration</th>
            <th>TestData</th>
            <th>Error/Remarks</th>
            <th>Screenshot</th>
            <th>Suite</th>

        </tr>
    </thead>
    <tbody>
        <!-- TestCaseDetailRow -->
<tr>
    <td>1</td>
    <td>TC-115</td>
    <td>Click on Card Number</td>
    <td style="color: red; font-weight: bold;">Failed</td>
    <td>00:24</td>
    <td>
<div><strong>UserName</strong>: amiratest</div>
<div><strong>ChallengeAnswer</strong>: bmw</div>
</td>

    <td>Error executing step: Click on Card.</td>
    <td><img class='test-step-image' style='height: 120px;' src='D:\TFS\DevWork\R and D Work\TestAutomationFramework\TestAutomationFramework\Projects\CA\Results\ScreenShots\Cards Inquiry\2025-08-14 11.26.33.650_Click on the Login Button.png' alt='Step Screenshot'></td>
<td rowspan=3>Cards Inquiry</td>
</tr>
<!-- TestCaseDetailRow -->
<tr>
    <td>2</td>
    <td>TC-117</td>
    <td>Click Statements</td>
    <td style="color: red; font-weight: bold;">Failed</td>
    <td>00:25</td>
    <td>
<div><strong>UserName</strong>: amiratest</div>
<div><strong>ChallengeAnswer</strong>: bmw</div>
</td>

    <td>Error executing step: Type the answer for the Challenge Question.</td>
    <td></td>
</tr>
<!-- TestCaseDetailRow -->
<tr>
    <td>3</td>
    <td>TC-118</td>
    <td>Click Pay From</td>
    <td style="color: red; font-weight: bold;">Failed</td>
    <td>00:22</td>
    <td>
<div><strong>UserName</strong>: amiratest</div>
<div><strong>ChallengeAnswer</strong>: bmw</div>
</td>

    <td>Error executing step: Click on the Login Button.</td>
    <td><img class='test-step-image' style='height: 120px;' src='D:\TFS\DevWork\R and D Work\TestAutomationFramework\TestAutomationFramework\Projects\CA\Results\ScreenShots\Cards Inquiry\2025-08-14 11.27.04.466_Type the answer for the Challenge Question.png' alt='Step Screenshot'></td>
</tr>
<!-- TestCaseDetailRow -->
<tr>
    <td>4</td>
    <td>TC-142</td>
    <td>Bill Payment - New Payment</td>
    <td style="color: red; font-weight: bold;">Failed</td>
    <td>00:19</td>
    <td>
<div><strong>UserName</strong>: amiratest</div>
<div><strong>ChallengeAnswer</strong>: bmw</div>
</td>

    <td>Error executing step: Click on Continue Button.</td>
    <td><img class='test-step-image' style='height: 120px;' src='D:\TFS\DevWork\R and D Work\TestAutomationFramework\TestAutomationFramework\Projects\CA\Results\ScreenShots\Fawry Services\2025-08-14 11.26.59.670_Type the User Name.png' alt='Step Screenshot'></td>
<td rowspan=2>Fawry Services</td>
</tr>
<!-- TestCaseDetailRow -->
<tr>
    <td>5</td>
    <td>TC-143</td>
    <td>Bill payment - existing bill</td>
    <td style="color: red; font-weight: bold;">Failed</td>
    <td>00:22</td>
    <td>
<div><strong>UserName</strong>: amiratest</div>
<div><strong>ChallengeAnswer</strong>: bmw</div>
</td>

    <td>Error executing step: Type the answer for the Challenge Question.</td>
    <td><img class='test-step-image' style='height: 120px;' src='D:\TFS\DevWork\R and D Work\TestAutomationFramework\TestAutomationFramework\Projects\CA\Results\ScreenShots\Fawry Services\2025-08-14 11.27.41.072_Type the Password.png' alt='Step Screenshot'></td>
</tr>
<!-- TestCaseDetailRow -->
<tr>
    <td>6</td>
    <td>TC-153</td>
    <td>Go to Merchant</td>
    <td style="color: red; font-weight: bold;">Failed</td>
    <td>00:24</td>
    <td>
<div><strong>UserName</strong>: amiratest</div>
<div><strong>ChallengeAnswer</strong>: bmw</div>
</td>

    <td>Error executing step: Click on Login Button.</td>
    <td><img class='test-step-image' style='height: 120px;' src='D:\TFS\DevWork\R and D Work\TestAutomationFramework\TestAutomationFramework\Projects\CA\Results\ScreenShots\Happy Points\2025-08-14 11.27.42.508_Click on Finish Button to skip the demo.png' alt='Step Screenshot'></td>
<td rowspan=1>Happy Points</td>
</tr>

    </tbody>
</table>
        
    </div>
    <div class="footer">
        <p>End of Report</p>
    </div>
</body>
</html>
