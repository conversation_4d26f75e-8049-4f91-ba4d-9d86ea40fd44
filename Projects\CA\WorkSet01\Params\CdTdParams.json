{"Selectors": {"DepositsButton": "xpath=//div[@class='mainmenuitems']//div[@class='imgmenunormal'][.//img[contains(@src, 'Dep_Icon.svg')]]", "AccountsButton": "xpath=//div[@class='mainmenuitems']//label[contains(text(), 'Accounts')]", "ApplyForProduct": "xpath=//div[@class='mainmenuitems']//label[contains(text(), 'Apply For Product')]", "Deposits": "xpath=/html/body/div[2]/div[2]/div/div/div[2]/div/div[3]/div/div/div/div[2]/div/div[3]/div[2]", "ApplyForDeposit": "id=Apply", "ApplyForCertificate": "id=Apply", "btnApplyForDepositApplyNow": "xpath=/html/body/div[2]/div[2]/div/div/div[4]/div[1]/div/div[2]/div/div[1]/div/div/div/div[2]/div/div[6]/div/span", "CertificateDeposits": "xpath=//div[contains(@class, 'mainmenuitems')]//label[contains(text(), 'Certificate Deposits')]", "Certificates": "xpath=//div[contains(@class, 'mainmenuitems')]//label[contains(text(), 'Certificates')]", "CloseMaturity": "id=ProductBlock8", "TimeDeposits": "xpath=//div[contains(@class, 'mainmenuitems')]//label[contains(text(), 'Time Deposits')]", "SelectCertificateBtn": "id=SelectCertificateBtn", "Certificate": "id=ProductBlock1", "Deposit": "id=ProductBlock13", "DepositEGPFloating": "id=ProductBlock19", "durationDay": "id=durationDay", "CertificateUsd": "id=ProductBlock3", "AmountMaxMinMulHint": "id=amoutHintLbl", "accountUsd": "id=Account00021810031968", "Amount": "id=Amount", "input_hint": "xpath=//div[@class='input_hint']", "CalculatedMaturityAmountSpn": "id=maturityAmountSpn", "CalculatedTotalInterestRateSpn": "id=totalInterestRateSpn", "SelectAccountBtn": "id=SelectAccountBtn1", "Account": "id=Account00022090000052", "SameAccOption": "css=#deductingAccDiv > div:nth-child(1) > div > div > label:nth-child(1) > input", "SameAccOptionNo": "css=#deductingAccDiv > div:nth-child(1) > div > div > label:nth-child(2) > input", "ContinueBookProductBtn": "id=ContinueBookProductBtn", "AutorenewalToggle": "id=toggle", "Principal": "css=#prinAmpDiv > div > label:nth-child(2) > input", "TermsLink": "css=#htmlTemp > div > div > div.section_body > div > div > div.terms.mb-2.mt-2 > a", "TermsOptions": "id=IsAgree", "ContinueBookDepositBtn": "id=ContinueBookDepositBtn", "Done": "css=#InnerHtmlwidget > div > div > div > div > div.success_text_content.back_white > div.success_text > h1", "popup_message": "id=popup_message", "popup_ok": "id=popup_ok", "popupCancel": "id=popup_cancel", "popup": "css=#popbuttons > div > button.btn.Cancel.back_gradient2", "kyc": "class=Cancel", "deposits": "xpath=/html/body/div[2]/div[2]/div/div/div[2]/div/div[3]/div/div/div/div[2]/div/div[3]", "certificate_deposits": "xpath=/html/body/div[2]/div[2]/div/div/div[2]/div/div[3]/div/div/div/div[2]/div/div[3]/div[3]/div/ul[2]", "cds3years_Accumulative": "id=ProductBlock1", "SelectAccount": "id=SelectAccountBtn1", "Account00022090000052": "id=Account00022090000052", "yes": "xpath=/html/body/div[2]/div[2]/div/div/div[4]/div[1]/div/div[2]/div/div/div/div[2]/div/div/div[6]/div[3]/div[1]/div/div/label[1]/input", "verification_text": "class=text_red", "Time_deposits": "xpath=/html/body/div[2]/div[2]/div/div/div[2]/div/div[3]/div/div/div/div[2]/div/div[3]/div[3]/div/ul[1]", "ProductBlock0": "id=ProductBlock0", "ProductBlock3": "id=ProductBlock3", "ProductBlock6": "id=ProductBlock6", "ProductBlock19": "id=ProductBlock19", "ProductBlock1": "id=ProductBlock1", "ProductBlock7": "id=ProductBlock7", "ProductBlock8": "id=ProductBlock8", "ProductBlock14": "id=ProductBlock14", "DepositBlock79": "id=DepositBlock79", "FloatingDeposit": "id=DepositBlock71", "FixedDeposit": "id=DepositBlock81", "accounts": "xpath=//*[@id='RDMenuItemsSection']/div[2]", "InsideDepoists": "xpath=/html/body/div[2]/div[2]/div/div/div[2]/div/div[3]/div/div/div/div[2]/div/div[2]/div[3]/div/ul[5]", "DepositBlock61": "id=DepositBlock61", "Account00021110017534": "id=Account00021110017534", "DepositWidgetTitle": "id=DepositWidgetTitle", "InterestRate": "id=interestRate", "maturity_Amount": "id=maturityAmount", "text_primary": "xpath=//*[@id='htmlTemp']//*[text()='Confirmation']", "durationYear": "id=durationYear", "durationMonth": "id=durationMonth", "AccountBtn": "xpath=//div[@class='mainmenuitems']//label[text()='Accounts']", "EGPfixedDeposit": "xpath=//h4[text()='CAE Diamond CD 3 Years - Monthly Interest']", "DepositsBtn": "xpath=//div[contains(@class, 'mainmenuitems')]//div[contains(@class, 'collapsedmenuitem')]//label[text()='Deposits']", "CertificateDepositsBtn": "xpath=//div[@class='submenuitem']//label[text()='Certificate Deposits']", "TimeDepositsBtn": "xpath=//div[@class='mainmenuitems']//label[text()='Time Deposits']", "SupAccountsBtn": "xpath=//div[@class='submenuitem']//label[contains(text(), 'Accounts')]", "LoansBtn": "xpath=/html/body/div[2]/div[2]/div/div/div[2]/div/div[3]/div/div/div/div[2]/div/div[2]/div[3]/div/ul[2]", "SupDepositsBtn": "xpath=/html/body/div[2]/div[2]/div/div/div[2]/div/div[3]/div/div/div/div[2]/div/div[2]/div[3]/div/ul[5]", "CertificatesBtn": "xpath=//label[text()='Certificates']", "BioMetricAlert": "xpath=//*[@id='popbuttons']/div/button[2]", "InterestFrequencyValue": "id=interestFrequency", "SelectAccountBtn1": "id=SelectAccountBtn1", "deductingAccyesOption": "css=#deductingAccDiv > div:nth-child(1) > div > div > label:nth-child(1) > input", "deductingAccnoOption": "css=#deductingAccDiv > div:nth-child(1) > div > div > label:nth-child(2) > input", "IsAgree": "id=IsAgree", "productDescription": "id=productDescription", "bankiRefNo": "id=bankiRefNo", "accountOpenDate": "id=accountOpenDate", "maturityDate": "id=maturityDate", "Instructions": "id=Instructions", "Type": "id=Type", "Tenor": "id=Tenor", "status": "id=status", "blockamount": "id=blockamount", "nextInterestDate": "id=nextInterestDate", "interestFrequency": "id=interestFrequency", "Currency": "id=C<PERSON>rency"}}