<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Test Execution Report</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <div class="header">
            <img src="D:\TFS\DevWork\R and D Work\TestAutomationFramework\TestAutomationFramework\Projects\CA\Templates/CA_Logo.png" alt="Company Logo">
            <h1  >Cards Inquiry, Fawry Services, Happy Points Suites Execution Report</h1>
            <p>Generated on: <strong>2025-08-14 11:28 AM</strong> | Environment: <strong>UAT</strong> | Browser: <strong>Chrome</strong> | Duration:<strong>00:02:18</strong></p>
        </div>
        <h2  >Overall Summary</h2>
<table  class="summary-table overall-summary"  >
    <tr>
        <th>Total Suites</th>
        <th>Total Modules</th>
        <th>Total Test Cases</th>
        <!-- <th>Total Steps</th>-->
        <th>Passed</th>
        <th>Failed</th>
        <th>Total Duration</th>

        <!-- <th>Skipped</th>-->
    </tr>
    <tr>
        <td>3</td>
        <td>0</td>
        <td>6</td>
        <!--<td>0</td>-->
        <td>0</td>
        <td>6</td>
        <!-- <td>{{TotalSkipped}}</td>-->
         <td>00:02:18</td>

    </tr>
</table>

		<table border="1" cellpadding="5" cellspacing="0" style="border-collapse: collapse; width: 100%;">
    <thead style="background-color: #00796B; color: white;">
        <tr>
            <th>#</th>
            <th>Test Case ID</th>
            <th>Test Case Name</th>
            <th>Status</th>
            <th>Duration</th>
            <th>TestData</th>
            <th>Error/Remarks</th>
            <th>Screenshot</th>
            <th>Suite</th>

        </tr>
    </thead>
    <tbody>
        <!-- TestCaseDetailRow -->
<tr>
    <td>1</td>
    <td>TC-115</td>
    <td>Click on Card Number</td>
    <td style="color: red; font-weight: bold;">Failed</td>
    <td>00:24</td>
    <td>
<div><strong>UserName</strong>: amiratest</div>
<div><strong>ChallengeAnswer</strong>: bmw</div>
</td>

    <td>Error executing step: Click on Card.</td>
    <td><img class='test-step-image' style='height: 120px;' src='D:\TFS\DevWork\R and D Work\TestAutomationFramework\TestAutomationFramework\Projects\CA\Results\ScreenShots\Cards Inquiry\2025-08-14 11.26.33.650_Click on the Login Button.png' alt='Step Screenshot'></td>
<td rowspan=3>Cards Inquiry</td>
</tr>
<!-- TestCaseDetailRow -->
<tr>
    <td>2</td>
    <td>TC-117</td>
    <td>Click Statements</td>
    <td style="color: red; font-weight: bold;">Failed</td>
    <td>00:25</td>
    <td>
<div><strong>UserName</strong>: amiratest</div>
<div><strong>ChallengeAnswer</strong>: bmw</div>
</td>

    <td>Error executing step: Type the answer for the Challenge Question.</td>
    <td></td>
</tr>
<!-- TestCaseDetailRow -->
<tr>
    <td>3</td>
    <td>TC-118</td>
    <td>Click Pay From</td>
    <td style="color: red; font-weight: bold;">Failed</td>
    <td>00:22</td>
    <td>
<div><strong>UserName</strong>: amiratest</div>
<div><strong>ChallengeAnswer</strong>: bmw</div>
</td>

    <td>Error executing step: Click on the Login Button.</td>
    <td><img class='test-step-image' style='height: 120px;' src='D:\TFS\DevWork\R and D Work\TestAutomationFramework\TestAutomationFramework\Projects\CA\Results\ScreenShots\Cards Inquiry\2025-08-14 11.27.04.466_Type the answer for the Challenge Question.png' alt='Step Screenshot'></td>
</tr>
<!-- TestCaseDetailRow -->
<tr>
    <td>4</td>
    <td>TC-142</td>
    <td>Bill Payment - New Payment</td>
    <td style="color: red; font-weight: bold;">Failed</td>
    <td>00:19</td>
    <td>
<div><strong>UserName</strong>: amiratest</div>
<div><strong>ChallengeAnswer</strong>: bmw</div>
</td>

    <td>Error executing step: Click on Continue Button.</td>
    <td><img class='test-step-image' style='height: 120px;' src='D:\TFS\DevWork\R and D Work\TestAutomationFramework\TestAutomationFramework\Projects\CA\Results\ScreenShots\Fawry Services\2025-08-14 11.26.59.670_Type the User Name.png' alt='Step Screenshot'></td>
<td rowspan=2>Fawry Services</td>
</tr>
<!-- TestCaseDetailRow -->
<tr>
    <td>5</td>
    <td>TC-143</td>
    <td>Bill payment - existing bill</td>
    <td style="color: red; font-weight: bold;">Failed</td>
    <td>00:22</td>
    <td>
<div><strong>UserName</strong>: amiratest</div>
<div><strong>ChallengeAnswer</strong>: bmw</div>
</td>

    <td>Error executing step: Type the answer for the Challenge Question.</td>
    <td><img class='test-step-image' style='height: 120px;' src='D:\TFS\DevWork\R and D Work\TestAutomationFramework\TestAutomationFramework\Projects\CA\Results\ScreenShots\Fawry Services\2025-08-14 11.27.41.072_Type the Password.png' alt='Step Screenshot'></td>
</tr>
<!-- TestCaseDetailRow -->
<tr>
    <td>6</td>
    <td>TC-153</td>
    <td>Go to Merchant</td>
    <td style="color: red; font-weight: bold;">Failed</td>
    <td>00:24</td>
    <td>
<div><strong>UserName</strong>: amiratest</div>
<div><strong>ChallengeAnswer</strong>: bmw</div>
</td>

    <td>Error executing step: Click on Login Button.</td>
    <td><img class='test-step-image' style='height: 120px;' src='D:\TFS\DevWork\R and D Work\TestAutomationFramework\TestAutomationFramework\Projects\CA\Results\ScreenShots\Happy Points\2025-08-14 11.27.42.508_Click on Finish Button to skip the demo.png' alt='Step Screenshot'></td>
<td rowspan=1>Happy Points</td>
</tr>

    </tbody>
</table>
        <h2  >7. Cards Inquiry</h2>
<table  class="summary-table suite-summary"  >
    <tr>
        <th>Total Modules</th>
        <th>Total Test Cases</th>
        <th>Passed</th>
        <th>Failed</th>
      <!--  <th>Skipped</th>-->
    </tr>
    <tr>
        <td>0</td>
        <td>3</td>
        <td>0</td>
        <td>3</td>
       <!-- <td>{{SuiteSkipped}}</td>-->
    </tr>
</table>
<div class="suite-content">
    <div class="metadata">
    <div class="test-case">
        <strong>7.1.1. Click on Card Number</strong><span style="font-weight: normal;"><i> (TC-115 | Cards Inquiry)</i></span>
    </div>
    <div class="status">
        <span>Status:</span>
        <br>
        <span class="status-failed">Failed</span>
    </div>
    <div class="status">
        <span>Exec Time (mm:ss)</span>
        <br>
        <span>00:24</span>
    </div>
</div>
<div class="metadata error" style="display: block">
    <span>Error Message:</span>
    <span class="status-failed">Error executing step: Click on Card.</span>
</div>
<table class="test-steps-table">
    <tr>
        <th>Step</th>
        <th>Description</th>
        <th style="display: none">Result</th>
        <th style="display: none">Execution Time</th>
        <th>Screenshot</th>
    </tr>
    <tr>
    <td>1</td>
    <td>Click on Finish Button to skip the demo</td>
    <td style="display: none;">Passed</td>
    <td style="display: none;">0s</td>
    <td><img class='test-step-image' src='D:\TFS\DevWork\R and D Work\TestAutomationFramework\TestAutomationFramework\Projects\CA\Results\ScreenShots\Cards Inquiry\2025-08-14 11.26.18.204_Click on Finish Button to skip the demo.png' alt='Step Screenshot'></td>
</tr>
<tr>
    <td>2</td>
    <td>Click on Login Button</td>
    <td style="display: none;">Passed</td>
    <td style="display: none;">0s</td>
    <td><img class='test-step-image' src='D:\TFS\DevWork\R and D Work\TestAutomationFramework\TestAutomationFramework\Projects\CA\Results\ScreenShots\Cards Inquiry\2025-08-14 11.26.22.509_Click on Login Button.png' alt='Step Screenshot'></td>
</tr>
<tr>
    <td>3</td>
    <td>Type the User Name</td>
    <td style="display: none;">Passed</td>
    <td style="display: none;">0s</td>
    <td><img class='test-step-image' src='D:\TFS\DevWork\R and D Work\TestAutomationFramework\TestAutomationFramework\Projects\CA\Results\ScreenShots\Cards Inquiry\2025-08-14 11.26.23.472_Type the User Name.png' alt='Step Screenshot'></td>
</tr>
<tr>
    <td>4</td>
    <td>Click on Continue Button</td>
    <td style="display: none;">Passed</td>
    <td style="display: none;">0s</td>
    <td><img class='test-step-image' src='D:\TFS\DevWork\R and D Work\TestAutomationFramework\TestAutomationFramework\Projects\CA\Results\ScreenShots\Cards Inquiry\2025-08-14 11.26.26.076_Click on Continue Button.png' alt='Step Screenshot'></td>
</tr>
<tr>
    <td>5</td>
    <td>Type the Password</td>
    <td style="display: none;">Passed</td>
    <td style="display: none;">0s</td>
    <td><img class='test-step-image' src='D:\TFS\DevWork\R and D Work\TestAutomationFramework\TestAutomationFramework\Projects\CA\Results\ScreenShots\Cards Inquiry\2025-08-14 11.26.26.860_Type the Password.png' alt='Step Screenshot'></td>
</tr>
<tr>
    <td>6</td>
    <td>Type the answer for the Challenge Question</td>
    <td style="display: none;">Passed</td>
    <td style="display: none;">0s</td>
    <td><img class='test-step-image' src='D:\TFS\DevWork\R and D Work\TestAutomationFramework\TestAutomationFramework\Projects\CA\Results\ScreenShots\Cards Inquiry\2025-08-14 11.26.27.759_Type the answer for the Challenge Question.png' alt='Step Screenshot'></td>
</tr>
<tr>
    <td>7</td>
    <td>Click on the Login Button</td>
    <td style="display: none;">Passed</td>
    <td style="display: none;">0s</td>
    <td><img class='test-step-image' src='D:\TFS\DevWork\R and D Work\TestAutomationFramework\TestAutomationFramework\Projects\CA\Results\ScreenShots\Cards Inquiry\2025-08-14 11.26.33.650_Click on the Login Button.png' alt='Step Screenshot'></td>
</tr>

    <tr>
        <td class="status-failed" style="display:none" ; colspan="3">Error Details: invalid session id</td>
    </tr>
</table>
<div class="metadata">
    <div class="test-case">
        <strong>7.1.2. Click Statements</strong><span style="font-weight: normal;"><i> (TC-117 | Cards Inquiry)</i></span>
    </div>
    <div class="status">
        <span>Status:</span>
        <br>
        <span class="status-failed">Failed</span>
    </div>
    <div class="status">
        <span>Exec Time (mm:ss)</span>
        <br>
        <span>00:25</span>
    </div>
</div>
<div class="metadata error" style="display: block">
    <span>Error Message:</span>
    <span class="status-failed">Error executing step: Type the answer for the Challenge Question.</span>
</div>
<table class="test-steps-table">
    <tr>
        <th>Step</th>
        <th>Description</th>
        <th style="display: none">Result</th>
        <th style="display: none">Execution Time</th>
        <th>Screenshot</th>
    </tr>
    <tr>
    <td>1</td>
    <td>Click on Finish Button to skip the demo</td>
    <td style="display: none;">Passed</td>
    <td style="display: none;">0s</td>
    <td><img class='test-step-image' src='D:\TFS\DevWork\R and D Work\TestAutomationFramework\TestAutomationFramework\Projects\CA\Results\ScreenShots\Cards Inquiry\2025-08-14 11.26.22.507_Click on Finish Button to skip the demo.png' alt='Step Screenshot'></td>
</tr>
<tr>
    <td>2</td>
    <td>Click on Login Button</td>
    <td style="display: none;">Passed</td>
    <td style="display: none;">0s</td>
    <td><img class='test-step-image' src='D:\TFS\DevWork\R and D Work\TestAutomationFramework\TestAutomationFramework\Projects\CA\Results\ScreenShots\Cards Inquiry\2025-08-14 11.26.26.723_Click on Login Button.png' alt='Step Screenshot'></td>
</tr>
<tr>
    <td>3</td>
    <td>Type the User Name</td>
    <td style="display: none;">Passed</td>
    <td style="display: none;">0s</td>
    <td><img class='test-step-image' src='D:\TFS\DevWork\R and D Work\TestAutomationFramework\TestAutomationFramework\Projects\CA\Results\ScreenShots\Cards Inquiry\2025-08-14 11.26.27.636_Type the User Name.png' alt='Step Screenshot'></td>
</tr>
<tr>
    <td>4</td>
    <td>Click on Continue Button</td>
    <td style="display: none;">Passed</td>
    <td style="display: none;">0s</td>
    <td><img class='test-step-image' src='D:\TFS\DevWork\R and D Work\TestAutomationFramework\TestAutomationFramework\Projects\CA\Results\ScreenShots\Cards Inquiry\2025-08-14 11.26.31.627_Click on Continue Button.png' alt='Step Screenshot'></td>
</tr>
<tr>
    <td>5</td>
    <td>Type the Password</td>
    <td style="display: none;">Passed</td>
    <td style="display: none;">0s</td>
    <td></td>
</tr>

    <tr>
        <td class="status-failed" style="display:none" ; colspan="3">Error Details: invalid session id: session deleted as the browser has closed the connection
from disconnected: not connected to DevTools
  (Session info: chrome=139.0.7258.66)</td>
    </tr>
</table>
<div class="metadata">
    <div class="test-case">
        <strong>7.1.3. Click Pay From</strong><span style="font-weight: normal;"><i> (TC-118 | Cards Inquiry)</i></span>
    </div>
    <div class="status">
        <span>Status:</span>
        <br>
        <span class="status-failed">Failed</span>
    </div>
    <div class="status">
        <span>Exec Time (mm:ss)</span>
        <br>
        <span>00:22</span>
    </div>
</div>
<div class="metadata error" style="display: block">
    <span>Error Message:</span>
    <span class="status-failed">Error executing step: Click on the Login Button.</span>
</div>
<table class="test-steps-table">
    <tr>
        <th>Step</th>
        <th>Description</th>
        <th style="display: none">Result</th>
        <th style="display: none">Execution Time</th>
        <th>Screenshot</th>
    </tr>
    <tr>
    <td>1</td>
    <td>Click on Finish Button to skip the demo</td>
    <td style="display: none;">Passed</td>
    <td style="display: none;">0s</td>
    <td><img class='test-step-image' src='D:\TFS\DevWork\R and D Work\TestAutomationFramework\TestAutomationFramework\Projects\CA\Results\ScreenShots\Cards Inquiry\2025-08-14 11.26.55.851_Click on Finish Button to skip the demo.png' alt='Step Screenshot'></td>
</tr>
<tr>
    <td>2</td>
    <td>Click on Login Button</td>
    <td style="display: none;">Passed</td>
    <td style="display: none;">0s</td>
    <td><img class='test-step-image' src='D:\TFS\DevWork\R and D Work\TestAutomationFramework\TestAutomationFramework\Projects\CA\Results\ScreenShots\Cards Inquiry\2025-08-14 11.26.58.871_Click on Login Button.png' alt='Step Screenshot'></td>
</tr>
<tr>
    <td>3</td>
    <td>Type the User Name</td>
    <td style="display: none;">Passed</td>
    <td style="display: none;">0s</td>
    <td><img class='test-step-image' src='D:\TFS\DevWork\R and D Work\TestAutomationFramework\TestAutomationFramework\Projects\CA\Results\ScreenShots\Cards Inquiry\2025-08-14 11.26.59.862_Type the User Name.png' alt='Step Screenshot'></td>
</tr>
<tr>
    <td>4</td>
    <td>Click on Continue Button</td>
    <td style="display: none;">Passed</td>
    <td style="display: none;">0s</td>
    <td><img class='test-step-image' src='D:\TFS\DevWork\R and D Work\TestAutomationFramework\TestAutomationFramework\Projects\CA\Results\ScreenShots\Cards Inquiry\2025-08-14 11.27.01.823_Click on Continue Button.png' alt='Step Screenshot'></td>
</tr>
<tr>
    <td>5</td>
    <td>Type the Password</td>
    <td style="display: none;">Passed</td>
    <td style="display: none;">0s</td>
    <td><img class='test-step-image' src='D:\TFS\DevWork\R and D Work\TestAutomationFramework\TestAutomationFramework\Projects\CA\Results\ScreenShots\Cards Inquiry\2025-08-14 11.27.02.950_Type the Password.png' alt='Step Screenshot'></td>
</tr>
<tr>
    <td>6</td>
    <td>Type the answer for the Challenge Question</td>
    <td style="display: none;">Passed</td>
    <td style="display: none;">0s</td>
    <td><img class='test-step-image' src='D:\TFS\DevWork\R and D Work\TestAutomationFramework\TestAutomationFramework\Projects\CA\Results\ScreenShots\Cards Inquiry\2025-08-14 11.27.04.466_Type the answer for the Challenge Question.png' alt='Step Screenshot'></td>
</tr>

    <tr>
        <td class="status-failed" style="display:none" ; colspan="3">Error Details: Can`t Find Element with target: id=LoginAuthBtn </td>
    </tr>
</table>

</div>
<h2  >8. Fawry Services</h2>
<table  class="summary-table suite-summary"  >
    <tr>
        <th>Total Modules</th>
        <th>Total Test Cases</th>
        <th>Passed</th>
        <th>Failed</th>
      <!--  <th>Skipped</th>-->
    </tr>
    <tr>
        <td>0</td>
        <td>2</td>
        <td>0</td>
        <td>2</td>
       <!-- <td>{{SuiteSkipped}}</td>-->
    </tr>
</table>
<div class="suite-content">
    <div class="metadata">
    <div class="test-case">
        <strong>8.1.1. Bill Payment - New Payment</strong><span style="font-weight: normal;"><i> (TC-142 | Fawry Services)</i></span>
    </div>
    <div class="status">
        <span>Status:</span>
        <br>
        <span class="status-failed">Failed</span>
    </div>
    <div class="status">
        <span>Exec Time (mm:ss)</span>
        <br>
        <span>00:19</span>
    </div>
</div>
<div class="metadata error" style="display: block">
    <span>Error Message:</span>
    <span class="status-failed">Error executing step: Click on Continue Button.</span>
</div>
<table class="test-steps-table">
    <tr>
        <th>Step</th>
        <th>Description</th>
        <th style="display: none">Result</th>
        <th style="display: none">Execution Time</th>
        <th>Screenshot</th>
    </tr>
    <tr>
    <td>1</td>
    <td>Click on Finish Button to skip the demo</td>
    <td style="display: none;">Passed</td>
    <td style="display: none;">0s</td>
    <td><img class='test-step-image' src='D:\TFS\DevWork\R and D Work\TestAutomationFramework\TestAutomationFramework\Projects\CA\Results\ScreenShots\Fawry Services\2025-08-14 11.26.55.754_Click on Finish Button to skip the demo.png' alt='Step Screenshot'></td>
</tr>
<tr>
    <td>2</td>
    <td>Click on Login Button</td>
    <td style="display: none;">Passed</td>
    <td style="display: none;">0s</td>
    <td><img class='test-step-image' src='D:\TFS\DevWork\R and D Work\TestAutomationFramework\TestAutomationFramework\Projects\CA\Results\ScreenShots\Fawry Services\2025-08-14 11.26.58.555_Click on Login Button.png' alt='Step Screenshot'></td>
</tr>
<tr>
    <td>3</td>
    <td>Type the User Name</td>
    <td style="display: none;">Passed</td>
    <td style="display: none;">0s</td>
    <td><img class='test-step-image' src='D:\TFS\DevWork\R and D Work\TestAutomationFramework\TestAutomationFramework\Projects\CA\Results\ScreenShots\Fawry Services\2025-08-14 11.26.59.670_Type the User Name.png' alt='Step Screenshot'></td>
</tr>

    <tr>
        <td class="status-failed" style="display:none" ; colspan="3">Error Details: invalid session id: session deleted as the browser has closed the connection
from disconnected: not connected to DevTools
  (Session info: chrome=139.0.7258.66)</td>
    </tr>
</table>
<div class="metadata">
    <div class="test-case">
        <strong>8.1.2. Bill payment - existing bill</strong><span style="font-weight: normal;"><i> (TC-143 | Fawry Services)</i></span>
    </div>
    <div class="status">
        <span>Status:</span>
        <br>
        <span class="status-failed">Failed</span>
    </div>
    <div class="status">
        <span>Exec Time (mm:ss)</span>
        <br>
        <span>00:22</span>
    </div>
</div>
<div class="metadata error" style="display: block">
    <span>Error Message:</span>
    <span class="status-failed">Error executing step: Type the answer for the Challenge Question.</span>
</div>
<table class="test-steps-table">
    <tr>
        <th>Step</th>
        <th>Description</th>
        <th style="display: none">Result</th>
        <th style="display: none">Execution Time</th>
        <th>Screenshot</th>
    </tr>
    <tr>
    <td>1</td>
    <td>Click on Finish Button to skip the demo</td>
    <td style="display: none;">Passed</td>
    <td style="display: none;">0s</td>
    <td><img class='test-step-image' src='D:\TFS\DevWork\R and D Work\TestAutomationFramework\TestAutomationFramework\Projects\CA\Results\ScreenShots\Fawry Services\2025-08-14 11.27.24.887_Click on Finish Button to skip the demo.png' alt='Step Screenshot'></td>
</tr>
<tr>
    <td>2</td>
    <td>Click on Login Button</td>
    <td style="display: none;">Passed</td>
    <td style="display: none;">0s</td>
    <td><img class='test-step-image' src='D:\TFS\DevWork\R and D Work\TestAutomationFramework\TestAutomationFramework\Projects\CA\Results\ScreenShots\Fawry Services\2025-08-14 11.27.34.152_Click on Login Button.png' alt='Step Screenshot'></td>
</tr>
<tr>
    <td>3</td>
    <td>Type the User Name</td>
    <td style="display: none;">Passed</td>
    <td style="display: none;">0s</td>
    <td><img class='test-step-image' src='D:\TFS\DevWork\R and D Work\TestAutomationFramework\TestAutomationFramework\Projects\CA\Results\ScreenShots\Fawry Services\2025-08-14 11.27.37.319_Type the User Name.png' alt='Step Screenshot'></td>
</tr>
<tr>
    <td>4</td>
    <td>Click on Continue Button</td>
    <td style="display: none;">Passed</td>
    <td style="display: none;">0s</td>
    <td><img class='test-step-image' src='D:\TFS\DevWork\R and D Work\TestAutomationFramework\TestAutomationFramework\Projects\CA\Results\ScreenShots\Fawry Services\2025-08-14 11.27.39.824_Click on Continue Button.png' alt='Step Screenshot'></td>
</tr>
<tr>
    <td>5</td>
    <td>Type the Password</td>
    <td style="display: none;">Passed</td>
    <td style="display: none;">0s</td>
    <td><img class='test-step-image' src='D:\TFS\DevWork\R and D Work\TestAutomationFramework\TestAutomationFramework\Projects\CA\Results\ScreenShots\Fawry Services\2025-08-14 11.27.41.072_Type the Password.png' alt='Step Screenshot'></td>
</tr>

    <tr>
        <td class="status-failed" style="display:none" ; colspan="3">Error Details: Can`t Find Element with target: id=ChallengeQuestionAnswer </td>
    </tr>
</table>

</div>
<h2  >9. Happy Points</h2>
<table  class="summary-table suite-summary"  >
    <tr>
        <th>Total Modules</th>
        <th>Total Test Cases</th>
        <th>Passed</th>
        <th>Failed</th>
      <!--  <th>Skipped</th>-->
    </tr>
    <tr>
        <td>0</td>
        <td>1</td>
        <td>0</td>
        <td>1</td>
       <!-- <td>{{SuiteSkipped}}</td>-->
    </tr>
</table>
<div class="suite-content">
    <div class="metadata">
    <div class="test-case">
        <strong>9.1.1. Go to Merchant</strong><span style="font-weight: normal;"><i> (TC-153 | Happy Points)</i></span>
    </div>
    <div class="status">
        <span>Status:</span>
        <br>
        <span class="status-failed">Failed</span>
    </div>
    <div class="status">
        <span>Exec Time (mm:ss)</span>
        <br>
        <span>00:24</span>
    </div>
</div>
<div class="metadata error" style="display: block">
    <span>Error Message:</span>
    <span class="status-failed">Error executing step: Click on Login Button.</span>
</div>
<table class="test-steps-table">
    <tr>
        <th>Step</th>
        <th>Description</th>
        <th style="display: none">Result</th>
        <th style="display: none">Execution Time</th>
        <th>Screenshot</th>
    </tr>
    <tr>
    <td>1</td>
    <td>Click on Finish Button to skip the demo</td>
    <td style="display: none;">Passed</td>
    <td style="display: none;">0s</td>
    <td><img class='test-step-image' src='D:\TFS\DevWork\R and D Work\TestAutomationFramework\TestAutomationFramework\Projects\CA\Results\ScreenShots\Happy Points\2025-08-14 11.27.42.508_Click on Finish Button to skip the demo.png' alt='Step Screenshot'></td>
</tr>

    <tr>
        <td class="status-failed" style="display:none" ; colspan="3">Error Details: Can`t Find Element with target: id=LoginBtn </td>
    </tr>
</table>

</div>

    </div>
    <div class="footer">
        <p>End of Report</p>
    </div>
</body>
</html>
