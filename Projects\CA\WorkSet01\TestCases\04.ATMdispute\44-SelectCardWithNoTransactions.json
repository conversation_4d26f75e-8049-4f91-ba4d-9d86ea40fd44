{"TestCaseName": "Choose card with no transactions", "TestCaseCode": "TC-44", "Steps": [{"TestCaseReference": {"TestCasePath": "WorkSet01/TestCases/04.ATMdispute/ATMdispute.json", "ParamsReference": "WorkSet01/Params/ATMdisputeParams.json"}}, {"Name": "Click on Select Cards button", "Command": "Click", "Target": "{{Selectors.SelectButton}}", "CustomDelayBeforeStepExecustionInMilliseconds": 3000}, {"Name": "Choose a card with no ATM transactions", "Command": "click", "Target": "{{Selectors.CardWithNoTransactions}}", "CustomDelayBeforeStepExecustionInMilliseconds": 3000}, {"Name": "Verify messeage with value: {{Selectors.NoTransactionMesseage}}", "Command": "verify", "Target": "{{Selectors.popupMessage}}", "Value": "{{Selectors.NoTransactionMesseage}}"}]}