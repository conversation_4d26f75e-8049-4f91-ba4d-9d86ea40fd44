<table border="1" cellpadding="5" cellspacing="0" style="border-collapse: collapse; width: 100%;">
    <thead style="background-color: #00796B; color: white;">
        <tr>
            <th>#</th>
            <th>Test Case ID</th>
            <th>Test Case Name</th>
            <th>Status</th>
            <th>Duration</th>
            <th>TestData</th>
            <th>Error/Remarks</th>
			<th>Module</th>
            <th>Suite</th>

        </tr>
    </thead>
    <tbody>
        <!-- TestCaseDetailRow -->
<tr>
    <td>1</td>
    <td>TC-121</td>
    <td>Activate Card, check that inactive card in backend are displayed</td>
    <td>Failed</td>
    <td>00:10</td>
      <td>
<div><strong>UserName</strong>: mahmoudsayed022</div>
<div><strong>ChallengeAnswer</strong>: bmw</div>
</td>

    <td>Error executing step: Click on <PERSON><PERSON> Button.</td>


<td rowspan=4>Direct</td>
<td rowspan=4>Sanity Cases Suite</td>
</tr>
<!-- TestCaseDetailRow -->
<tr>
    <td>2</td>
    <td>TC-120</td>
    <td>Click Block card</td>
    <td>Failed</td>
    <td>00:10</td>
      <td>
<div><strong>UserName</strong>: mahmoudsayed022</div>
<div><strong>ChallengeAnswer</strong>: bmw</div>
</td>

    <td>Error executing step: Click on Continue Button.</td>


</tr>
<!-- TestCaseDetailRow -->
<tr>
    <td>3</td>
    <td>TC-125</td>
    <td>Check Blocked Cards List</td>
    <td>Failed</td>
    <td>00:13</td>
      <td>
<div><strong>UserName</strong>: mahmoudsayed022</div>
<div><strong>ChallengeAnswer</strong>: bmw</div>
</td>

    <td>Error executing step: Click on Login Button.</td>


</tr>
<!-- TestCaseDetailRow -->
<tr>
    <td>4</td>
    <td>TC-114</td>
    <td>Credit cards and prepaid cards displayed on 2 widgets</td>
    <td>Failed</td>
    <td>00:04</td>
      <td>
<div><strong>UserName</strong>: mahmoudsayed022</div>
<div><strong>ChallengeAnswer</strong>: bmw</div>
</td>

    <td>Error executing step: Click on Login Button.</td>


</tr>

    </tbody>
</table>