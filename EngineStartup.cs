using NUnit.Framework;
using System.Runtime.CompilerServices;
using TestAutomationFramework.Helpers;

namespace TestAutomationFramework
{
    public static class EngineStartup
    {
        [ModuleInitializer]
        public static void Initialize()
        {
            try
            {

                Logger.Log("Starting pre-discovery initialization...", LogLevel.Info);

                var baseDir = TestContext.Parameters.Get("Projects");

                if (string.IsNullOrWhiteSpace(baseDir))
                {
                    baseDir = GetDefaultWorkDirectory();
                }

                Environment.CurrentDirectory = baseDir;

                Logger.Log("Ending pre-discovery initialization...", LogLevel.Info);

            }
            catch (Exception ex)
            {
                Logger.Log($"Error during initialization: {ex}", LogLevel.Error);
                Environment.Exit(1); // Fail fast
            }
        }


        private static string? GetDefaultWorkDirectory()
        {

            foreach (var workPath in Config.Settings.WorkDirectories)
            {
                Logger.Log($"Checking for work directory: {workPath}", LogLevel.Info);
                string resolvedPath;

                // Expand '~' to user profile directory
                if (workPath.StartsWith("~"))
                {
                    var homeDir = Environment.GetFolderPath(Environment.SpecialFolder.UserProfile);
                    resolvedPath = Path.Combine(homeDir, workPath.TrimStart('~', '\\', '/'));
                }
                else if (Path.IsPathRooted(workPath))
                {
                    // Already absolute or UNC
                    resolvedPath = Path.GetFullPath(workPath);
                }
                else
                {
                    // Relative to AppContext.BaseDirectory
                    resolvedPath = Path.GetFullPath(Path.Combine(AppContext.BaseDirectory, workPath));
                }

                if (Directory.Exists(resolvedPath))
                {
                    Logger.Log($"Using work directory: {resolvedPath}", LogLevel.Info);
                    return resolvedPath;
                }
            }

            Logger.Log("No valid work directory found.", LogLevel.Warn);
            return null;
        }
    }
}